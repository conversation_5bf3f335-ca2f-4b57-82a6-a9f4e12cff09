#!/bin/bash

# Script to run the BusinessLM Python backend
#
# Usage:
#   ./run_backend.sh [--dev] [--port PORT]
#
# Options:
#   --dev       Run in development mode
#   --port PORT Run on specified port (default: 8000)
#
# Safety features:
#   - Fails early on errors
#   - Handles special characters and spaces in filenames
#   - Checks for Python environment

# Fail on errors, unset variables, and propagate pipe failures
set -euo pipefail

# Default values
DEV_MODE=false
PORT=8000

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --dev)
      DEV_MODE=true
      shift
      ;;
    --port)
      PORT="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: ./run_backend.sh [--dev] [--port PORT]"
      exit 1
      ;;
  esac
done

# Check if we're in the right directory
if [ ! -d "./backend" ]; then
    echo "Error: backend directory not found. Please run this script from the project root."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "./backend_env" ] && [ ! -d "./venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv backend_env
    echo "Installing dependencies..."
    ./backend_env/bin/pip install -r backend/requirements.txt
fi

# Determine which virtual environment to use
if [ -d "./backend_env" ]; then
    VENV_DIR="./backend_env"
elif [ -d "./venv" ]; then
    VENV_DIR="./venv"
else
    echo "Error: Virtual environment not found."
    exit 1
fi

# Activate virtual environment
source "${VENV_DIR}/bin/activate"

# Set environment variables
if [ "$DEV_MODE" = true ]; then
    export DEVELOPMENT_MODE=true
    echo "Running in DEVELOPMENT mode"
else
    export DEVELOPMENT_MODE=false
    echo "Running in PRODUCTION mode"
fi

export PORT="$PORT"
export FRONTEND_URL="http://localhost:3000"

# Check for .env file and load it if it exists
if [ -f ".env" ]; then
    echo "Loading environment variables from .env file"
    # Export all variables from .env file
    export $(grep -v '^#' .env | xargs)
fi

# Run the backend
echo "Starting BusinessLM Python backend on port $PORT..."
if [ "$DEV_MODE" = true ]; then
    # Run with auto-reload in development mode
    cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port "$PORT" --reload
else
    # Run without auto-reload in production mode
    cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port "$PORT"
fi
