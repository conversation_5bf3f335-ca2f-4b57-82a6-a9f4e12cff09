# Backend CLI Testing Guide

This guide explains how to use the CLI tools for testing RAG and multi-agent orchestration capabilities in the backend system.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Testing with Makefile (Recommended)](#testing-with-makefile-recommended)
4. [LLM Provider Options](#llm-provider-options)
5. [Testing Approaches](#testing-approaches)
6. [Managing Mock Documents](#managing-mock-documents)
7. [Direct Script Execution](#direct-script-execution)
8. [Benchmarking](#benchmarking)
9. [Troubleshooting](#troubleshooting)

## Overview

The backend CLI testing tools allow you to:
- Test RAG (Retrieval-Augmented Generation) capabilities
- Test multi-agent orchestration
- Create and manage mock documents
- Run benchmarks to evaluate system performance

Key files:
- `backend/scripts/test_backend_cli.py`: Main CLI testing tool
- `makefiles/backend-cli-testing/Makefile`: Convenient Makefile commands
- `backend/app/rag/mock_document_adapter.py`: Mock document adapter for testing

## Prerequisites

- Python 3.9+
- Backend dependencies installed (see `backend/requirements.txt`)
- API keys for LLM providers (if using real providers)
  - OpenAI: `OPENAI_API_KEY`
  - Anthropic: `ANTHROPIC_API_KEY`
  - Google Gemini: `GOOGLE_API_KEY`

## Testing with Makefile (Recommended)

The easiest way to run tests is using the Makefile commands:

```bash
# Show available commands
make -f makefiles/backend-cli-testing/Makefile help

# Initialize mock document storage
make -f makefiles/backend-cli-testing/Makefile init

# Create example documents
make -f makefiles/backend-cli-testing/Makefile create-docs

# List documents
make -f makefiles/backend-cli-testing/Makefile list-docs

# Test RAG with custom query
make -f makefiles/backend-cli-testing/Makefile test-rag QUERY="What is our revenue?"

# Test RAG with specific department
make -f makefiles/backend-cli-testing/Makefile test-rag QUERY="What is our financial performance?" DEPARTMENT=finance

# Test multi-agent orchestration
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our marketing strategy and financial performance?"

# Test multi-agent with specific department
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our strategy?" DEPARTMENT=co_ceo

# Start interactive session
make -f makefiles/backend-cli-testing/Makefile interactive
```

### Common Makefile Options

- `QUERY="your query"` - Custom query for test commands
- `PROVIDER=<provider>` - LLM provider (openai, anthropic, gemini, mock)
- `MODEL=<model>` - Specific model name
- `DEPARTMENT=<department>` - Target department (co_ceo, finance, marketing)
- `VERBOSE=false` - Disable verbose output

## LLM Provider Options

You can test with different LLM providers:

```bash
# Test with OpenAI
make -f makefiles/backend-cli-testing/Makefile test-rag PROVIDER=openai MODEL=gpt-4-turbo-preview

# Test with Anthropic
make -f makefiles/backend-cli-testing/Makefile test-rag PROVIDER=anthropic MODEL=claude-3-sonnet-20240229

# Test with Google Gemini
make -f makefiles/backend-cli-testing/Makefile test-rag PROVIDER=gemini MODEL=gemini-1.5-pro

# Test with mock adapter (default)
make -f makefiles/backend-cli-testing/Makefile test-rag PROVIDER=mock
```

## Testing Approaches

The system offers four main testing approaches:

### 1. RAG Testing

Tests the Retrieval-Augmented Generation system with a specific department:

```bash
make -f makefiles/backend-cli-testing/Makefile test-rag QUERY="What is our financial performance?" DEPARTMENT=finance PROVIDER=openai
```

### 2. Multi-Agent Testing

Tests the multi-agent orchestration system, which automatically routes queries to relevant departments:

```bash
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our marketing strategy and financial performance?" PROVIDER=openai
```

### 3. Department-Specific Multi-Agent Testing

Simulates a user interacting with a specific department agent, which may use RAG or multi-agent orchestration as needed:

```bash
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our financial performance?" DEPARTMENT=finance PROVIDER=openai
```

### 4. Interactive Testing

Allows interactive testing in a conversational format:

```bash
make -f makefiles/backend-cli-testing/Makefile interactive PROVIDER=openai MODEL=gpt-4-turbo-preview
```

## Managing Mock Documents

### Default Location

Mock documents are stored in:
- Default: `~/.businesslm/mock_documents.json`
- Repository: `backend/data/mock_documents/mock_documents.json`

### Setting Document Location

To use the repository location (recommended):

```bash
export MOCK_DOCUMENT_PATH=./backend/data/mock_documents/mock_documents.json
```

Add to your `.env` file for persistence:
```
MOCK_DOCUMENT_PATH=./backend/data/mock_documents/mock_documents.json
```

### Creating Mock Documents

```bash
# Using Makefile (creates example documents)
make -f makefiles/backend-cli-testing/Makefile create-docs

# Using direct script
python backend/scripts/test_backend_cli.py create-mock-document \
  --title "Q2 Financial Report" \
  --content "In Q2 2025, our revenue increased by 15%." \
  --department finance \
  --tags finance q2
```

### Managing Documents

```bash
# List documents
make -f makefiles/backend-cli-testing/Makefile list-docs

# View document (direct script)
python backend/scripts/test_backend_cli.py view-mock-document mock-doc-id

# Delete document (direct script)
python backend/scripts/test_backend_cli.py delete-mock-document mock-doc-id
```

## Direct Script Execution

You can also run the script directly:

```bash
# From project root
python backend/scripts/test_backend_cli.py test-rag "What is our financial performance?"

# From backend directory
cd backend
python scripts/test_backend_cli.py test-rag "What is our financial performance?"
```

### Common Options

- `--provider`: LLM provider (openai, anthropic, gemini, mock)
- `--model`: Model name
- `--department`: Target department
- `--verbose`: Enable verbose output
- `--limit`: Maximum documents to retrieve (for RAG)
- `--thread-id`: Thread ID for continuing conversations
- `--user-id`: User ID for the session

## Benchmarking

The benchmarking system evaluates RAG and multi-agent performance using predefined queries.

### Running Benchmarks

```bash
# Run all benchmarks
make -f makefiles/backend-cli-testing/Makefile run-benchmarks PROVIDER=openai MODEL=gpt-4-turbo-preview

# Run specific category
make -f makefiles/backend-cli-testing/Makefile run-benchmarks-category CATEGORY=finance PROVIDER=openai
```

### Benchmark Categories

- **finance**: Financial information queries
- **marketing**: Marketing information queries
- **co_ceo**: Company vision and strategy queries
- **cross_department**: Queries requiring multiple departments
- **edge_cases**: Edge case queries for robustness testing

### Metrics Collected

- **Retrieval**: Document count, relevance scores, retrieval time
- **Routing**: Department routing accuracy (precision, recall, F1)
- **Generation**: Response quality, generation time
- **Overall**: Total processing time, success rate

## Troubleshooting

- **No mock documents**: Create mock documents using `create-docs` or `create-mock-document`
- **LLM API errors**: Verify API keys in environment variables
- **Provider not available**: Install required dependencies:
  - OpenAI: `pip install openai`
  - Anthropic: `pip install anthropic`
  - Google Gemini: `pip install google-generativeai`
- **Module errors**: Ensure backend dependencies are installed in your environment
