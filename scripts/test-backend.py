#!/usr/bin/env python3
"""
Backend Test Script

This script tests the connection to the Python backend and verifies that
the RAG and multi-agent orchestration functionality is working correctly.
"""

import argparse
import json
import os
import sys
import time
from typing import Dict, List, Optional, Union

import requests

# Default backend URL
DEFAULT_BACKEND_URL = "http://localhost:8000"

# Test document content
TEST_DOCUMENT = """
# Company Overview

Our company is a leading provider of innovative solutions in the technology sector.
We specialize in artificial intelligence, machine learning, and data analytics.

## Mission

To revolutionize the industry through innovative solutions that empower our customers.

## Vision

To become the global leader in our field, known for excellence, innovation, and customer satisfaction.

## Core Values

- Innovation
- Integrity
- Customer Focus
- Excellence
- Teamwork

## Departments

### Marketing

The marketing department is responsible for promoting our products and services.
They develop marketing strategies, conduct market research, and manage our brand.

### Sales

The sales department is responsible for generating revenue through product sales.
They identify potential customers, build relationships, and close deals.

### Finance

The finance department is responsible for managing our financial resources.
They handle budgeting, accounting, financial reporting, and investment decisions.

### Operations

The operations department is responsible for the day-to-day operations of our company.
They manage supply chains, production processes, and quality control.

### Human Resources

The HR department is responsible for managing our human capital.
They handle recruitment, training, performance management, and employee relations.

### Product Development

The product development department is responsible for creating new products.
They conduct research, design prototypes, and develop new features.
"""

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test the Python backend")
    parser.add_argument(
        "--url", 
        default=DEFAULT_BACKEND_URL,
        help=f"Backend URL (default: {DEFAULT_BACKEND_URL})"
    )
    parser.add_argument(
        "--test", 
        choices=["connection", "upload", "rag", "agents", "all"],
        default="all",
        help="Test to run (default: all)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Enable verbose output"
    )
    return parser.parse_args()

def test_connection(url: str, verbose: bool = False) -> bool:
    """Test the connection to the backend."""
    print("\n=== Testing Backend Connection ===")
    
    try:
        response = requests.get(f"{url}/health")
        if verbose:
            print(f"Response: {response.status_code} {response.reason}")
            print(f"Content: {response.text}")
        
        if response.status_code == 200:
            print("✅ Backend connection successful!")
            return True
        else:
            print(f"❌ Backend connection failed: {response.status_code} {response.reason}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def test_document_upload(url: str, verbose: bool = False) -> Optional[str]:
    """Test document upload functionality."""
    print("\n=== Testing Document Upload ===")
    
    try:
        # Create a test document
        document_data = {
            "name": "Test Company Overview",
            "content": TEST_DOCUMENT,
            "departmentId": "co-ceo",
            "tags": ["test", "company", "overview"],
            "metadata": {
                "section": "knowledge",
                "priority": "high"
            }
        }
        
        if verbose:
            print(f"Uploading document: {document_data['name']}")
        
        response = requests.post(
            f"{url}/documents",
            json=document_data
        )
        
        if verbose:
            print(f"Response: {response.status_code} {response.reason}")
            print(f"Content: {response.text}")
        
        if response.status_code in (200, 201):
            document_id = response.json().get("id")
            print(f"✅ Document upload successful! Document ID: {document_id}")
            return document_id
        else:
            print(f"❌ Document upload failed: {response.status_code} {response.reason}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Document upload failed: {e}")
        return None

def test_rag(url: str, document_id: Optional[str] = None, verbose: bool = False) -> bool:
    """Test RAG functionality."""
    print("\n=== Testing RAG Functionality ===")
    
    try:
        # Create a test query
        query_data = {
            "query": "What are our company's core values?",
            "sessionId": f"test-session-{int(time.time())}",
            "departmentId": "co-ceo"
        }
        
        if document_id:
            query_data["documentIds"] = [document_id]
        
        if verbose:
            print(f"Sending query: {query_data['query']}")
        
        response = requests.post(
            f"{url}/chat/query",
            json=query_data
        )
        
        if verbose:
            print(f"Response: {response.status_code} {response.reason}")
            print(f"Content: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            answer = response_data.get("answer", "")
            
            # Check if the answer contains information about core values
            if "innovation" in answer.lower() and "integrity" in answer.lower():
                print("✅ RAG functionality successful!")
                print(f"Answer: {answer[:100]}...")  # Print first 100 chars
                return True
            else:
                print("❓ RAG functionality may not be working correctly.")
                print(f"Answer: {answer[:100]}...")  # Print first 100 chars
                return False
        else:
            print(f"❌ RAG test failed: {response.status_code} {response.reason}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ RAG test failed: {e}")
        return False

def test_multi_agent(url: str, verbose: bool = False) -> bool:
    """Test multi-agent orchestration."""
    print("\n=== Testing Multi-Agent Orchestration ===")
    
    try:
        # Create a test query that should trigger multiple agents
        query_data = {
            "query": "Can you analyze our marketing strategy and suggest improvements based on our financial data?",
            "sessionId": f"test-session-{int(time.time())}",
            "departmentId": "co-ceo",
            "useAgents": True  # Explicitly request agent orchestration
        }
        
        if verbose:
            print(f"Sending query: {query_data['query']}")
        
        response = requests.post(
            f"{url}/chat/query",
            json=query_data
        )
        
        if verbose:
            print(f"Response: {response.status_code} {response.reason}")
            print(f"Content: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            answer = response_data.get("answer", "")
            agent_info = response_data.get("agentInfo", {})
            
            # Check if multiple agents were involved
            if agent_info and len(agent_info.get("agents", [])) > 1:
                print("✅ Multi-agent orchestration successful!")
                print(f"Agents involved: {', '.join(agent_info.get('agents', []))}")
                print(f"Answer: {answer[:100]}...")  # Print first 100 chars
                return True
            else:
                print("❓ Multi-agent orchestration may not be working correctly.")
                print(f"Agent info: {agent_info}")
                print(f"Answer: {answer[:100]}...")  # Print first 100 chars
                return False
        else:
            print(f"❌ Multi-agent test failed: {response.status_code} {response.reason}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Multi-agent test failed: {e}")
        return False

def run_all_tests(url: str, verbose: bool = False) -> Dict[str, bool]:
    """Run all tests and return results."""
    results = {}
    
    # Test connection
    connection_result = test_connection(url, verbose)
    results["connection"] = connection_result
    
    if not connection_result:
        print("\n❌ Connection test failed. Skipping remaining tests.")
        return results
    
    # Test document upload
    document_id = test_document_upload(url, verbose)
    results["upload"] = document_id is not None
    
    # Test RAG
    rag_result = test_rag(url, document_id, verbose)
    results["rag"] = rag_result
    
    # Test multi-agent orchestration
    agent_result = test_multi_agent(url, verbose)
    results["agents"] = agent_result
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    print(f"Testing backend at {args.url}")
    
    if args.test == "connection":
        test_connection(args.url, args.verbose)
    elif args.test == "upload":
        test_document_upload(args.url, args.verbose)
    elif args.test == "rag":
        test_rag(args.url, None, args.verbose)
    elif args.test == "agents":
        test_multi_agent(args.url, args.verbose)
    else:  # all
        results = run_all_tests(args.url, args.verbose)
        
        print("\n=== Test Summary ===")
        for test, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test}")
        
        # Overall result
        if all(results.values()):
            print("\n✅ All tests passed! Your backend is working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the logs for details.")

if __name__ == "__main__":
    main()
