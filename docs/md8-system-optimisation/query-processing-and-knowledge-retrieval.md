# Multi-Agent RAG System Knowledge Retrieval Enhancement Plan

## Executive Summary

This document outlines a comprehensive plan to enhance the reliability and effectiveness of knowledge retrieval between the Co-CEO agent and department agents in our multi-agent RAG system. The focus is on improving retrieval from existing knowledge bases, particularly the "Goals & Priorities" and "Knowledge" sections, with "Google Drive" integration as a secondary priority.

## 1. Current System Analysis

### 1.1 Knowledge Architecture

Our current multi-agent RAG system implements:

- **Department-specific knowledge bases** for Finance, Marketing, Operations, HR, Sales, and Product
- **Co-CEO knowledge base** with access to all department information
- **Categorized knowledge sections** including "Goals & Priorities", "Knowledge", and "Google Drive"
- **Vector-based retrieval** with basic caching (5-minute TTL)
- **Query processing** with refresh detection and model-specific optimizations

### 1.2 Identified Limitations

Analysis of the current system reveals several limitations:

- **Inconsistent retrieval results** between Co-CEO and department agents
- **Imprecise section targeting** when querying specific knowledge categories
- **Suboptimal cache management** for cross-department queries
- **Insufficient context maintenance** for follow-up questions
- **Limited query understanding** for intent classification
- **Basic vector search implementation** without section-specific optimizations
- **Rudimentary Google Drive integration** with limited metadata utilization

### 1.3 Technical Debt Assessment

The current implementation has accumulated technical debt in several areas:

- **Knowledge retrieval logic** is tightly coupled with caching mechanisms
- **Query processing** lacks sophisticated intent analysis
- **Vector search parameters** are not optimized for different knowledge sections
- **Cache invalidation** is time-based rather than content-change-based
- **Cross-department knowledge access** lacks proper coordination
- **Google Drive document processing** is not optimized for incremental updates

## 2. Enhancement Strategy

### 2.1 Core Principles

The enhancement strategy is guided by the following principles:

- **Maintain backward compatibility** with existing knowledge structures
- **Prioritize reliability** over adding new features
- **Optimize for specific knowledge sections** (Goals & Priorities, Knowledge)
- **Enhance query understanding** to improve retrieval precision
- **Implement targeted improvements** rather than system-wide refactoring

### 2.2 Key Enhancement Areas

#### 2.2.1 Query Understanding and Processing

Enhance how the system interprets and processes user queries:

- **Intent classification** to identify the target knowledge section
- **Department context detection** to determine relevant knowledge bases
- **Query expansion** to improve retrieval accuracy
- **Follow-up detection** to maintain conversation context
- **Query reformulation** to optimize for vector search

#### 2.2.2 Knowledge Retrieval Pipeline

Optimize the retrieval process for different knowledge sections:

- **Section-aware retrieval** with specialized processing for Goals & Priorities
- **Weighted multi-section search** for queries spanning multiple categories
- **Enhanced result ranking** with section-specific boosting
- **Context-enhanced retrieval** incorporating conversation history
- **Hybrid retrieval strategies** combining different search approaches

#### 2.2.3 Cache Management System

Refine the caching mechanism to improve performance and reliability:

- **Section-specific caching** with tailored invalidation strategies
- **Cross-department cache coordination** for Co-CEO queries
- **Content-change detection** for smarter cache invalidation
- **Hierarchical caching** with different TTLs for different knowledge types
- **Query-result mapping** to improve cache hit rates

#### 2.2.4 Google Drive Integration

Enhance the integration with Google Drive documents:

- **Improved metadata extraction** for better categorization
- **Incremental synchronization** to detect and process changes
- **Selective re-embedding** for modified documents
- **Enhanced document chunking** for more effective retrieval
- **Metadata-based filtering** during retrieval

## 3. Detailed Implementation Plan

### 3.0 Implemented Cross-Department Knowledge Coordination System

The following sections describe the already implemented Cross-Department Knowledge Coordination System that addresses many of the enhancement goals outlined in this document.

#### 3.0.1 Files Created

1. **`src/genkit/knowledge/crossDepartmentCoordinator.ts`**
   - Purpose: Centralizes knowledge retrieval across departments, optimizes relevance, deduplicates content, and ensures comprehensive coverage
   - Core functionality: Parallel retrieval, relevance scoring, section coverage analysis, and intelligent deduplication

2. **`src/genkit/knowledge/conflictResolver.ts`**
   - Purpose: Detects and resolves knowledge conflicts between departments to ensure consistent responses
   - Core functionality: Fact extraction, conflict detection, prioritization rules, and confidence-based resolution

3. **`src/genkit/knowledge/crossDepartmentCache.ts`**
   - Purpose: Manages caching of cross-department knowledge to improve performance and reduce redundant retrievals
   - Core functionality: LRU cache implementation, metrics tracking, targeted invalidation, and adaptive cache management

#### 3.0.2 Files Modified

1. **`src/genkit/agents/coCEOAgent.ts`**
   - Purpose: Main agent implementation that orchestrates department interactions and generates responses
   - Modifications: Integrated cross-department coordination, conflict resolution, normalized department handling, and enhanced error recovery

#### 3.0.3 Cross-Department Knowledge Coordinator Implementation

**Parallel Knowledge Retrieval**
- Implemented `coordinateKnowledgeRetrieval` function that processes multiple departments simultaneously
- Created `Promise.all()` pattern for efficient parallel execution
- Added department normalization with `normalizeDepartmentId` for consistent processing
- Implemented query expansion using `analyzeQuery` results for more comprehensive retrieval

**Timeout Mechanism**
- Added configurable `RETRIEVAL_TIMEOUT` (10 seconds) to prevent hanging on slow responses
- Implemented `Promise.race()` pattern between retrieval and timeout promises
- Created graceful error handling for timed-out retrievals
- Added fallback knowledge generation for departments that time out

**Relevance Scoring System**
- Implemented `calculateRelevanceScore` function with multi-factor scoring:
  - Base score of 0.5 for all departments
  - +0.3 boost for explicitly mentioned departments
  - Keyword matching between query and department keywords (+0.05 per match)
  - Section-specific boosts (e.g., +0.1 for finance in goals section)
  - Score capping at 1.0 to prevent over-weighting

**Section Coverage Analysis**
- Created `analyzeSectionCoverage` function to determine knowledge coverage across sections
- Implemented keyword-based section detection for:
  - Goals section (objectives, targets, KPIs)
  - Knowledge section (information, data, analysis)
  - Drive section (documents, files, attachments)
  - General section (default fallback)
- Added coverage scoring from 0.0 to 1.0 for each section

**Advanced Text Similarity Calculation**
- Implemented `calculateTextSimilarity` function with multi-strategy approach:
  - Direct comparison for very short texts (<100 chars)
  - Length ratio check to quickly filter vastly different texts
  - Sampling approach for long texts (max 20 sentences)
  - Term-based similarity using Jaccard similarity of key terms
  - Sentence-based similarity with significant word matching
  - Adaptive weighting based on text length (term vs. sentence similarity)

**Intelligent Deduplication**
- Created `deduplicateAndMergeKnowledge` function to prevent redundant information
- Implemented similarity threshold of 0.7 to identify duplicates
- Added sorting by relevance score to prioritize most relevant knowledge
- Implemented minimum department count logic to ensure diverse perspectives
- Added detailed logging of deduplication decisions

#### 3.0.4 Knowledge Conflict Resolution Implementation

**Department Prioritization System**
- Created `DEPARTMENT_PRIORITIES` with expertise-based weighting:
  - Finance: 5 (highest priority for financial data)
  - Operations/Product: 4 (high priority for operational/product data)
  - Sales/Marketing/HR: 3 (medium-high priority)
  - Co-CEO: 2 (lower priority as not specialized)

**Section-Specific Expertise Mapping**
- Implemented `SECTION_PRIORITIES` for domain-specific expertise:
  - Goals section: Finance (5), Product (4)
  - Knowledge section: Operations (5), Product (4)
  - Drive section: Operations (5), HR (4)
  - General section: No specific priorities

**Sophisticated Fact Extraction**
- Created `extractKeyFacts` function with:
  - Sentence-level parsing and filtering
  - Non-informative sentence detection and removal
  - Section classification with expanded keyword sets
  - Multi-factor confidence scoring:
    - Financial figures (+0.25)
    - General numbers (+0.15)
    - Recent dates (+0.2)
    - Older dates (+0.1)
    - Factual terminology (+0.15)
    - Definitive statements (+0.1)
    - Uncertainty penalties (-0.15)
    - Source references (+0.2)
    - Department-specific terminology (up to +0.25)

**Department-Specific Terminology Recognition**
- Implemented `getDepartmentSpecificTerms` function with:
  - Finance: 15 terms including revenue, profit, margin, budget
  - Marketing: 13 terms including campaign, brand, audience
  - Sales: 14 terms including pipeline, deal, customer
  - HR: 13 terms including employee, talent, recruitment
  - Operations: 12 terms including process, efficiency, optimization
  - Product: 14 terms including feature, roadmap, release

**Advanced Conflict Detection**
- Created `findConflictingFacts` function with:
  - Cross-department fact comparison
  - Section-specific conflict detection
  - Jaccard similarity calculation for fact comparison
  - Contradictory language pattern detection:
    - Positive vs. negative terms
    - Completion status contradictions
    - Approval status contradictions
    - Success/failure contradictions
    - Truth/accuracy contradictions
  - Numerical value comparison with percentage difference threshold

**Sophisticated Conflict Resolution**
- Implemented `resolveKnowledgeConflicts` function with:
  - Primary department preference when specified
  - Expertise-based confidence adjustment via `adjustConfidenceByExpertise`
  - Multi-factor confidence scoring
  - Detailed decision logging with confidence differentials
  - Confidence-based sorting and selection

#### 3.0.5 Cross-Department Cache Management Implementation

**LRU Cache Implementation**
- Created `LRUCacheEntry` interface with value and access timestamp
- Implemented `evictLRUEntry` function to remove least recently used entries
- Added access time tracking on cache hits
- Created ordered eviction based on access timestamps

**Comprehensive Metrics Tracking**
- Implemented `CacheMetrics` interface with:
  - hits: Count of cache hit occurrences
  - misses: Count of cache miss occurrences
  - evictions: Count of LRU evictions
  - invalidations: Count of explicit invalidations
  - size: Current cache size
  - maxSize: Maximum allowed cache size
- Added `getCacheMetrics` function for external monitoring
- Implemented metrics updates throughout all cache operations

**Sophisticated Cache Key Generation**
- Created `generateCrossDepartmentCacheKey` function with:
  - Department ID normalization
  - Alphabetical sorting of departments for consistency
  - Query hashing with `simpleHash` function
  - Analysis intent inclusion for context-aware caching

**Adaptive Cache Management**
- Implemented `MAX_CACHE_SIZE` constant (100 entries)
- Created `CACHE_TTL` constant (5 minutes)
- Added `cleanupExpiredEntries` function for TTL enforcement
- Implemented size-based eviction with LRU policy
- Added detailed logging of cache operations

**Targeted Cache Invalidation**
- Created `invalidateCrossDepartmentCache` function with:
  - Department-specific invalidation
  - Optional section-specific targeting
  - Metrics tracking of invalidation operations
  - Detailed invalidation logging

#### 3.0.6 Co-CEO Agent Integration Implementation

**Enhanced Query Processing**
- Updated `queryDepartments` function to:
  - Use cross-department coordinator for knowledge retrieval
  - Check cache before coordination
  - Normalize department IDs consistently
  - Handle coordination failures gracefully
  - Use coordinated knowledge for department queries

**Robust Fallback Mechanism**
- Implemented try-catch block around coordination
- Added detailed error logging for debugging
- Created empty coordinated knowledge fallback
- Ensured individual department retrieval as backup
- Added type safety with proper type assertions

**Conflict-Aware Response Generation**
- Updated `generateOrchestrationResponse` function to:
  - Detect conflicts with `detectKnowledgeConflicts`
  - Resolve conflicts with `resolveKnowledgeConflicts`
  - Include resolved conflicts in response generation
  - Add conflict notes to reasoning
  - Present unified view in final response

**Normalized Department Handling**
- Ensured consistent use of `normalizeDepartmentId` throughout:
  - Department interactions
  - Fallback responses
  - Memory system
  - Memory formatting
  - Memory updates
  - Department queries

**Enhanced Department Query Formatting**
- Updated `formatDepartmentQuery` function to:
  - Use normalized department IDs
  - Include department-specific keywords
  - Enhance context with department focus areas
  - Improve clarity of department-specific queries

**Knowledge Change Management**
- Enhanced `resetKnowledgeChangeIndicator` to:
  - Update last checked timestamp
  - Reset department-specific change indicators
  - Reset global change flag when appropriate
  - Invalidate cross-department cache when knowledge changes

### 3.1 Phase 1: Query Understanding Enhancement (Week 1-2) ✅

#### 3.1.1 Query Analyzer Implementation

**File**: `src/genkit/knowledge/queryAnalyzer.ts`

```typescript
// Enhanced query analysis functionality with confidence scoring and context awareness
export interface QueryAnalysisResult {
  // Core properties
  primaryIntent: KnowledgeSection;
  departmentContext: DepartmentId | 'all' | null;
  isFollowUp: boolean;
  keywords: string[];
  expandedQuery: string;
  confidence: number;
  relevantContext?: string;
  sectionScores?: Record<KnowledgeSection, number>;

  // Legacy properties for backward compatibility
  normalizedQuery?: string;
  potentialTitles?: string[];
  keyPhrases?: string[];
  tokens?: string[];
}

export function analyzeQuery(
  query: string,
  conversationHistory: string[] = [],
  currentDepartment: DepartmentId | 'co-ceo' = 'co-ceo'
): QueryAnalysisResult {
  // Normalize query
  const normalizedQuery = query.trim();
  const normalizedQueryLower = normalizedQuery.toLowerCase();

  // Extract keywords
  const keywords = extractKeywords(normalizedQueryLower);

  // Detect if this is a follow-up question
  const isFollowUp = detectFollowUpQuery(normalizedQuery, conversationHistory);

  // Extract relevant context if this is a follow-up
  const relevantContext = isFollowUp ?
    extractRelevantContext(normalizedQuery, conversationHistory) :
    undefined;

  // Detect target knowledge section with confidence scoring
  const sectionResult = detectTargetSectionWithConfidence(normalizedQuery);
  const primaryIntent = sectionResult.section;
  const confidence = sectionResult.confidence;
  const sectionScores = sectionResult.allScores;

  // Detect department context
  const departmentContext = detectDepartmentContext(normalizedQuery, normalizedDepartment);

  // Enhance query with context if it's a follow-up
  let enhancedQuery = isFollowUp ?
    enhanceQueryWithContext(normalizedQuery, conversationHistory) :
    normalizedQuery;

  // Expand query with additional terms
  const expandedQuery = expandQueryWithTerms(
    enhancedQuery,
    primaryIntent,
    departmentContext as DepartmentId,
    {
      includeTimeContext: primaryIntent === 'goals' || primaryIntent === 'knowledge',
      expansionStrength: confidence > 0.7 ? 'light' : 'medium'
    }
  );

  return {
    primaryIntent,
    departmentContext,
    isFollowUp,
    keywords,
    expandedQuery,
    confidence,
    relevantContext,
    sectionScores,
    // Legacy properties...
  };
}
```

#### 3.1.2 Follow-Up Detection Implementation

**File**: `src/genkit/knowledge/followUpDetector.ts`

```typescript
// Patterns that strongly indicate a follow-up question
const FOLLOW_UP_PATTERNS = [
  // Pronouns referring to previous content
  /\b(it|they|them|those|these|that|this)\b/i,
  // Explicit follow-up indicators
  /\b(also|additionally|furthermore|moreover|besides|too)\b/i,
  // Questions about previous answers
  /\b(why|how|what|when|where|who) (is|are|was|were) (that|it|they|them)\b/i,
  // Continuation phrases
  /\b(and|but|so|then) (what|how|why|when|where|who)\b/i,
  // Clarification requests
  /\b(can you|could you) (clarify|explain|elaborate|tell me more)\b/i,
  // Incomplete questions that rely on previous context
  /^(what about|how about)\b/i,
  // Questions with missing subjects
  /^(is|are|was|were|do|does|did|has|have|had) (it|they|them|that|this)\b/i
];

export function detectFollowUp(
  query: string,
  conversationHistory: string[] = []
): boolean {
  // If no conversation history, it can't be a follow-up
  if (!conversationHistory || conversationHistory.length < 2) {
    return false;
  }

  const normalizedQuery = query.toLowerCase().trim();

  // Check for new topic indicators
  const hasNewTopicIndicator = NEW_TOPIC_INDICATORS.some(indicator =>
    normalizedQuery.includes(indicator)
  );

  if (hasNewTopicIndicator) {
    return false;
  }

  // Check for follow-up patterns
  for (const pattern of FOLLOW_UP_PATTERNS) {
    if (pattern.test(normalizedQuery)) {
      return true;
    }
  }

  // Additional checks for short queries and missing context
  // ...

  return false;
}
```

#### 3.1.3 Query Expansion Implementation

**File**: `src/genkit/knowledge/queryExpander.ts`

```typescript
// Section-specific expansion terms
const SECTION_EXPANSION_TERMS: Record<KnowledgeSection, string[]> = {
  'goals': [
    'objective', 'target', 'goal', 'priority', 'strategy', 'plan', 'roadmap',
    'milestone', 'achievement', 'kpi', 'metric', 'measure', 'performance'
  ],
  'knowledge': [
    'information', 'data', 'fact', 'detail', 'insight', 'understanding',
    'context', 'background', 'overview', 'summary', 'explanation'
  ],
  'drive': [
    'document', 'file', 'attachment', 'report', 'presentation', 'spreadsheet',
    'pdf', 'doc', 'sheet', 'slide', 'folder'
  ],
  'general': [
    'information', 'detail', 'overview', 'summary'
  ]
};

export function expandQuery(
  query: string,
  section: KnowledgeSection,
  departmentId?: DepartmentId,
  options?: {
    includeTimeContext?: boolean;
    expansionStrength?: 'light' | 'medium' | 'strong';
    maxAdditionalTerms?: number;
  }
): string {
  // Get section-specific and department-specific terms
  // Filter out terms already in the query
  // Select terms based on expansion strength
  // Add time context if requested
  // Combine terms into an expansion phrase
  // Return expanded query
}
```

#### 3.1.4 Context Integration Implementation

```typescript
export function enhanceQueryWithContext(
  query: string,
  conversationHistory: string[] = []
): string {
  // Check if this is a follow-up query
  const isFollowUp = detectFollowUp(query, conversationHistory);

  if (!isFollowUp) {
    return query;
  }

  // Extract relevant context
  const context = extractRelevantContext(query, conversationHistory);

  if (!context) {
    return query;
  }

  // Combine context with query
  return `Previous context: ${context} \n\nCurrent query: ${query}`;
}

export function extractRelevantContext(
  query: string,
  conversationHistory: string[] = [],
  maxTokens: number = 500
): string {
  // Get the most recent exchanges
  // Extract key entities from the query
  // Find mentions of those entities in conversation history
  // Prioritize most recent and most relevant content
  // Truncate to token limit
}
```

**File**: `src/genkit/knowledge/knowledgeBase.ts`

```typescript
// Integrate query analyzer into knowledge retrieval
import { analyzeQuery, expandQuery } from './queryAnalyzer';

export async function retrieveRelevantKnowledge(
  departmentId: DepartmentId,
  query?: string,
  modelId?: string,
  forceRefresh: boolean = false,
  conversationHistory: string[] = []
): Promise<string> {
  // Analyze the query to determine intent and context
  const queryAnalysis = analyzeQuery(
    query || '',
    conversationHistory,
    departmentId
  );

  // Use analysis results to enhance retrieval
  // ...
}
```

#### 3.1.2 Section Detection Enhancement

**File**: `src/genkit/knowledge/sectionDetector.ts`

```typescript
// New file to implement section detection
export type KnowledgeSection = 'goals' | 'knowledge' | 'drive' | 'general';

export function detectTargetSection(
  query: string,
  metadata?: Record<string, any>
): KnowledgeSection {
  // Implementation of section detection logic
}

export function getSectionKeywords(section: KnowledgeSection): string[] {
  // Return section-specific keywords for query expansion
}

export function getSectionBoostFactors(
  section: KnowledgeSection
): Record<string, number> {
  // Return boost factors for different sections based on target section
}
```

#### 3.1.3 Department Context Recognition

**File**: `src/genkit/knowledge/departmentDetector.ts`

```typescript
// New file to implement department detection
export function detectDepartmentContext(
  query: string,
  currentDepartment: DepartmentId | 'co-ceo'
): DepartmentId | 'all' | null {
  // Implementation of department detection logic
}

export function getDepartmentKeywords(departmentId: DepartmentId): string[] {
  // Return department-specific keywords
}

export function mapCrossDepartmentQuery(
  query: string,
  departments: DepartmentId[]
): Record<DepartmentId, string> {
  // Map a query to department-specific variants
}
```

### 3.2 Phase 2: Retrieval Pipeline Optimization (Week 3-4)

#### 3.2.1 Section-Aware Retrieval Implementation

**File**: `src/genkit/knowledge/knowledgeService.ts`

```typescript
/**
 * Retrieves knowledge from a specific section with optimized parameters
 */
export async function retrieveFromSection(
  departmentId: DepartmentId,
  section: KnowledgeSection,
  query: string,
  options?: {
    conversationHistory?: string[];
    modelId?: string;
    forceRefresh?: boolean;
  }
): Promise<string> {
  console.log(`[KNOWLEDGE] Retrieving from ${section} section for ${departmentId}`);

  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User must be authenticated to retrieve knowledge');
    }

    // Get section-specific search parameters
    const searchParams = getSectionSearchParameters(section);

    // Analyze and enhance the query for this specific section
    const enhancedQuery = enhanceQueryForSection(query, section);
    console.log(`[KNOWLEDGE] Enhanced query for ${section}: "${enhancedQuery}"`);

    // Generate embedding with section-specific parameters
    const { embedding } = await generateEmbedding(enhancedQuery);

    // Search with section-specific parameters
    const documents = await searchSimilarDocuments(
      embedding,
      departmentId,
      searchParams.topK,
      searchParams.similarityThreshold,
      currentUser.uid,
      { section }
    );

    // If no results, return early
    if (!documents || documents.length === 0) {
      return `No relevant ${section} information found for ${departmentId}.`;
    }

    // Process and format results with section-specific formatting
    return formatSectionResults(documents, section, departmentId);
  } catch (error) {
    console.error(`[KNOWLEDGE] Error retrieving from ${section} section:`, error);
    throw error;
  }
}

/**
 * Returns optimized search parameters for different knowledge sections
 */
function getSectionSearchParameters(section: KnowledgeSection): {
  topK: number;
  similarityThreshold: number;
  contextWindow?: number;
} {
  switch (section) {
    case 'goals':
      return {
        topK: 5, // Fewer, more precise results for goals
        similarityThreshold: 0.7, // Higher threshold for goals to ensure relevance
        contextWindow: 3 // Smaller context window for goals
      };
    case 'knowledge':
      return {
        topK: 8, // More results for knowledge to ensure coverage
        similarityThreshold: 0.6, // Medium threshold for knowledge
        contextWindow: 5 // Medium context window for knowledge
      };
    case 'drive':
      return {
        topK: 4, // Fewer results for drive documents
        similarityThreshold: 0.65, // Medium-high threshold for drive
        contextWindow: 2 // Smaller context window for drive documents
      };
    case 'general':
    default:
      return {
        topK: 10, // Most results for general queries
        similarityThreshold: 0.5, // Lower threshold for general queries
        contextWindow: 5 // Larger context window for general queries
      };
  }
}

/**
 * Enhances a query for a specific knowledge section
 */
function enhanceQueryForSection(query: string, section: KnowledgeSection): string {
  // Get section-specific keywords
  const sectionKeywords = getSectionKeywords(section);

  // Check if query already contains section-specific terms
  const containsSectionTerms = sectionKeywords.some(keyword =>
    query.toLowerCase().includes(keyword.toLowerCase())
  );

  // Only enhance if query doesn't already contain section terms
  if (!containsSectionTerms) {
    // Add section context to the query
    const sectionContext = getSectionContext(section);
    return `${query} ${sectionContext}`;
  }

  return query;
}

/**
 * Returns contextual phrases for different sections
 */
function getSectionContext(section: KnowledgeSection): string {
  switch (section) {
    case 'goals':
      return "regarding goals, objectives, targets, or priorities";
    case 'knowledge':
      return "about information, data, facts, or context";
    case 'drive':
      return "from documents, files, or reports";
    case 'general':
    default:
      return "";
  }
}

/**
 * Formats search results based on the knowledge section
 */
function formatSectionResults(
  documents: Array<any>,
  section: KnowledgeSection,
  departmentId: DepartmentId
): string {
  if (!documents || documents.length === 0) {
    return `No relevant ${section} information found for ${departmentId}.`;
  }

  switch (section) {
    case 'goals':
      return formatGoalsResults(documents, departmentId);
    case 'knowledge':
      return formatKnowledgeResults(documents, departmentId);
    case 'drive':
      return formatDriveResults(documents, departmentId);
    case 'general':
    default:
      return formatGeneralResults(documents, departmentId);
  }
}

/**
 * Formats results for the Goals & Priorities section
 */
function formatGoalsResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Extract key goals and priorities
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const name = doc.metadata?.name || 'Untitled';

    return `GOAL: ${name}\n\n${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} GOALS & PRIORITIES:\n\n${formattedDocs}`;
}

/**
 * Formats results for the Knowledge section
 */
function formatKnowledgeResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Format knowledge with more detailed context
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const name = doc.metadata?.name || 'Untitled';
    const date = doc.metadata?.lastModified ? new Date(doc.metadata.lastModified).toLocaleDateString() : 'Unknown date';

    return `DOCUMENT: ${name}\nDATE: ${date}\n\n${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} KNOWLEDGE BASE:\n\n${formattedDocs}`;
}

/**
 * Formats results for the Drive section
 */
function formatDriveResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Include document metadata for Drive results
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const metadata = doc.metadata || {};
    const name = metadata.name || 'Untitled';
    const date = metadata.lastModified ? new Date(metadata.lastModified).toLocaleDateString() : 'Unknown date';
    const fileType = metadata.fileType || 'Unknown type';

    return `DOCUMENT: ${name}\nDATE: ${date}\nTYPE: ${fileType}\n\n${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} DRIVE DOCUMENTS:\n\n${formattedDocs}`;
}

/**
 * Formats results for general queries
 */
function formatGeneralResults(documents: Array<any>, _departmentId: DepartmentId): string {
  // Standard formatting for general results
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const name = doc.metadata?.name || 'Untitled';

    return `Document: ${name}\n\n${content}`;
  }).join('\n\n---\n\n');

  return formattedDocs;
}

// Function to retrieve relevant knowledge for a query
export async function retrieveRelevantKnowledge(
  departmentId: DepartmentId,
  query: string = '',
  modelId?: string,
  forceRefresh: boolean = false,
  conversationHistory: string[] = []
): Promise<string> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('User must be authenticated to retrieve knowledge');
    }

    // Default to empty query if none provided
    const safeQuery = query.trim() || 'general information';

    console.log(`[KNOWLEDGE] Processing query for knowledge retrieval: "${query}" => Using: "${safeQuery}"`);

    // STEP 1: Enhanced query analysis - identify topics, entities, and specific documents
    const queryAnalysis = analyzeQuery(safeQuery);

    // STEP 2: Detect target section
    const targetSection = detectTargetSection(safeQuery);
    console.log(`[KNOWLEDGE] Detected target section: ${targetSection}`);

    // STEP 3: If query targets a specific section, use section-specific retrieval
    if (targetSection !== 'general') {
      try {
        // Try section-specific retrieval first
        const sectionKnowledge = await retrieveFromSection(
          departmentId,
          targetSection,
          safeQuery,
          { conversationHistory, modelId, forceRefresh }
        );

        // If we got meaningful results, return them
        if (sectionKnowledge && sectionKnowledge.trim().length > 50) {
          console.log(`[KNOWLEDGE] Successfully retrieved from ${targetSection} section`);
          return sectionKnowledge;
        }

        console.log(`[KNOWLEDGE] Section-specific retrieval returned insufficient results, falling back to general retrieval`);
      } catch (error) {
        console.error(`[KNOWLEDGE] Error in section-specific retrieval:`, error);
        console.log(`[KNOWLEDGE] Falling back to general retrieval`);
      }
    }

    // STEP 4: Fall back to general retrieval (existing implementation)
    // ...
  } catch (error) {
    console.error('Error in retrieveRelevantKnowledge:', error);
    return `Error retrieving knowledge: ${error instanceof Error ? error.message : 'Unknown error'}`;
  }
}
```

#### 3.2.2 Result Ranking Enhancement

**File**: `src/genkit/services/vectorService.ts`

```typescript
// Interface for vector search document
export interface VectorDocument {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    departmentId: DepartmentId;
    documentId: string;
    name: string;
    userId: string;
    tags?: string[];
    fileType?: 'text' | 'pdf' | 'image';
    chunkIndex?: number;
    totalChunks?: number;
    // Added for section-aware retrieval
    section?: 'goals' | 'knowledge' | 'drive' | 'general';
    updatedAt?: string;
    createdAt?: string;
  };
}

// Function to search for similar documents
export async function searchSimilarDocuments(
  embedding: number[],
  departmentId: DepartmentId,
  maxResults: number = 3,
  minScore: number = 0.7,
  userId?: string,
  options?: {
    section?: 'goals' | 'knowledge' | 'drive' | 'general';
    metadata?: Record<string, any>;
  }
): Promise<(VectorDocument & { score: number })[]> {
  // Get the current user ID if not provided
  const currentUserId = userId || (auth.currentUser?.uid || '');

  if (!currentUserId) {
    throw new Error('User must be authenticated to search documents');
  }

  try {
    // Build query constraints
    const constraints = [
      where('metadata.departmentId', '==', departmentId),
      where('metadata.userId', '==', currentUserId)
    ];

    // Add section filter if provided
    if (options?.section && options.section !== 'general') {
      // Note: This assumes metadata.section is set when documents are added
      // For backward compatibility, we don't apply this filter yet
      console.log(`[VECTOR] Section filter for ${options.section} requested but not applied (prepared for future use)`);
    }

    // Add any additional metadata filters
    if (options?.metadata) {
      Object.entries(options.metadata).forEach(([key, value]) => {
        constraints.push(where(`metadata.${key}`, '==', value));
      });
    }

    // Execute query with all constraints
    const q = query(collection(firestore, 'embeddings'), ...constraints);
    const querySnapshot = await getDocs(q);
    const documents: VectorDocument[] = [];

    // Convert query snapshot to documents
    querySnapshot.forEach(doc => {
      const data = doc.data();
      documents.push({
        id: doc.id,
        content: data.content,
        embedding: data.embedding,
        metadata: data.metadata
      });
    });

    // Calculate similarity scores client-side with section-specific boosting
    const scoredDocuments = documents.map(doc => {
      // Calculate base similarity score
      let score = cosineSimilarity(embedding, doc.embedding);

      // Apply section-specific boosting if section is provided
      if (options?.section && options.section !== 'general') {
        // Boost documents that match the requested section
        // Note: metadata.section might not exist in older documents
        if (doc.metadata.section && doc.metadata.section === options.section) {
          score *= 1.3; // 30% boost for matching section
        }

        // Apply recency boost for goals section
        // Note: metadata.lastModified might not exist in older documents
        if (options.section === 'goals' && doc.metadata.updatedAt) {
          const lastModified = new Date(doc.metadata.updatedAt);
          const now = new Date();
          const daysDiff = Math.floor((now.getTime() - lastModified.getTime()) / (1000 * 60 * 60 * 24));

          if (daysDiff < 30) {
            // Higher boost for more recent goals (up to 30% for very recent)
            score *= 1 + (0.3 * (30 - daysDiff) / 30);
          }
        }
      }

      return {
        ...doc,
        score: Math.min(score, 1.0) // Cap at 1.0
      };
    });

    // Sort by score in descending order (highest similarity first)
    const sortedDocuments = scoredDocuments.sort((a, b) => b.score - a.score);

    // Filter by minimum score and limit results
    return sortedDocuments
      .filter(doc => doc.score >= minScore)
      .slice(0, maxResults);
  } catch (error) {
    console.error('Error searching for similar documents:', error);
    // Return an empty array if search fails
    return [];
  }
}

// Function to add a vector document
export async function addVectorDocument(
  content: string,
  embedding: number[],
  metadata: VectorDocument['metadata'],
  options?: {
    section?: 'goals' | 'knowledge' | 'drive' | 'general';
  }
): Promise<string> {
  // Ensure metadata has all required fields
  const enhancedMetadata = {
    ...metadata,
    // Add section information if provided
    section: options?.section || metadata.section || 'general',
    // Add timestamps if not already present
    updatedAt: metadata.updatedAt || new Date().toISOString(),
    createdAt: metadata.createdAt || new Date().toISOString()
  };

  const docRef = await addDoc(collection(firestore, 'embeddings'), {
    content,
    embedding,
    metadata: enhancedMetadata,
    // Add timestamp for sorting
    createdAt: new Date().toISOString()
  });

  return docRef.id;
}
```

#### 3.2.3 Context-Enhanced Retrieval ✅

**File**: `src/genkit/knowledge/followUpDetector.ts` and `src/genkit/knowledge/queryAnalyzer.ts`

```typescript
// Context enhancement implementation
export function enhanceQueryWithContext(
  query: string,
  conversationHistory: string[] = []
): string {
  // Check if this is a follow-up query
  const isFollowUp = detectFollowUp(query, conversationHistory);

  if (!isFollowUp) {
    return query;
  }

  // Extract relevant context
  const context = extractRelevantContext(query, conversationHistory);

  if (!context) {
    return query;
  }

  // Combine context with query
  return `Previous context: ${context} \n\nCurrent query: ${query}`;
}

export function extractRelevantContext(
  query: string,
  conversationHistory: string[] = [],
  maxTokens: number = 500
): string {
  if (!conversationHistory || conversationHistory.length < 2) {
    return '';
  }

  // Get the most recent exchanges (last 2-3 turns)
  const recentExchanges = conversationHistory.slice(-4);

  // Extract entities from the query
  const entities = extractEntities(query);

  // Find relevant context containing these entities
  let relevantContext = '';

  if (entities.length > 0) {
    // Find mentions of entities in conversation history
    for (const exchange of recentExchanges) {
      for (const entity of entities) {
        if (exchange.toLowerCase().includes(entity.toLowerCase())) {
          relevantContext += exchange + '\n';
          break; // Only add each exchange once
        }
      }
    }
  }

  // If no entity matches, use recent exchanges
  if (!relevantContext) {
    relevantContext = recentExchanges.join('\n\n');
  }

  // Truncate to approximate token limit
  if (relevantContext.length > maxTokens * 4) {
    relevantContext = relevantContext.slice(-(maxTokens * 4));
    // Try to start at a sentence boundary
    const firstPeriod = relevantContext.indexOf('.');
    if (firstPeriod > 0 && firstPeriod < 100) {
      relevantContext = relevantContext.slice(firstPeriod + 1).trim();
    }
  }

  return relevantContext;
}
```

### 3.3 Phase 3: Cache Management Enhancement (Week 5-6)

#### 3.3.1 Section-Specific Caching

**File**: `src/genkit/knowledge/cacheManager.ts`

```typescript
// New file to implement enhanced caching
export interface CacheOptions {
  section?: KnowledgeSection;
  departmentId: DepartmentId;
  ttl?: number;
  forceRefresh?: boolean;
}

export class KnowledgeCache {
  private cache: Map<string, CacheEntry> = new Map();

  public get(key: string, options?: CacheOptions): string | null {
    // Implementation of section-aware cache retrieval
  }

  public set(key: string, value: string, options?: CacheOptions): void {
    // Implementation of section-aware cache storage
  }

  public invalidate(pattern: string): void {
    // Implementation of pattern-based cache invalidation
  }

  public invalidateSection(
    section: KnowledgeSection,
    departmentId?: DepartmentId
  ): void {
    // Implementation of section-specific invalidation
  }
}
```

**File**: `src/genkit/knowledge/knowledgeBase.ts`

```typescript
// Integrate enhanced cache manager
import { KnowledgeCache, CacheOptions } from './cacheManager';

const knowledgeCache = new KnowledgeCache();

export async function retrieveRelevantKnowledge(
  departmentId: DepartmentId,
  query?: string,
  modelId?: string,
  forceRefresh: boolean = false,
  conversationHistory: string[] = []
): Promise<string> {
  // Use enhanced caching with section awareness
  const analysis = analyzeQuery(query || '', conversationHistory, departmentId);

  const cacheOptions: CacheOptions = {
    section: analysis.primaryIntent,
    departmentId,
    forceRefresh
  };

  // Check cache with enhanced options
  const cacheKey = generateCacheKey(departmentId, query || '', analysis);
  const cachedResult = knowledgeCache.get(cacheKey, cacheOptions);

  if (cachedResult) {
    return cachedResult;
  }

  // Retrieve and cache result
  // ...
}
```

#### 3.3.2 Cross-Department Cache Coordination

**File**: `src/genkit/knowledge/crossDepartmentCache.ts`

```typescript
// New file to implement cross-department caching
export function generateCrossDepartmentCacheKey(
  departments: DepartmentId[],
  query: string,
  analysis: QueryAnalysisResult
): string {
  // Generate a cache key for cross-department queries
}

export function invalidateCrossDepartmentCache(
  affectedDepartment: DepartmentId,
  section?: KnowledgeSection
): void {
  // Invalidate cross-department cache entries
}

export async function retrieveCrossDepartmentKnowledge(
  departments: DepartmentId[],
  query: string,
  options?: RetrievalOptions
): Promise<Record<DepartmentId, string>> {
  // Implementation of cross-department retrieval with caching
}
```

### 3.4 Phase 4: Google Drive Integration Enhancement (Week 7-8)

#### 3.4.1 Drive Document Processing Improvement

**File**: `src/genkit/services/driveService.ts`

```typescript
// Enhance Drive service with improved document processing
export interface DriveDocumentMetadata {
  id: string;
  name: string;
  mimeType: string;
  lastModified: string;
  owners: string[];
  departments: DepartmentId[];
  categories: string[];
  contentHash: string;
}

export async function processDocument(
  document: DriveDocument,
  options?: ProcessingOptions
): Promise<ProcessedDocument> {
  // Enhanced document processing with better metadata extraction
}

export async function detectDocumentChanges(
  documentId: string,
  previousMetadata: DriveDocumentMetadata
): Promise<boolean> {
  // Detect if a document has changed
}

export async function syncChangedDocuments(): Promise<number> {
  // Synchronize changed documents and update embeddings
}
```

#### 3.4.2 Metadata-Enhanced Retrieval

**File**: `src/genkit/knowledge/driveKnowledge.ts`

```typescript
// New file to implement Drive-specific knowledge retrieval
export async function retrieveDriveKnowledge(
  departmentId: DepartmentId,
  query: string,
  options?: DriveRetrievalOptions
): Promise<string> {
  // Implementation of Drive-specific retrieval with metadata filtering
}

export function enhanceQueryForDrive(
  query: string,
  metadata?: Record<string, any>
): string {
  // Enhance query for Drive document retrieval
}

export function extractDriveMetadata(
  document: ProcessedDocument
): DriveDocumentMetadata {
  // Extract enhanced metadata from Drive documents
}
```

### 3.5 Phase 5: Agent Integration (Week 9-10)

#### 3.5.1 Co-CEO Agent Knowledge Access Enhancement

**File**: `src/genkit/agents/coCEOAgent.ts`

```typescript
// Enhance Co-CEO agent with improved knowledge access
import { retrieveCrossDepartmentKnowledge } from '../knowledge/crossDepartmentCache';

async function retrieveKnowledgeForQuery(
  query: string,
  conversationHistory: string[]
): Promise<string> {
  // Enhanced knowledge retrieval for Co-CEO
  const analysis = analyzeQuery(query, conversationHistory, 'co-ceo');

  if (analysis.departmentContext && analysis.departmentContext !== 'all') {
    // Retrieve from specific department
    return retrieveRelevantKnowledge(
      analysis.departmentContext,
      query,
      undefined,
      false,
      conversationHistory
    );
  }

  // Cross-department retrieval
  const departments = getAllDepartments();
  const results = await retrieveCrossDepartmentKnowledge(
    departments,
    query,
    { conversationHistory }
  );

  // Combine and format results
  return formatCrossDepartmentResults(results, analysis);
}
```

#### 3.5.2 Department Agent Knowledge Access Enhancement

**File**: `src/genkit/agents/departmentAgents.ts`

```typescript
// Enhance department agents with improved knowledge access
export async function retrieveDepartmentKnowledge(
  departmentId: DepartmentId,
  query: string,
  conversationHistory: string[]
): Promise<string> {
  // Enhanced department-specific knowledge retrieval
  const analysis = analyzeQuery(query, conversationHistory, departmentId);

  // Prioritize Goals & Priorities for relevant queries
  if (analysis.primaryIntent === 'goals') {
    const goalsKnowledge = await retrieveFromSection(
      departmentId,
      'goals',
      query,
      { conversationHistory }
    );

    if (goalsKnowledge) {
      return goalsKnowledge;
    }
  }

  // Fall back to general knowledge retrieval
  return retrieveRelevantKnowledge(
    departmentId,
    query,
    undefined,
    false,
    conversationHistory
  );
}
```

## 4. Testing and Validation Strategy

### 4.1 Unit Testing

Implement comprehensive unit tests for all new components:

- **Query analyzer tests** to verify intent classification accuracy
- **Section detector tests** to validate section identification
- **Cache manager tests** to ensure proper caching behavior
- **Vector service tests** to verify ranking enhancements
- **Drive integration tests** to validate document processing

### 4.2 Integration Testing

Develop integration tests to verify system behavior:

- **End-to-end retrieval tests** for different knowledge sections
- **Cross-department query tests** for Co-CEO agent
- **Follow-up handling tests** to verify context maintenance
- **Cache coordination tests** to validate cross-department caching
- **Drive document retrieval tests** to verify metadata-based filtering

### 4.3 Performance Testing

Implement performance benchmarks to measure improvements:

- **Retrieval latency tests** for different query types and sections
- **Cache efficiency tests** to measure hit rates
- **Memory usage tests** to ensure efficient resource utilization
- **Scalability tests** with increasing knowledge base sizes
- **Concurrent query tests** to verify system behavior under load

### 4.4 Validation Metrics

Define clear metrics to validate the success of enhancements:

- **Retrieval precision** improvement for section-specific queries
- **Cache hit rate** increase for frequently accessed knowledge
- **Response time** reduction for common query patterns
- **Cross-department retrieval accuracy** for Co-CEO queries
- **Goals & Priorities retrieval success rate** for relevant queries

## 5. Implementation Timeline

### Completed: Cross-Department Knowledge Coordination
- ✅ Created cross-department knowledge coordinator
- ✅ Implemented knowledge conflict resolution
- ✅ Developed cross-department cache management
- ✅ Enhanced Co-CEO agent integration
- ✅ Implemented normalized department handling
- ✅ Added robust fallback mechanisms

### Week 1-2: Query Understanding Enhancement ✅
- Implement query analyzer (completed)
- Develop section detection with confidence scoring (completed)
- Create department context recognition (completed)
- Implement follow-up detection (completed)
- Implement query expansion (completed)
- Integrate with knowledge retrieval (completed)

### Week 3-4: Retrieval Pipeline Optimization ✅
- Implement section-aware retrieval (completed)
- Enhance result ranking (completed)
- Develop context-enhanced retrieval (completed)
- Integrate with knowledge service (completed)

### Week 5-6: Cache Management Enhancement
- Implement section-specific caching (completed)
- Develop cross-department cache coordination (completed)
- Create content-change detection (completed)
- Integrate with knowledge base (completed)

### Week 7-8: Google Drive Integration Enhancement
- Improve Drive document processing
- Enhance metadata extraction
- Implement incremental synchronization
- Integrate with knowledge retrieval

### Week 9-10: Agent Integration and Testing
- Enhance Co-CEO agent knowledge access (completed)
- Improve department agent knowledge access (partially completed)
- Implement comprehensive testing
- Measure performance improvements

## 6. Risk Assessment and Mitigation

### 6.1 Potential Risks

- **Performance degradation** due to additional processing
- **Cache invalidation issues** leading to stale data
- **Query analysis errors** resulting in incorrect section targeting
- **Integration complexity** with existing agent architecture
- **Google Drive API limitations** affecting document processing

### 6.2 Mitigation Strategies

- **Performance monitoring** to identify and address bottlenecks
- **Gradual rollout** with A/B testing to validate improvements
- **Fallback mechanisms** to ensure system reliability
- **Comprehensive logging** to track and debug issues
- **API usage optimization** to work within rate limits

## 7. Success Criteria

### 7.1 Achieved Success Criteria

The implemented Cross-Department Knowledge Coordination System has already achieved several key success criteria:

- **Consistent cross-department knowledge retrieval** with the coordinator ensuring unified access
- **Conflict detection and resolution** providing coherent responses across departments
- **Efficient caching with LRU eviction** improving performance and resource utilization
- **Robust error handling** with graceful degradation when components fail
- **Normalized department handling** ensuring consistent identification throughout the system
- **Enhanced response quality** through conflict-aware response generation

### 7.2 Remaining Success Criteria

The enhancement plan will be considered fully successful when it additionally achieves:

- **90%+ accuracy** in retrieving from the correct knowledge section (section-aware retrieval implemented with UI-specific metadata support and solopreneur-focused query patterns) - see [detailed documentation](./section-aware-retrieval-implementation.md)
- **30%+ improvement** in retrieval precision for Goals &