# Testing Framework for Section-Aware Retrieval

This document describes the testing framework for the section-aware retrieval system and provides instructions for running tests locally.

## Overview

The testing framework uses Jest with TypeScript support and is organized into unit tests, integration tests, and edge case tests. The tests are designed to verify the functionality of the section-aware retrieval system, including confidence-based section detection, document classification, and the retrieval pipeline.

## Test Directory Structure

```
src/__tests__/
├── unit/
│   └── knowledge/
│       ├── sectionDetector.test.ts    # Tests for section detection with confidence scoring
│       ├── documentClassification.test.ts  # Tests for document classification
│       └── edgeCases.test.ts          # Tests for edge cases
├── integration/
│   └── knowledge/
│       └── retrievalPipeline.test.ts  # Tests for the retrieval pipeline
├── mocks/
│   └── firebaseMock.ts                # Mocks for Firebase services
├── helpers/
│   └── mockHelpers.ts                 # Helper functions for mocking
├── setup.ts                           # Jest setup file
├── runTests.js                        # Test runner script
└── simple.test.ts                     # Simple test to verify Jest setup
```

## Test Categories

### Unit Tests

Unit tests verify individual components in isolation:

1. **Section Detection Tests**: Test the confidence-based section detection logic, including:
   - Explicit section detection
   - Personal pronoun detection
   - Ambiguous query handling
   - Confidence scoring

2. **Document Classification Tests**: Test the document classification logic, including:
   - UI metadata classification
   - Tag-based classification
   - Name-based classification
   - Mixed signals handling

3. **Edge Case Tests**: Test handling of edge cases, including:
   - Very short queries
   - Very long queries
   - Special characters
   - Spelling errors
   - Conflicting section signals
   - Technical jargon
   - Empty queries

### Integration Tests

Integration tests verify the end-to-end retrieval pipeline:

1. **Retrieval Pipeline Tests**: Test the complete retrieval process, including:
   - Section-specific retrieval
   - Error handling
   - Query enhancement
   - Result formatting

## Running Tests Locally

### Prerequisites

- Node.js 16+ installed
- npm or yarn installed
- Project dependencies installed (`npm install` or `yarn install`)

### Running All Tests

To run all tests:

```bash
npm test
```

or

```bash
yarn test
```

### Running Specific Test Categories

To run specific test categories:

```bash
# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run edge case tests
npm run test:edge
```

### Running Specific Test Files

To run specific test files:

```bash
# Run section detector tests
npm run test -- src/__tests__/unit/knowledge/sectionDetector.test.ts

# Run document classification tests
npm run test -- src/__tests__/unit/knowledge/documentClassification.test.ts

# Run retrieval pipeline tests
npm run test -- src/__tests__/integration/knowledge/retrievalPipeline.test.ts
```

### Running Tests with Coverage

To run tests with coverage reporting:

```bash
npm run test:coverage
```

This will generate a coverage report in the `coverage` directory.

## Mocking

The tests use mocks for external dependencies:

1. **Firebase Mocks**: Mocks for Firebase authentication and Firestore
2. **Embedding Service Mocks**: Mocks for embedding generation
3. **Vector Service Mocks**: Mocks for vector search

These mocks are set up in the Jest setup file (`src/__tests__/setup.ts`) and in individual test files.

## Troubleshooting

If you encounter issues running tests:

1. **Module Resolution Issues**: Make sure the `tsconfig.test.json` file is correctly configured
2. **Jest Configuration Issues**: Check the `jest.config.js` file
3. **Missing Dependencies**: Make sure all dependencies are installed
4. **TypeScript Errors**: Check for TypeScript errors in the test files

## Adding New Tests

When adding new tests:

1. Follow the existing directory structure
2. Use descriptive test names
3. Group related tests using `describe` blocks
4. Use appropriate assertions
5. Mock external dependencies as needed

## Continuous Integration

The tests are designed to run in a CI/CD pipeline. The test runner script (`src/__tests__/runTests.js`) can be used to run specific test categories in CI.
