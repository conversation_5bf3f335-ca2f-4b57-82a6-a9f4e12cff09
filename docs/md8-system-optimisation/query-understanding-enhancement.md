# Query Understanding Enhancement

This document describes the implementation of enhanced query understanding capabilities in the BusinessLM system, focusing on follow-up detection, query expansion, and context integration.

## 1. Overview

The Query Understanding Enhancement improves the system's ability to understand user queries by:

1. Detecting follow-up questions in conversations
2. Extracting relevant context from conversation history
3. Expanding queries with additional terms for better retrieval
4. Integrating conversation context into the retrieval process

## 2. Follow-Up Detection

### 2.1 Implementation

The follow-up detection system identifies when a user's query is a follow-up to previous conversation, allowing the system to maintain context across multiple turns.

```typescript
export function detectFollowUp(
  query: string,
  conversationHistory: string[] = []
): boolean {
  // Implementation details...
}
```

### 2.2 Detection Patterns

The system uses multiple patterns to detect follow-up questions:

1. **Pronoun References**: Detecting pronouns like "it", "they", "them", "that", "this"
2. **Explicit Follow-Up Indicators**: Words like "also", "additionally", "furthermore"
3. **Questions About Previous Answers**: Patterns like "why is that", "how are they"
4. **Continuation Phrases**: Patterns like "and what", "but how", "so why"
5. **Clarification Requests**: Phrases like "can you clarify", "tell me more"
6. **Incomplete Questions**: Phrases like "what about", "how about"
7. **Questions with Missing Subjects**: Patterns like "is it", "are they", "was that"

### 2.3 New Topic Detection

The system also detects when a user is changing the topic rather than following up:

```typescript
const NEW_TOPIC_INDICATORS = [
  'new', 'different', 'another', 'separate', 'unrelated', 
  'change', 'topic', 'instead', 'rather'
];
```

## 3. Context Extraction

### 3.1 Implementation

When a follow-up is detected, the system extracts relevant context from the conversation history:

```typescript
export function extractRelevantContext(
  query: string,
  conversationHistory: string[] = [],
  maxTokens: number = 500
): string {
  // Implementation details...
}
```

### 3.2 Context Selection

The context extraction process:

1. Focuses on recent exchanges (last 2-3 turns)
2. Prioritizes content related to the current query
3. Limits context size to avoid token limits
4. Ensures context starts at sentence boundaries

## 4. Query Expansion

### 4.1 Implementation

Query expansion enhances queries with additional terms to improve retrieval accuracy:

```typescript
export function expandQuery(
  query: string,
  section: KnowledgeSection,
  departmentId?: DepartmentId,
  options?: {
    includeTimeContext?: boolean;
    expansionStrength?: 'light' | 'medium' | 'strong';
    maxAdditionalTerms?: number;
  }
): string {
  // Implementation details...
}
```

### 4.2 Expansion Strategies

The system uses multiple expansion strategies:

1. **Section-Specific Terms**: Adding terms related to the detected knowledge section
2. **Department-Specific Terms**: Adding terms related to the department context
3. **Time-Related Terms**: Adding recency terms for time-sensitive queries
4. **Synonym Expansion**: Adding synonyms for key terms in the query

### 4.3 Expansion Strength

The expansion strength is configurable:

- **Light**: 1-2 additional terms for already long queries
- **Medium**: 3 additional terms (default)
- **Strong**: 5+ additional terms for very short queries

## 5. Integration with Retrieval Pipeline

### 5.1 Enhanced Query Analysis

The query analysis process now incorporates follow-up detection and context integration:

```typescript
export function analyzeQuery(
  query: string, 
  conversationHistory: string[] = [],
  currentDepartment: DepartmentId | 'co-ceo' = 'co-ceo'
): QueryAnalysisResult {
  // Implementation details...
}
```

### 5.2 Context-Enhanced Retrieval

The retrieval process now uses the enhanced query with context:

```typescript
export async function retrieveRelevantKnowledge(
  departmentId: DepartmentId,
  query: string = '',
  modelId?: string,
  forceRefresh: boolean = false,
  conversationHistory: string[] = []
): Promise<string> {
  // Implementation details...
}
```

## 6. Performance Considerations

### 6.1 Efficiency

The implementation is designed for efficiency:

1. Early termination for non-follow-up queries
2. Selective context extraction only when needed
3. Configurable expansion strength based on query length
4. Caching of expansion terms for frequently used sections

### 6.2 Error Handling

Comprehensive error handling ensures robustness:

1. Graceful handling of empty or invalid queries
2. Fallbacks for missing conversation history
3. Handling of edge cases like very short queries
4. Logging for debugging and monitoring

## 7. Future Improvements

Potential future improvements include:

1. **Entity Extraction**: More sophisticated entity extraction for better context matching
2. **Semantic Similarity**: Using embeddings to find semantically similar content in history
3. **Personalization**: Adapting expansion terms based on user preferences
4. **Learning from Feedback**: Adjusting detection patterns based on user feedback

## 8. Testing

The implementation includes comprehensive tests:

1. **Unit Tests**: Testing individual components like follow-up detection
2. **Integration Tests**: Testing the end-to-end query understanding pipeline
3. **Edge Case Tests**: Testing handling of ambiguous queries and special cases
