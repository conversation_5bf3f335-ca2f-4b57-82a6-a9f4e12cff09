# Section-Aware Retrieval, Query Understanding, and Department Agent Knowledge Access

## Overview

The Section-Aware Retrieval system enhances BusinessLM's knowledge retrieval capabilities by optimizing search and presentation based on the specific knowledge section being targeted. This document provides a comprehensive guide to the implementation, covering architecture, components, algorithms, and integration points.

This system is further enhanced by the Query Understanding Enhancement, which improves the system's ability to understand user queries by detecting follow-up questions, extracting relevant context from conversation history, expanding queries with additional terms, and integrating conversation context into the retrieval process.

Additionally, the Department Agent Knowledge Access implementation improves how department agents access knowledge by implementing section prioritization based on department-specific needs, enhancing context handling for department-specific queries, and providing specialized knowledge retrieval for each department.

BusinessLM is targeted towards solopreneurs and entrepreneurs, and the system is designed to support their specific needs. The system recognizes personal possessive pronouns like 'my' in queries about goals and objectives, making it more natural for individual business owners to interact with their knowledge base.

## Table of Contents

1. [Architecture](#1-architecture)
2. [Section Detection](#2-section-detection)
3. [Follow-Up Detection](#3-follow-up-detection)
4. [Context Extraction](#4-context-extraction)
5. [Query Enhancement](#5-query-enhancement)
6. [Search Parameter Optimization](#6-search-parameter-optimization)
7. [Result Ranking and Boosting](#7-result-ranking-and-boosting)
8. [UI-Specific Metadata Integration](#8-ui-specific-metadata-integration)
9. [Result Formatting](#9-result-formatting)
10. [Document Classification](#10-document-classification)
11. [Department-Specific Section Prioritization](#11-department-specific-section-prioritization)
12. [Department Context Enhancement](#12-department-context-enhancement)
13. [Department Knowledge Retrieval Process](#13-department-knowledge-retrieval-process)
14. [Integration with Department Agents](#14-integration-with-department-agents)
15. [Integration Points](#15-integration-points)
16. [Edge Cases and Fallbacks](#16-edge-cases-and-fallbacks)
17. [Performance Optimizations](#17-performance-optimizations)
18. [Real-Time RAG for Updated Knowledge Base Information](#real-time-rag-for-updated-knowledge-base-information)
    - [Optimistic UI Updates](#10-optimistic-ui-updates-implemented)
19. [Testing and Validation](#19-testing-and-validation)
20. [Backward Compatibility](#20-backward-compatibility)
21. [Google Drive Section-Aware Retrieval](#21-google-drive-section-aware-retrieval)
22. [Future Improvements](#22-future-improvements)

## 1. Architecture

The Section-Aware Retrieval system is built on a modular architecture that integrates with the existing knowledge retrieval pipeline:

```
Query → Section Detection → Parameter Optimization → Vector Search → Result Boosting → Formatting → Response
```

### Key Components

- **Section Detector**: Identifies which knowledge section a query is targeting
- **Parameter Optimizer**: Provides section-specific search parameters
- **Query Enhancer**: Adds section-specific context to queries
- **Result Booster**: Applies section-specific boosting to search results
- **Result Formatter**: Formats results according to section-specific templates
- **Document Classifier**: Determines which section a document belongs to

### Files Involved

- `src/genkit/knowledge/sectionDetector.ts`: Section detection logic
- `src/genkit/knowledge/knowledgeService.ts`: Main retrieval flow and formatting
- `src/genkit/services/vectorService.ts`: Vector search and result boosting

## 2. Section Detection

The system detects which knowledge section a query is targeting using pattern matching and keyword analysis.

### Supported Sections

- **Goals & Priorities**: Strategic objectives, targets, KPIs
- **Knowledge**: Information, facts, context, explanations
- **Drive**: Documents, files, reports, presentations
- **General**: Default when no specific section is detected

### Detection Logic with Confidence Scoring

Section detection is implemented in `sectionDetector.ts` using a confidence scoring approach that provides more nuanced results than simple boolean detection. This allows the system to handle ambiguous queries more effectively and consider multiple sections when appropriate.

#### Confidence Calculation

```typescript
function calculateSectionConfidence(query: string, section: KnowledgeSection): number {
  let score = 0;

  // Get section-specific data for scoring
  const { keywords, explicitPatterns, personalPatterns } = getSectionScoringData(section);

  // Check for explicit section mentions (highest weight: 0.6)
  for (const pattern of explicitPatterns) {
    if (pattern.test(query)) {
      score += 0.6;
      break; // Only count one explicit pattern match
    }
  }

  // Check for section-specific keywords (medium weight: up to 0.3)
  const keywordMatches = keywords.filter(keyword =>
    query.includes(keyword)
  ).length;

  if (keywords.length > 0) {
    // Calculate normalized score based on keyword matches
    score += Math.min((keywordMatches / keywords.length) * 0.3, 0.3);
  }

  // Check for personal pronouns for solopreneurs (additional weight: 0.1)
  for (const pattern of personalPatterns) {
    if (pattern.test(query)) {
      score += 0.1;
      break; // Only count one personal pattern match
    }
  }

  return Math.min(score, 1.0); // Cap at 1.0
}
```

#### Section-Specific Patterns

Each section has its own set of patterns and keywords for confidence scoring:

```typescript
function getSectionScoringData(section: KnowledgeSection): {
  keywords: string[];
  explicitPatterns: RegExp[];
  personalPatterns: RegExp[];
} {
  switch (section) {
    case 'goals':
      return {
        keywords: [
          'goal', 'goals', 'priority', 'priorities', 'objective', 'objectives',
          // Additional keywords...
        ],
        explicitPatterns: [
          /\b(goals?|priorities|objectives?)\b/i,
          /\b(what are|show me|list) (my|our|the|company) (goals|priorities|objectives)\b/i,
          // Additional patterns...
        ],
        personalPatterns: [
          /\b(my|our) (goals?|priorities|objectives?)\b/i,
          // Additional patterns...
        ]
      };

    case 'knowledge':
      // Similar structure for knowledge section

    case 'drive':
      // Similar structure for drive section
  }
}
```

#### Multi-Section Consideration

The system can detect when a query might be targeting multiple sections and handle it appropriately:

```typescript
export function shouldConsiderMultipleSections(result: SectionDetectionResult): boolean {
  const scores = result.allScores;
  const sections = Object.keys(scores) as KnowledgeSection[];

  // Sort sections by score in descending order
  const sortedSections = sections.sort((a, b) => scores[b] - scores[a]);

  // If we have at least two sections with scores above secondary threshold
  if (sortedSections.length >= 2 &&
      scores[sortedSections[0]] >= PRIMARY_CONFIDENCE_THRESHOLD &&
      scores[sortedSections[1]] >= SECONDARY_CONFIDENCE_THRESHOLD) {

    // Check if the difference between top two scores is small
    const scoreDiff = scores[sortedSections[0]] - scores[sortedSections[1]];
    return scoreDiff <= CONFIDENCE_DIFF_THRESHOLD;
  }

  return false;
}
```

### Solopreneur Focus

The detection logic is optimized for BusinessLM's target audience of solopreneurs and entrepreneurs by:

1. Including "my" in all relevant patterns
2. Adding solopreneur-specific phrases
3. Supporting first-person queries

## 3. Follow-Up Detection

The system includes a robust follow-up detection mechanism to identify when a query is a follow-up to a previous question, which is crucial for maintaining conversation context.

### Detection Algorithm

```typescript
export function detectFollowUpQuery(query: string, conversationHistory: string[] = []): boolean {
  // If no conversation history, it can't be a follow-up
  if (!conversationHistory || conversationHistory.length === 0) {
    return false;
  }

  // Check for explicit follow-up indicators
  const followUpIndicators = [
    /^(and|also|what about|how about)/i,
    /^(can you|could you)/i,
    /^(is|are|was|were|do|does|did) (it|they|there)/i,
    /^(what|when|where|who|how|why) (is|are|was|were)/i,
    /^(tell me more|elaborate|explain further)/i,
    /\?$/  // Ends with question mark
  ];

  // Check for pronouns that likely refer to previous context
  const pronounReferencePatterns = [
    /\b(it|its|they|them|their|these|those|this|that)\b/i,
    /\b(he|him|his|she|her|hers)\b/i,
    /\b(the|such)\s+(one|ones)\b/i
  ];

  // Check for missing subject (implicit reference to previous subject)
  const missingSubjectPatterns = [
    /^(is|are|was|were|have|has|had|do|does|did|can|could|will|would|should)/i,
    /^(how|what|when|where|why|which)\s+(is|are|was|were|have|has|had|do|does|did|can|could|will|would|should)/i
  ];

  // Check for explicit follow-up indicators
  const hasFollowUpIndicator = followUpIndicators.some(pattern => pattern.test(query));

  // Check for pronoun references
  const hasPronounReference = pronounReferencePatterns.some(pattern => pattern.test(query));

  // Check for missing subject
  const hasMissingSubject = missingSubjectPatterns.some(pattern => pattern.test(query));

  // Check for short queries (often follow-ups)
  const isShortQuery = query.split(' ').length <= 3;

  // Check for semantic similarity with previous query
  const previousQuery = conversationHistory[conversationHistory.length - 1];
  const semanticSimilarity = calculateQuerySimilarity(query, previousQuery);
  const isSemanticallySimilar = semanticSimilarity > 0.6; // Threshold

  // Combine signals with weights
  const isFollowUp = (
    hasFollowUpIndicator ||
    (hasPronounReference && (hasMissingSubject || isShortQuery)) ||
    (isShortQuery && isSemanticallySimilar)
  );

  return isFollowUp;
}
```

### Semantic Similarity Calculation

To detect semantic similarity between queries:

```typescript
function calculateQuerySimilarity(query1: string, query2: string): number {
  // Normalize queries
  const normalizedQuery1 = query1.toLowerCase().trim();
  const normalizedQuery2 = query2.toLowerCase().trim();

  // Extract keywords
  const keywords1 = extractKeywords(normalizedQuery1);
  const keywords2 = extractKeywords(normalizedQuery2);

  // Calculate Jaccard similarity
  const intersection = keywords1.filter(word => keywords2.includes(word)).length;
  const union = new Set([...keywords1, ...keywords2]).size;

  return union === 0 ? 0 : intersection / union;
}
```

## 4. Context Extraction

When a follow-up query is detected, the system extracts relevant context from the conversation history to enhance the query.

### Context Extraction Algorithm

```typescript
export function extractRelevantContext(
  query: string,
  conversationHistory: string[] = [],
  maxContextLength: number = 1000
): string {
  // If no conversation history, return empty context
  if (!conversationHistory || conversationHistory.length === 0) {
    return '';
  }

  // Get the most recent queries and responses
  const recentHistory = conversationHistory.slice(-6); // Last 3 turns (Q&A pairs)

  // Extract entities from the current query
  const queryEntities = extractEntities(query);

  // Score each history item based on relevance to current query
  const scoredHistory = recentHistory.map(item => {
    const itemEntities = extractEntities(item);
    const commonEntities = queryEntities.filter(entity =>
      itemEntities.some(itemEntity =>
        itemEntity.toLowerCase().includes(entity.toLowerCase()) ||
        entity.toLowerCase().includes(itemEntity.toLowerCase())
      )
    );

    // Calculate relevance score based on common entities and recency
    const entityScore = commonEntities.length / Math.max(1, queryEntities.length);
    const index = recentHistory.indexOf(item);
    const recencyScore = (recentHistory.length - index) / recentHistory.length;

    return {
      text: item,
      score: entityScore * 0.7 + recencyScore * 0.3 // Weight entity match higher than recency
    };
  });

  // Sort by relevance score
  scoredHistory.sort((a, b) => b.score - a.score);

  // Combine the most relevant history items up to maxContextLength
  let context = '';
  let currentLength = 0;

  for (const item of scoredHistory) {
    if (currentLength + item.text.length <= maxContextLength) {
      context += item.text + '\n';
      currentLength += item.text.length + 1;
    } else {
      break;
    }
  }

  return context.trim();
}
```

### Entity Extraction

The entity extraction function identifies key entities in text:

```typescript
function extractEntities(text: string): string[] {
  // Remove stopwords and extract potential entities
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !STOPWORDS.includes(word));

  // Extract noun phrases (simplified approach)
  const entities = [];
  for (let i = 0; i < words.length; i++) {
    // Single word entities
    entities.push(words[i]);

    // Two-word entities
    if (i < words.length - 1) {
      entities.push(`${words[i]} ${words[i+1]}`);
    }

    // Three-word entities
    if (i < words.length - 2) {
      entities.push(`${words[i]} ${words[i+1]} ${words[i+2]}`);
    }
  }

  return entities;
}
```

## 5. Query Enhancement

To improve retrieval accuracy, queries are enhanced with section-specific context, conversation history, and additional relevant terms.

### Section-Specific Enhancement

```typescript
function enhanceQueryForSection(query: string, section: KnowledgeSection): string {
  // Check if query already contains section-specific terms
  const sectionKeywords = getSectionKeywords(section);
  const hasExplicitSectionTerms = sectionKeywords.some(keyword =>
    query.toLowerCase().includes(keyword)
  );

  // If query already has section terms, no need to enhance
  if (hasExplicitSectionTerms) {
    return query;
  }

  // Add section-specific context
  const sectionContext = getSectionContext(section);
  return `${query} ${sectionContext}`;
}
```

### Comprehensive Query Expansion

The system implements a more sophisticated query expansion mechanism that considers multiple factors:

```typescript
export function expandQuery(
  query: string,
  departmentId: DepartmentId | 'all' | null,
  section: KnowledgeSection,
  keywords: string[],
  conversationContext?: string
): string {
  // Start with the original query
  let expandedQuery = query;

  // Add department-specific terms if applicable
  if (departmentId && departmentId !== 'all' && DEPARTMENT_KEYWORDS[departmentId]) {
    const relevantTerms = DEPARTMENT_KEYWORDS[departmentId]
      .filter(term => !query.includes(term))
      .slice(0, 3);

    if (relevantTerms.length > 0) {
      expandedQuery += ` (${relevantTerms.join(' OR ')})`;
    }
  }

  // Add section-specific terms
  const sectionTerms = getSectionKeywords(section)
    .filter(term => !expandedQuery.includes(term))
    .slice(0, 2);

  if (sectionTerms.length > 0) {
    expandedQuery += ` (${sectionTerms.join(' OR ')})`;
  }

  // Add synonyms for important keywords
  const synonyms = keywords
    .flatMap(keyword => getSynonyms(keyword))
    .filter(synonym => !expandedQuery.includes(synonym))
    .slice(0, 3);

  if (synonyms.length > 0) {
    expandedQuery += ` (${synonyms.join(' OR ')})`;
  }

  // Add context terms from conversation history if available
  if (conversationContext && conversationContext.length > 0) {
    const contextKeywords = extractKeywords(conversationContext)
      .filter(keyword =>
        !expandedQuery.includes(keyword) &&
        keyword.length > 3 &&
        !COMMON_WORDS.includes(keyword)
      )
      .slice(0, 3);

    if (contextKeywords.length > 0) {
      expandedQuery += ` (${contextKeywords.join(' OR ')})`;
    }
  }

  return expandedQuery;
}
```

### Keyword Extraction

The system extracts important keywords from queries for expansion:

```typescript
export function extractKeywords(text: string): string[] {
  // Normalize text
  const normalizedText = text.toLowerCase().trim();

  // Remove punctuation and split into words
  const words = normalizedText
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2);

  // Remove stopwords
  const filteredWords = words.filter(word => !STOPWORDS.includes(word));

  // Count word frequencies
  const wordFrequencies = {};
  filteredWords.forEach(word => {
    wordFrequencies[word] = (wordFrequencies[word] || 0) + 1;
  });

  // Sort by frequency
  const sortedWords = Object.entries(wordFrequencies)
    .sort((a, b) => b[1] - a[1])
    .map(entry => entry[0]);

  return sortedWords.slice(0, 10); // Return top 10 keywords
}
```

### Synonym Generation

The system expands queries with synonyms for important keywords:

```typescript
function getSynonyms(keyword: string): string[] {
  // Simplified synonym lookup using predefined mappings
  const synonymMap: Record<string, string[]> = {
    'goal': ['objective', 'target', 'aim', 'plan'],
    'priority': ['important', 'critical', 'essential', 'key'],
    'knowledge': ['information', 'data', 'insight', 'understanding'],
    'document': ['file', 'record', 'paper', 'report'],
    'business': ['company', 'enterprise', 'organization', 'firm'],
    'customer': ['client', 'consumer', 'buyer', 'purchaser'],
    'revenue': ['income', 'earnings', 'sales', 'profit'],
    'strategy': ['plan', 'approach', 'method', 'tactic'],
    // ... more synonym mappings
  };

  // Check for exact match
  if (synonymMap[keyword]) {
    return synonymMap[keyword];
  }

  // Check for partial matches
  for (const [key, synonyms] of Object.entries(synonymMap)) {
    if (keyword.includes(key) || key.includes(keyword)) {
      return synonyms;
    }
  }

  return []; // No synonyms found
}
```

### Section-Specific Context

- **Goals**: "regarding goals, objectives, targets, or priorities"
- **Knowledge**: "about information, data, facts, or context"
- **Drive**: "from documents, files, or reports"

### Metadata Filtering

The system includes a prepared function for generating metadata filters based on query analysis and detected section:

```typescript
function generateMetadataFilters(
  queryAnalysis: ReturnType<typeof analyzeQuery>,
  targetSection?: KnowledgeSection
): Record<string, any> {
  const filters: Record<string, any> = {};

  // Add basic filters from query analysis
  if (queryAnalysis.potentialTitles.length > 0) {
    filters.potentialTitles = queryAnalysis.potentialTitles;
  }

  if (queryAnalysis.keyPhrases.length > 0) {
    filters.keyPhrases = queryAnalysis.keyPhrases;
  }

  // Add section-specific filters
  if (targetSection) {
    filters.section = targetSection;
  }

  // Add UI-specific metadata filters for Goals & Priorities
  if (targetSection === 'goals') {
    // Extract potential strategic objective components
    const verbMatches = queryAnalysis.keyPhrases.filter(phrase =>
      ['increase', 'decrease', 'improve', 'reduce', 'achieve', 'develop', 'implement'].includes(phrase.toLowerCase())
    );

    if (verbMatches.length > 0) {
      filters.verb = verbMatches[0];
    }

    // Check for deadline patterns
    // ...
  }

  // Add UI-specific metadata filters for Knowledge documents
  if (targetSection === 'knowledge') {
    // Add recency filters if query mentions recent documents
    if (queryAnalysis.normalizedQuery.includes('recent')) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filters.uploadedAfter = thirtyDaysAgo.toISOString();
    }
  }

  return filters;
}
```

This function is prepared for future integration with the section-aware retrieval system to provide more targeted filtering based on query analysis and detected section.

## 6. Search Parameter Optimization

Each section uses optimized search parameters to balance precision and recall based on the nature of the section.

### Parameter Sets

```typescript
function getSectionSearchParameters(section: KnowledgeSection): SearchParameters {
  switch (section) {
    case 'goals':
      return {
        topK: 5, // Fewer, more precise results for goals
        similarityThreshold: 0.7, // Higher threshold for goals to ensure relevance
        contextWindow: 3 // Smaller context window for goals
      };
    case 'knowledge':
      return {
        topK: 8, // More results for knowledge to ensure coverage
        similarityThreshold: 0.6, // Medium threshold for knowledge
        contextWindow: 5 // Medium context window for knowledge
      };
    case 'drive':
      return {
        topK: 4, // Fewer results for drive documents
        similarityThreshold: 0.65, // Medium-high threshold for drive
        contextWindow: 2 // Smaller context window for drive documents
      };
    case 'general':
    default:
      return {
        topK: 10, // Most results for general queries
        similarityThreshold: 0.5, // Lower threshold for general queries
        contextWindow: 5 // Larger context window for general queries
      };
  }
}
```

### Parameter Rationale

- **Goals & Priorities**:
  - Higher precision (fewer results, higher threshold)
  - Focused context (smaller window)
  - Rationale: Goals are specific and structured

- **Knowledge**:
  - Balanced precision and recall
  - Medium context depth
  - Rationale: Knowledge queries benefit from broader coverage

- **Drive**:
  - Document-focused precision
  - Minimal context
  - Rationale: Drive queries typically target specific documents

- **General**:
  - Maximum recall (more results, lower threshold)
  - Comprehensive context
  - Rationale: General queries are exploratory

## 7. Result Ranking and Boosting

Search results are boosted based on section-specific factors to improve relevance.

### Boosting Factors

```typescript
// Calculate similarity scores client-side with section-specific boosting
const scoredDocuments = documents.map(doc => {
  // Calculate base similarity score
  let score = cosineSimilarity(embedding, doc.embedding);

  // Apply section-specific boosting if section is provided
  if (options?.section && options.section !== 'general') {
    // Boost documents that match the requested section
    if (doc.metadata.section && doc.metadata.section === options.section) {
      score *= 1.3; // 30% boost for matching section
    }

    // Apply recency and deadline-based boosting for goals section
    if (options.section === 'goals') {
      // Boost based on deadline proximity if available
      if (doc.metadata.deadline) {
        const deadline = new Date(doc.metadata.deadline);
        const now = new Date();

        // Higher boost for goals with upcoming deadlines
        if (deadline > now) {
          const daysDiff = Math.floor((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
          if (daysDiff < 30) {
            // Higher boost for more imminent deadlines (up to 40% for very close deadlines)
            score *= 1 + (0.4 * (30 - daysDiff) / 30);
          }
        }
      }
      // Fall back to updatedAt if deadline isn't available
      else if (doc.metadata.updatedAt) {
        // Apply recency boost
        // ...
      }
    }

    // Apply recency boost for knowledge section
    if (options.section === 'knowledge') {
      // Apply moderate recency boost
      // ...
    }
  }

  return {
    ...doc,
    score: Math.min(score, 1.0) // Cap at 1.0
  };
});
```

### Boosting Logic

- **Section Matching**: 30% boost for documents matching the requested section
- **Goals Deadline Proximity**: Up to 40% boost for goals with imminent deadlines
- **Goals Recency**: Up to 30% boost for recently updated goals
- **Knowledge Recency**: Up to 20% boost for recently updated knowledge documents

## 8. UI-Specific Metadata Integration

The system leverages UI-specific metadata to improve retrieval and formatting.

### VectorDocument Interface

```typescript
export interface VectorDocument {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    departmentId: DepartmentId;
    documentId: string;
    name: string;
    userId: string;
    tags?: string[];
    fileType?: 'text' | 'pdf' | 'image';
    chunkIndex?: number;
    totalChunks?: number;
    // Added for section-aware retrieval
    section?: 'goals' | 'knowledge' | 'drive' | 'general';
    updatedAt?: string;
    createdAt?: string;
    // UI-specific metadata for Goals & Priorities
    strategicObjective?: string;
    type?: string;
    verb?: string;
    object?: string;
    measurableUnit?: string;
    deadline?: string;
    // UI-specific metadata for Knowledge documents
    uploadDate?: string;
    lastModified?: string;
  };
}
```

### Document Creation

```typescript
export async function addVectorDocument(
  content: string,
  embedding: number[],
  metadata: VectorDocument['metadata'],
  options?: {
    section?: 'goals' | 'knowledge' | 'drive' | 'general';
    // UI-specific metadata
    strategicObjective?: string;
    type?: string;
    verb?: string;
    object?: string;
    measurableUnit?: string;
    deadline?: string;
    uploadDate?: string;
  }
): Promise<string> {
  // Ensure metadata has all required fields
  const enhancedMetadata = {
    ...metadata,
    // Add section information if provided
    section: options?.section || metadata.section || 'general',
    // Add timestamps if not already present
    updatedAt: metadata.updatedAt || new Date().toISOString(),
    createdAt: metadata.createdAt || new Date().toISOString(),

    // Add UI-specific metadata for Goals & Priorities if provided
    ...(options?.strategicObjective && { strategicObjective: options.strategicObjective }),
    ...(options?.type && { type: options.type }),
    ...(options?.verb && { verb: options.verb }),
    ...(options?.object && { object: options.object }),
    ...(options?.measurableUnit && { measurableUnit: options.measurableUnit }),
    ...(options?.deadline && { deadline: options.deadline }),

    // Add UI-specific metadata for Knowledge documents
    ...(options?.uploadDate && { uploadDate: options.uploadDate }),
    // Use lastModified from metadata or set it to updatedAt
    lastModified: metadata.lastModified || metadata.updatedAt || new Date().toISOString()
  };

  // Add document to collection
  // ...
}
```

## 9. Result Formatting

Results are formatted according to section-specific templates that match the UI structure.

### Goals & Priorities Formatting

```typescript
function formatGoalsResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Extract key goals and priorities with UI-specific formatting
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const metadata = doc.metadata || {};
    const name = metadata.strategicObjective || metadata.name || 'Untitled';

    // Extract UI-specific fields
    const type = metadata.type || '';
    const verb = metadata.verb || '';
    const object = metadata.object || '';
    const measurableUnit = metadata.measurableUnit || '';
    const deadline = metadata.deadline ? new Date(metadata.deadline).toLocaleDateString() : 'No deadline';

    // Format in a tabular structure similar to the UI
    return `STRATEGIC OBJECTIVE: ${name}\n` +
           `TYPE: ${type}\n` +
           `VERB: ${verb}\n` +
           `OBJECT: ${object}\n` +
           `MEASURABLE UNIT: ${measurableUnit}\n` +
           `DEADLINE: ${deadline}\n\n` +
           `${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} GOALS & PRIORITIES:\n\n` +
         `Anatomy of a strategic objective: An effective strategic objective has four elements: a verb, an object, a measurable unit, and a deadline.\n\n` +
         `${formattedDocs}`;
}
```

### Knowledge Formatting

```typescript
function formatKnowledgeResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Format knowledge with more detailed context matching the UI
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const metadata = doc.metadata || {};
    const name = metadata.name || 'Untitled';

    // Use UI-specific date fields if available, with fallbacks
    let dateStr = 'Unknown date';
    if (metadata.uploadDate) {
      dateStr = new Date(metadata.uploadDate).toLocaleDateString();
    } else if (metadata.lastModified) {
      dateStr = new Date(metadata.lastModified).toLocaleDateString();
    } else if (metadata.updatedAt) {
      dateStr = new Date(metadata.updatedAt).toLocaleDateString();
    }

    // Include document icon representation and date as shown in UI
    return `DOCUMENT: ${name} 📄\nUPLOADED: ${dateStr}\n\n${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} KNOWLEDGE BASE:\n\n${formattedDocs}`;
}
```

### Drive Formatting

```typescript
function formatDriveResults(documents: Array<any>, departmentId: DepartmentId): string {
  // Include document metadata for Drive results with Google Drive UI styling
  const formattedDocs = documents.map(doc => {
    const content = doc.content;
    const metadata = doc.metadata || {};
    const name = metadata.name || 'Untitled';

    // Use UI-specific date fields if available, with fallbacks
    let dateStr = 'Unknown date';
    if (metadata.uploadDate) {
      dateStr = new Date(metadata.uploadDate).toLocaleDateString();
    } else if (metadata.lastModified) {
      dateStr = new Date(metadata.lastModified).toLocaleDateString();
    } else if (metadata.updatedAt) {
      dateStr = new Date(metadata.updatedAt).toLocaleDateString();
    }

    // Determine file type icon based on metadata
    let fileIcon = '📄'; // Default document icon
    if (metadata.fileType === 'pdf') {
      fileIcon = '📄'; // PDF icon
    } else if (metadata.fileType === 'spreadsheet') {
      fileIcon = '📃'; // Spreadsheet icon
    } else if (metadata.fileType === 'presentation') {
      fileIcon = '📅'; // Presentation icon
    } else if (metadata.fileType === 'image') {
      fileIcon = '🖼'; // Image icon
    }

    const fileType = metadata.fileType || 'Document';

    // Format similar to Google Drive UI
    return `${fileIcon} ${name}\nType: ${fileType}\nUploaded: ${dateStr}\n\n${content}`;
  }).join('\n\n---\n\n');

  return `${departmentId.toUpperCase()} GOOGLE DRIVE:\n\n${formattedDocs}`;
}
```

## 10. Document Classification

Documents are classified into sections based on their content and metadata.

### Classification Logic

```typescript
function determineDocumentSection(
  document: KnowledgeDocument | Record<string, any>,
  updates?: Partial<KnowledgeDocument>
): KnowledgeSection {
  // Check if the document already has a section assigned
  if (document.metadata?.section) {
    return document.metadata.section as KnowledgeSection;
  }

  // Check for UI-specific metadata from Goals & Priorities section
  const metadata = document.metadata || {};
  const updatedMetadata = updates?.metadata || {};

  // Check for strategic objective structure (from UI)
  if (
    metadata.strategicObjective || updatedMetadata.strategicObjective ||
    metadata.verb || updatedMetadata.verb ||
    metadata.object || updatedMetadata.object ||
    metadata.measurableUnit || updatedMetadata.measurableUnit ||
    metadata.deadline || updatedMetadata.deadline ||
    metadata.type === 'strategic_objective' || updatedMetadata.type === 'strategic_objective'
  ) {
    return 'goals';
  }

  // Check tags for section hints
  const tags = updates?.tags || document.tags || [];

  // Check for section-specific tags
  if (tags.some((tag: string) => ['goal', 'goals', 'priority', 'priorities', 'objective'].includes(tag.toLowerCase()))) {
    return 'goals';
  }

  // Additional classification logic
  // ...

  // Default to knowledge if no clear section is detected
  return 'knowledge';
}
```

### Classification Criteria

1. **Existing Metadata**: Check if section is already assigned
2. **UI-Specific Fields**: Check for strategic objective structure
3. **Tags Analysis**: Check for section-specific tags
4. **Name Analysis**: Check document name for section hints
5. **Default Classification**: Default to 'knowledge' if no clear section is detected

## 11. Department-Specific Section Prioritization

### 11.1 Department-Specific Section Priorities

Each department has different priorities for knowledge sections based on their typical information needs:

```typescript
const DEPARTMENT_SECTION_PRIORITIES: Record<DepartmentId, KnowledgeSection[]> = {
  'finance': ['goals', 'knowledge', 'drive'],
  'marketing': ['goals', 'knowledge', 'drive'],
  'operations': ['knowledge', 'goals', 'drive'],
  'hr': ['goals', 'knowledge', 'drive'],
  'sales': ['goals', 'knowledge', 'drive'],
  'product': ['knowledge', 'goals', 'drive'],
  'co-ceo': ['goals', 'knowledge', 'drive']
};
```

### 11.2 Confidence Thresholds

Different departments have different confidence thresholds for section detection:

```typescript
const SECTION_CONFIDENCE_THRESHOLDS: Record<DepartmentId, Record<KnowledgeSection, number>> = {
  'finance': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'marketing': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'operations': { 'goals': 0.4, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 },
  'hr': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'sales': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'product': { 'goals': 0.4, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 },
  'co-ceo': { 'goals': 0.3, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 }
};
```

### 11.3 Section Selection Algorithm

The system determines which sections to try based on:

1. Section override (if provided)
2. Detected section (if confidence is high enough)
3. Department-specific section priorities

```typescript
// Determine target sections to try based on priorities
let sectionsToTry: KnowledgeSection[] = [];

// If a section override is provided, use it first
if (sectionOverride) {
  sectionsToTry = [sectionOverride, ...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
}
// If confidence is high enough, use the detected section first
else if (analysis.confidence >= SECTION_CONFIDENCE_THRESHOLDS[departmentId][analysis.primaryIntent]) {
  sectionsToTry = [analysis.primaryIntent, ...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
}
// Otherwise, use department priorities
else {
  sectionsToTry = [...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
}
```

## 12. Department Context Enhancement

### 12.1 Department-Specific Context Terms

Each department has specific context terms that are relevant to their domain:

```typescript
const DEPARTMENT_CONTEXT_TERMS: Record<DepartmentId, string[]> = {
  'finance': ['budget', 'financial', 'revenue', 'cost', 'expense', 'profit', 'investment'],
  'marketing': ['campaign', 'brand', 'audience', 'customer', 'market', 'promotion', 'content'],
  'operations': ['process', 'workflow', 'efficiency', 'productivity', 'logistics', 'supply chain'],
  'hr': ['employee', 'talent', 'recruitment', 'hiring', 'training', 'performance', 'benefits'],
  'sales': ['customer', 'client', 'lead', 'opportunity', 'deal', 'pipeline', 'revenue'],
  'product': ['feature', 'roadmap', 'release', 'user', 'requirement', 'design', 'development'],
  'co-ceo': ['strategy', 'vision', 'goal', 'priority', 'performance', 'growth', 'challenge']
};
```

### 12.2 Query Enhancement

Queries are enhanced with department-specific context terms:

```typescript
function enhanceQueryWithDepartmentContext(
  query: string,
  departmentId: DepartmentId,
  section?: KnowledgeSection
): string {
  // Get department-specific context terms
  const contextTerms = DEPARTMENT_CONTEXT_TERMS[departmentId];

  // Check if query already contains department context
  const containsDepartmentContext = contextTerms.some(term =>
    query.toLowerCase().includes(term.toLowerCase())
  );

  // If query already has department context, return as is
  if (containsDepartmentContext) {
    return query;
  }

  // Select 1-2 relevant context terms based on section
  let selectedTerms = [...];

  // Add department context to query
  return `${query} ${selectedTerms.join(' ')}`;
}
```

### 12.3 Section-Specific Term Selection

Terms are selected based on the target section:

```typescript
// Select terms that are most relevant to the section
switch (section) {
  case 'goals':
    selectedTerms = contextTerms.filter(term =>
      ['strategy', 'goal', 'priority', 'target', 'objective', 'plan'].some(goalTerm =>
        term.toLowerCase().includes(goalTerm.toLowerCase())
      )
    );
    break;
  case 'knowledge':
    selectedTerms = contextTerms.filter(term =>
      ['process', 'information', 'data', 'report', 'analysis'].some(knowledgeTerm =>
        term.toLowerCase().includes(knowledgeTerm.toLowerCase())
      )
    );
    break;
  case 'drive':
    selectedTerms = contextTerms.filter(term =>
      ['document', 'file', 'report', 'presentation'].some(driveTerm =>
        term.toLowerCase().includes(driveTerm.toLowerCase())
      )
    );
    break;
}
```

## 13. Department Knowledge Retrieval Process

### 13.1 Main Retrieval Function

The main retrieval function orchestrates the entire process:

```typescript
export async function retrieveDepartmentKnowledge(
  departmentId: DepartmentId,
  query: string,
  conversationHistory: string[] = [],
  options: {
    forceRefresh?: boolean;
    modelId?: string;
    sectionOverride?: KnowledgeSection;
    directKnowledgeAccess?: boolean;
  } = {}
): Promise<string> {
  // Implementation details...
}
```

### 13.2 Retrieval Algorithm

The retrieval process follows these steps:

1. Analyze the query with enhanced context awareness
2. Determine target sections to try based on priorities
3. Try each section in priority order
4. Fall back to general knowledge retrieval if section-specific retrieval fails

### 13.3 Error Handling

Comprehensive error handling ensures robustness:

```typescript
try {
  // Retrieval logic...
} catch (error) {
  logger.error(`[${departmentId.toUpperCase()}] Error in retrieveDepartmentKnowledge:`, error);

  // Return a generic error message as knowledge
  return `I apologize, but I encountered an error while retrieving information for your query. Please try rephrasing your question or ask about something else.`;
}
```

## 14. Integration with Department Agents

### 14.1 Department Agent Flow

The department agent flow uses the enhanced knowledge access:

```typescript
export const createDepartmentAgentFlow = (departmentId: DepartmentId) => {
  const department = departmentPersonalities[departmentId];

  return async (query: string, context: any) => {
    // Get relevant knowledge from the department's knowledge base with enhanced access
    let knowledgeContext = context.knowledgeContext || '';

    // If no knowledge context is provided, retrieve it using the enhanced department knowledge access
    if (!knowledgeContext && context.conversationHistory) {
      try {
        knowledgeContext = await retrieveDepartmentKnowledge(
          departmentId,
          query,
          context.conversationHistory,
          {
            forceRefresh: context.forceRefresh,
            modelId: context.modelId,
            sectionOverride: context.sectionOverride
          }
        );
      } catch (error) {
        console.error(`Error retrieving knowledge for ${departmentId}:`, error);
        knowledgeContext = '';
      }
    }

    // Generate response using the department's system prompt and knowledge context
    // ...
  };
};
```

### 14.2 Department Tools

The department tools also use the enhanced knowledge access:

```typescript
export async function queryDepartment(input: DepartmentQueryInput): Promise<string> {
  // ...

  // Use enhanced department knowledge access with conversation history
  const knowledgeContext = await retrieveDepartmentKnowledge(
    departmentId,
    input.query,
    input.history || [],
    {
      forceRefresh: isRecheckQuery,
      modelId: modelId,
      sectionOverride: requestedSection,
      directKnowledgeAccess: isDirectKnowledgeRequest
    }
  );

  // ...
}
```

## 15. Integration Points

The section-aware retrieval system integrates with the existing knowledge retrieval pipeline at several points.

### Main Retrieval Flow

```typescript
export async function retrieveRelevantKnowledge(
  query: string,
  departmentId: DepartmentId,
  options?: {
    maxResults?: number;
    similarityThreshold?: number;
    targetSection?: KnowledgeSection;
  }
): Promise<string> {
  try {
    // Analyze query for entities, intents, etc.
    const queryAnalysis = analyzeQuery(query);

    // Detect target section if not explicitly provided
    const targetSection = options?.targetSection || detectTargetSection(query, queryAnalysis);

    // If a specific section is detected, use section-specific retrieval
    if (targetSection !== 'general') {
      console.log(`[KNOWLEDGE] Detected ${targetSection} section query: "${query}"`);

      // Retrieve from specific section with optimized parameters
      const sectionResults = await retrieveFromSection(query, departmentId, targetSection, queryAnalysis);

      // Check if we got meaningful results (more than just a few characters)
      if (sectionResults && sectionResults.length > 50) {
        return sectionResults;
      }

      // If section-specific retrieval failed or returned insufficient results, log and continue with general approach
      console.log(`[KNOWLEDGE] Section-specific retrieval failed or returned insufficient results, falling back to general retrieval`);
    }

    // Proceed with general retrieval approach
    // ...
  } catch (error) {
    console.error('Error retrieving knowledge:', error);
    return `I encountered an error while retrieving knowledge. Please try again or rephrase your question.`;
  }
}
```

### Section-Specific Retrieval

```typescript
export async function retrieveFromSection(
  departmentId: DepartmentId,
  section: KnowledgeSection,
  query: string,
  _options?: {
    conversationHistory?: string[];
    modelId?: string;
    forceRefresh?: boolean;
  }
): Promise<string> {
  try {
    // Validate inputs
    if (!departmentId || !section || !query.trim()) {
      return `Unable to retrieve knowledge: Missing required information.`;
    }

    // Authentication check
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return `You must be logged in to access ${section} information.`;
    }

    // Get section-specific search parameters
    const searchParams = getSectionSearchParameters(section);

    try {
      // Enhance query with section-specific context
      const enhancedQuery = enhanceQueryForSection(query, section);

      try {
        // Generate embedding
        const { embedding } = await generateEmbedding(enhancedQuery);

        try {
          // Search with section-specific parameters
          const documents = await searchSimilarDocuments(
            embedding,
            departmentId,
            searchParams.topK,
            searchParams.similarityThreshold,
            currentUser.uid,
            { section }
          );

          // Format results according to section-specific template
          return formatSectionResults(documents, section, departmentId);
        } catch (searchError) {
          // Fallback if search fails
          return `Error retrieving ${section} information.`;
        }
      } catch (embeddingError) {
        // Fallback if embedding generation fails
        return `Error processing your query.`;
      }
    } catch (enhancementError) {
      // Try with original query if enhancement fails
      // Additional fallback logic...
    }
  } catch (error) {
    // Catch-all for unexpected errors
    return `An unexpected error occurred.`;
  }
}
```

## 16. Edge Cases and Fallbacks

The system includes comprehensive error handling and fallback mechanisms to ensure graceful degradation and reliability.

### Comprehensive Error Handling

The implementation uses a nested try-catch approach to handle errors at different levels of the retrieval process:

```typescript
try {
  // Outer level: Input validation and authentication

  try {
    // Query enhancement level

    try {
      // Embedding generation level

      try {
        // Document search level

        try {
          // Result formatting level
        } catch (formattingError) {
          // Formatting fallback
        }
      } catch (searchError) {
        // Search fallback
      }
    } catch (embeddingError) {
      // Embedding fallback
    }
  } catch (enhancementError) {
    // Enhancement fallback
  }
} catch (error) {
  // Global fallback
}
```

### Fallback Mechanisms

1. **Section Detection Fallback**: If no section has confidence above threshold, default to 'general'
2. **Multi-Section Fallback**: If primary section retrieval fails, try secondary section
3. **Query Enhancement Fallback**: If enhanced query fails, try original query
4. **Embedding Fallback**: If embedding generation fails, return helpful error message
5. **Search Fallback**: If search fails, return appropriate error message
6. **Formatting Fallback**: If section-specific formatting fails, use basic formatting
7. **Metadata Fallback**: If UI-specific metadata is missing, use available metadata with fallbacks
8. **Empty Results Fallback**: Return helpful guidance if no results are found

### Backward Compatibility

The system maintains backward compatibility with existing documents that don't have section information:

1. **Optional Section Field**: Section field is optional in document metadata
2. **Default Section**: Documents without a section are treated as 'general'
3. **Metadata Fallbacks**: Multiple fallback paths for missing metadata fields

## 17. Performance Optimizations

The section-aware retrieval system includes several performance optimizations to ensure efficient operation:

### Caching Implementation

The system uses a hybrid caching approach with two levels of caching:

1. **In-Memory LRU Cache**:
   - Implemented as a Map with LRU (Least Recently Used) eviction policy
   - Used for section detection results and frequently accessed embeddings
   - Configurable maximum size (default: 200 entries for section detection, 100 for embeddings)
   - Configurable TTL (Time-To-Live): 5 minutes for section detection, 1 hour for embeddings
   - Automatic cleanup of expired entries every 10 minutes

```typescript
class LRUCache<K, V> {
  private cache: Map<K, CacheEntry<V>>;
  private maxSize: number;
  private defaultTTL: number; // Time-to-live in milliseconds

  constructor(maxSize: number = 100, defaultTTL: number = 15 * 60 * 1000) {
    this.cache = new Map<K, CacheEntry<V>>();
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
  }

  // Methods for get, set, delete, clear, etc.
}
```

2. **Caching Service**:
   - Singleton service that manages all caches
   - Provides methods for getting and setting cached values
   - Handles cache invalidation and cleanup
   - Includes error handling for cache operations

```typescript
export class CachingService {
  private static instance: CachingService;

  // Section detection cache - short TTL (5 minutes)
  private sectionDetectionCache: LRUCache<string, any>;

  // Embedding cache - longer TTL (1 hour)
  private embeddingCache: LRUCache<string, any>;

  // Methods for cache management
}
```

### Enhanced Cache Management System

The system includes a sophisticated multi-level cache management system to ensure real-time knowledge updates and proper session isolation. This system addresses critical challenges in multi-user environments and real-time knowledge retrieval:

1. **Targeted Cache Clearing Architecture**:
   - **Document-specific cache invalidation**: Precisely targets only affected documents
   - **Section-specific cache clearing**: Focuses refreshes on specific knowledge sections
   - **User-specific cache isolation**: Prevents data leakage between user accounts
   - **Multi-level cache coordination**: Synchronizes clearing across storage types
   - **Intelligent key pattern matching**: Uses prefix and pattern recognition for precise targeting

```typescript
/**
 * Clear knowledge-related caches to ensure fresh data retrieval
 * @param documentId Optional specific document ID to target for cache clearing
 */
function clearKnowledgeCaches(documentId?: string): void {
  if (documentId) {
    logger.info(`Clearing caches for document ID: ${documentId}`);
  } else {
    logger.info('Clearing all knowledge-related caches');
  }

  // Clear browser cache entries related to embeddings and knowledge
  try {
    // Clear embedding-specific cache entries
    Object.keys(localStorage).forEach(key => {
      // If documentId is provided, only clear caches related to that document
      if (documentId) {
        if (key.includes(documentId) ||
            key.startsWith(`embedding_${documentId}`) ||
            key.startsWith(`knowledge_${documentId}`)) {
          localStorage.removeItem(key);
          logger.debug(`Cleared localStorage cache: ${key}`);
        }
      } else {
        // Otherwise clear all knowledge and embedding caches
        if (key.startsWith('embedding_') || key.startsWith('knowledge_')) {
          localStorage.removeItem(key);
        }
      }
    });

    // Clear browser cache service entries
    // Additional cache clearing logic...
  } catch (e) {
    logger.warn('Error clearing knowledge caches:', e);
  }
}
```

2. **Real-Time Knowledge Updates System**:
   - **Immediate embedding refresh**: Regenerates embeddings instantly when documents change
   - **Comprehensive cache invalidation**: Triggers on document creation, update, and deletion
   - **Force refresh capabilities**: Provides manual refresh tools for troubleshooting
   - **Progressive update mechanism**: Updates search index in real-time without blocking user operations
   - **Optimized chunking algorithm**: Ensures consistent document representation across updates
   - **Metadata preservation**: Maintains document relationships during refresh operations
   - **Performance monitoring**: Tracks refresh operations with detailed metrics

```typescript
/**
 * Force refresh of vector embeddings for a specific document
 * This ensures that updated knowledge is properly indexed
 * @param documentId The ID of the document to refresh
 */
export async function refreshDocumentEmbeddings(documentId: string): Promise<boolean> {
  try {
    logger.info(`Forcing refresh of embeddings for document ID: ${documentId}`);

    // Get the document
    const docRef = doc(firestore, 'knowledge', documentId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      logger.warn(`Document not found for refresh: ${documentId}`);
      return false;
    }

    const document = docSnap.data() as KnowledgeDocument;

    // Delete existing embeddings
    await deleteVectorDocuments(documentId);
    logger.debug(`Deleted existing embeddings for document: ${documentId}`);

    // Generate new embeddings
    if (document.content) {
      // For text content, chunk and generate embeddings
      const chunks = chunkText(document.content);
      logger.debug(`Generated ${chunks.length} chunks for document: ${documentId}`);

      // Generate embeddings for each chunk
      const embeddings = await generateEmbeddings(chunks);
      logger.debug(`Generated ${embeddings.length} embeddings for document: ${documentId}`);

      // Add vector documents for each chunk
      for (let i = 0; i < chunks.length; i++) {
        await addVectorDocument(
          chunks[i],
          embeddings[i],
          {
            departmentId: document.departmentId,
            documentId: documentId,
            name: document.name,
            userId: document.userId,
            chunkIndex: i,
            totalChunks: chunks.length,
            tags: document.tags
          },
          { section: document.section as any }
        );
      }

      logger.info(`Successfully refreshed embeddings for document: ${documentId}`);
    } else {
      logger.warn(`Document has no content to refresh: ${documentId}`);
    }

    // Clear caches for this document
    clearKnowledgeCaches(documentId);

    return true;
  } catch (error) {
    logger.error(`Error refreshing document embeddings: ${error}`);
    return false;
  }
}
```

3. **Advanced Session Isolation Architecture**:
   - **User ID tracking system**: Persistently monitors user identity changes across sessions
   - **Complete cache clearing protocol**: Implements comprehensive cleanup during logout
   - **Automatic cache refresh mechanism**: Triggers full refresh when switching users
   - **Cross-storage coordination**: Synchronizes localStorage, sessionStorage, and in-memory caches
   - **Security boundary enforcement**: Prevents data leakage between different user accounts
   - **Identity change detection**: Uses stored user IDs to detect account switching
   - **Graceful session transition**: Ensures smooth experience when changing accounts

```typescript
// In AuthContext.tsx
useEffect(() => {
  // Set up Firebase auth state observer
  const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
    setIsLoading(false);
    if (firebaseUser) {
      // Check if this is a different user than before
      const previousUserId = localStorage.getItem('current_user_id');
      const currentUserId = firebaseUser.uid;

      if (previousUserId && previousUserId !== currentUserId) {
        // Different user logged in - clear all caches to prevent data leakage
        console.log('Different user detected, clearing all caches');
        clearAllCaches();
      }

      // Store current user ID for future comparison
      localStorage.setItem('current_user_id', currentUserId);

      const formattedUser = formatUser(firebaseUser);
      setUser(formattedUser);
      setIsAuthenticated(true);
    } else {
      setUser(null);
      setIsAuthenticated(false);
    }
  });

  // Clean up subscription
  return () => unsubscribe();
}, []);
```

4. **Comprehensive Debugging Toolkit**:
   - **Global debugging console**: Exposes specialized functions through browser console
   - **Manual cache management tools**: Provides granular control over cache clearing and refresh
   - **Knowledge base diagnostics**: Offers targeted refresh capabilities for specific documents
   - **System information gathering**: Collects detailed metrics about storage usage and cache state
   - **Performance monitoring dashboard**: Tracks operation timing and resource utilization
   - **Error recovery tools**: Provides mechanisms to recover from cache corruption
   - **Session state inspection**: Allows examination of current user session state
   - **Logging level control**: Enables dynamic adjustment of logging verbosity
   - **Cache visualization**: Displays cache contents and relationships graphically

```typescript
// Make debugging utilities available globally
if (typeof window !== 'undefined') {
  (window as any).businesslmDebug = {
    // Cache management
    clearAllCaches: () => {
      logger.info('Manually clearing all caches');
      clearAllCaches();
      return 'All caches cleared successfully';
    },

    // Knowledge management
    refreshKnowledge: async (departmentId?: DepartmentId) => {
      logger.info('Manually refreshing knowledge base');
      const result = await forceRefreshAllKnowledge(departmentId);
      return result;
    },

    // System information
    getSystemInfo: () => {
      return {
        userAgent: navigator.userAgent,
        localStorage: {
          size: Object.keys(localStorage).length,
          keys: Object.keys(localStorage)
        },
        sessionStorage: {
          size: Object.keys(sessionStorage).length,
          keys: Object.keys(sessionStorage)
        }
      };
    }
  };
}
```

### Query Optimization

1. **Selective Enhancement**: Only enhance queries when necessary
2. **Efficient Constraints**: Build Firestore queries with only necessary constraints
3. **Client-Side Filtering**: Use client-side filtering for complex conditions

### Result Processing

1. **Score Capping**: Prevent over-boosting which could skew results
2. **Minimal Document Processing**: Process documents efficiently during creation and update
3. **Optimized Formatting**: Format only the necessary fields for each section

### Additional Performance Optimizations

1. **Lazy Loading**: Heavy operations are performed only when needed
2. **Normalized Cache Keys**: Query strings are normalized before caching to improve hit rates
3. **Graceful Degradation**: System continues to function even if caching fails
4. **Selective Caching**: Metadata-influenced results are not cached to ensure accuracy
5. **Resource Management**: Memory usage is optimized through size limits and TTL
6. **Logging**: Performance-related events are logged for monitoring and optimization

## 18. Real-Time Knowledge Update Implementation

A critical enhancement to the section-aware retrieval system is the implementation of real-time knowledge updates. This ensures that any changes to knowledge documents are immediately reflected in retrieval results without requiring system restarts or manual refreshes.

### Architecture Overview

The real-time knowledge update system is built on several key components:

1. **Document Operation Hooks**
   - Intercepts document creation, update, and deletion operations
   - Triggers appropriate cache invalidation and embedding refresh
   - Maintains operation logs for debugging and recovery

2. **Targeted Cache Invalidation**
   - Identifies and clears only caches related to modified documents
   - Preserves unrelated caches to maintain system performance
   - Implements multi-level cache clearing across storage types

3. **Embedding Refresh Pipeline**
   - Deletes outdated vector embeddings for modified documents
   - Generates new embeddings with optimized chunking algorithms
   - Stores updated embeddings with complete metadata preservation

4. **Session Isolation Mechanisms**
   - Prevents data leakage between user accounts
   - Detects user changes and triggers appropriate cache clearing
   - Maintains security boundaries between different sessions

5. **Debugging and Monitoring Tools**
   - Provides console utilities for manual cache management
   - Offers diagnostic functions for troubleshooting
   - Tracks performance metrics for optimization

### Implementation Benefits

The real-time knowledge update system provides several key benefits:

1. **Immediate Knowledge Availability**
   - Updates to knowledge documents (e.g., changing values from 20% to 30% or dates from November 2 to December 15) are immediately available for retrieval
   - No delay between document updates and retrieval availability
   - Consistent user experience with up-to-date information

2. **Enhanced User Isolation**
   - Complete separation of data between different user accounts
   - No cross-contamination of knowledge between sessions
   - Secure multi-user environment with proper data boundaries

3. **Improved System Reliability**
   - Graceful degradation when errors occur
   - Comprehensive error handling with recovery mechanisms
   - Detailed logging for troubleshooting and optimization

4. **Optimized Performance**
   - Targeted cache invalidation to minimize processing overhead
   - Efficient embedding refresh to reduce resource utilization
   - Background processing for non-blocking operations

### Implementation Plan

The real-time knowledge update system will be implemented in three phases, integrating Google Drive improvements alongside the core real-time RAG functionality:

#### Phase 1: Foundation (1-2 weeks) ✅ *Completed*

1. **Precision Cache Invalidation** ✅ *Fully Implemented*
   - Created `AdaptiveCacheService` with comprehensive caching capabilities
   - Implemented dependency graph for tracking relationships between documents and cached results
   - Added sophisticated content fingerprinting with the `ContentFingerprinter` class
   - Created specialized document update handlers for lifecycle events
   - Implemented `KnowledgeCacheService` for knowledge-specific caching operations
   - Fixed all type issues and circular dependencies
   - Ensured proper error handling and recovery

   **Implementation Details:**
   - Created new `AdaptiveCacheService` class with the following key functions:
     - `get<T>(key, options)`: Gets an item from the cache with options for freshness
     - `set<T>(key, value, options)`: Stores an item with dependencies and metadata
     - `invalidate(key, options)`: Invalidates cache entries with precision control
     - `invalidateDependents(key)`: Recursively invalidates all dependent cache entries
     - `hasContentChanged<T>(key, newValue)`: Detects content changes using fingerprints
     - `calculateContentSimilarity<T>(key, newValue)`: Measures content similarity (0-1)

   - Created `ContentFingerprinter` utility class:
     - `generateFingerprint(content)`: Creates a unique fingerprint for any content
     - `hasChanged(oldFingerprint, newFingerprint)`: Detects content changes
     - `calculateSimilarity(fingerprint1, fingerprint2)`: Measures similarity between content

   - Implemented `KnowledgeCacheService` for knowledge operations:
     - `getCachedResult(query, options)`: Retrieves cached knowledge results
     - `cacheResult(query, result, options)`: Caches knowledge with dependencies
     - `invalidateDocument(documentId)`: Invalidates cache for specific documents
     - `invalidateBySource(source)`: Invalidates cache by knowledge source
     - `invalidateByTag(tag)`: Invalidates cache by tag (e.g., department, document)
     - `clearCache()`: Clears all knowledge cache entries

   - Created document update handlers for lifecycle events:
     - `handleDocumentCreated(document)`: Processes new document creation
     - `handleDocumentUpdated(documentId, oldContent, newContent)`: Handles content updates with similarity detection
     - `handleDocumentDeleted(documentId)`: Manages document deletion and dependency cleanup
     - `handleBulkDocumentUpdates(documents)`: Efficiently processes multiple document updates
     - `handleSourceUpdate(source, documentIds)`: Manages updates from external sources like Google Drive

   - Integrated with knowledge management functions:
     - Enhanced `clearKnowledgeCaches()` to use the new cache invalidation system
     - Updated `retrieveRelevantKnowledge()` to use the KnowledgeCacheService with proper document handling
     - Modified document operations to use document update handlers for all lifecycle events
     - Added content fingerprinting to document update operations for intelligent cache invalidation
     - Fixed circular dependencies between knowledge modules
     - Ensured type safety throughout the implementation
     - Added proper error handling with fallback mechanisms

   **Performance Optimizations:**
   - Targeted Operations: Only invalidates cache entries for specifically changed documents
   - Change Detection: Uses fingerprinting to avoid unnecessary cache invalidation
   - Cache Preservation: Maintains unrelated cache entries to preserve performance
   - Smart Refresh: Automatically detects when to bypass cache based on document updates
   - Graceful Degradation: Falls back to legacy methods if precision invalidation fails

2. **Google Drive Authentication and Permission Handling**
   - Create robust token management system with automatic refreshes
   - Implement proper permission scopes for Drive access
   - Store user-specific Drive permissions alongside document dependencies
   - Add secure credential storage with encryption

3. **Error Handling and Retries**
   - Implement unified error handling patterns for all knowledge sources
   - Create retry mechanisms with exponential backoff for API calls
   - Add detailed error logging with contextual information
   - Implement graceful degradation when services are unavailable

#### Phase 2: Change Detection & Notification (1-2 weeks) ✅ *Completed*

1. **File Change Monitoring for Google Drive** ✅ *Implemented*
   - Implemented efficient polling with configurable intervals
   - Created change detection system with minimal API usage
   - Added support for detecting content and metadata changes
   - Implemented change processing with Firebase Functions

2. **Real-Time Update Notification System** ✅ *Fully Implemented*
   - Created central event broker for knowledge updates
   - Implemented publish-subscribe pattern for component communication
   - Added event types for different update operations
   - Ensured proper event propagation across the application
   - Connected all knowledge sources to the broker
   - Implemented Google Drive integration

   **Implementation Details:**

   - **Knowledge Update Broker**:
     - Created comprehensive `KnowledgeUpdateBroker` class with full publish-subscribe pattern:
       ```typescript
       class KnowledgeUpdateBroker {
         // Core pub-sub functionality
         subscribe(eventType: string, callback: EventCallback, options?: SubscriptionOptions): string { ... }
         unsubscribe(subscriptionId: string): boolean { ... }
         async publish(eventType: string, eventData: Partial<KnowledgeUpdateEvent>): Promise<void> { ... }

         // Event history management
         getEventHistory(eventType: string): KnowledgeUpdateEvent[] { ... }
         clearEventHistory(eventType: string): void { ... }
         setHistoryLimit(limit: number): void { ... }

         // Error handling
         addErrorHandler(handler: (error: Error, event: KnowledgeUpdateEvent) => void): void { ... }
       }
       ```

     - Created `KnowledgeUpdateEvent` interface hierarchy with specialized types:
       ```typescript
       interface KnowledgeUpdateEvent {
         eventType: string;
         timestamp: string;
         source: string;
         sequenceId?: number;
       }

       interface DocumentUpdateEvent extends KnowledgeUpdateEvent {
         documentId: string;
         changeType: 'created' | 'updated' | 'deleted';
         contentChanged: boolean;
         metadataChanged: boolean;
         departmentId?: string;
         documentName?: string;
       }
       ```
     - Implemented subscription management with filtering and prioritization:
       ```typescript
       subscribe(eventType: string, callback: EventCallback, options?: SubscriptionOptions): string
       ```
     - Added event publishing with sequence IDs for ordering:
       ```typescript
       publish(eventType: string, eventData: Partial<KnowledgeUpdateEvent>): Promise<void>
       ```
     - Built event queue processing with retry logic and exponential backoff
     - Added event history tracking for debugging and recovery
     - Implemented global error handlers for centralized error management

   - **Knowledge Source Connector**:
     - Created centralized connector for all knowledge sources:
       ```typescript
       export async function initializeKnowledgeSourceConnectors(): Promise<void> { ... }
       export function publishDocumentUpdateEvent(
         documentId: string,
         changeType: 'created' | 'updated' | 'deleted',
         contentChanged: boolean,
         metadataChanged: boolean,
         departmentId?: string,
         documentName?: string,
         source: string = 'internal'
       ): void { ... }
       export async function forceFullSync(): Promise<void> { ... }
       ```
     - Implemented event handlers for different event types:
       ```typescript
       function handleDocumentUpdateEvent(event: KnowledgeUpdateEvent): Promise<void> { ... }
       function handleCacheInvalidationEvent(event: KnowledgeUpdateEvent): Promise<void> { ... }
       function handleSourceSyncEvent(event: KnowledgeUpdateEvent): Promise<void> { ... }
       ```

   - **Google Drive Update Connector**:
     - Created specialized connector for Google Drive:
       ```typescript
       export async function initializeGoogleDriveConnector(): Promise<void> { ... }
       async function checkConnectionForUpdates(connection: DriveConnection): Promise<void> { ... }
       async function processChangedFile(file: DriveFile, connection: DriveConnection): Promise<void> { ... }
       export async function forceFullSync(): Promise<void> { ... }
       ```
     - Implemented polling-based change detection with configurable intervals
     - Added support for incremental and full synchronization
     - Integrated with Firebase Functions for secure API access

   - **Document Update Handlers**:
     - Enhanced document lifecycle handlers with event publishing:
       ```typescript
       export function handleDocumentCreated(document: Partial<KnowledgeDocument> & { id: string, content: string }): void {
         // Generate fingerprint and log
         // ...

         // Publish document creation event
         knowledgeUpdateBroker.publish('document.update.created', {
           documentId: document.id,
           changeType: 'created',
           contentChanged: true,
           metadataChanged: true,
           departmentId: document.departmentId,
           documentName: document.name,
           source: document.metadata?.source || 'internal'
         } as DocumentUpdateEvent);
       }
       ```
     - Added similar event publishing to update and delete handlers
     - Implemented bulk update handling with individual and aggregate events

   - **Browser Event Bridge**:
     - Created cross-tab communication using BroadcastChannel API:
       ```typescript
       class BrowserEventBridge {
         initialize(): void
         bridgeEventToOtherTabs(event: KnowledgeUpdateEvent): void
         private handleIncomingEvent(data: any): void
         dispose(): void
       }
       ```
     - Implemented localStorage fallback for older browsers
     - Added bidirectional event propagation between tabs
     - Implemented duplicate event detection with unique bridgeId tracking
     - Added event namespacing with `bridge.` prefix to prevent infinite loops

   - **Event Persistence Service**:
     - Created localStorage-based event persistence for offline scenarios:
       ```typescript
       class EventPersistenceService {
         initialize(): void
         persistEvent(event: KnowledgeUpdateEvent, maxAttempts?: number): void
         private shouldPersistEvent(event: KnowledgeUpdateEvent): boolean
         private processPendingEvents(): Promise<void>
         getPendingEventCount(): number
         clearPendingEvents(): void
       }
       ```
     - Implemented retry system with configurable intervals: [1s, 5s, 30s, 5m, 1h]
     - Added intelligent event filtering to prioritize critical events
     - Integrated with network status monitoring for automatic recovery

   - **Network Status Utility**:
     - Created browser-compatible network detection utilities:
       ```typescript
       function isOnline(): boolean
       function addOnlineStatusListener(callback: (isOnline: boolean) => void): () => void
       function checkConnectivity(url?: string, timeout?: number): Promise<boolean>
       function monitorConnectivity(callback: (isConnected: boolean) => void): () => void
       ```
     - Implemented active connectivity checking with fetch API
     - Added graceful degradation for non-browser environments
     - Implemented connectivity monitoring with callbacks

   - **Document Operation Integration**:
     - Updated document operations to publish events:
       ```typescript
       // In addDocument function
       knowledgeUpdateBroker.publish('document.created', {
         eventType: 'document.created',
         documentId: docRef.id,
         changeType: 'created',
         contentChanged: true,
         metadataChanged: true,
         departmentId,
         documentName: sanitizedName,
         source: 'internal'
       } as DocumentUpdateEvent);
       ```
     - Added content and metadata change detection in updateDocument
     - Implemented embedding update events in refreshDocumentEmbeddings
     - Integrated with existing cache invalidation system

   - **System Initialization**:
     - Created centralized initialization module:
       ```typescript
       export function initializeRealTimeUpdateSystem(): void
       export function shutdownRealTimeUpdateSystem(): void
       ```
     - Implemented proper component initialization order
     - Added global error handler registration
     - Set up event subscriptions for document and embedding events
     - Integrated with connectivity monitoring
     - Added proper resource cleanup in shutdown function

   **Technical Implementation:**

   - **Event Ordering**: Implemented sequence IDs to ensure events are processed in the correct order
   - **Error Handling**: Added comprehensive error handling with retry mechanisms and fallbacks
   - **Event Persistence**: Created localStorage-based persistence for offline scenarios
   - **Cross-Tab Synchronization**: Implemented dual-mode communication (BroadcastChannel/localStorage)
   - **Performance Optimizations**: Added event batching, selective persistence, and efficient filtering

3. **Incremental Sync for Google Drive**
   - Use modification dates to efficiently sync only changed documents
   - Implement delta-based synchronization to minimize bandwidth
   - Add support for handling deleted and moved files
   - Integrate with the selective vector refresh mechanism

#### Phase 3: Optimization (1-2 weeks)

1. **Pagination for Large Google Drive Folders**
   - Create background processing system for large document sets
   - Implement efficient pagination with cursor-based approach
   - Add progress tracking and resumable operations
   - Ensure compatibility with real-time update mechanisms

2. **Smart Query Recognition** ✅ *Fully Implemented*
   - Enhanced query analysis to detect queries about recently updated content
   - Implemented timestamp comparison for cache freshness determination
   - Created query classifier for update-related queries
   - Added automatic cache bypass for relevant queries
   - Integrated with ChatService for seamless user experience
   - Added UI feedback for users when fresh content is retrieved

3. **Selective Vector Refresh** ✅ *Implemented*
   - Implement differential update system for document embeddings
   - Add chunk-specific embedding regeneration to minimize processing
   - Create verification steps for vector document consistency
   - Integrate with document update workflow

   **Implementation Details:**

   - **Selective Vector Refresh**:
     - Created new `selectiveVectorRefresh.ts` module with key functions:
       ```typescript
       // Compare chunks to identify differences
       function compareChunks(oldChunks: string[], newChunks: string[], options?: object): ChunkComparisonResult

       // Selectively refresh vector embeddings
       async function selectivelyRefreshEmbeddings(
         documentId: string,
         oldContent: string,
         newContent: string,
         metadata: ChunkMetadata,
         options?: SelectiveRefreshOptions
       ): Promise<SelectiveRefreshResult>

       // Verify embedding consistency
       async function verifyEmbeddingConsistency(documentId: string, content: string): Promise<object>
       ```
     - Implemented text similarity calculation for chunk comparison:
       ```typescript
       function calculateTextSimilarity(text1: string, text2: string): number {
         // Normalize texts
         const normalizedText1 = text1.toLowerCase().trim();
         const normalizedText2 = text2.toLowerCase().trim();

         // If texts are identical after normalization, return 1
         if (normalizedText1 === normalizedText2) return 1;

         // Split into words for comparison
         const words1 = new Set(normalizedText1.split(/\s+/));
         const words2 = new Set(normalizedText2.split(/\s+/));

         // Calculate Jaccard similarity
         const intersection = new Set([...words1].filter(word => words2.has(word)));
         const union = new Set([...words1, ...words2]);

         return intersection.size / union.size;
       }
       ```
     - Added intelligent refresh strategy selection based on change percentage
     - Implemented detailed refresh statistics tracking
     - Created fallback mechanism for large-scale changes

   - **Knowledge Service Integration**:
     - Updated `updateDocument()` to use selective refresh:
       ```typescript
       // Inside updateDocument function
       if (updates.content) {
         // Get the original document content for comparison
         const originalContent = docSnap.data().content || '';
         const newContent = updates.content;

         // Use selective vector refresh to minimize re-embedding
         const refreshResult = await selectivelyRefreshEmbeddings(
           documentId,
           originalContent,
           newContent,
           {
             departmentId: docSnap.data().departmentId,
             documentId: documentId,
             name: updates.name || docSnap.data().name,
             userId: docSnap.data().userId,
             section: docSnap.data().metadata?.section,
             tags: updates.tags || docSnap.data().tags
           },
           {
             chunkSimilarityThreshold: 0.8,
             verifyConsistency: true
           }
         );
       }
       ```
     - Added detailed logging of refresh statistics
     - Implemented fallback to full refresh when selective refresh fails

   - **Smart Query Recognizer**:
     - Created new `smartQueryRecognizer.ts` module with key functions:
       ```typescript
       // Detect freshness indicators in queries
       function detectFreshnessIndicators(query: string): { matched: string[], confidence: number }

       // Find and categorize recently updated documents
       function findRecentlyUpdatedDocuments(departmentId: DepartmentId | 'all', thresholdMs?: number): DocumentUpdateInfo[]

       // Measure relevance between queries and documents
       function detectQueryDocumentRelevance(query: string, recentDocuments: DocumentUpdateInfo[]): number

       // Comprehensive recency analysis
       function analyzeQueryForRecency(query: string, departmentId: DepartmentId | 'all', options?: object): SmartQueryRecognitionResult
       ```
     - Implemented time-based recency categorization with configurable thresholds:
       ```typescript
       export const RECENCY_THRESHOLDS = {
         VERY_RECENT: 5 * 60 * 1000,    // 5 minutes
         RECENT: 30 * 60 * 1000,        // 30 minutes
         SOMEWHAT_RECENT: 2 * 60 * 60 * 1000,  // 2 hours
         TODAY: 24 * 60 * 60 * 1000     // 24 hours
       };
       ```
     - Created comprehensive list of 40+ freshness indicators:
       ```typescript
       export const FRESHNESS_INDICATORS = [
         'latest', 'newest', 'recent', 'updated', 'current', 'fresh',
         'new information', 'latest information', 'most recent',
         // ... and many more
       ];
       ```
     - Enhanced ChatService integration:
       ```typescript
       // In ChatService.ts
       private async getKnowledgeBase(departmentId: DepartmentId, userQuery?: string, modelId?: string): Promise<string> {
         // Use Smart Query Recognition to detect if this is a query about recent updates
         let shouldBypassCache = false;
         let recencyExplanation = '';

         if (userQuery) {
           // Analyze the query for recency indicators
           const recencyAnalysis = analyzeQueryForRecency(userQuery, departmentId);

           // Determine if we should bypass cache based on the analysis
           shouldBypassCache = recencyAnalysis.shouldBypassCache;
           recencyExplanation = recencyAnalysis.explanation;

           // Pass forceRefresh flag based on Smart Query Recognition
           const relevantKnowledge = await retrieveRelevantKnowledge(
             departmentId,
             userQuery,
             modelId,
             shouldBypassCache // Force refresh based on Smart Query Recognition
           );

           // If we bypassed the cache, add a note about it in the response
           if (shouldBypassCache && recencyExplanation) {
             knowledgeWithNote = `[Note: Using the most up-to-date information. ${recencyExplanation}]\n\n${relevantKnowledge}`;
           }
         }
       }
       ```
     - Implemented multi-factor confidence scoring based on indicators and document relevance
     - Added document recency categorization (very_recent, recent, somewhat_recent, today)
     - Created semantic matching between queries and document metadata

   - **Enhanced Query Analysis**:
     - Updated `QueryAnalysisResult` interface with recency properties:
       ```typescript
       export interface QueryAnalysisResult {
         // Existing properties...

         // Recency detection properties
         isAboutRecentUpdates?: boolean;
         recencyConfidence?: number;
         freshnessIndicators?: string[];
         shouldBypassCache?: boolean;
         recentlyUpdatedDocuments?: any[];
       }
       ```
     - Modified `analyzeQuery()` to incorporate recency analysis
     - Added recency confidence scoring to query analysis results
     - Updated return values to include freshness indicators and updated documents

   - **Knowledge Base Integration**:
     - Updated `retrieveRelevantKnowledge()` to use smart query recognition:
       ```typescript
       // Use Smart Query Recognition to determine if we should bypass cache
       const smartQueryResult = analyzeQueryForRecency(safeQuery, departmentId);

       // Determine if we should bypass cache based on smart query recognition
       const shouldRefresh = forceRefresh || smartQueryResult.shouldBypassCache;
       ```
     - Replaced simple refresh indicators with comprehensive analysis
     - Enhanced cache bypass decision making based on recency analysis
     - Added detailed logging of recency analysis results and decisions

   - **Knowledge Service Integration**:
     - Enhanced `integrateQueryAnalysis()` to include recency properties
     - Added `formatTimeSince()` for human-readable time formatting:
       ```typescript
       function formatTimeSince(ms: number): string {
         const seconds = Math.floor(ms / 1000);
         const minutes = Math.floor(seconds / 60);
         const hours = Math.floor(minutes / 60);
         const days = Math.floor(hours / 24);

         if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
         if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
         if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''}`;
         return `${seconds} second${seconds !== 1 ? 's' : ''}`;
       }
       ```
     - Added detailed logging of recently updated documents
     - Updated return types to include recency information

3. **Selective Vector Refresh**
   - Implement content comparison to identify changed sections
   - Create chunking strategy that minimizes re-embedding
   - Add chunk-tracking system for vector documents
   - Implement verification steps for vector document updates

4. **UI Enhancements** ✅ *Implemented*
   - Add subtle UI elements showing cache status
   - Implement timestamp display for retrieved information
   - Create non-intrusive refresh button
   - Add optimistic UI updates for document changes

   **Implementation Details:**

   - **Transparent Cache Status Indicators**:
     - Created new `CacheStatusIndicator` component for displaying cache status:
       ```typescript
       interface CacheStatusIndicatorProps {
         isCached: boolean;
         timestamp: string | number;
         onRefresh?: () => void;
         isRefreshing?: boolean;
         source?: string;
       }
       ```
     - Added cache information to message metadata:
       ```typescript
       interface Message {
         // ... existing properties
         metadata?: {
           // ... existing properties
           cacheInfo?: {
             isCached: boolean;
             timestamp: string;
             source?: string;
           }
         }
       }
       ```
     - Implemented refresh functionality in ChatPanel:
       ```typescript
       const handleRefreshKnowledge = useCallback((message: Message) => {
         // Set the message as refreshing
         setRefreshingMessageId(messageId);

         // Call the refresh function
         onRefreshKnowledge(message);
       }, [onRefreshKnowledge, isLoading]);
       ```
     - Added optimistic UI updates when refreshing knowledge
     - Integrated with the existing knowledge retrieval system

### Technical Implementation

The real-time knowledge update system is implemented through several key components and functions that work across all knowledge sources, including Google Drive:

#### Core Components

1. **Cache Dependency Service** (Implemented)
   ```typescript
   export class CacheDependencyService {
     private dependencies = new Map<string, CacheDependency>();
     private cacheKeyToDocuments = new Map<string, Set<string>>();

     trackDependency(documentId: string, cacheKey: string, source?: string): void { /* ... */ }
     getDependentCacheKeys(documentId: string): Set<string> { /* ... */ }
     updateFingerprint(documentId: string, content: string): boolean { /* ... */ }
     wasRecentlyUpdated(documentId: string, thresholdMs: number = 300000): boolean { /* ... */ }
     addRelatedDocument(documentId: string, relatedDocumentId: string): void { /* ... */ }
     removeDocument(documentId: string): void { /* ... */ }
     getStats(): { documentCount: number; cacheKeyCount: number; /* ... */ } { /* ... */ }
     private generateFingerprint(content: string): string { /* ... */ }
   }
   ```

2. **Enhanced Adaptive Cache Service** (Fully Implemented)
   ```typescript
   export class AdaptiveCacheService {
     // Core caching operations
     get<T>(key: string, options: CacheRetrievalOptions = {}): CacheResult<T> | null { /* ... */ }
     set<T>(key: string, value: T, options: CacheStorageOptions = {}): CacheEntry<T> { /* ... */ }
     has(key: string, options: CacheRetrievalOptions = {}): boolean { /* ... */ }

     // Invalidation operations
     invalidate(key: string, options: InvalidationOptions = {}): void { /* ... */ }
     private invalidateDependents(key: string): void { /* ... */ }

     // Dependency tracking
     private updateDependencyGraph(key: string, dependencies: string[]): void { /* ... */ }
     private removeFromDependencyGraph(key: string): void { /* ... */ }

     // Content change detection
     hasContentChanged<T>(key: string, newValue: T): boolean { /* ... */ }
     calculateContentSimilarity<T>(key: string, newValue: T): number { /* ... */ }

     // Utility methods
     getKeys(): string[] { /* ... */ }
     getStats(): { size: number, dependencyGraphSize: number } { /* ... */ }
     getDependencies(key: string): string[] { /* ... */ }
     getDependents(key: string): string[] { /* ... */ }
   }
   ```

#### Key Functions

1. **Precision Cache Invalidation** (Fully Implemented)
   - Document-specific cache invalidation with dependency tracking
   - Content fingerprinting for change detection
   - Selective cache clearing based on document relationships
   - Includes comprehensive error handling and logging
   - Falls back to legacy cache clearing for backward compatibility
   - Fixed all type issues and circular dependencies

   ```typescript
   // AdaptiveCacheService provides intelligent caching with dependency tracking
   export class AdaptiveCacheService {
     private cache: Map<string, CacheEntry<any>> = new Map();
     private dependencyGraph: Map<string, DependencyNode> = new Map();

     // Get an item from the cache with options
     get<T>(key: string, options: CacheRetrievalOptions = {}): CacheResult<T> | null { ... }

     // Store an item in the cache with dependencies
     set<T>(key: string, value: T, options: CacheStorageOptions = {}): CacheEntry<T> { ... }

     // Invalidate cache entries with precision
     invalidate(key: string, options: InvalidationOptions = {}): void { ... }

     // Invalidate all dependents of a key
     private invalidateDependents(key: string): void { ... }

     // Check if content has changed by comparing fingerprints
     hasContentChanged<T>(key: string, newValue: T): boolean { ... }
   }
   ```

   ```typescript
   // ContentFingerprinter provides sophisticated content change detection
   export class ContentFingerprinter {
     // Generate a fingerprint for content with support for various data types
     static generateFingerprint(content: any): string {
       if (typeof content === 'string') {
         return this.hashString(content);
       } else if (content === null || content === undefined) {
         return 'null';
       } else if (Array.isArray(content)) {
         return this.hashString(content.map(item => this.generateFingerprint(item)).join('|'));
       } else if (typeof content === 'object') {
         const sortedKeys = Object.keys(content).sort();
         const fingerprints = sortedKeys.map(key => `${key}:${this.generateFingerprint(content[key])}`);
         return this.hashString(fingerprints.join('|'));
       } else {
         return this.hashString(String(content));
       }
     }

     // Compare two fingerprints to determine if content has changed
     static hasChanged(oldFingerprint: string, newFingerprint: string): boolean {
       return oldFingerprint !== newFingerprint;
     }

     // Calculate similarity between two fingerprints (0-1)
     static calculateSimilarity(fingerprint1: string, fingerprint2: string): number {
       if (fingerprint1 === fingerprint2) return 1.0;

       // Simple Jaccard similarity for demonstration
       const set1 = new Set(fingerprint1.split(''));
       const set2 = new Set(fingerprint2.split(''));

       const intersection = new Set([...set1].filter(x => set2.has(x)));
       const union = new Set([...set1, ...set2]);

       return intersection.size / union.size;
     }
   }
   ```

2. **`refreshDocumentEmbeddings(documentId: string, options?: RefreshOptions)`**
   - Manages the complete embedding refresh workflow
   - Handles document retrieval, chunking, embedding generation, and storage
   - Supports source-specific refresh strategies
   - Includes detailed performance metrics and error recovery

3. **`forceRefreshAllKnowledge(options?: { departmentId?: DepartmentId, source?: string })`**
   - Provides global knowledge refresh capabilities
   - Supports filtering by department and knowledge source
   - Includes comprehensive progress tracking and reporting
   - Handles both internal knowledge and Google Drive documents

4. **Document Update Handlers** (Fully Implemented)
   - Specialized handlers for document lifecycle events
   - Content fingerprinting for change detection
   - Intelligent cache invalidation based on content similarity
   - Comprehensive error handling and logging
   - Support for bulk operations and source-specific updates

   ```typescript
   /**
    * Handle document creation
    * @param document The newly created document
    */
   export function handleDocumentCreated(document: Partial<KnowledgeDocument> & { id: string, content: string }): void {
     logger.info(`New document created: ${document.id} - "${document.name}"`);

     // Generate a fingerprint for the document content
     const fingerprint = ContentFingerprinter.generateFingerprint(document.content);
     logger.debug(`Generated fingerprint for new document: ${fingerprint}`);
   }

   /**
    * Handle document update
    * @param documentId The ID of the updated document
    * @param oldContent The previous content
    * @param newContent The new content
    */
   export function handleDocumentUpdated(documentId: string, oldContent: string, newContent: string): void {
     logger.info(`Document updated: ${documentId}`);

     // Generate fingerprints for old and new content
     const oldFingerprint = ContentFingerprinter.generateFingerprint(oldContent);
     const newFingerprint = ContentFingerprinter.generateFingerprint(newContent);

     // Calculate similarity between old and new content
     const similarity = ContentFingerprinter.calculateSimilarity(oldFingerprint, newFingerprint);

     logger.debug(`Content change detected for document ${documentId}:`);
     logger.debug(`Old fingerprint: ${oldFingerprint}`);
     logger.debug(`New fingerprint: ${newFingerprint}`);
     logger.debug(`Content similarity: ${similarity.toFixed(2)}`);

     // Invalidate cache entries related to this document
     knowledgeCacheService.invalidateDocument(documentId);

     // If the content has changed significantly (less than 80% similar),
     // we might want to invalidate more aggressively
     if (similarity < 0.8) {
       logger.info(`Significant content change detected for document ${documentId} (similarity: ${similarity.toFixed(2)})`);
       knowledgeCacheService.invalidateByTag(`doc:${documentId}`);
     }
   }

   /**
    * Handle document deletion
    * @param documentId The ID of the deleted document
    */
   export function handleDocumentDeleted(documentId: string): void {
     logger.info(`Document deleted: ${documentId}`);

     // Invalidate cache entries related to this document
     knowledgeCacheService.invalidateDocument(documentId);

     // Invalidate cache entries by tag for this document
     knowledgeCacheService.invalidateByTag(`doc:${documentId}`);
   }

   /**
    * Handle bulk document updates
    * @param documents Array of updated documents
    */
   export function handleBulkDocumentUpdates(documents: KnowledgeDocument[]): void {
     logger.info(`Bulk document update: ${documents.length} documents`);

     // Process each document
     documents.forEach(document => {
       // Invalidate cache entries related to this document
       knowledgeCacheService.invalidateDocument(document.id);
     });

     // If there are many documents, consider clearing the entire cache
     if (documents.length > 10) {
       logger.info(`Large bulk update detected (${documents.length} documents). Considering full cache invalidation.`);
     }
   }
   ```

   - Integration with document operations in knowledgeService.ts
   ```typescript
   // Inside addDocument function
   handleDocumentCreated({
     id: docRef.id,
     name: sanitizedName,
     content: sanitizedContent,
     departmentId,
     userId: currentUser.uid,
     // ... other properties
   });

   // Inside updateDocument function
   if (contentChanged && updates.content) {
     // Get the original content for comparison
     const originalContent = docSnap.data().content || '';

     // Handle document update for cache management
     handleDocumentUpdated(documentId, originalContent, updates.content);
   }

   // Inside deleteDocument function
   handleDocumentDeleted(documentId);
   ```

   - `updateDriveDocument()` for Google Drive-specific updates (Planned)

5. **Knowledge Cache Service** (Fully Implemented)
   - Specialized caching for knowledge retrieval operations
   - Built on top of the AdaptiveCacheService
   - Document-specific cache management
   - Tag-based and source-based invalidation
   - Fixed circular dependencies and type issues

   ```typescript
   /**
    * Knowledge retrieval result with cache information
    */
   export interface KnowledgeRetrievalResult {
     documents: KnowledgeDocument[];
     query: string;
     cacheInfo: {
       isCached: boolean;
       timestamp: string;
       source: string;
     };
   }

   /**
    * Knowledge query options
    */
   export interface KnowledgeQueryOptions {
     bypassCache?: boolean;
     requiredFreshness?: number;
     maxResults?: number;
     tags?: string[];
     source?: string;
   }

   export class KnowledgeCacheService {
     // Generate a cache key for a knowledge query
     private generateCacheKey(query: string, options: KnowledgeQueryOptions = {}): string {
       const { maxResults, tags, source } = options;

       // Create a key that includes the query and relevant options
       let key = `knowledge:${query}`;

       if (maxResults !== undefined) {
         key += `:limit=${maxResults}`;
       }

       if (tags && tags.length > 0) {
         key += `:tags=${tags.sort().join(',')}`;
       }

       if (source) {
         key += `:source=${source}`;
       }

       return key;
     }

     // Get cached knowledge retrieval result
     getCachedResult(query: string, options: KnowledgeQueryOptions = {}): KnowledgeRetrievalResult | null { ... }

     // Cache a knowledge retrieval result
     cacheResult(query: string, result: KnowledgeRetrievalResult, options: KnowledgeQueryOptions = {}): void { ... }

     // Invalidate cache entries related to a document
     invalidateDocument(documentId: string): void { ... }

     // Invalidate cache entries by source
     invalidateBySource(source: string): void { ... }

     // Invalidate cache entries by tag
     invalidateByTag(tag: string | string[]): void { ... }

     // Clear all knowledge cache
     clearCache(): void { ... }
   }
   ```

6. **Smart Query Recognition** (Implemented)
   - Automatic detection of recently updated documents
   ```typescript
   function findRecentlyUpdatedDocuments(departmentId: DepartmentId, thresholdMs: number = 300000): string[] {
     // Get all dependencies from the cache dependency service
     const dependencies = cacheDependencyService['dependencies'] as Map<string, any>;
     if (!dependencies) return [];

     const now = Date.now();
     const recentlyUpdated: string[] = [];

     // Check each dependency for recent updates
     dependencies.forEach((dependency, documentId) => {
       // Check if the document was updated within the threshold
       const updateTime = new Date(dependency.lastUpdated).getTime();
       if ((now - updateTime) < thresholdMs) {
         // Add to the list if it matches the department
         if (documentId.includes(departmentId)) {
           recentlyUpdated.push(documentId);
         }
       }
     });

     return recentlyUpdated;
   }
   ```

   - Cache bypass for queries about updated content
   ```typescript
   // Inside retrieveRelevantKnowledge
   const recentlyUpdatedDocuments = findRecentlyUpdatedDocuments(departmentId);
   const hasRecentUpdates = recentlyUpdatedDocuments.length > 0;

   // Determine if we should bypass cache
   const shouldRefresh = forceRefresh ||
     refreshIndicators.some(indicator => query.toLowerCase().includes(indicator)) ||
     hasRecentUpdates;
   ```

6. **Real-Time Update Notification** (Implemented)
   - Event-based architecture for immediate notification of knowledge changes
   - Cross-tab synchronization for multi-tab usage
   - Offline event persistence with automatic replay
   - Comprehensive error handling and recovery

7. **Smart Query Recognition** (Implemented)
   - Intelligent detection of queries about recently updated content
   - Freshness indicator detection with confidence scoring
   - Document recency categorization and relevance analysis
   - Automatic cache bypass for queries about updated content

8. **Selective Vector Refresh** (Implemented)
   - Differential update system for document embeddings
   - Chunk-specific embedding regeneration to minimize processing
   - Intelligent refresh strategy selection based on change percentage
   - Detailed refresh statistics tracking and verification
   - Comprehensive embedding consistency verification
   - Hash-based chunk comparison for efficient matching
   - Jaccard similarity for detecting modified chunks
   - Automatic detection of missing, extra, and out-of-order chunks

9. **Transparent Cache Status Indicators** (Enhanced)
   - Subtle UI elements showing cache status and source
   - Human-readable timestamp display for retrieved information
   - Non-intrusive refresh button for manual cache invalidation
   - Clean UI design without confusing update buttons
   - Visual indicators for potentially stale critical content
   - Display of refresh reason for bypassed cache results
   - Improved tooltips with detailed cache information

   ```typescript
   // Example of enhanced cache status indicator in ChatMessage component
   {message.metadata?.cacheInfo && (
     <div className="mt-2">
       <CacheStatusIndicator
         isCached={message.metadata.cacheInfo.isCached}
         timestamp={message.metadata.cacheInfo.timestamp}
         source={message.metadata.cacheInfo.source}
         refreshReason={message.metadata.cacheInfo.refreshReason}
         isRefreshing={isRefreshing}
         onRefresh={onRefresh ? () => onRefresh(message) : undefined}
         criticalContent={message.metadata.criticalContent === true}
         cacheAge={message.metadata.cacheInfo.isCached ?
           Date.now() - new Date(message.metadata.cacheInfo.timestamp).getTime() :
           0
         }
       />
     </div>
   )}
   ```

   ```typescript
   // Using selective refresh in document updates
   const refreshResult = await selectivelyRefreshEmbeddings(
     documentId,
     originalContent,
     newContent,
     metadata,
     {
       chunkSimilarityThreshold: 0.8,
       verifyConsistency: true,
       useHashComparison: true
     }
   );

   // Verification of embedding consistency
   const verificationResult = await verifyEmbeddingConsistency(
     documentId,
     content,
     { maxChunkSize: 1000 }
   );

   if (verificationResult.consistent) {
     console.log('Embeddings are consistent with document content');
   } else {
     console.log(`Inconsistency detected: Missing: ${verificationResult.missingChunks}, ` +
       `Extra: ${verificationResult.extraChunks}, ` +
       `Out of order: ${verificationResult.outOfOrderChunks}`);
   }
   ```

   ```typescript
   // Chunk comparison for selective refresh
   const comparison = compareChunks(
     oldChunks,
     newChunks,
     {
       similarityThreshold: 0.8,
       useHash: true // Use hash-based comparison for exact matching
     }
   );

   console.log(`Chunk comparison results:`, {
     added: comparison.added.length,
     removed: comparison.removed.length,
     unchanged: comparison.unchanged.length,
     modified: comparison.modified.length
   });
   ```

10. **Forced Refresh Query Parameter** (Implemented)
    - Programmatic control of cache bypassing
    - Tracking and display of refresh reasons
    - Propagation of refresh information through the call chain
    - Detailed logging for debugging and monitoring

    ```typescript
    // Example of using the Forced Refresh Query Parameter
    export async function retrieveRelevantKnowledge(
      departmentId: DepartmentId,
      query?: string,
      _modelId?: string,
      forceRefresh: boolean = false,
      forceRefreshReason?: string // New parameter to track the reason for forced refresh
    ): Promise<KnowledgeRetrievalResult> {
      // ...
      if (forceRefresh) {
        logger.info(`Cache bypass requested for query: "${safeQuery}" - Reason: ${forceRefreshReason || 'Manual request'}`);

        // Add the refresh reason to the query options for tracking
        queryOptions.refreshReason = forceRefreshReason || 'manual_request';
      }
      // ...
    }
    ```

    ```typescript
    // Using the Forced Refresh Parameter in ChatService
    const assistantMessage = await chatService.sendMessage(
      selectedChatId,
      message,
      selectedModelId,
      {
        forceRefresh: true,
        refreshReason: 'user_requested_refresh'
      }
    );
    ```

11. **Global Refresh Control** (Implemented)
    - User-controlled cache bypassing toggle
    - Clear visual feedback for enabled state
    - Detailed tooltip explaining implications
    - Seamless integration with existing UI

    ```typescript
    // GlobalRefreshControl component
    const GlobalRefreshControl: React.FC<GlobalRefreshControlProps> = ({
      onChange,
      initialState = false
    }) => {
      const [enabled, setEnabled] = useState(initialState);

      const handleToggle = () => {
        const newState = !enabled;
        setEnabled(newState);
        onChange(newState);
      };

      return (
        <div className="flex items-center gap-2 p-2 bg-gray-800 rounded-md">
          <Switch
            checked={enabled}
            onChange={handleToggle}
            className={cn(
              "relative inline-flex h-5 w-10 items-center rounded-full",
              enabled ? "bg-blue-600" : "bg-gray-600"
            )}
          />
          <span className="text-sm text-gray-200">Always use fresh data</span>
          <div className="group relative">
            <InformationCircleIcon className="h-4 w-4 text-gray-400 cursor-help" />
            <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-gray-900 text-xs text-gray-300 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
              When enabled, all queries will bypass the cache and retrieve the most up-to-date information. This may increase response time but ensures you always get the latest data.
            </div>
          </div>
        </div>
      );
    };
    ```

    ```typescript
    // Using Global Refresh Control in ChatPanel
    const handleSubmit = (e?: React.FormEvent) => {
      e?.preventDefault();
      if (isLoading) return;

      const messageToSend = message.trim();
      if (!messageToSend) return;

      // Pass the global refresh flag to the send message function
      onSendMessage(messageToSend, selectedModel, {
        forceRefresh: globalRefreshEnabled
      });

      setMessage('');
    };
    ```

12. **Optimistic UI Updates** (Implemented)
    - Immediate UI updates when documents change
    - Content tracking system for targeted updates
    - Optimistic state management with fallback mechanisms
    - Seamless integration with existing UI components

### High-Priority Real-Time RAG Improvements

The three high-priority improvements (Forced Refresh Query Parameter, Enhanced Cache Status Indicator, and Global Refresh Control) work together to provide a comprehensive framework for managing information freshness:

#### Layered Control

These improvements provide control at different levels:

- **System level**: Forced Refresh Query Parameter for programmatic control
- **Session level**: Global Refresh Control for user-defined sessions
- **Information level**: Enhanced Cache Status Indicator for per-response awareness

This layered approach follows the principle of "defense in depth," ensuring that freshness concerns can be addressed at multiple levels.

#### Feedback Loop Completion

Together, they create a complete feedback loop:

- **Input control**: Global Refresh Control and Forced Refresh Parameter
- **Output transparency**: Enhanced Cache Status Indicator

This closed loop follows cybernetic principles, where both input and output sides of the system are addressed, creating a more balanced and effective system.

#### Trust Architecture

The combination builds what can be called a "trust architecture":

- **Control mechanisms**: Give users confidence through agency
- **Transparency mechanisms**: Give users confidence through visibility

This dual approach addresses both the action and information aspects of trust, creating a more robust foundation for user confidence in the system.

```typescript
// Content tracking service for optimistic updates
class ContentTrackingService {
     private displayedContent: Map<string, TrackedContent> = new Map();
     private optimisticUpdates: Map<string, OptimisticUpdate> = new Map();
     private listeners: Map<string, Set<() => void>> = new Map();

     // Track content that is displayed in the UI
     trackContent(content: TrackedContent): void { ... }

     // Start an optimistic update for content
     startOptimisticUpdate(id: string, pendingContent?: string): void { ... }

     // Complete an optimistic update (success)
     completeOptimisticUpdate(id: string, finalContent?: string): void { ... }

     // Fail an optimistic update
     failOptimisticUpdate(id: string, error: string): void { ... }
   }
   ```

   ```typescript
   // Example of optimistic update in action
   const handleOptimisticUpdate = async (message: Message, newContent: string) => {
     try {
       // First, update the UI optimistically
       setMessages(prev => {
         const index = prev.findIndex(m => m.contentId === message.contentId);
         if (index === -1) return prev;

         const newMessages = [...prev];
         newMessages[index] = {
           ...newMessages[index],
           content: newContent,
           metadata: {
             ...newMessages[index].metadata,
             optimisticUpdate: { isPending: true }
           }
         };
         return newMessages;
       });

       // Then perform the actual update
       await updateContentOnServer(message.contentId, newContent);

       // Finally, confirm the update in the UI
       setMessages(prev => {
         const index = prev.findIndex(m => m.contentId === message.contentId);
         if (index === -1) return prev;

         const newMessages = [...prev];
         newMessages[index] = {
           ...newMessages[index],
           metadata: {
             ...newMessages[index].metadata,
             optimisticUpdate: { isPending: false }
           }
         };
         return newMessages;
       });
     } catch (error) {
       // Handle failure and revert the optimistic update
       // ...
     }
   };
   ```

   // Logging refresh statistics
   logger.debug(`Selective refresh stats: Total chunks: ${refreshResult.totalChunks}, ` +
     `Added: ${refreshResult.addedChunks}, Removed: ${refreshResult.removedChunks}, ` +
     `Unchanged: ${refreshResult.unchangedChunks}, Modified: ${refreshResult.modifiedChunks}`);
   ```

   ### Selective Vector Refresh Implementation Details

   The Selective Vector Refresh component optimizes the process of updating vector embeddings when document content changes. Instead of regenerating embeddings for the entire document, it identifies which chunks have changed and only updates those specific chunks.

   #### Key Functions

   1. **compareChunks**: Analyzes differences between old and new content chunks
      ```typescript
      function compareChunks(
        oldChunks: string[],
        newChunks: string[],
        options: { similarityThreshold?: number, useHash?: boolean }
      ): ChunkComparisonResult
      ```

   2. **calculateTextSimilarity**: Measures similarity between text chunks using Jaccard similarity
      ```typescript
      function calculateTextSimilarity(text1: string, text2: string): number
      ```

   3. **selectivelyRefreshEmbeddings**: Main function that orchestrates the selective refresh process
      ```typescript
      async function selectivelyRefreshEmbeddings(
        documentId: string,
        oldContent: string,
        newContent: string,
        metadata: ChunkMetadata,
        options: SelectiveRefreshOptions
      ): Promise<SelectiveRefreshResult>
      ```

   4. **verifyEmbeddingConsistency**: Ensures vector database consistency with document content
      ```typescript
      async function verifyEmbeddingConsistency(
        documentId: string,
        content: string,
        options: { maxChunkSize?: number }
      ): Promise<{
        consistent: boolean;
        totalChunks: number;
        missingChunks: number;
        extraChunks: number;
        outOfOrderChunks: number;
      }>
      ```

   #### Refresh Strategy Selection

   The system intelligently selects the most efficient refresh strategy based on the extent of changes:

   1. **No Changes**: If no chunks have changed, skip the refresh entirely
   2. **Small Changes (<50%)**: Use selective refresh to update only changed chunks
   3. **Large Changes (>50%)**: Use full refresh for better efficiency

   #### Verification Process

   After refresh, the system can verify embedding consistency by:

   1. Retrieving all vector documents for the document from the vector store
   2. Generating chunks from the current document content
   3. Comparing hashes of stored chunks with newly generated chunks
   4. Identifying missing, extra, and out-of-order chunks
   5. Providing detailed statistics on inconsistencies

   ```typescript
   // Analyzing a query for recency requirements
   const recencyAnalysis = analyzeQueryForRecency(query, departmentId);

   // Determining if cache should be bypassed
   const shouldBypassCache = recencyAnalysis.shouldBypassCache;

   // Logging recency analysis results
   if (recencyAnalysis.isAboutRecentUpdates) {
     logger.info(`Query detected as requesting recent updates (confidence: ${recencyAnalysis.confidence.toFixed(2)})`);
     logger.info(`Freshness indicators: ${recencyAnalysis.matchedIndicators.join(', ')}`);
   }
   ```

   ```typescript
   // Publishing document update events
   knowledgeUpdateBroker.publish('document.updated', {
     eventType: 'document.updated',
     documentId,
     changeType: 'updated',
     contentChanged,
     metadataChanged,
     departmentId,
     documentName,
     source: 'internal'
   });

   // Subscribing to document events
   knowledgeUpdateBroker.subscribe('document.*', (event) => {
     if (event.eventType.startsWith('document.')) {
       const documentEvent = event as any;
       if (documentEvent.documentId) {
         // Clear caches for this document
         adaptiveCacheService.clearDocumentCache(documentEvent.documentId);
       }
     }
   });
   ```

9. **Session Management**
   - Enhanced logout function with complete cache clearing
   - User ID tracking for session isolation
   - Automatic cache refresh when switching users
   - Proper handling of Google Drive authentication tokens

### Benefits of Integrated Approach

Implementing Google Drive improvements together with the Real-Time RAG system provides several key advantages:

1. **Unified System**
   - One cohesive system for real-time updates across all knowledge sources
   - Consistent behavior regardless of where knowledge is stored
   - Simplified architecture with shared components
   - Reduced code duplication and maintenance overhead

2. **Efficient Development**
   - Shared infrastructure for both internal and external knowledge sources
   - Reusable components for authentication, caching, and event handling
   - Coordinated testing across all knowledge sources
   - Faster implementation timeline through parallel development

3. **Consistent User Experience**
   - Same real-time behavior for all knowledge sources
   - Unified UI indicators and controls
   - Consistent performance characteristics
   - Seamless transitions between different knowledge types

4. **Robust Architecture**
   - Designed from the ground up to handle multiple knowledge sources
   - Comprehensive error handling across all components
   - Graceful degradation when individual services are unavailable
   - Scalable design that can accommodate future growth

5. **Future-Proof Design**
   - Extensible architecture that can incorporate additional knowledge sources
   - Abstracted interfaces for knowledge operations
   - Modular components that can be enhanced independently
   - Well-documented integration points for future extensions

## 19. Testing and Validation

The section-aware retrieval system is thoroughly tested using Jest as the testing framework. The tests are organized into unit tests, integration tests, and edge case tests.

### Testing Framework

The testing framework is set up with the following components:

1. **Jest Configuration**: `jest.config.js` at the project root configures TypeScript support, coverage reporting, and module resolution.

2. **Test Directory Structure**:
   ```
   src/__tests__/
   ├── unit/
   │   └── knowledge/
   │       ├── sectionDetector.test.ts
   │       ├── documentClassification.test.ts
   │       └── edgeCases.test.ts
   ├── integration/
   │   └── knowledge/
   │       └── retrievalPipeline.test.ts
   └── helpers/
       └── mockHelpers.ts
   ```

3. **Test Runner**: A custom test runner script (`src/__tests__/runTests.js`) allows running specific test suites or all tests.

### Test Categories

1. **Unit Tests**:
   - **Section Detection**: Tests for confidence-based section detection, including explicit section mentions, personal pronouns, and ambiguous queries.
   - **Document Classification**: Tests for document classification based on metadata, tags, and document names.
   - **Edge Cases**: Tests for handling very short queries, very long queries, special characters, spelling errors, conflicting signals, and empty queries.

2. **Integration Tests**:
   - **Retrieval Pipeline**: Tests for the end-to-end retrieval process, including query enhancement, section-specific parameter optimization, and result formatting.
   - **Error Handling**: Tests for graceful handling of errors at different stages of the pipeline.

3. **Mocking**:
   - Firebase authentication and Firestore are mocked to simulate a logged-in user.
   - Embedding generation is mocked to return predictable vectors.
   - Vector search is mocked to return section-specific test documents.

### Running Tests

Tests can be run using the following commands:

```bash
# Run all tests
npm test

# Run specific test categories
node src/__tests__/runTests.js unit
node src/__tests__/runTests.js integration
node src/__tests__/runTests.js edge

# Run specific test files
node src/__tests__/runTests.js section
node src/__tests__/runTests.js document
node src/__tests__/runTests.js retrieval
```

### Validation Metrics

1. **Accuracy**: Percentage of queries where the correct section is detected with confidence above threshold
2. **Precision**: Relevance of retrieved documents for section-specific queries
3. **Recall**: Coverage of relevant documents across sections
4. **Error Handling**: Graceful degradation and appropriate fallbacks
5. **Edge Case Handling**: Correct behavior for unusual or ambiguous inputs

## 20. Backward Compatibility

The section-aware retrieval system includes a comprehensive migration strategy for existing documents to ensure backward compatibility.

### Document Migration Service

A dedicated document migration service handles the classification and updating of existing documents:

```typescript
export async function migrateDocuments(
  options: MigrationOptions = {}
): Promise<MigrationProgress> {
  // Implementation details...
}
```

### Migration Strategies

1. **High-Priority Migration**
   - Migrates the most recently updated documents first (up to 100 documents)
   - Prioritizes documents that are most likely to be accessed by users
   - Provides immediate value while full migration runs in the background

2. **Full Migration**
   - Processes all documents in the system in batches
   - Uses a configurable batch size (default: 50 documents)
   - Adds small delays between batches to avoid overloading the system

3. **On-Demand Migration**
   - Individual documents can be migrated when accessed
   - Provides a fallback for documents missed by batch processes
   - Ensures all documents eventually get section metadata

### Document Classification

The migration process uses the same confidence-based classification algorithm as the main system:

```typescript
function determineDocumentSection(document: Partial<KnowledgeDocument>): KnowledgeSection {
  // Initialize confidence scores for each section
  const scores: Record<KnowledgeSection, number> = {
    'goals': 0,
    'knowledge': 0,
    'drive': 0,
    'general': 0
  };

  // Calculate scores based on metadata, tags, name, and content
  // ...

  // Return the section with the highest confidence
  return highestSection;
}
```

### Admin Interface

An administrative interface allows authorized users to manage the migration process:

1. **Migration Dashboard**
   - Shows progress of ongoing migrations
   - Displays statistics on successful and failed migrations
   - Provides controls to start, pause, or cancel migrations

2. **Migration Options**
   - High-priority migration for quick results
   - Full migration for comprehensive coverage
   - Dry-run simulation to preview changes without applying them

3. **Progress Tracking**
   - Real-time updates on migration progress
   - Detailed logs for troubleshooting
   - Performance metrics for optimization

## 21. Google Drive Section-Aware Retrieval

This section details the implementation of section-aware retrieval specifically for Google Drive documents, leveraging the existing Google Drive integration and section-aware retrieval framework.

### 21.1 Overview

The Google Drive section-aware retrieval extends the existing section detection and confidence scoring capabilities to documents sourced from Google Drive. This ensures that Drive documents benefit from the same advanced retrieval capabilities as other knowledge content.

### 21.2 Integration with Existing Google Drive Components

The implementation leverages the existing Google Drive integration:

```typescript
// Existing Google Drive components
// 1. Authentication and API access
// 2. Folder connection UI
// 3. Document syncing
// 4. Content extraction
```

### 21.3 Enhanced Document Processing

The core enhancement is in the document processing pipeline, specifically in the `syncGoogleDriveFolder` function:

```typescript
// Enhanced syncGoogleDriveFolder with section detection
export async function syncGoogleDriveFolder(connectionId: string): Promise<boolean> {
  // Existing code for getting connection and listing files

  // Process each file with section awareness
  for (const file of files) {
    // Only process text-based files
    if (file.mimeType.includes('text') ||
        file.mimeType.includes('document') ||
        file.mimeType.includes('pdf')) {

      // Get file content
      const content = await getFileContent(file.id);

      if (content) {
        // Determine file type
        let fileType: 'text' | 'pdf' | 'image' = 'text';
        if (file.mimeType.includes('pdf')) {
          fileType = 'pdf';
        }

        // NEW: Determine section using existing document classifier
        const section = determineDocumentSection({
          name: file.name,
          content,
          tags: ['google-drive'],
          metadata: { fileType }
        });

        // Add to knowledge base with section metadata
        await addDocument(
          connection.departmentId,
          file.name,
          content,
          fileType,
          ['google-drive'],
          {
            driveFileId: file.id,
            driveConnectionId: connectionId,
            driveFolderId: connection.folderId,
            webViewLink: file.webViewLink,
            mimeType: file.mimeType,
            modifiedTime: file.modifiedTime,
            // NEW: Add section metadata
            section,
            sectionConfidence: 0.8,
            sectionDetectionMethod: 'automatic'
          }
        );
      }
    }
  }

  // Rest of existing function
}
```

### 21.4 Section Detection for Drive Documents

The implementation uses the existing `determineDocumentSection` function with enhancements for Drive-specific content:

```typescript
// Enhanced document classification for Drive documents
function determineDocumentSection(document: KnowledgeDocument): KnowledgeSection {
  // Existing classification logic

  // Additional Drive-specific classification
  if (document.tags && document.tags.includes('google-drive')) {
    // Check for Drive-specific indicators
    const text = `${document.name} ${document.content}`.toLowerCase();

    // Check for spreadsheet indicators
    if (document.metadata?.mimeType?.includes('spreadsheet') ||
        text.includes('spreadsheet') ||
        text.includes('table') ||
        text.includes('excel')) {
      // Spreadsheets are often knowledge documents
      return 'knowledge';
    }

    // Check for presentation indicators
    if (document.metadata?.mimeType?.includes('presentation') ||
        text.includes('presentation') ||
        text.includes('slide') ||
        text.includes('powerpoint')) {
      // Presentations could be goals or knowledge
      return text.includes('goal') || text.includes('objective') ||
             text.includes('strategy') ? 'goals' : 'knowledge';
    }

    // Default Drive documents to 'drive' section
    return 'drive';
  }

  // Rest of existing function
}
```

### 21.5 Real-Time Updates for Drive Documents

To ensure Drive documents benefit from real-time updates:

```typescript
// Enhanced Drive document update handling
export async function updateDriveDocument(file: DriveFile, content: string): Promise<boolean> {
  try {
    // Find existing document by Drive file ID
    const existingDocs = await findDocumentsByMetadata('driveFileId', file.id);

    if (existingDocs.length > 0) {
      const existingDoc = existingDocs[0];

      // Update document content
      await updateDocument(existingDoc.id, {
        content,
        metadata: {
          ...existingDoc.metadata,
          modifiedTime: new Date().toISOString()
        }
      });

      // Invalidate caches for this document
      invalidateDocumentCache(existingDoc.id);

      // Regenerate embeddings
      await regenerateDocumentEmbeddings(existingDoc.id);

      return true;
    }
    return false;
  } catch (error) {
    console.error('Error updating Drive document:', error);
    return false;
  }
}
```

### 21.6 Integration with Section-Aware Retrieval

Drive documents are now fully integrated with the section-aware retrieval system:

```typescript
// Section-aware retrieval now includes Drive documents
export async function retrieveFromSection(
  departmentId: DepartmentId,
  section: KnowledgeSection,
  query: string,
  options?: {
    conversationHistory?: string[];
    modelId?: string;
    forceRefresh?: boolean;
  }
): Promise<string> {
  // Existing retrieval logic

  // Drive documents are now included in section-specific searches
  const documents = await searchSimilarDocuments(
    embedding,
    departmentId,
    searchParams.topK,
    searchParams.similarityThreshold,
    currentUser.uid,
    { section }
  );

  // Format results according to section-specific template
  return formatSectionResults(documents, section, departmentId);
}
```

### 21.7 Files and Functions Modified

1. **`src/services/googleDriveService.ts`**
   - Enhanced `syncGoogleDriveFolder` function to include section detection
   - Added `updateDriveDocument` function for real-time updates

2. **`src/genkit/knowledge/documentClassifier.ts`**
   - Enhanced `determineDocumentSection` function with Drive-specific logic

3. **`src/genkit/knowledge/knowledgeService.ts`**
   - Ensured `retrieveFromSection` properly handles Drive documents

4. **`src/genkit/knowledge/vectorUtils.ts`**
   - Updated search functions to include Drive document metadata

### 21.8 Testing and Validation

The implementation includes comprehensive testing:

1. **Unit Tests**
   - Test section detection for various Drive document types
   - Verify correct metadata handling for Drive documents

2. **Integration Tests**
   - Test end-to-end flow from Drive connection to retrieval
   - Verify real-time updates for Drive documents

3. **Performance Tests**
   - Measure retrieval latency for Drive documents
   - Compare performance with non-Drive documents

## 22. Future Improvements

While the current implementation provides a solid foundation for section-aware retrieval and department agent knowledge access, several areas for future improvement have been identified:

### Refinement and Optimization

1. **Boosting Calibration**
   - Implement A/B testing for optimal boost values
   - Dynamically adjust boost values based on user feedback
   - Explore machine learning approaches to optimize boost parameters

2. **Metadata Enhancement**
   - Add validation and enrichment processes for document metadata
   - Implement batch processes to improve metadata quality across the knowledge base
   - Develop tools for users to easily enhance document metadata

3. **Advanced Analytics**
   - Implement detailed monitoring of section detection accuracy
   - Track user satisfaction with section-specific results
   - Develop feedback loops to continuously improve the system

4. **Advanced Real-Time Knowledge Updates**
   - **Cross-department knowledge coordination**: Extend real-time synchronization across departments
   - **Collaborative editing**: Enable multiple users to edit knowledge simultaneously
   - **Conflict resolution system**: Develop strategies for handling concurrent edits
   - **Versioning and rollback capabilities**: Add history tracking for knowledge documents
   - **Real-time collaboration UI**: Create interfaces for seeing others' edits in real-time
   - **Edit suggestion system**: Allow users to suggest changes to knowledge they can't directly edit

### Machine Learning Integration

Future versions could replace the rule-based confidence scoring with machine learning models:

1. **Classification Model**: Train a model to classify queries into sections
2. **Embedding-Based Approach**: Use embeddings to compare queries to section exemplars
3. **Hybrid Approach**: Combine rule-based and ML-based approaches for robustness

### Performance Optimization

The system now includes several performance optimizations that significantly improve both retrieval quality and speed:

1. **Hybrid Retrieval Strategies**
   - **Two-stage retrieval process**: Combines fast lexical search with precise vector search
   - **BM25-like algorithm**: Efficiently filters candidate documents based on keyword relevance
   - **Reduced vector computations**: Performs expensive vector operations only on pre-filtered candidates
   - **Weighted scoring**: Combines lexical and semantic similarity for better relevance
   - **Graceful degradation**: Multiple fallback mechanisms ensure results even when one approach fails
   - **Performance monitoring**: Detailed metrics for lexical search time, vector search time, and total retrieval time

2. **Adaptive Caching**
   - **Predictive caching**: Analyzes query patterns to predict and pre-fetch likely future queries
   - **Dynamic TTL adjustment**: Automatically adjusts cache expiration times based on usage patterns
   - **Volatility detection**: Identifies frequently changing data and adjusts caching strategy accordingly
   - **Priority-based eviction**: Uses frequency and recency of access to prioritize cache entries
   - **Category-specific caching**: Applies different caching strategies for different types of data
   - **Related key generation**: Identifies and caches related queries for faster access

3. **Dynamic Thresholds**
   - **System load adaptation**: Automatically adjusts confidence thresholds based on current system load
   - **Query complexity awareness**: Modifies thresholds based on query complexity and characteristics
   - **Department-specific adjustments**: Customizes thresholds for different departments' requirements
   - **Section-specific tuning**: Optimizes thresholds for different knowledge sections
   - **Performance feedback loop**: Uses response time metrics to continuously refine thresholds
   - **Graceful degradation**: Becomes more lenient under high load to maintain responsiveness

4. **Implementation Benefits**
   - **Improved latency**: Lexical pre-filtering and predictive caching significantly reduce overall retrieval time
   - **Better relevance**: Combining keyword matching with semantic similarity captures more aspects of relevance
   - **Reduced resource usage**: Fewer vector operations and smarter caching mean lower computational requirements
   - **Enhanced reliability**: Multiple fallback mechanisms ensure consistent results
   - **Adaptive performance**: System automatically adjusts to usage patterns and data volatility
   - **Seamless user transitions**: Proper cache isolation prevents data leakage between user accounts

For detailed information about performance optimizations implemented in the system, please refer to the [RAG Latency Improvement](./rag-latency-improvement.md) documentation.

### Department Knowledge Access Enhancements

1. **Department-Specific Embedding Models**
   - Train specialized embedding models for each department's domain
   - Implement domain-specific vector spaces for improved similarity matching
   - Develop department-specific stopwords and boosting terms

2. **Cross-Department Knowledge Coordination**
   - Implement knowledge sharing between departments
   - Develop conflict resolution for contradictory information
   - Create a unified knowledge representation across departments

3. **Adaptive Department Context**
   - Learn from user interactions to improve department context
   - Implement feedback loops for department relevance
   - Develop dynamic department priority adjustment

## Real-Time RAG for Updated Knowledge Base Information

A critical enhancement to the RAG system is ensuring that any updates to the knowledge base are immediately reflected in retrieval results. This section outlines the architecture and implementation plan for real-time knowledge updates.

### Problem Statement

When information in the knowledge base is updated (e.g., changing a date from November 2 to December 15, or adjusting a percentage from 20% to 30%), the RAG system must immediately reflect these changes in retrieval results without requiring system restarts or manual cache clearing.

### Architecture Overview

```
Document Update → Precision Cache Invalidation → Vector Refresh → Event Notification → Real-Time Retrieval
```

### Key Components

1. **Precision Cache Invalidation System**
   - Document-specific cache tracking and invalidation
   - Cache dependency graph for related content
   - Content fingerprinting for change detection
   - Multi-level cache coordination

2. **Real-Time Update Notification System**
   - Event-based architecture for knowledge updates
   - Publish-subscribe pattern for component communication
   - Update propagation across the application

3. **Smart Query Recognition**
   - Update-aware query analysis
   - Timestamp comparison for cache freshness
   - Automatic cache bypass for queries about updated content

4. **Selective Vector Refresh**
   - Differential update detection
   - Chunk-specific embedding regeneration
   - Metadata consistency verification

5. **Transparent Cache Status UI**
   - Cache freshness indicators
   - User-controlled refresh options
   - Update timestamp visibility

### Implementation Plan

#### Phase 1: Foundation - Cache Dependency Tracking

1. **Document-to-Cache Mapping**
   - Extend `adaptiveCacheService` with document tracking capabilities
   - Implement a bidirectional mapping between documents and cache keys
   - Create a dependency graph to track relationships between documents and cached results

   ```typescript
   interface CacheDependency {
     documentId: string;
     cacheKeys: string[];
     lastUpdated: string;
     contentFingerprint: string;
   }
   ```

2. **Precision Cache Invalidation**
   - Implement targeted cache clearing based on document IDs
   - Develop content fingerprinting to detect exactly what changed
   - Create a cache invalidation strategy that preserves unrelated cache entries

   ```typescript
   function invalidateDocumentCache(documentId: string, newFingerprint: string): void {
     const dependency = cacheDependencies.get(documentId);
     if (dependency && dependency.contentFingerprint !== newFingerprint) {
       // Clear only affected cache entries
       dependency.cacheKeys.forEach(key => adaptiveCacheService.delete(key));
       // Update fingerprint
       dependency.contentFingerprint = newFingerprint;
       dependency.lastUpdated = new Date().toISOString();
     }
   }
   ```

3. **Cache Monitoring**
   - Add comprehensive logging for cache operations
   - Implement metrics collection for cache hit/miss rates
   - Create debugging tools for cache inspection

#### Phase 2: Real-Time Communication

1. **Knowledge Update Broker**
   - Implement a central event broker for knowledge updates
   - Create event types for different update operations (create, update, delete)
   - Develop a subscription mechanism for system components

   ```typescript
   class KnowledgeUpdateBroker {
     private subscribers: Map<string, Function[]> = new Map();

     subscribe(eventType: string, callback: Function): void {
       // Add subscriber to the event type
     }

     publish(eventType: string, data: any): void {
       // Notify all subscribers of the event
     }
   }
   ```

2. **Event Propagation**
   - Integrate event emission into document operations
   - Ensure proper event handling in retrieval components
   - Implement cross-component communication

3. **Update Verification**
   - Add confirmation mechanisms for update propagation
   - Implement retry logic for failed updates
   - Create consistency checks for system state

#### Phase 3: Smart Query Handling

1. **Update-Aware Query Analysis**
   - Enhance query analysis to detect queries about recently updated content
   - Implement timestamp comparison for cache freshness determination
   - Create a query classifier for update-related queries

   ```typescript
   function shouldBypassCache(query: string, relatedDocuments: string[]): boolean {
     // Check if query contains update indicators
     const hasUpdateIndicators = updateIndicators.some(indicator =>
       query.toLowerCase().includes(indicator));

     // Check if related documents were recently updated
     const hasRecentUpdates = relatedDocuments.some(docId => {
       const dependency = cacheDependencies.get(docId);
       if (!dependency) return false;

       const updateTime = new Date(dependency.lastUpdated).getTime();
       const now = Date.now();
       return (now - updateTime) < RECENT_UPDATE_THRESHOLD;
     });

     return hasUpdateIndicators || hasRecentUpdates;
   }
   ```

2. **Automatic Cache Bypass**
   - Implement intelligent cache bypass for relevant queries
   - Create a prediction model for when to use fresh retrieval
   - Develop a balance between cache utilization and freshness

3. **Query-Document Relationship Tracking**
   - Track which documents are relevant to which queries
   - Create a reverse index from documents to queries
   - Use this relationship for targeted cache invalidation

#### Phase 4: Vector Embedding Optimization

1. **Differential Update Detection**
   - Implement content comparison to identify changed sections
   - Create a chunking strategy that minimizes re-embedding
   - Develop change magnitude assessment for optimization

   ```typescript
   function detectChangedChunks(oldContent: string, newContent: string): number[] {
     const oldChunks = chunkText(oldContent);
     const newChunks = chunkText(newContent);

     // Identify which chunks have changed
     return newChunks.map((chunk, index) => {
       if (index >= oldChunks.length) return index; // New chunk
       return chunk !== oldChunks[index] ? index : -1;
     }).filter(index => index !== -1);
   }
   ```

2. **Selective Embedding Regeneration**
   - Regenerate embeddings only for changed chunks
   - Implement a chunk-tracking system for vector documents
   - Create an efficient update strategy for the vector database

3. **Metadata Consistency**
   - Ensure consistent metadata across all vector documents
   - Implement verification steps for vector document updates
   - Create recovery mechanisms for inconsistent states

#### Phase 5: UI Enhancements

1. **Cache Status Indicators**
   - Add subtle UI elements showing cache status
   - Implement timestamp display for retrieved information
   - Create visual differentiation for fresh vs. cached results

2. **User-Controlled Refresh**
   - Add a non-intrusive refresh button
   - Implement user preferences for cache behavior
   - Create keyboard shortcuts for power users

3. **Optimistic UI Updates**
   - Update UI immediately when documents change
   - Track displayed content for targeted updates
   - Implement consistency verification between display and storage

### Performance Safeguards

1. **Targeted Operations**
   - Only invalidate caches for affected documents
   - Only regenerate embeddings for changed chunks
   - Only update UI elements displaying affected content

2. **Background Processing**
   - Run embedding generation in background threads
   - Use web workers for computationally intensive tasks
   - Implement progressive loading for large updates

3. **Incremental Updates**
   - Update the vector database incrementally
   - Implement batching for multiple simultaneous updates
   - Use transaction-like patterns for consistency

4. **Graceful Degradation**
   - Implement fallbacks that prioritize speed if needed
   - Create a tiered approach to update propagation
   - Ensure system responsiveness even during updates

### Cache Consistency Verification

To ensure that when Fresh Data Mode is OFF, the information provided is still reliable and not hallucinating, we've implemented a comprehensive cache consistency verification system:

1. **Enhanced Cache Dependency Tracking**
   - Content versioning to track changes over time
   - Consistency status tracking for each document
   - Metadata validation for cache entries
   - Detailed fingerprinting for content change detection

   ```typescript
   interface CacheDependency {
     documentId: string;
     cacheKeys: Set<string>;
     lastUpdated: string;
     contentFingerprint: string;
     contentVersion?: number;
     lastVerified?: string;
     consistencyStatus?: 'verified' | 'unverified' | 'inconsistent';
     metadataHash?: string;
   }
   ```

2. **Consistency Verification Process**
   - Cache entries are checked against their source documents
   - Fingerprints are compared to detect content changes
   - Metadata is validated for consistency
   - Version numbers are checked to detect outdated entries

   ```typescript
   interface ConsistencyVerificationResult {
     isConsistent: boolean;
     inconsistentKeys: string[];
     verifiedKeys: string[];
     totalKeys: number;
     verificationTime: string;
     issues?: {
       missingMetadata?: string[];
       invalidFingerprint?: string[];
       outdatedVersion?: string[];
       corruptedData?: string[];
     };
   }
   ```

3. **Automatic Repair Mechanism**
   - Inconsistent cache entries are automatically removed
   - Dependency tracking is updated to reflect the current state
   - Detailed logs are generated for troubleshooting
   - Periodic verification runs every 30 minutes

4. **Smart TTL Calculation**
   - Adaptive cache expiration based on content characteristics
   - Frequently accessed items get longer TTL
   - Business hours detection for shorter TTL during high-activity periods
   - Category-specific TTL adjustments

5. **Integration with Fresh Data Mode**
   - When Fresh Data Mode is OFF: Consistency verification ensures reliability
   - When Fresh Data Mode is ON: Cache is bypassed, but consistency verification still runs in background
   - Transparent to users: Reliable information regardless of mode setting

### Success Metrics

1. **Update Reflection Time**: Time between document update and retrieval of updated information (target: <5 seconds)
2. **Cache Efficiency**: Percentage of cache preserved during document updates (target: >90%)
3. **Query Response Time**: Impact on query response time compared to current system (target: <10% increase)
4. **User Perception**: User ratings of system responsiveness to changes (target: >4.5/5)

### Integration with Existing Components

The real-time update system integrates with several existing components:

1. **Knowledge Service**: Enhanced to emit update events and perform targeted cache invalidation
2. **Vector Service**: Modified to support selective vector document updates
3. **Adaptive Cache Service**: Extended with document dependency tracking
4. **Query Processing**: Enhanced with update awareness and cache bypass logic
5. **UI Components**: Updated to display freshness indicators and refresh controls

By implementing this comprehensive real-time update system, the RAG architecture will maintain its current performance and accuracy while ensuring that any changes to the knowledge base are immediately reflected in retrieval results.

## Conclusion

The integrated system of Section-Aware Retrieval, Query Understanding Enhancement, and Department Agent Knowledge Access significantly enhances BusinessLM's knowledge retrieval capabilities. By combining these three components, the system provides a comprehensive approach to understanding user queries and delivering the most relevant information.

The Section-Aware Retrieval component optimizes search and presentation based on the specific knowledge section being targeted. The Query Understanding Enhancement improves the system's ability to understand user intent by detecting follow-up questions, extracting relevant context from conversation history, and expanding queries with additional terms. The Department Agent Knowledge Access component provides specialized knowledge retrieval for each department with section prioritization based on department-specific needs.

By leveraging confidence-based section detection, follow-up question detection, context extraction, query expansion, parameter optimization, result boosting, department-specific prioritization, and UI-specific formatting, the system provides more relevant and better-formatted results for users.

The implementation is designed with BusinessLM's target audience of solopreneurs and entrepreneurs in mind, supporting personal pronouns and natural language queries. The system is built to be robust, with comprehensive error handling and fallback mechanisms to ensure a smooth user experience even in edge cases.

This holistic approach to knowledge retrieval creates a powerful, context-aware knowledge system that understands not just what information is being requested, but also the conversation context, which department's perspective is most relevant, and which section of knowledge should be prioritized. This significantly improves the quality and relevance of information provided to users, making the system more intuitive and helpful for business owners.

With the planned future improvements, the system will continue to evolve and provide even better results for BusinessLM users.