# Department Agent Knowledge Access Enhancement

This document describes the implementation of enhanced knowledge access for department agents in the BusinessLM system, focusing on section prioritization and context handling.

## 1. Overview

The Department Agent Knowledge Access Enhancement improves how department agents access knowledge by:

1. Implementing section prioritization based on department-specific needs
2. Enhancing context handling for department-specific queries
3. Providing specialized knowledge retrieval for each department

## 2. Section Prioritization

### 2.1 Department-Specific Section Priorities

Each department has different priorities for knowledge sections based on their typical information needs:

```typescript
const DEPARTMENT_SECTION_PRIORITIES: Record<DepartmentId, KnowledgeSection[]> = {
  'finance': ['goals', 'knowledge', 'drive'],
  'marketing': ['goals', 'knowledge', 'drive'],
  'operations': ['knowledge', 'goals', 'drive'],
  'hr': ['goals', 'knowledge', 'drive'],
  'sales': ['goals', 'knowledge', 'drive'],
  'product': ['knowledge', 'goals', 'drive'],
  'co-ceo': ['goals', 'knowledge', 'drive']
};
```

### 2.2 Confidence Thresholds

Different departments have different confidence thresholds for section detection:

```typescript
const SECTION_CONFIDENCE_THRESHOLDS: Record<DepartmentId, Record<KnowledgeSection, number>> = {
  'finance': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'marketing': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'operations': { 'goals': 0.4, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 },
  'hr': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'sales': { 'goals': 0.3, 'knowledge': 0.4, 'drive': 0.5, 'general': 0.5 },
  'product': { 'goals': 0.4, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 },
  'co-ceo': { 'goals': 0.3, 'knowledge': 0.3, 'drive': 0.5, 'general': 0.5 }
};
```

### 2.3 Section Selection Algorithm

The system determines which sections to try based on:

1. Section override (if provided)
2. Detected section (if confidence is high enough)
3. Department-specific section priorities

```typescript
// Determine target sections to try based on priorities
let sectionsToTry: KnowledgeSection[] = [];

// If a section override is provided, use it first
if (sectionOverride) {
  sectionsToTry = [sectionOverride, ...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
} 
// If confidence is high enough, use the detected section first
else if (analysis.confidence >= SECTION_CONFIDENCE_THRESHOLDS[departmentId][analysis.primaryIntent]) {
  sectionsToTry = [analysis.primaryIntent, ...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
} 
// Otherwise, use department priorities
else {
  sectionsToTry = [...DEPARTMENT_SECTION_PRIORITIES[departmentId]];
}
```

## 3. Context Enhancement

### 3.1 Department-Specific Context Terms

Each department has specific context terms that are relevant to their domain:

```typescript
const DEPARTMENT_CONTEXT_TERMS: Record<DepartmentId, string[]> = {
  'finance': ['budget', 'financial', 'revenue', 'cost', 'expense', 'profit', 'investment'],
  'marketing': ['campaign', 'brand', 'audience', 'customer', 'market', 'promotion', 'content'],
  'operations': ['process', 'workflow', 'efficiency', 'productivity', 'logistics', 'supply chain'],
  'hr': ['employee', 'talent', 'recruitment', 'hiring', 'training', 'performance', 'benefits'],
  'sales': ['customer', 'client', 'lead', 'opportunity', 'deal', 'pipeline', 'revenue'],
  'product': ['feature', 'roadmap', 'release', 'user', 'requirement', 'design', 'development'],
  'co-ceo': ['strategy', 'vision', 'goal', 'priority', 'performance', 'growth', 'challenge']
};
```

### 3.2 Query Enhancement

Queries are enhanced with department-specific context terms:

```typescript
function enhanceQueryWithDepartmentContext(
  query: string,
  departmentId: DepartmentId,
  section?: KnowledgeSection
): string {
  // Get department-specific context terms
  const contextTerms = DEPARTMENT_CONTEXT_TERMS[departmentId];
  
  // Check if query already contains department context
  const containsDepartmentContext = contextTerms.some(term => 
    query.toLowerCase().includes(term.toLowerCase())
  );
  
  // If query already has department context, return as is
  if (containsDepartmentContext) {
    return query;
  }
  
  // Select 1-2 relevant context terms based on section
  let selectedTerms = [...];
  
  // Add department context to query
  return `${query} ${selectedTerms.join(' ')}`;
}
```

### 3.3 Section-Specific Term Selection

Terms are selected based on the target section:

```typescript
// Select terms that are most relevant to the section
switch (section) {
  case 'goals':
    selectedTerms = contextTerms.filter(term => 
      ['strategy', 'goal', 'priority', 'target', 'objective', 'plan'].some(goalTerm => 
        term.toLowerCase().includes(goalTerm.toLowerCase())
      )
    );
    break;
  case 'knowledge':
    selectedTerms = contextTerms.filter(term => 
      ['process', 'information', 'data', 'report', 'analysis'].some(knowledgeTerm => 
        term.toLowerCase().includes(knowledgeTerm.toLowerCase())
      )
    );
    break;
  case 'drive':
    selectedTerms = contextTerms.filter(term => 
      ['document', 'file', 'report', 'presentation'].some(driveTerm => 
        term.toLowerCase().includes(driveTerm.toLowerCase())
      )
    );
    break;
}
```

## 4. Knowledge Retrieval Process

### 4.1 Main Retrieval Function

The main retrieval function orchestrates the entire process:

```typescript
export async function retrieveDepartmentKnowledge(
  departmentId: DepartmentId,
  query: string,
  conversationHistory: string[] = [],
  options: {
    forceRefresh?: boolean;
    modelId?: string;
    sectionOverride?: KnowledgeSection;
  } = {}
): Promise<string> {
  // Implementation details...
}
```

### 4.2 Retrieval Algorithm

The retrieval process follows these steps:

1. Analyze the query with enhanced context awareness
2. Determine target sections to try based on priorities
3. Try each section in priority order
4. Fall back to general knowledge retrieval if section-specific retrieval fails

### 4.3 Error Handling

Comprehensive error handling ensures robustness:

```typescript
try {
  // Retrieval logic...
} catch (error) {
  logger.error(`[${departmentId.toUpperCase()}] Error in retrieveDepartmentKnowledge:`, error);
  
  // Return a generic error message as knowledge
  return `I apologize, but I encountered an error while retrieving information for your query. Please try rephrasing your question or ask about something else.`;
}
```

## 5. Integration with Department Agents

### 5.1 Department Agent Flow

The department agent flow uses the enhanced knowledge access:

```typescript
export const createDepartmentAgentFlow = (departmentId: DepartmentId) => {
  const department = departmentPersonalities[departmentId];
  
  return async (query: string, context: any) => {
    // Get relevant knowledge from the department's knowledge base with enhanced access
    let knowledgeContext = context.knowledgeContext || '';
    
    // If no knowledge context is provided, retrieve it using the enhanced department knowledge access
    if (!knowledgeContext && context.conversationHistory) {
      try {
        knowledgeContext = await retrieveDepartmentKnowledge(
          departmentId,
          query,
          context.conversationHistory,
          {
            forceRefresh: context.forceRefresh,
            modelId: context.modelId,
            sectionOverride: context.sectionOverride
          }
        );
      } catch (error) {
        console.error(`Error retrieving knowledge for ${departmentId}:`, error);
        knowledgeContext = '';
      }
    }
    
    // Generate response using the department's system prompt and knowledge context
    // ...
  };
};
```

### 5.2 Department Tools

The department tools also use the enhanced knowledge access:

```typescript
export async function queryDepartment(input: DepartmentQueryInput): Promise<string> {
  // ...
  
  // Use enhanced department knowledge access with conversation history
  const knowledgeContext = await retrieveDepartmentKnowledge(
    departmentId, 
    input.query, 
    input.history || [],
    {
      forceRefresh: isRecheckQuery,
      modelId: modelId
    }
  );
  
  // ...
}
```

## 6. Performance Considerations

### 6.1 Efficiency

The implementation is designed for efficiency:

1. Early termination when meaningful results are found
2. Prioritization of sections to minimize unnecessary searches
3. Caching of results for frequently accessed knowledge
4. Selective context enhancement to avoid query bloat

### 6.2 Error Handling

Comprehensive error handling ensures robustness:

1. Section-specific error handling
2. Fallback to general retrieval when section-specific retrieval fails
3. Detailed error logging for debugging
4. User-friendly error messages

## 7. Future Improvements

Potential future improvements include:

1. **Learning from User Interactions**: Adjusting section priorities based on user feedback
2. **Personalization**: Adapting priorities based on user preferences
3. **Dynamic Confidence Thresholds**: Adjusting thresholds based on query complexity
4. **Cross-Department Knowledge Sharing**: Enabling knowledge sharing between departments
