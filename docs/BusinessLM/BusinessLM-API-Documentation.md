# BusinessLM API Documentation

## Overview

The BusinessLM API provides developers with programmatic access to the BusinessLM platform, allowing for integration with other business tools and services. This document outlines the available endpoints, authentication methods, and data models.

## Base URL

```
https://api.businesslm.com/v1
```

## Authentication

All API requests require authentication using an API key. You can obtain your API key from the BusinessLM dashboard under Settings > API Access.

Include your API key in the Authorization header of all requests:

```
Authorization: Bearer YOUR_API_KEY
```

## Rate Limits

The API is rate-limited to protect the service from abuse. The current limits are:

- 100 requests per minute per API key
- 5,000 requests per day per API key

Rate limit headers are included in all API responses:

- `X-RateLimit-Limit`: The rate limit ceiling for your account
- `X-RateLimit-Remaining`: The number of requests remaining in the current rate limit window
- `X-RateLimit-Reset`: The time at which the current rate limit window resets (UTC epoch seconds)

## Endpoints

### Departments

#### List Departments

```
GET /departments
```

Returns a list of all available department types.

**Response**

```json
{
  "departments": [
    {
      "id": "finance",
      "name": "Finance Department Head",
      "description": "Financial planning and analysis",
      "expertise": [
        "Financial planning and analysis",
        "Budgeting and forecasting",
        "Investment strategy",
        "Cost optimization",
        "Financial risk management",
        "Cash flow management",
        "Financial reporting"
      ]
    },
    {
      "id": "marketing",
      "name": "Marketing Department Head",
      "description": "Marketing strategy and campaigns",
      "expertise": [
        "Brand strategy",
        "Digital marketing",
        "Content marketing",
        "Social media strategy",
        "Marketing analytics",
        "Customer acquisition",
        "Market research"
      ]
    },
    // Additional departments...
  ]
}
```

### Chat Sessions

#### Create Chat Session

```
POST /chat/sessions
```

Creates a new chat session with a department agent.

**Request Body**

```json
{
  "departmentId": "finance",
  "userId": "user123",
  "metadata": {
    "businessType": "SaaS",
    "employeeCount": 5,
    "industry": "Technology"
  }
}
```

**Response**

```json
{
  "sessionId": "sess_123456789",
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T12:00:00Z",
  "updatedAt": "2023-05-01T12:00:00Z"
}
```

#### List Chat Sessions

```
GET /chat/sessions
```

Returns a list of all chat sessions for the authenticated user.

**Query Parameters**

- `departmentId` (optional): Filter by department
- `limit` (optional): Number of results to return (default: 20, max: 100)
- `offset` (optional): Pagination offset (default: 0)

**Response**

```json
{
  "sessions": [
    {
      "sessionId": "sess_123456789",
      "departmentId": "finance",
      "userId": "user123",
      "createdAt": "2023-05-01T12:00:00Z",
      "updatedAt": "2023-05-01T12:00:00Z",
      "lastMessage": "We were discussing your cash flow forecast..."
    },
    // Additional sessions...
  ],
  "total": 5,
  "limit": 20,
  "offset": 0
}
```

#### Get Chat Session

```
GET /chat/sessions/{sessionId}
```

Returns details for a specific chat session.

**Response**

```json
{
  "sessionId": "sess_123456789",
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T12:00:00Z",
  "updatedAt": "2023-05-01T12:00:00Z",
  "messages": [
    {
      "id": "msg_123",
      "role": "user",
      "content": "Can you help me create a financial forecast?",
      "timestamp": "2023-05-01T12:01:00Z"
    },
    {
      "id": "msg_124",
      "role": "assistant",
      "content": "I'd be happy to help you create a financial forecast. Let's start by discussing your revenue streams...",
      "timestamp": "2023-05-01T12:01:30Z",
      "metadata": {
        "departmentId": "finance"
      }
    }
    // Additional messages...
  ]
}
```

#### Delete Chat Session

```
DELETE /chat/sessions/{sessionId}
```

Deletes a specific chat session.

**Response**

```json
{
  "deleted": true,
  "sessionId": "sess_123456789"
}
```

### Messages

#### Send Message

```
POST /chat/sessions/{sessionId}/messages
```

Sends a new message to a chat session.

**Request Body**

```json
{
  "content": "What financial metrics should I track for my SaaS business?",
  "role": "user"
}
```

**Response**

```json
{
  "id": "msg_125",
  "sessionId": "sess_123456789",
  "role": "user",
  "content": "What financial metrics should I track for my SaaS business?",
  "timestamp": "2023-05-01T12:05:00Z"
}
```

#### List Messages

```
GET /chat/sessions/{sessionId}/messages
```

Returns a list of all messages in a chat session.

**Query Parameters**

- `limit` (optional): Number of results to return (default: 50, max: 100)
- `before` (optional): Return messages before this timestamp
- `after` (optional): Return messages after this timestamp

**Response**

```json
{
  "messages": [
    {
      "id": "msg_123",
      "sessionId": "sess_123456789",
      "role": "user",
      "content": "Can you help me create a financial forecast?",
      "timestamp": "2023-05-01T12:01:00Z"
    },
    {
      "id": "msg_124",
      "role": "assistant",
      "content": "I'd be happy to help you create a financial forecast. Let's start by discussing your revenue streams...",
      "timestamp": "2023-05-01T12:01:30Z",
      "metadata": {
        "departmentId": "finance"
      }
    }
    // Additional messages...
  ],
  "hasMore": false
}
```

### Knowledge Base

#### Add Knowledge Document

```
POST /departments/{departmentId}/knowledge
```

Adds a new document to a department's knowledge base.

**Request Body**

```json
{
  "name": "2023 Financial Projections",
  "content": "Our financial projections for 2023 include...",
  "tags": ["financial", "projections", "2023"]
}
```

**Response**

```json
{
  "id": "doc_123456",
  "name": "2023 Financial Projections",
  "content": "Our financial projections for 2023 include...",
  "tags": ["financial", "projections", "2023"],
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T13:00:00Z",
  "updatedAt": "2023-05-01T13:00:00Z"
}
```

#### List Knowledge Documents

```
GET /departments/{departmentId}/knowledge
```

Returns a list of all knowledge documents for a department.

**Query Parameters**

- `limit` (optional): Number of results to return (default: 20, max: 100)
- `offset` (optional): Pagination offset (default: 0)
- `tag` (optional): Filter by tag

**Response**

```json
{
  "documents": [
    {
      "id": "doc_123456",
      "name": "2023 Financial Projections",
      "preview": "Our financial projections for 2023 include...",
      "tags": ["financial", "projections", "2023"],
      "departmentId": "finance",
      "userId": "user123",
      "createdAt": "2023-05-01T13:00:00Z",
      "updatedAt": "2023-05-01T13:00:00Z"
    },
    // Additional documents...
  ],
  "total": 5,
  "limit": 20,
  "offset": 0
}
```

#### Get Knowledge Document

```
GET /departments/{departmentId}/knowledge/{documentId}
```

Returns details for a specific knowledge document.

**Response**

```json
{
  "id": "doc_123456",
  "name": "2023 Financial Projections",
  "content": "Our financial projections for 2023 include...",
  "tags": ["financial", "projections", "2023"],
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T13:00:00Z",
  "updatedAt": "2023-05-01T13:00:00Z"
}
```

#### Update Knowledge Document

```
PUT /departments/{departmentId}/knowledge/{documentId}
```

Updates a specific knowledge document.

**Request Body**

```json
{
  "name": "2023 Financial Projections - Updated",
  "content": "Our updated financial projections for 2023 include...",
  "tags": ["financial", "projections", "2023", "updated"]
}
```

**Response**

```json
{
  "id": "doc_123456",
  "name": "2023 Financial Projections - Updated",
  "content": "Our updated financial projections for 2023 include...",
  "tags": ["financial", "projections", "2023", "updated"],
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T13:00:00Z",
  "updatedAt": "2023-05-01T14:30:00Z"
}
```

#### Delete Knowledge Document

```
DELETE /departments/{departmentId}/knowledge/{documentId}
```

Deletes a specific knowledge document.

**Response**

```json
{
  "deleted": true,
  "documentId": "doc_123456"
}
```

#### Search Knowledge Base

```
GET /departments/{departmentId}/knowledge/search
```

Searches the knowledge base for a department.

**Query Parameters**

- `query` (required): Search query
- `limit` (optional): Number of results to return (default: 10, max: 50)

**Response**

```json
{
  "results": [
    {
      "id": "doc_123456",
      "name": "2023 Financial Projections",
      "preview": "Our financial projections for 2023 include...",
      "relevanceScore": 0.92,
      "departmentId": "finance"
    },
    // Additional results...
  ]
}
```

### Department Goals

#### Create Goal

```
POST /departments/{departmentId}/goals
```

Creates a new goal for a department.

**Request Body**

```json
{
  "description": "Increase marketing ROI",
  "verb": "Increase",
  "object": "marketing ROI",
  "measurableUnit": "percentage",
  "value": 20,
  "timeline": "Q2 2023",
  "priority": "high",
  "type": "strategic"
}
```

**Response**

```json
{
  "id": "goal_123456",
  "description": "Increase marketing ROI",
  "verb": "Increase",
  "object": "marketing ROI",
  "measurableUnit": "percentage",
  "value": 20,
  "timeline": "Q2 2023",
  "priority": "high",
  "type": "strategic",
  "departmentId": "marketing",
  "userId": "user123",
  "createdAt": "2023-05-01T15:00:00Z",
  "updatedAt": "2023-05-01T15:00:00Z"
}
```

#### List Goals

```
GET /departments/{departmentId}/goals
```

Returns a list of all goals for a department.

**Response**

```json
{
  "goals": [
    {
      "id": "goal_123456",
      "description": "Increase marketing ROI",
      "verb": "Increase",
      "object": "marketing ROI",
      "measurableUnit": "percentage",
      "value": 20,
      "timeline": "Q2 2023",
      "priority": "high",
      "type": "strategic",
      "departmentId": "marketing",
      "userId": "user123",
      "createdAt": "2023-05-01T15:00:00Z",
      "updatedAt": "2023-05-01T15:00:00Z"
    },
    // Additional goals...
  ]
}
```

#### Update Goal

```
PUT /departments/{departmentId}/goals/{goalId}
```

Updates a specific goal.

**Request Body**

```json
{
  "value": 25,
  "priority": "critical"
}
```

**Response**

```json
{
  "id": "goal_123456",
  "description": "Increase marketing ROI",
  "verb": "Increase",
  "object": "marketing ROI",
  "measurableUnit": "percentage",
  "value": 25,
  "timeline": "Q2 2023",
  "priority": "critical",
  "type": "strategic",
  "departmentId": "marketing",
  "userId": "user123",
  "createdAt": "2023-05-01T15:00:00Z",
  "updatedAt": "2023-05-01T16:30:00Z"
}
```

#### Delete Goal

```
DELETE /departments/{departmentId}/goals/{goalId}
```

Deletes a specific goal.

**Response**

```json
{
  "deleted": true,
  "goalId": "goal_123456"
}
```

### Google Drive Integration

#### Connect Drive Folder

```
POST /departments/{departmentId}/integrations/gdrive
```

Connects a Google Drive folder to a department's knowledge base.

**Request Body**

```json
{
  "folderId": "1a2b3c4d5e6f7g8h9i",
  "folderName": "Financial Documents"
}
```

**Response**

```json
{
  "id": "drive_conn_123456",
  "folderId": "1a2b3c4d5e6f7g8h9i",
  "folderName": "Financial Documents",
  "departmentId": "finance",
  "userId": "user123",
  "createdAt": "2023-05-01T17:00:00Z",
  "status": "connected"
}
```

#### List Drive Connections

```
GET /departments/{departmentId}/integrations/gdrive
```

Returns a list of all Google Drive connections for a department.

**Response**

```json
{
  "connections": [
    {
      "id": "drive_conn_123456",
      "folderId": "1a2b3c4d5e6f7g8h9i",
      "folderName": "Financial Documents",
      "departmentId": "finance",
      "userId": "user123",
      "createdAt": "2023-05-01T17:00:00Z",
      "lastSynced": "2023-05-01T17:05:00Z",
      "status": "connected"
    },
    // Additional connections...
  ]
}
```

#### Sync Drive Folder

```
POST /departments/{departmentId}/integrations/gdrive/{connectionId}/sync
```

Initiates a synchronization of a connected Google Drive folder.

**Response**

```json
{
  "id": "drive_conn_123456",
  "status": "syncing",
  "lastSyncInitiated": "2023-05-01T18:00:00Z"
}
```

#### Delete Drive Connection

```
DELETE /departments/{departmentId}/integrations/gdrive/{connectionId}
```

Deletes a Google Drive connection.

**Response**

```json
{
  "deleted": true,
  "connectionId": "drive_conn_123456"
}
```

## Error Handling

The API uses conventional HTTP status codes to indicate the success or failure of an API request.

- 2xx: Success
- 4xx: Client Error
- 5xx: Server Error

Error responses include a consistent error object:

```json
{
  "error": {
    "code": "invalid_request",
    "message": "The request was invalid",
    "details": [
      "The 'departmentId' field is required"
    ]
  }
}
```

Common error codes:

- `authentication_error`: Invalid API key
- `authorization_error`: Insufficient permissions
- `invalid_request`: Malformed request
- `resource_not_found`: The requested resource doesn't exist
- `rate_limit_exceeded`: You've exceeded the API rate limit
- `internal_error`: An internal server error occurred

## Webhooks

BusinessLM supports webhooks for real-time notifications of events. You can configure webhooks in the BusinessLM dashboard under Settings > API Access > Webhooks.

### Webhook Events

- `chat.message.created`: A new message was created
- `knowledge.document.created`: A new knowledge document was created
- `knowledge.document.updated`: A knowledge document was updated
- `goal.created`: A new goal was created
- `goal.updated`: A goal was updated

### Webhook Payload

```json
{
  "event": "chat.message.created",
  "created": "2023-05-01T12:05:00Z",
  "data": {
    "id": "msg_125",
    "sessionId": "sess_123456789",
    "role": "assistant",
    "content": "Based on your SaaS business model, I recommend tracking these key financial metrics...",
    "timestamp": "2023-05-01T12:05:00Z"
  }
}
```

### Webhook Authentication

To ensure the webhook is coming from BusinessLM, a signature is included in the `X-BusinessLM-Signature` header. The signature is an HMAC SHA-256 hash of the request body, using your webhook secret as the key.

You can find your webhook secret in the BusinessLM dashboard under Settings > API Access > Webhooks.

## SDKs and Client Libraries

BusinessLM provides official SDKs for popular programming languages:

- [JavaScript/TypeScript](https://github.com/businesslm/businesslm-js)
- [Python](https://github.com/businesslm/businesslm-python)
- [Ruby](https://github.com/businesslm/businesslm-ruby)
- [PHP](https://github.com/businesslm/businesslm-php)
- [Java](https://github.com/businesslm/businesslm-java)
- [Go](https://github.com/businesslm/businesslm-go)

## Support

If you have any questions or need assistance with the API, please contact our developer support <NAME_EMAIL> or visit our [Developer Forum](https://community.businesslm.com/developers). 