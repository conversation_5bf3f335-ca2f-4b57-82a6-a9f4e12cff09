# Adding Department Actions Guide

This guide provides step-by-step instructions for adding new actions to any department in BusinessLM, including the Co-CEO department. These actions allow users to perform specific department-related tasks through the chat interface.

## Overview

Actions in BusinessLM are implemented as buttons in the chat interface that, when clicked:
1. Send a message to the chat (e.g., "Start Action: Create Lead")
2. Set up the UI to expect a follow-up message with details
3. Process the follow-up message using a specialized LLM prompt with function calling
4. Execute the action through an n8n webhook
5. Return a confirmation response to the user

## Prerequisites

Before adding a new action, make sure you have:
- Access to the BusinessLM codebase
- An n8n webhook URL for the action
- Knowledge of the data structure needed for the action

## Implementation Steps

### 1. Update the ActionId Type

**File:** `/src/genkit/tools/agentActions.ts`

Add your new action ID to the ActionId type:

```typescript
export type ActionId = 'record-revenue' | 'record-expense' | 'your-new-action-id';
```

Example for a "create-lead" action:
```typescript
export type ActionId = 'record-revenue' | 'record-expense' | 'create-lead';
```

### 2. Add the Action to the Department Actions Configuration

**File:** `/src/frontend/playground/components/ChatInput/ActionsButton.tsx`

Add your action to the appropriate department in the `departmentActions` object:

```typescript
const departmentActions = {
  finance: [
    // Existing finance actions...
  ],
  sales: [
    {
      id: 'your-new-action-id',
      label: 'Your Action Label',
      description: 'Brief description of the action'
    }
  ],
  // Other departments...
};
```

Example for a "create-lead" action in the sales department:
```typescript
const departmentActions = {
  finance: [
    // Existing finance actions...
  ],
  sales: [
    {
      id: 'create-lead',
      label: 'Create Lead',
      description: 'Add a new sales lead to the CRM'
    }
  ],
  // Other departments...
};
```

### 3. Create a Function Declaration Schema

**File:** `/src/genkit/tools/agentActions.ts`

Define the schema for your new action. This will be used by the LLM's function calling capability:

```typescript
export const yourActionFunctionDeclaration = {
  name: 'your_action_function_name',
  description: 'Detailed description of what this action does',
  parameters: {
    type: SchemaType.OBJECT,
    properties: {
      action: {
        type: SchemaType.STRING,
        enum: ['your-new-action-id'],
        description: 'The action to perform'
      },
      // Add all parameters needed for your action
      parameterName1: {
        type: SchemaType.STRING,
        description: 'Description of this parameter'
      },
      parameterName2: {
        type: SchemaType.STRING,
        enum: ['option1', 'option2', 'option3'],
        description: 'Description of this parameter with enum values'
      },
      // ... other parameters
    },
    required: ['action', 'parameterName1', 'parameterName2']
  }
};
```

Example for a "create-lead" action:
```typescript
export const createLeadFunctionDeclaration = {
  name: 'create_lead',
  description: 'Creates a new sales lead with the provided details',
  parameters: {
    type: SchemaType.OBJECT,
    properties: {
      action: {
        type: SchemaType.STRING,
        enum: ['create-lead'],
        description: 'The action to perform'
      },
      'Lead Name': {
        type: SchemaType.STRING,
        description: 'Full name of the lead contact person'
      },
      'Company': {
        type: SchemaType.STRING,
        description: 'Name of the company or organization'
      },
      'Email': {
        type: SchemaType.STRING,
        description: 'Email address of the lead'
      },
      'Phone': {
        type: SchemaType.STRING,
        description: 'Phone number of the lead'
      },
      'Lead Source': {
        type: SchemaType.STRING,
        enum: [
          'Website',
          'Referral',
          'Conference',
          'Social Media',
          'Direct Contact',
          'Other'
        ],
        description: 'The source of the lead'
      },
      'Lead Status': {
        type: SchemaType.STRING,
        enum: [
          'New',
          'Contacted',
          'Qualified',
          'Proposal',
          'Negotiation'
        ],
        description: 'Current status of the lead'
      },
      'Estimated Value': {
        type: SchemaType.STRING,
        description: 'Estimated value of the potential deal (e.g., "5000000")'
      },
      'Notes': {
        type: SchemaType.STRING,
        description: 'Additional notes about the lead'
      }
    },
    required: ['action', 'Lead Name', 'Company', 'Email', 'Lead Source', 'Lead Status']
  }
};
```

### 4. Update the getDepartmentFunctionDeclarations Function

**File:** `/src/genkit/tools/agentActions.ts`

Update this function to include your new action's function declaration for the appropriate department:

```typescript
export const getDepartmentFunctionDeclarations = (departmentId?: string): any[] => {
  // Add your department and action here
  if (departmentId === 'finance') {
    return [recordRevenueFunctionDeclaration, recordExpenseFunctionDeclaration];
  } else if (departmentId === 'your-department') {
    return [yourActionFunctionDeclaration];
  }
  
  return [];
};
```

Example for the sales department:
```typescript
export const getDepartmentFunctionDeclarations = (departmentId?: string): any[] => {
  if (departmentId === 'finance') {
    return [recordRevenueFunctionDeclaration, recordExpenseFunctionDeclaration];
  } else if (departmentId === 'sales') {
    return [createLeadFunctionDeclaration];
  }
  
  return [];
};
```

### 5. Add a System Prompt for Your Action

**File:** `/src/genkit/tools/agentActions.ts`

Update the `getAgentActionSystemPrompt` function to add a prompt for your new action:

```typescript
export const getAgentActionSystemPrompt = (actionId: string): string => {
  switch (actionId) {
    // Existing cases...
    
    case 'your-new-action-id':
      return `Hello! Let's [do your action]. Please provide the following information:

1. [Parameter 1]: (description/format)
2. [Parameter 2]: (description/format)
3. [Parameter 3]: (description/format)
...

I'll guide you through the process step-by-step.`;

    default:
      return '';
  }
};
```

Example for a "create-lead" action:
```typescript
export const getAgentActionSystemPrompt = (actionId: string): string => {
  switch (actionId) {
    // Existing cases...
    
    case 'create-lead':
      return `Hello! Let's create a new sales lead. Please provide the following information:

1. Lead Name: (full name of the contact person)
2. Company: (company or organization name)
3. Email: (contact email address)
4. Phone: (contact phone number)
5. Lead Source: (Website, Referral, Conference, Social Media, Direct Contact, or Other)
6. Lead Status: (New, Contacted, Qualified, Proposal, or Negotiation)
7. Estimated Value: (potential deal value)
8. Notes: (any additional information about this lead)

I'll guide you through the process step-by-step.`;

    default:
      return '';
  }
};
```

### 6. Implement the Action Function

**File:** `/src/genkit/tools/agentActions.ts`

Implement a function to handle your action, including the n8n webhook call:

```typescript
async function yourActionFunction(args: Record<string, any>): Promise<any> {
  logger.info(`[AGENT ACTION] Executing your action with args:`, JSON.stringify(args, null, 2));
  
  // Your n8n webhook URL
  const webhookUrl = 'your-n8n-webhook-url';
  
  try {
    logger.info(`[AGENT ACTION] Sending data to webhook`);
    
    // Make the HTTP request to the webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(args),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`[AGENT ACTION] Webhook request failed: ${response.status} - ${errorText}`);
      throw new Error(`Action failed: ${errorText}`);
    }
    
    const responseData = await response.json();
    logger.info(`[AGENT ACTION] Webhook response:`, JSON.stringify(responseData, null, 2));
    
    // Return a formatted response for the agent to use
    return {
      success: true,
      message: `Your action completed successfully: [custom success message with relevant details from args]`,
      details: responseData
    };
  } catch (error) {
    logger.error(`[AGENT ACTION] Error executing action:`, error);
    
    // Return an error response
    return {
      success: false,
      message: `Failed to execute action: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error: error
    };
  }
}
```

Example for a "create-lead" action:
```typescript
async function createLead(args: Record<string, any>): Promise<any> {
  logger.info(`[AGENT ACTION] Creating lead with args:`, JSON.stringify(args, null, 2));
  
  // Webhook URL for lead creation
  const webhookUrl = 'https://magnitudeminds.co/webhook/sales-lead-creation';
  
  try {
    logger.info(`[AGENT ACTION] Sending lead data to webhook`);
    
    // Make the HTTP request to the webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(args),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`[AGENT ACTION] Webhook request failed: ${response.status} - ${errorText}`);
      throw new Error(`Lead creation failed: ${errorText}`);
    }
    
    const responseData = await response.json();
    logger.info(`[AGENT ACTION] Webhook response:`, JSON.stringify(responseData, null, 2));
    
    // Return a formatted response for the agent to use
    return {
      success: true,
      message: `New lead "${args['Lead Name']}" from ${args['Company']} has been successfully created with status "${args['Lead Status']}".`,
      details: responseData
    };
  } catch (error) {
    logger.error(`[AGENT ACTION] Error creating lead:`, error);
    
    // Return an error response
    return {
      success: false,
      message: `Failed to create lead: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error: error
    };
  }
}
```

### 7. Update the executeAgentAction Switch Statement

**File:** `/src/genkit/tools/agentActions.ts`

Add your new action function to the switch statement in the `executeAgentAction` function:

```typescript
export async function executeAgentAction(functionCall: FunctionCall): Promise<any> {
  try {
    // Existing code...
    
    switch (functionCall.name) {
      // Existing cases...
      case 'your_action_function_name':
        return await yourActionFunction(sanitizedArgs);
      default:
        throw new Error(`Unknown function "${functionCall.name}"`);
    }
  } catch (error) {
    // Existing error handling...
  }
}
```

Example for a "create-lead" action:
```typescript
export async function executeAgentAction(functionCall: FunctionCall): Promise<any> {
  try {
    // Existing code...
    
    switch (functionCall.name) {
      case 'record_expense':
        return await recordExpense(sanitizedArgs);
      case 'record_revenue':
        return await recordRevenue(sanitizedArgs);
      case 'create_lead':
        return await createLead(sanitizedArgs);
      default:
        throw new Error(`Unknown function "${functionCall.name}"`);
    }
  } catch (error) {
    // Existing error handling...
  }
}
```

### 8. Update the Argument Sanitization Function (If Needed)

**File:** `/src/genkit/tools/agentActions.ts`

If your action requires specific argument sanitization, update the `sanitizeArgs` function:

```typescript
function sanitizeArgs(functionName: string, args: any): any {
  // Existing code...
  
  // Add function-specific sanitization
  if (functionName === 'your_action_function_name') {
    // Ensure required fields are present
    const requiredFields = ['Field1', 'Field2', 'Field3'];
    const missingFields = requiredFields.filter(field => !sanitized[field]);
    
    if (missingFields.length > 0) {
      logger.warn(`⚠️ [AGENT ACTION] Missing required fields for ${functionName}: ${missingFields.join(', ')}`);
    }
    
    // Add any field-specific sanitization
  }
  
  // Existing code...
}
```

Example for a "create-lead" action:
```typescript
function sanitizeArgs(functionName: string, args: any): any {
  // Existing code...
  
  if (functionName === 'create_lead') {
    // Ensure required fields are present
    const requiredFields = ['Lead Name', 'Company', 'Email', 'Lead Source', 'Lead Status'];
    const missingFields = requiredFields.filter(field => !sanitized[field]);
    
    if (missingFields.length > 0) {
      logger.warn(`⚠️ [AGENT ACTION] Missing required fields for create_lead: ${missingFields.join(', ')}`);
    }
    
    // Sanitize email format if needed
    if (sanitized['Email'] && !sanitized['Email'].includes('@')) {
      logger.warn(`⚠️ [AGENT ACTION] Invalid email format: ${sanitized['Email']}`);
    }
    
    // Sanitize estimated value to ensure it's numeric
    if (sanitized['Estimated Value']) {
      sanitized['Estimated Value'] = sanitized['Estimated Value'].toString()
        .replace(/[$€£¥,]/g, '')  // Remove currency symbols and commas
        .trim();
    }
  }
  
  // Existing code...
}
```

## Testing Your New Action

1. **Compile and Start the Application:**
   ```bash
   npm run build
   npm run dev
   ```

2. **Navigate to the Appropriate Department:**
   - Select the department where you added the action in the department panel

3. **Check for the Action Button:**
   - Verify that the action button appears in the chat input area
   - Click on the button to see your action in the dropdown menu

4. **Test the Action Flow:**
   - Click on your action
   - Verify that the "Start Action: [Your Action]" message appears in the chat
   - Enter the required information in the chat input
   - Verify that the action is processed correctly and a confirmation message is displayed

## Troubleshooting

### Common Issues and Solutions

1. **Action button doesn't appear:**
   - Check that you correctly added the action to the `departmentActions` object for the right department
   - Verify that you're viewing the correct department in the UI

2. **Action is not being processed correctly:**
   - Check the console logs for any errors in the function declarations or webhook calls
   - Verify that the webhook URL is correct and accessible

3. **LLM not properly understanding the action:**
   - Review the system prompt for clarity and completeness
   - Ensure the function declaration parameters are clearly described

4. **Webhook errors:**
   - Verify that the n8n workflow is properly configured and active
   - Check that the data format being sent matches what the workflow expects

## Adding Actions to the Co-CEO Department

The process for adding actions to the Co-CEO department is the same as for other departments. Simply use `'co-ceo'` as the departmentId:

```typescript
const departmentActions = {
  // Other departments...
  'co-ceo': [
    {
      id: 'your-co-ceo-action-id',
      label: 'Your Co-CEO Action Label',
      description: 'Brief description of the Co-CEO action'
    }
  ]
};
```

And in the `getDepartmentFunctionDeclarations` function:
```typescript
export const getDepartmentFunctionDeclarations = (departmentId?: string): any[] => {
  if (departmentId === 'finance') {
    return [recordRevenueFunctionDeclaration, recordExpenseFunctionDeclaration];
  } else if (departmentId === 'sales') {
    return [createLeadFunctionDeclaration];
  } else if (departmentId === 'co-ceo') {
    return [yourCoCEOActionFunctionDeclaration];
  }
  
  return [];
};
```

## Best Practices

1. **Use clear, consistent naming conventions** for action IDs, function names, and parameters

2. **Provide comprehensive documentation** in function descriptions and parameter descriptions

3. **Implement robust error handling** in webhook calls and response processing

4. **Add detailed logging** throughout the action flow for easier debugging

5. **Follow UX best practices** for action labels and descriptions to ensure user clarity

6. **Test thoroughly** with various input combinations before deploying to production

## Conclusion

Adding new actions to departments in BusinessLM allows for expanding the system's capabilities and providing more specialized functionality to users. By following this guide, you should be able to implement new actions for any department, including the Co-CEO department, using n8n webhooks for backend processing. 