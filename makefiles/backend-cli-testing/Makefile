# ==============================================================================
# 🧪 BusinessLM PoC – CLI Testing Makefile
# ==============================================================================
# Defines automation commands for RAG + multi-agent testing via the CLI tool.
#
# Basic Commands:
#   make init               - Create mock document folder
#   make create-doc         - Create example mock document
#   make create-example-docs - Create a set of example documents
#   make list-docs          - List mock documents
#   make view-doc ID=       - View a document by ID
#   make delete-doc ID=     - Delete a document by ID
#
# Simple Testing Commands:
#   make test-rag           - Run a RAG query test with custom query
#   make test-agents        - Run a multi-agent orchestration test with custom query
#   make interactive        - Start interactive session
#
# Trace Management Commands:
#   make list-traces        - List available trace files
#   make view-trace         - View a trace file with visualization
#   make delete-trace       - Delete a trace file
#   make cleanup-traces     - Clean up old trace files
#   make compare-traces     - Compare two trace files
#
# Comprehensive Testing Commands:
#   make setup-test-env     - Set up test environment with example documents
#   make comprehensive-test - Run comprehensive test suite
#   make conversation-test  - Test conversation context maintenance
#   make retrieval-test     - Test document retrieval quality
#
# ==============================================================================

# This tells make that these targets are not real files,
# so make should always run them when called.
.PHONY: help init create-doc create-example-docs list-docs view-doc delete-doc load-docs-to-db test-rag test-agents interactive setup-test-env comprehensive-test conversation-test retrieval-test run-benchmarks run-benchmarks-category list-traces view-trace delete-trace compare-traces cleanup-traces run-jupyter migrate-docs-to-postgres verify-pgvector list-postgres-docs list-postgres-chunks clean-postgres-db

# Path to mock documents file
MOCK_DOCUMENT_PATH=./backend/data/mock_documents/mock_documents.json
export MOCK_DOCUMENT_PATH

# Use mock documents
USE_MOCK_DOCUMENTS=true
export USE_MOCK_DOCUMENTS

# Define a space variable for substitution
empty :=
space := $(empty) $(empty)

# Verbose mode toggle (default: true)
VERBOSE ?= true
ifeq ($(VERBOSE),true)
	VERBOSE_FLAG = --verbose
else
	VERBOSE_FLAG =
endif

# LLM provider selection (default: mock)
PROVIDER ?= mock
PROVIDER_FLAG = --provider $(PROVIDER)

# LLM model selection (optional)
ifdef MODEL
	MODEL_FLAG = --model $(MODEL)
else
	MODEL_FLAG =
endif

# Check if required parameters are provided
check_id:
ifndef ID
	$(error ID parameter is required. Usage: make [target] ID=<document-id>)
endif

################################################################################
# 🧠 TEST-RAG: Run a RAG query with custom input
#
# Command: make test-rag QUERY="your query here" [DEPARTMENT=dept]
#
# What it does:
#   - Sends a custom query through the RAG pipeline.
#   - If QUERY is not provided, prompts for input.
#   - If DEPARTMENT is provided, filters documents by department.
#   - Always uses PostgreSQL for document retrieval.
#
# Why it's useful:
#   - Allows testing with different query types and edge cases.
#   - Helps debug specific RAG issues with custom queries.
#   - With DEPARTMENT parameter, simulates department-specific knowledge retrieval.

# Always use PostgreSQL by default
test-rag:
ifdef QUERY
	@echo "Testing RAG with query: \"$(QUERY)\""
	@echo "Using PostgreSQL for document retrieval"
	@cd ../.. && python backend/scripts/test_backend_cli.py test-rag "$(QUERY)" $(PROVIDER_FLAG) $(MODEL_FLAG) $(VERBOSE_FLAG) $(if $(DEPARTMENT),--department $(DEPARTMENT),) --use-postgres
else
	@echo "Enter your RAG query (type and press Enter):"
	@echo "Using PostgreSQL for document retrieval"
	@bash -c 'read -r query; cd ../.. && python backend/scripts/test_backend_cli.py test-rag "$$query" $(PROVIDER_FLAG) $(MODEL_FLAG) $(VERBOSE_FLAG) $(if $(DEPARTMENT),--department $(DEPARTMENT),) --use-postgres'
endif

################################################################################
# 🤖 TEST-AGENTS: Run a multi-agent orchestration query with custom input
#
# Command: make test-agents QUERY="your query here" [DEPARTMENT=finance] [SHOW_FULL=true|false] [SHOW_NODES=true|false]
#
# What it does:
#   - Sends a custom query through the multi-agent orchestration.
#   - If QUERY is not provided, prompts for input.
#   - If DEPARTMENT is provided, starts with that specific department.
#   - SHOW_FULL controls whether to show full content in agent communications (default: true).
#   - SHOW_NODES controls whether to show node execution events in agent communications (default: true).
#   - Always uses PostgreSQL for document retrieval.
#
# Why it's useful:
#   - Allows testing with different query types and edge cases.
#   - Helps debug specific agent routing and collaboration issues.
#   - With DEPARTMENT parameter, simulates a real-life scenario where a user
#     interacts with a specific department agent, which then decides whether to
#     handle the query itself or engage in multi-agent orchestration.
#   - Includes tracing and visualization of agent communications.
#   - Provides detailed visualization of agent communications and graph execution.

# Default values for visualization options
SHOW_FULL ?= true
SHOW_NODES ?= true

# Convert boolean values to CLI flags
ifeq ($(SHOW_FULL),true)
	SHOW_FULL_FLAG = --show-full-content
else
	SHOW_FULL_FLAG =
endif

ifeq ($(SHOW_NODES),true)
	SHOW_NODES_FLAG = --show-node-events
else
	SHOW_NODES_FLAG = --no-show-node-events
endif

test-agents:
ifdef QUERY
	@echo "Testing multi-agent orchestration with query: \"$(QUERY)\""
	@echo "Show full content: $(SHOW_FULL), Show node events: $(SHOW_NODES)"
	@echo "Using PostgreSQL for document retrieval"
	@cd ../.. && python backend/scripts/test_backend_cli.py test-multi-agent "$(QUERY)" $(PROVIDER_FLAG) $(MODEL_FLAG) $(VERBOSE_FLAG) $(if $(DEPARTMENT),--department $(DEPARTMENT),) $(SHOW_FULL_FLAG) $(SHOW_NODES_FLAG) --use-postgres
else
	@echo "Enter your multi-agent query (type and press Enter):"
	@echo "Using PostgreSQL for document retrieval"
	@bash -c 'read -r query; cd ../.. && python backend/scripts/test_backend_cli.py test-multi-agent "$$query" $(PROVIDER_FLAG) $(MODEL_FLAG) $(VERBOSE_FLAG) $(if $(DEPARTMENT),--department $(DEPARTMENT),) $(SHOW_FULL_FLAG) $(SHOW_NODES_FLAG) --use-postgres'
endif

################################################################################
# 🧹 CLEAN-POSTGRES-DB: Clean PostgreSQL database
#
# Command: make clean-postgres-db [CONFIRM=true]
#
# What it does:
#   - Removes all documents and document chunks from the PostgreSQL database.
#   - Requires confirmation with CONFIRM=true to prevent accidental deletion.
#
# Why it's useful:
#   - Provides a clean slate for testing with fresh documents.
#   - Helps resolve issues with duplicate or corrupted documents.

clean-postgres-db:
ifeq ($(CONFIRM),true)
	@echo "Cleaning PostgreSQL database..."
	@cd ../.. && python backend/scripts/clean_postgres_db.py --confirm
else
	@echo "⚠️ This will delete ALL documents and document chunks from the database."
	@echo "Run with CONFIRM=true to proceed: make clean-postgres-db CONFIRM=true"
endif

################################################################################
# 📋 LIST-POSTGRES-DOCS: List documents in PostgreSQL database
#
# Command: make list-postgres-docs [LIMIT=10]
#
# What it does:
#   - Lists documents stored in the PostgreSQL database
#   - Shows document ID, title, department, and creation date
#
# Why it's useful:
#   - Provides a quick overview of documents in the database
#   - Helps verify document migration was successful

# Default limit for listing documents
LIMIT ?= 10

list-postgres-docs:
	@echo "Listing documents in PostgreSQL database..."
	@cd ../.. && python backend/scripts/test_backend_cli.py list-postgres-docs --limit $(LIMIT)

################################################################################
# 📋 LIST-POSTGRES-CHUNKS: List document chunks in PostgreSQL database
#
# Command: make list-postgres-chunks [LIMIT=10] [DOCUMENT_ID=id]
#
# What it does:
#   - Lists document chunks stored in the PostgreSQL database
#   - Shows chunk ID, document ID, chunk index, document title, and content length
#   - Can filter by document ID
#
# Why it's useful:
#   - Provides a quick overview of document chunks in the database
#   - Helps verify document chunking and embedding was successful

list-postgres-chunks:
	@echo "Listing document chunks in PostgreSQL database..."
	@cd ../.. && python backend/scripts/test_backend_cli.py list-postgres-chunks \
		--limit $(LIMIT) \
		$(if $(DOCUMENT_ID),--document-id $(DOCUMENT_ID),)

################################################################################
# 🔄 MIGRATE-DOCS-TO-POSTGRES: Migrate mock documents to PostgreSQL
#
# Command: make migrate-docs-to-postgres [CHUNK_SIZE=1000] [OVERLAP=200] [CLEAR=true]
#
# What it does:
#   - Loads documents from the JSON file
#   - Processes them into document chunks
#   - Generates embeddings for each chunk
#   - Stores documents and chunks in PostgreSQL with proper metadata
#
# Why it's useful:
#   - Ensures documents are properly stored in PostgreSQL for RAG
#   - Creates proper vector embeddings for similarity search
#   - Sets up the database for multi-agent orchestration

# Default values for migration options
CHUNK_SIZE ?= 1000
OVERLAP ?= 200
CLEAR ?= false

# Convert boolean values to CLI flags
ifeq ($(CLEAR),true)
	CLEAR_FLAG = --clear
else
	CLEAR_FLAG =
endif

migrate-docs-to-postgres:
	@echo "Migrating documents to PostgreSQL..."
	@cd ../.. && python backend/scripts/migrate_documents_to_postgres.py \
		--file-path "$(MOCK_DOCUMENT_PATH)" \
		--chunk-size $(CHUNK_SIZE) \
		--overlap $(OVERLAP) \
		$(CLEAR_FLAG)

################################################################################
# 🔍 VERIFY-PGVECTOR: Verify pgvector extension is installed and working
#
# Command: make verify-pgvector
#
# What it does:
#   - Checks if pgvector extension is installed
#   - Tests vector operations
#   - Verifies document_chunks table has vector column
#   - Checks if there's an index on the embedding column
#
# Why it's useful:
#   - Ensures the database is properly set up for vector search
#   - Helps diagnose issues with pgvector integration

verify-pgvector:
	@echo "Verifying pgvector extension..."
	@cd ../.. && python backend/scripts/verify_pgvector.py
