# ==============================================================================
# 🛠️ BusinessLM PoC – Project Makefile
# ==============================================================================
# Defines automation commands for development, testing, linting, and setup.
#
# Targets:
#   make dev       - Start backend dev server with FastAPI
#   make test      - Run all Python tests using pytest
#   make lint      - Format and lint backend codebase
#   make clean     - Remove build/test artifacts and caches
#   make install   - Install dependencies from requirements.txt
#   make compile   - Generate pinned requirements.txt from requirements.in
#
# ==============================================================================

# This tells make that these targets (help, dev, test...) are not actual files,
# so make should always run them when called.
.PHONY: help dev test lint clean install

################################################################################
# 🆘 HELP
#
# Command: make help
#
# What it does:
#   - Prints a list of all available `make` commands with short descriptions.
#
# Why it’s useful:
#   - Helps you remember what each command does, especially when you're new to the project.

help:
	@echo "Available commands:"
	@echo "  make dev       - Start the development server"
	@echo "  make test      - Run tests"
	@echo "  make lint      - Run linting checks"
	@echo "  make clean     - Clean up cache files and artifacts"
	@echo "  make install   - Install dependencies"

################################################################################
# 🚀 DEV: Start the development server
#
# Command: make dev
#
# What it does:
#   - Changes into the `backend/` directory.
#   - Starts the FastAPI development server using `uvicorn`.
#   - Uses `--reload` mode so the server restarts automatically on code changes.
#   - Serves the app defined in `app.main:app` on localhost port 8000.
#
# Why it’s useful:
#   - Lets you instantly start the backend and see your API changes live as you code.

dev:
	@echo "Starting development server..."
	cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

################################################################################
# 🧪 TEST: Run tests
#
# Command: make test
#
# What it does:
#   - Changes into the `backend/` directory.
#   - Runs all Python test files located in the `tests/` folder using `pytest`.
#   - Uses `-v` for verbose output (shows more detailed test results).
#
# Why it’s useful:
#   - Allows you to check if your code is working correctly.
#   - Super important for catching bugs as your project grows.

test:
	@echo "Running tests..."
	cd backend && python -m pytest tests/ -v

################################################################################
# 🧹 LINT: Run linting checks
#
# Command: make lint
#
# What it does:
#   - Runs 3 code quality tools on the `backend/` codebase:
#     1. `black`  → auto-formats your Python code to follow best practices.
#     2. `isort`  → automatically organizes your import statements.
#     3. `flake8` → analyzes your code for style issues, bugs, and inconsistencies.
#
# Why it’s useful:
#   - Keeps your code clean, consistent, and professional.
#   - Helps you (and your team) avoid common coding errors.

lint:
	@echo "Running linting checks..."
	cd backend && python -m black .
	cd backend && python -m isort .
	cd backend && python -m flake8 .

################################################################################
# 🧼 CLEAN: Clean up cache files and artifacts
#
# Command: make clean
#
# What it does:
#   - Deletes common cache files and artifacts that Python creates:
#     - `__pycache__/`, `.pytest_cache/`, `.coverage`
#     - Temporary files like `.pyc`, `.pyo`, `.pyd`
#
# Why it’s useful:
#   - Prevents weird bugs or outdated files from affecting your runs.
#   - Keeps your project directory tidy and clean.

clean:
	@echo "Cleaning up..."
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type d -name .pytest_cache -exec rm -rf {} +
	find . -type d -name .coverage -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete

################################################################################
# 🧾 COMPILE: Generate `requirements.txt` from `requirements.in`
#
# Command: make compile
#
# What it does:
#   - Uses `pip-compile` to generate a fully-pinned `requirements.txt`
#     from your editable `requirements.in` file.
#
# Why it’s useful:
#   - Ensures consistent environments for everyone.
#   - Keeps your `requirements.txt` up-to-date with changes in `requirements.in`.

compile:
	@echo "Compiling requirements.txt from requirements.in..."
	uv pip compile backend/requirements.in --output-file backend/requirements.txt

################################################################################
# 📦 INSTALL: Install dependencies
#
# Command: make install
#
# What it does:
#   - Installs all the Python dependencies listed in `backend/requirements.txt`
#     using pip.
#
# Why it’s useful:
#   - Sets up your development environment with all the necessary libraries.
#   - One-liner to get your project ready to run on a new machine or after cloning.

install:
	@echo "Installing dependencies..."
	uv pip install -r backend/requirements.txt