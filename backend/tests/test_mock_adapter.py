"""
Unit tests for the mock adapter.
"""
import pytest
from unittest.mock import patch, MagicMock

from app.core.llm.mock_adapter import <PERSON>ck<PERSON>dapter


@pytest.fixture
def mock_adapter():
    """Create a mock adapter for testing."""
    return MockAdapter()


@pytest.mark.parametrize(
    "query,expected_pattern",
    [
        # Test with financial query
        (
            "What is our financial performance?",
            "financial performance"
        ),
        # Test with marketing query
        (
            "Tell me about our marketing strategy.",
            "marketing strategy"
        ),
        # Test with both finance and marketing
        (
            "What is our marketing strategy and financial performance?",
            "I've consulted with multiple departments"
        ),
    ]
)
@pytest.mark.asyncio
async def test_chat_query_specific_responses(mock_adapter, query, expected_pattern):
    """Test that the chat method returns query-specific responses."""
    # Create messages with the test query
    messages = [
        {"role": "user", "content": query}
    ]

    # Call the chat method
    response = await mock_adapter.chat(messages)

    # Check that the response contains the expected pattern
    assert expected_pattern.lower() in response.lower()


@pytest.mark.parametrize(
    "query,department,expected_text",
    [
        # Test with finance department
        (
            "What is our financial performance?",
            "finance",
            "From the Finance department"
        ),
        # Test with marketing department
        (
            "Tell me about our marketing strategy.",
            "marketing",
            "From the Marketing department"
        ),
        # Test with co_ceo department
        (
            "What is our strategic vision?",
            "co_ceo",
            "From the Co-CEO"
        ),
    ]
)
@pytest.mark.asyncio
async def test_chat_department_specific_responses(mock_adapter, query, department, expected_text):
    """Test that the chat method returns department-specific responses."""
    # Create messages with the test query
    messages = [
        {"role": "user", "content": query}
    ]

    # Call the chat method with the department parameter
    response = await mock_adapter.chat(messages, department=department)

    # Check that the response contains the expected text
    assert expected_text in response


@pytest.mark.parametrize(
    "query,expected_departments",
    [
        # Test with financial query
        (
            "analyze the following query: What is our financial performance?",
            ["finance"]
        ),
        # Test with marketing query
        (
            "analyze the following query: Tell me about our marketing strategy.",
            ["marketing"]
        ),
        # Test with both finance and marketing
        (
            "analyze the following query: What is our marketing strategy and financial performance?",
            ["finance", "marketing"]
        ),
    ]
)
@pytest.mark.asyncio
async def test_chat_analysis_responses(mock_adapter, query, expected_departments):
    """Test that the chat method returns correct analysis responses."""
    # Create messages with the test query
    messages = [
        {"role": "user", "content": query}
    ]

    # Call the chat method
    response = await mock_adapter.chat(messages)

    # Check that the response contains the expected departments
    for dept in expected_departments:
        assert f'"{dept}"' in response.lower() or f"'{dept}'" in response.lower()


@pytest.mark.asyncio
async def test_chat_streaming(mock_adapter):
    """Test that the chat method supports streaming."""
    # Create messages
    messages = [
        {"role": "user", "content": "Hello"}
    ]

    # Call the chat method with streaming
    stream = await mock_adapter.chat(messages, stream=True)

    # Check that the stream is an async iterator
    assert hasattr(stream, "__aiter__")

    # Collect all chunks
    chunks = []
    async for chunk in stream:
        chunks.append(chunk)

    # Check that we got some chunks
    assert len(chunks) > 0

    # Check that the combined chunks form a valid response
    combined = "".join(chunks)
    assert len(combined) > 0
