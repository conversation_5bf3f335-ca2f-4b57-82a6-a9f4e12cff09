"""
Tests for the base error handling module.

This module tests the core error handling utilities in app.core.errors.base.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch
import asyncio

from app.core.errors.base import (
    ErrorSeverity,
    ErrorCategory,
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ExternalServiceError,
    DatabaseError,
    TimeoutError,
    RateLimitError,
    handle_errors,
    handle_async_errors,
    convert_exception_to_app_error,
    format_error_for_response,
    log_error,
    create_error_response,
    ErrorContext
)


class TestErrorClasses:
    """Tests for the error classes."""

    def test_app_error_init(self):
        """Test AppError initialization."""
        error = AppError(
            message="Test error",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            details={"field": "test"},
            status_code=400
        )

        assert error.message == "Test error"
        assert error.category == ErrorCategory.VALIDATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.details == {"field": "test"}
        assert error.status_code == 400

    def test_app_error_to_dict(self):
        """Test AppError to_dict method."""
        error = AppError(
            message="Test error",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            details={"field": "test"},
            status_code=400
        )

        error_dict = error.to_dict()
        assert error_dict["message"] == "Test error"
        assert error_dict["category"] == "validation"
        assert error_dict["severity"] == "warning"
        assert error_dict["details"] == {"field": "test"}
        assert error_dict["status_code"] == 400
        assert "timestamp" in error_dict

    def test_validation_error(self):
        """Test ValidationError initialization."""
        error = ValidationError(
            message="Invalid input",
            field="username",
            value="test"
        )

        assert error.message == "Invalid input"
        assert error.category == ErrorCategory.VALIDATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["field"] == "username"
        assert error.details["value"] == "test"
        assert error.status_code == 400

    def test_authentication_error(self):
        """Test AuthenticationError initialization."""
        error = AuthenticationError(message="Authentication failed")

        assert error.message == "Authentication failed"
        assert error.category == ErrorCategory.AUTHENTICATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.status_code == 401

    def test_authorization_error(self):
        """Test AuthorizationError initialization."""
        error = AuthorizationError(message="Not authorized")

        assert error.message == "Not authorized"
        assert error.category == ErrorCategory.AUTHORIZATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.status_code == 403

    def test_resource_not_found_error(self):
        """Test ResourceNotFoundError initialization."""
        error = ResourceNotFoundError(
            message="User not found",
            resource_type="user",
            resource_id="123"
        )

        assert error.message == "User not found"
        assert error.category == ErrorCategory.RESOURCE_NOT_FOUND
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["resource_type"] == "user"
        assert error.details["resource_id"] == "123"
        assert error.status_code == 404

    def test_external_service_error(self):
        """Test ExternalServiceError initialization."""
        error = ExternalServiceError(
            message="API error",
            service_name="example_api",
            operation="get_data"
        )

        assert error.message == "API error"
        assert error.category == ErrorCategory.EXTERNAL_SERVICE
        assert error.severity == ErrorSeverity.ERROR
        assert error.details["service_name"] == "example_api"
        assert error.details["operation"] == "get_data"
        assert error.status_code == 502

    def test_database_error(self):
        """Test DatabaseError initialization."""
        error = DatabaseError(
            message="Query failed",
            operation="select"
        )

        assert error.message == "Query failed"
        assert error.category == ErrorCategory.DATABASE
        assert error.severity == ErrorSeverity.ERROR
        assert error.details["operation"] == "select"
        assert error.status_code == 500

    def test_timeout_error(self):
        """Test TimeoutError initialization."""
        error = TimeoutError(
            message="Operation timed out",
            operation="api_call",
            timeout_seconds=30
        )

        assert error.message == "Operation timed out"
        assert error.category == ErrorCategory.TIMEOUT
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["operation"] == "api_call"
        assert error.details["timeout_seconds"] == 30
        assert error.status_code == 504

    def test_rate_limit_error(self):
        """Test RateLimitError initialization."""
        error = RateLimitError(
            message="Rate limit exceeded",
            limit=100,
            reset_time="2023-01-01T00:00:00Z"
        )

        assert error.message == "Rate limit exceeded"
        assert error.category == ErrorCategory.RATE_LIMIT
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["limit"] == 100
        assert error.details["reset_time"] == "2023-01-01T00:00:00Z"
        assert error.status_code == 429


class TestErrorDecorators:
    """Tests for the error handling decorators."""

    def test_handle_errors_no_error(self):
        """Test handle_errors decorator with no error."""
        @handle_errors()
        def test_func():
            return "success"

        result = test_func()
        assert result == "success"

    def test_handle_errors_with_error(self):
        """Test handle_errors decorator with an error."""
        @handle_errors(fallback_return="fallback")
        def test_func():
            raise ValueError("Test error")

        result = test_func()
        assert result == "fallback"

    def test_handle_errors_with_reraise(self):
        """Test handle_errors decorator with reraise=True."""
        @handle_errors(reraise=True)
        def test_func():
            raise ValueError("Test error")

        with pytest.raises(ValueError):
            test_func()

    def test_handle_errors_with_error_handler(self):
        """Test handle_errors decorator with a custom error handler."""
        error_handler = MagicMock(return_value="handled")

        @handle_errors(error_handler=error_handler)
        def test_func():
            raise ValueError("Test error")

        result = test_func()
        assert result == "handled"
        error_handler.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_async_errors_no_error(self):
        """Test handle_async_errors decorator with no error."""
        @handle_async_errors()
        async def test_func():
            return "success"

        result = await test_func()
        assert result == "success"

    @pytest.mark.asyncio
    async def test_handle_async_errors_with_error(self):
        """Test handle_async_errors decorator with an error."""
        @handle_async_errors(fallback_return="fallback")
        async def test_func():
            raise ValueError("Test error")

        result = await test_func()
        assert result == "fallback"

    @pytest.mark.asyncio
    async def test_handle_async_errors_with_reraise(self):
        """Test handle_async_errors decorator with reraise=True."""
        @handle_async_errors(reraise=True)
        async def test_func():
            raise ValueError("Test error")

        with pytest.raises(ValueError):
            await test_func()

    @pytest.mark.asyncio
    async def test_handle_async_errors_with_error_handler(self):
        """Test handle_async_errors decorator with a custom error handler."""
        async def error_handler(error, context):
            return "handled"

        @handle_async_errors(error_handler=error_handler)
        async def test_func():
            raise ValueError("Test error")

        result = await test_func()
        assert result == "handled"


class TestErrorUtilities:
    """Tests for the error handling utilities."""

    def test_convert_exception_to_app_error(self):
        """Test convert_exception_to_app_error function."""
        # Test with ValueError
        error = convert_exception_to_app_error(ValueError("Invalid value"))
        assert isinstance(error, ValidationError)
        assert error.message == "Invalid value"

        # Test with KeyError
        error = convert_exception_to_app_error(KeyError("missing_key"))
        assert isinstance(error, ResourceNotFoundError)
        assert "missing_key" in error.message

        # Test with TimeoutError
        error = convert_exception_to_app_error(asyncio.TimeoutError())
        assert isinstance(error, TimeoutError)

        # Test with PermissionError
        error = convert_exception_to_app_error(PermissionError("No permission"))
        assert isinstance(error, AuthorizationError)
        assert error.message == "No permission"

        # Test with FileNotFoundError
        error = convert_exception_to_app_error(FileNotFoundError("File not found"))
        assert isinstance(error, ResourceNotFoundError)
        assert error.message == "File not found"
        assert error.details["resource_type"] == "file"

        # Test with generic exception
        error = convert_exception_to_app_error(Exception("Generic error"))
        assert isinstance(error, AppError)
        assert error.message == "Generic error"
        assert error.category == ErrorCategory.INTERNAL

    def test_format_error_for_response(self):
        """Test format_error_for_response function."""
        # Test with AppError
        app_error = ValidationError(message="Invalid input", field="username")
        response = format_error_for_response(app_error)
        assert response["message"] == "Invalid input"
        assert response["category"] == "validation"
        assert response["details"]["field"] == "username"

        # Test with standard exception
        std_error = ValueError("Invalid value")
        response = format_error_for_response(std_error)
        assert response["message"] == "Invalid value"
        assert response["category"] == "validation"

    @patch("app.core.errors.base.logger")
    def test_log_error(self, mock_logger):
        """Test log_error function."""
        # Test with AppError
        app_error = ValidationError(message="Invalid input", field="username")
        log_error(app_error, context={"request_id": "123"})
        mock_logger.log.assert_called_once()

        # Reset mock
        mock_logger.reset_mock()

        # Test with standard exception
        std_error = ValueError("Invalid value")
        log_error(std_error)
        mock_logger.log.assert_called_once()

    def test_create_error_response(self):
        """Test create_error_response function."""
        # Test with AppError
        app_error = ValidationError(message="Invalid input", field="username")
        response = create_error_response(app_error)
        assert response["success"] is False
        assert response["error"]["message"] == "Invalid input"
        assert response["error"]["type"] == "validation"
        assert response["error"]["code"] == 400
        assert "details" in response["error"]

        # Test with standard exception
        std_error = ValueError("Invalid value")
        response = create_error_response(std_error)
        assert response["success"] is False
        assert response["error"]["message"] == "Invalid value"
        assert "details" in response["error"]

        # Test with include_details=False
        response = create_error_response(app_error, include_details=False)
        assert "details" not in response["error"]


class TestErrorContext:
    """Tests for the ErrorContext context manager."""

    def test_error_context_no_error(self):
        """Test ErrorContext with no error."""
        with ErrorContext("Test operation") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_error_context_with_error_reraise(self):
        """Test ErrorContext with an error and reraise=True."""
        with pytest.raises(ValueError):
            with ErrorContext("Test operation", reraise=True) as ctx:
                raise ValueError("Test error")

    def test_error_context_with_error_no_reraise(self):
        """Test ErrorContext with an error and reraise=False."""
        with ErrorContext("Test operation", reraise=False) as ctx:
            raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, AppError)
        assert ctx.app_error.message == "Test error"

    @patch("app.core.errors.base.logger")
    def test_error_context_logging(self, mock_logger):
        """Test ErrorContext logs errors."""
        with ErrorContext("Test operation", reraise=False) as ctx:
            raise ValueError("Test error")

        mock_logger.log.assert_called_once()
