"""
Tests for the API error handling module.

This module tests the API error handling utilities in app.core.errors.api.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch
from fastapi import status
from fastapi.responses import JSONResponse

from app.core.errors.base import (
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ExternalServiceError,
    DatabaseError,
    TimeoutError,
    RateLimitError
)
from app.core.errors.api import (
    APIError,
    handle_api_errors,
    create_api_error_response
)


class TestAPIErrorClass:
    """Tests for the APIError class."""

    def test_api_error_init(self):
        """Test APIError initialization."""
        error = APIError(
            message="API operation failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details={"endpoint": "/api/test"}
        )

        assert error.message == "API operation failed"
        assert error.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert error.details == {"endpoint": "/api/test"}


class TestAPIErrorDecorator:
    """Tests for the handle_api_errors decorator."""

    @pytest.mark.asyncio
    async def test_handle_api_errors_no_error(self):
        """Test handle_api_errors decorator with no error."""
        @handle_api_errors()
        async def test_func():
            return {"success": True, "data": "test"}

        result = await test_func()
        assert result == {"success": True, "data": "test"}

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_validation_error(self):
        """Test handle_api_errors decorator with a ValidationError."""
        @handle_api_errors()
        async def test_func():
            raise ValidationError(message="Invalid input", field="username")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid input" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_authentication_error(self):
        """Test handle_api_errors decorator with an AuthenticationError."""
        @handle_api_errors()
        async def test_func():
            raise AuthenticationError(message="Authentication failed")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication failed" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_authorization_error(self):
        """Test handle_api_errors decorator with an AuthorizationError."""
        @handle_api_errors()
        async def test_func():
            raise AuthorizationError(message="Not authorized")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "Not authorized" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_resource_not_found_error(self):
        """Test handle_api_errors decorator with a ResourceNotFoundError."""
        @handle_api_errors()
        async def test_func():
            raise ResourceNotFoundError(message="User not found", resource_type="user", resource_id="123")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_timeout_error(self):
        """Test handle_api_errors decorator with a TimeoutError."""
        @handle_api_errors()
        async def test_func():
            raise TimeoutError(message="Operation timed out")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_504_GATEWAY_TIMEOUT
        assert "Operation timed out" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_rate_limit_error(self):
        """Test handle_api_errors decorator with a RateLimitError."""
        @handle_api_errors()
        async def test_func():
            raise RateLimitError(message="Rate limit exceeded")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
        assert "Rate limit exceeded" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_external_service_error(self):
        """Test handle_api_errors decorator with an ExternalServiceError."""
        @handle_api_errors()
        async def test_func():
            raise ExternalServiceError(message="External service error")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_502_BAD_GATEWAY
        assert "External service error" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_database_error(self):
        """Test handle_api_errors decorator with a DatabaseError."""
        @handle_api_errors()
        async def test_func():
            raise DatabaseError(message="Database error")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Database error" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_api_error(self):
        """Test handle_api_errors decorator with an APIError."""
        @handle_api_errors()
        async def test_func():
            raise APIError(message="API error", status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "API error" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_value_error(self):
        """Test handle_api_errors decorator with a ValueError."""
        @handle_api_errors()
        async def test_func():
            raise ValueError("Invalid value")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid value" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_key_error(self):
        """Test handle_api_errors decorator with a KeyError."""
        @handle_api_errors()
        async def test_func():
            raise KeyError("missing_key")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "missing_key" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_permission_error(self):
        """Test handle_api_errors decorator with a PermissionError."""
        @handle_api_errors()
        async def test_func():
            raise PermissionError("No permission")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "No permission" in response.body.decode()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_generic_exception(self):
        """Test handle_api_errors decorator with a generic exception."""
        @handle_api_errors()
        async def test_func():
            raise Exception("Generic error")

        response = await test_func()
        assert isinstance(response, JSONResponse)
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "API error" in response.body.decode()

    @pytest.mark.asyncio
    @patch("app.core.errors.api.logger")
    async def test_handle_api_errors_logging(self, mock_logger):
        """Test handle_api_errors decorator logs errors."""
        @handle_api_errors()
        async def test_func():
            raise ValidationError(message="Invalid input")

        await test_func()
        mock_logger.log.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_api_errors_with_custom_status_codes(self):
        """Test handle_api_errors decorator with custom status code mapping."""
        custom_status_codes = {
            ValidationError: status.HTTP_422_UNPROCESSABLE_ENTITY,
            AuthenticationError: status.HTTP_403_FORBIDDEN
        }

        @handle_api_errors(app_error_to_status_code=custom_status_codes)
        async def test_func(error_type):
            if error_type == "validation":
                raise ValidationError(message="Invalid input")
            elif error_type == "authentication":
                raise AuthenticationError(message="Authentication failed")
            return {"success": True}

        # Test with ValidationError
        response = await test_func("validation")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test with AuthenticationError
        response = await test_func("authentication")
        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestAPIErrorUtilities:
    """Tests for the API error handling utilities."""

    def test_create_api_error_response_with_app_error(self):
        """Test create_api_error_response function with an AppError."""
        error = ValidationError(message="Invalid input", field="username")
        response = create_api_error_response(error)

        assert response["success"] is False
        assert response["error"]["message"] == "Invalid input"
        assert response["error"]["type"] == "validation"
        assert response["error"]["code"] == 400
        assert "details" in response["error"]
        assert response["error"]["details"]["field"] == "username"

    def test_create_api_error_response_with_standard_exception(self):
        """Test create_api_error_response function with a standard exception."""
        error = ValueError("Invalid value")
        response = create_api_error_response(error)

        assert response["success"] is False
        assert response["error"]["message"] == "Invalid value"
        assert response["error"]["type"] == "validation"
        assert "details" in response["error"]

    def test_create_api_error_response_without_details(self):
        """Test create_api_error_response function with include_details=False."""
        error = ValidationError(message="Invalid input", field="username")
        response = create_api_error_response(error, include_details=False)

        assert response["success"] is False
        assert response["error"]["message"] == "Invalid input"
        assert response["error"]["type"] == "validation"
        assert response["error"]["code"] == 400
        assert "details" not in response["error"]
