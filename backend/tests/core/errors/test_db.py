"""
Tests for the database error handling module.

This module tests the database error handling utilities in app.core.errors.db.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch

from app.core.errors.base import (
    AppError,
    ValidationError,
    ResourceNotFoundError,
    DatabaseError
)
from app.core.errors.db import (
    handle_db_errors,
    handle_async_db_errors,
    DBErrorContext
)


class MockSQLAlchemyError(Exception):
    """Mock SQLAlchemy error for testing."""
    pass


class MockSQLAlchemyIntegrityError(MockSQLAlchemyError):
    """Mock SQLAlchemy IntegrityError for testing."""
    pass


class MockSQLAlchemyNoResultFound(MockSQLAlchemyError):
    """Mock SQLAlchemy NoResultFound for testing."""
    pass


class MockPsycopgError(Exception):
    """Mock psycopg error for testing."""
    pass


# Patch the type checking to recognize our mock errors
@patch("app.core.errors.db.type")
class TestDBErrorDecorators:
    """Tests for the database error handling decorators."""

    def test_handle_db_errors_no_error(self, mock_type):
        """Test handle_db_errors decorator with no error."""
        @handle_db_errors()
        def test_func():
            return "success"

        result = test_func()
        assert result == "success"

    def test_handle_db_errors_with_integrity_error(self, mock_type):
        """Test handle_db_errors decorator with an integrity error."""
        # Configure the mock to identify our error as an IntegrityError
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "IntegrityError"

        @handle_db_errors(fallback_return="fallback")
        def test_func():
            raise MockSQLAlchemyIntegrityError("Unique constraint violation")

        result = test_func()
        assert result == "fallback"

    def test_handle_db_errors_with_no_result_found(self, mock_type):
        """Test handle_db_errors decorator with a no result found error."""
        # Configure the mock to identify our error as a NoResultFound
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "NoResultFound"

        @handle_db_errors(fallback_return="fallback")
        def test_func():
            raise MockSQLAlchemyNoResultFound("No row was found")

        result = test_func()
        assert result == "fallback"

    def test_handle_db_errors_with_generic_sqlalchemy_error(self, mock_type):
        """Test handle_db_errors decorator with a generic SQLAlchemy error."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        @handle_db_errors(fallback_return="fallback")
        def test_func():
            raise MockSQLAlchemyError("Database error")

        result = test_func()
        assert result == "fallback"

    def test_handle_db_errors_with_psycopg_error(self, mock_type):
        """Test handle_db_errors decorator with a psycopg error."""
        # Configure the mock to identify our error as a psycopg error
        mock_type.return_value.__module__ = "psycopg"
        mock_type.return_value.__name__ = "Error"

        @handle_db_errors(fallback_return="fallback")
        def test_func():
            raise MockPsycopgError("Connection error")

        result = test_func()
        assert result == "fallback"

    def test_handle_db_errors_with_reraise(self, mock_type):
        """Test handle_db_errors decorator with reraise=True."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        @handle_db_errors(reraise=True)
        def test_func():
            raise MockSQLAlchemyError("Database error")

        with pytest.raises(MockSQLAlchemyError):
            test_func()

    @pytest.mark.asyncio
    async def test_handle_async_db_errors_no_error(self, mock_type):
        """Test handle_async_db_errors decorator with no error."""
        @handle_async_db_errors()
        async def test_func():
            return "success"

        result = await test_func()
        assert result == "success"

    @pytest.mark.asyncio
    async def test_handle_async_db_errors_with_error(self, mock_type):
        """Test handle_async_db_errors decorator with an error."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        @handle_async_db_errors(fallback_return="fallback")
        async def test_func():
            raise MockSQLAlchemyError("Database error")

        result = await test_func()
        assert result == "fallback"


# Patch the type checking to recognize our mock errors
@patch("app.core.errors.db.type")
class TestDBErrorContext:
    """Tests for the DBErrorContext context manager."""

    def test_db_error_context_no_error(self, mock_type):
        """Test DBErrorContext with no error."""
        with DBErrorContext("Test operation") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_db_error_context_with_integrity_error(self, mock_type):
        """Test DBErrorContext with an integrity error."""
        # Configure the mock to identify our error as an IntegrityError
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "IntegrityError"

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise MockSQLAlchemyIntegrityError("Unique constraint violation")

        assert isinstance(ctx.error, MockSQLAlchemyIntegrityError)
        assert isinstance(ctx.app_error, ValidationError)
        assert "Database integrity error" in ctx.app_error.message

    def test_db_error_context_with_no_result_found(self, mock_type):
        """Test DBErrorContext with a no result found error."""
        # Configure the mock to identify our error as a NoResultFound
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "NoResultFound"

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise MockSQLAlchemyNoResultFound("No row was found")

        assert isinstance(ctx.error, MockSQLAlchemyNoResultFound)
        assert isinstance(ctx.app_error, ResourceNotFoundError)
        assert "Resource not found in database" in ctx.app_error.message

    def test_db_error_context_with_generic_sqlalchemy_error(self, mock_type):
        """Test DBErrorContext with a generic SQLAlchemy error."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise MockSQLAlchemyError("Database error")

        assert isinstance(ctx.error, MockSQLAlchemyError)
        assert isinstance(ctx.app_error, DatabaseError)
        assert "Database error" in ctx.app_error.message

    def test_db_error_context_with_psycopg_error(self, mock_type):
        """Test DBErrorContext with a psycopg error."""
        # Configure the mock to identify our error as a psycopg error
        mock_type.return_value.__module__ = "psycopg"
        mock_type.return_value.__name__ = "Error"

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise MockPsycopgError("Connection error")

        assert isinstance(ctx.error, MockPsycopgError)
        assert isinstance(ctx.app_error, DatabaseError)
        assert "PostgreSQL error" in ctx.app_error.message

    def test_db_error_context_with_app_error(self, mock_type):
        """Test DBErrorContext with an AppError."""
        app_error = DatabaseError(message="Custom database error")

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise app_error

        assert ctx.error is app_error
        assert ctx.app_error is app_error

    def test_db_error_context_with_reraise(self, mock_type):
        """Test DBErrorContext with reraise=True."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        with pytest.raises(MockSQLAlchemyError):
            with DBErrorContext("Test operation", reraise=True) as ctx:
                raise MockSQLAlchemyError("Database error")

    @patch("app.core.errors.db.logger")
    def test_db_error_context_logging(self, mock_logger, mock_type):
        """Test DBErrorContext logs errors."""
        # Configure the mock to identify our error as a SQLAlchemy error
        mock_type.return_value.__module__ = "sqlalchemy"
        mock_type.return_value.__name__ = "SQLAlchemyError"

        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise MockSQLAlchemyError("Database error")

        mock_logger.log.assert_called_once()
