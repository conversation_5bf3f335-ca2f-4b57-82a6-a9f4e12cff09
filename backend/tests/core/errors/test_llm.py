"""
Tests for the LLM error handling module.

This module tests the LLM error handling utilities in app.core.errors.llm.
"""
import pytest
import logging
import asyncio
from unittest.mock import MagicMock, patch

from app.core.errors.base import (
    AppError,
    ExternalServiceError,
    TimeoutError,
    RateLimitError
)
from app.core.errors.llm import (
    LLMError,
    LLMTimeoutError,
    LLMRateLimitError,
    handle_llm_errors,
    handle_async_llm_errors,
    LLMErrorContext
)


class MockOpenAIError(Exception):
    """Mock OpenAI error for testing."""
    pass


class MockOpenAITimeoutError(MockOpenAIError):
    """Mock OpenAI timeout error for testing."""
    pass


class MockOpenAIRateLimitError(MockOpenAIError):
    """Mock OpenAI rate limit error for testing."""
    pass


class MockAnthropicError(Exception):
    """Mock Anthropic error for testing."""
    pass


class MockAnthropicTimeoutError(MockAnthropicError):
    """Mock Anthropic timeout error for testing."""
    pass


class MockAnthropicRateLimitError(MockAnthropicError):
    """Mock Anthropic rate limit error for testing."""
    pass


# Patch the type checking to recognize our mock errors
@patch("app.core.errors.llm.type")
class TestLLMErrorClasses:
    """Tests for the LLM error classes."""

    def test_llm_error_init(self, mock_type):
        """Test LLMError initialization."""
        error = LLMError(
            message="LLM operation failed",
            provider="openai",
            model="gpt-4",
            operation="chat"
        )

        assert error.message == "LLM operation failed"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["service_name"] == "openai"
        assert error.details["operation"] == "chat"
        assert error.status_code == 502

    def test_llm_timeout_error_init(self, mock_type):
        """Test LLMTimeoutError initialization."""
        error = LLMTimeoutError(
            message="LLM operation timed out",
            provider="openai",
            model="gpt-4",
            operation="chat",
            timeout_seconds=30
        )

        assert error.message == "LLM operation timed out"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["operation"] == "chat"
        assert error.details["timeout_seconds"] == 30
        assert error.status_code == 504

    def test_llm_rate_limit_error_init(self, mock_type):
        """Test LLMRateLimitError initialization."""
        error = LLMRateLimitError(
            message="LLM rate limit exceeded",
            provider="openai",
            model="gpt-4",
            limit=100,
            reset_time="2023-01-01T00:00:00Z"
        )

        assert error.message == "LLM rate limit exceeded"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["limit"] == 100
        assert error.details["reset_time"] == "2023-01-01T00:00:00Z"
        assert error.status_code == 429


# Patch the type checking to recognize our mock errors
@patch("app.core.errors.llm.type")
class TestLLMErrorDecorators:
    """Tests for the LLM error handling decorators."""

    def test_handle_llm_errors_no_error(self, mock_type):
        """Test handle_llm_errors decorator with no error."""
        @handle_llm_errors()
        def test_func():
            return "success"

        result = test_func()
        assert result == "success"

    def test_handle_llm_errors_with_openai_timeout(self, mock_type):
        """Test handle_llm_errors decorator with an OpenAI timeout error."""
        # Configure the mock to identify our error as an OpenAI timeout
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "Timeout"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockOpenAITimeoutError("Request timed out")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_openai_rate_limit(self, mock_type):
        """Test handle_llm_errors decorator with an OpenAI rate limit error."""
        # Configure the mock to identify our error as an OpenAI rate limit error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "RateLimitError"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockOpenAIRateLimitError("Rate limit exceeded")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_generic_openai_error(self, mock_type):
        """Test handle_llm_errors decorator with a generic OpenAI error."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockOpenAIError("API error")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_anthropic_timeout(self, mock_type):
        """Test handle_llm_errors decorator with an Anthropic timeout error."""
        # Configure the mock to identify our error as an Anthropic timeout
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "Timeout"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockAnthropicTimeoutError("Request timed out")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_anthropic_rate_limit(self, mock_type):
        """Test handle_llm_errors decorator with an Anthropic rate limit error."""
        # Configure the mock to identify our error as an Anthropic rate limit error
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "RateLimitError"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockAnthropicRateLimitError("Rate limit exceeded")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_generic_anthropic_error(self, mock_type):
        """Test handle_llm_errors decorator with a generic Anthropic error."""
        # Configure the mock to identify our error as an Anthropic error
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "APIError"

        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise MockAnthropicError("API error")

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_asyncio_timeout(self, mock_type):
        """Test handle_llm_errors decorator with an asyncio.TimeoutError."""
        @handle_llm_errors(fallback_return="fallback")
        def test_func():
            raise asyncio.TimeoutError()

        result = test_func()
        assert result == "fallback"

    def test_handle_llm_errors_with_retry(self, mock_type):
        """Test handle_llm_errors decorator with retry logic."""
        # Configure the mock to identify our error as an OpenAI timeout
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "Timeout"

        mock_func = MagicMock(side_effect=[
            MockOpenAITimeoutError("Timeout 1"),
            MockOpenAITimeoutError("Timeout 2"),
            "success"
        ])

        @handle_llm_errors(
            retry_count=2,
            retry_delay=0.01,
            retry_on=[MockOpenAITimeoutError]
        )
        def test_func():
            return mock_func()

        result = test_func()
        assert result == "success"
        assert mock_func.call_count == 3

    def test_handle_llm_errors_with_retry_failure(self, mock_type):
        """Test handle_llm_errors decorator with retry logic that fails."""
        # Configure the mock to identify our error as an OpenAI timeout
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "Timeout"

        mock_func = MagicMock(side_effect=[
            MockOpenAITimeoutError("Timeout 1"),
            MockOpenAITimeoutError("Timeout 2"),
            MockOpenAITimeoutError("Timeout 3")
        ])

        @handle_llm_errors(
            fallback_return="fallback",
            retry_count=2,
            retry_delay=0.01,
            retry_on=[MockOpenAITimeoutError]
        )
        def test_func():
            return mock_func()

        result = test_func()
        assert result == "fallback"
        assert mock_func.call_count == 3

    def test_handle_llm_errors_with_reraise(self, mock_type):
        """Test handle_llm_errors decorator with reraise=True."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        @handle_llm_errors(reraise=True)
        def test_func():
            raise MockOpenAIError("API error")

        with pytest.raises(MockOpenAIError):
            test_func()

    @pytest.mark.asyncio
    async def test_handle_async_llm_errors_no_error(self, mock_type):
        """Test handle_async_llm_errors decorator with no error."""
        @handle_async_llm_errors()
        async def test_func():
            return "success"

        result = await test_func()
        assert result == "success"

    @pytest.mark.asyncio
    async def test_handle_async_llm_errors_with_error(self, mock_type):
        """Test handle_async_llm_errors decorator with an error."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        @handle_async_llm_errors(fallback_return="fallback")
        async def test_func():
            raise MockOpenAIError("API error")

        result = await test_func()
        assert result == "fallback"

    @pytest.mark.asyncio
    async def test_handle_async_llm_errors_with_retry(self, mock_type):
        """Test handle_async_llm_errors decorator with retry logic."""
        # Configure the mock to identify our error as an OpenAI timeout
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "Timeout"

        # Create a mock that raises errors for the first two calls, then succeeds
        call_count = 0

        @handle_async_llm_errors(
            retry_count=2,
            retry_delay=0.01,
            retry_on=[MockOpenAITimeoutError]
        )
        async def test_func():
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise MockOpenAITimeoutError(f"Timeout {call_count}")
            return "success"

        result = await test_func()
        assert result == "success"
        assert call_count == 3


# Patch the type checking to recognize our mock errors
@patch("app.core.errors.llm.type")
class TestLLMErrorContext:
    """Tests for the LLMErrorContext context manager."""

    def test_llm_error_context_no_error(self, mock_type):
        """Test LLMErrorContext with no error."""
        with LLMErrorContext("Test operation", provider="openai", model="gpt-4") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_llm_error_context_with_openai_timeout(self, mock_type):
        """Test LLMErrorContext with an OpenAI timeout error."""
        # Configure the mock to identify our error as an OpenAI timeout
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "Timeout"

        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise MockOpenAITimeoutError("Request timed out")

        assert isinstance(ctx.error, MockOpenAITimeoutError)
        assert isinstance(ctx.app_error, LLMTimeoutError)
        assert "OpenAI API request timed out" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_llm_error_context_with_openai_rate_limit(self, mock_type):
        """Test LLMErrorContext with an OpenAI rate limit error."""
        # Configure the mock to identify our error as an OpenAI rate limit error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "RateLimitError"

        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise MockOpenAIRateLimitError("Rate limit exceeded")

        assert isinstance(ctx.error, MockOpenAIRateLimitError)
        assert isinstance(ctx.app_error, LLMRateLimitError)
        assert "OpenAI API rate limit exceeded" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_llm_error_context_with_generic_openai_error(self, mock_type):
        """Test LLMErrorContext with a generic OpenAI error."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise MockOpenAIError("API error")

        assert isinstance(ctx.error, MockOpenAIError)
        assert isinstance(ctx.app_error, LLMError)
        assert "OpenAI API error" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_llm_error_context_with_anthropic_timeout(self, mock_type):
        """Test LLMErrorContext with an Anthropic timeout error."""
        # Configure the mock to identify our error as an Anthropic timeout
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "Timeout"

        with LLMErrorContext("Test operation", provider="anthropic", model="claude-3", reraise=False) as ctx:
            raise MockAnthropicTimeoutError("Request timed out")

        assert isinstance(ctx.error, MockAnthropicTimeoutError)
        assert isinstance(ctx.app_error, LLMTimeoutError)
        assert "Anthropic API request timed out" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "anthropic"
        assert ctx.app_error.details["model"] == "claude-3"

    def test_llm_error_context_with_anthropic_rate_limit(self, mock_type):
        """Test LLMErrorContext with an Anthropic rate limit error."""
        # Configure the mock to identify our error as an Anthropic rate limit error
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "RateLimitError"

        with LLMErrorContext("Test operation", provider="anthropic", model="claude-3", reraise=False) as ctx:
            raise MockAnthropicRateLimitError("Rate limit exceeded")

        assert isinstance(ctx.error, MockAnthropicRateLimitError)
        assert isinstance(ctx.app_error, LLMRateLimitError)
        assert "Anthropic API rate limit exceeded" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "anthropic"
        assert ctx.app_error.details["model"] == "claude-3"

    def test_llm_error_context_with_generic_anthropic_error(self, mock_type):
        """Test LLMErrorContext with a generic Anthropic error."""
        # Configure the mock to identify our error as an Anthropic error
        mock_type.return_value.__module__ = "anthropic"
        mock_type.return_value.__name__ = "APIError"

        with LLMErrorContext("Test operation", provider="anthropic", model="claude-3", reraise=False) as ctx:
            raise MockAnthropicError("API error")

        assert isinstance(ctx.error, MockAnthropicError)
        assert isinstance(ctx.app_error, LLMError)
        assert "Anthropic API error" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "anthropic"
        assert ctx.app_error.details["model"] == "claude-3"

    def test_llm_error_context_with_asyncio_timeout(self, mock_type):
        """Test LLMErrorContext with an asyncio.TimeoutError."""
        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise asyncio.TimeoutError()

        assert isinstance(ctx.error, asyncio.TimeoutError)
        assert isinstance(ctx.app_error, LLMTimeoutError)
        assert "LLM request timed out" in ctx.app_error.message
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_llm_error_context_with_app_error(self, mock_type):
        """Test LLMErrorContext with an AppError."""
        app_error = LLMError(message="Custom LLM error")

        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise app_error

        assert ctx.error is app_error
        assert ctx.app_error is app_error

    def test_llm_error_context_with_reraise(self, mock_type):
        """Test LLMErrorContext with reraise=True."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        with pytest.raises(MockOpenAIError):
            with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=True) as ctx:
                raise MockOpenAIError("API error")

    @patch("app.core.errors.llm.logger")
    def test_llm_error_context_logging(self, mock_logger, mock_type):
        """Test LLMErrorContext logs errors."""
        # Configure the mock to identify our error as an OpenAI error
        mock_type.return_value.__module__ = "openai"
        mock_type.return_value.__name__ = "APIError"

        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise MockOpenAIError("API error")

        mock_logger.log.assert_called_once()
