"""
Unit tests for visualization components.
"""
import pytest
from unittest.mock import patch, MagicMock

from app.cli.formatting import (
    visualize_department_routing,
    visualize_agent_flow,
    visualize_graph_execution
)


@pytest.fixture
def mock_console():
    """Create a mock console for testing."""
    with patch("app.cli.formatting.console") as mock_console:
        yield mock_console


@pytest.fixture
def mock_create_table():
    """Create a mock create_table function for testing."""
    with patch("app.cli.formatting.create_table") as mock_create_table:
        mock_table = MagicMock()
        mock_create_table.return_value = mock_table
        yield mock_create_table, mock_table


@pytest.fixture
def mock_tree():
    """Create a mock Tree for testing."""
    with patch("app.cli.formatting.Tree") as mock_tree_class:
        mock_tree = MagicMock()
        mock_tree_class.return_value = mock_tree
        yield mock_tree_class, mock_tree


def test_visualize_department_routing(mock_console, mock_create_table):
    """Test that visualize_department_routing correctly visualizes department routing."""
    mock_create_table, mock_table = mock_create_table
    
    # Create test data
    analysis = {
        "embedding_scores": {
            "finance": 0.8,
            "marketing": 0.4,
            "co_ceo": 0.2
        },
        "relevant_departments": ["finance", "marketing"]
    }
    departments = ["finance", "marketing"]
    
    # Call the function
    visualize_department_routing(analysis, departments)
    
    # Check that the table was created with the correct columns
    mock_create_table.assert_called_once()
    assert "Department Routing Decision" in mock_create_table.call_args[0][0]
    
    # Check that the table has the correct number of rows
    assert mock_table.add_row.call_count == 3
    
    # Check that the console.print was called
    mock_console.print.assert_called_once_with(mock_table)


def test_visualize_agent_flow(mock_console, mock_tree):
    """Test that visualize_agent_flow correctly visualizes agent flow."""
    mock_tree_class, mock_tree = mock_tree
    
    # Create test data
    state = {
        "analysis": {
            "query_type": "financial",
            "priority": "normal",
            "topics": ["revenue", "expenses"]
        },
        "departments_consulted": ["finance", "marketing"],
        "response": "This is a test response."
    }
    
    # Call the function
    visualize_agent_flow(state)
    
    # Check that the tree was created
    mock_tree_class.assert_called_once()
    
    # Check that the tree has the correct nodes
    assert mock_tree.add.call_count >= 3  # At least 3 nodes (analysis, departments, response)
    
    # Check that the console.print was called
    mock_console.print.assert_called_once_with(mock_tree)


def test_visualize_graph_execution(mock_console, mock_create_table):
    """Test that visualize_graph_execution correctly visualizes graph execution."""
    mock_create_table, mock_table = mock_create_table
    
    # Create test data
    events = [
        {
            "component": "analyze_query_node",
            "action": "start",
            "details": {
                "query": "What is our financial performance?"
            }
        },
        {
            "component": "analyze_query_node",
            "action": "complete",
            "details": {
                "elapsed_time": 0.5,
                "analysis": {
                    "relevant_departments": ["finance"]
                }
            }
        },
        {
            "component": "route_to_departments_node",
            "action": "start",
            "details": {}
        },
        {
            "component": "route_to_departments_node",
            "action": "complete",
            "details": {
                "elapsed_time": 0.1,
                "departments": ["finance"]
            }
        },
        {
            "component": "generate_response_node",
            "action": "complete",
            "details": {
                "elapsed_time": 0.3,
                "response_length": 100,
                "departments": ["finance"]
            }
        }
    ]
    
    # Call the function
    visualize_graph_execution(events)
    
    # Check that the table was created with the correct columns
    mock_create_table.assert_called_once()
    assert "Graph Execution Steps" in mock_create_table.call_args[0][0]
    
    # Check that the table has the correct number of rows
    assert mock_table.add_row.call_count == 5
    
    # Check that the console.print was called
    mock_console.print.assert_called_once_with(mock_table)


def test_visualize_graph_execution_empty(mock_console, mock_create_table):
    """Test that visualize_graph_execution handles empty events correctly."""
    mock_create_table, mock_table = mock_create_table
    
    # Call the function with empty events
    visualize_graph_execution([])
    
    # Check that the table was not created
    mock_create_table.assert_not_called()
    
    # Check that the console.print was not called
    mock_console.print.assert_not_called()
