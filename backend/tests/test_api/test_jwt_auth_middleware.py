"""
Tests for the JWT Authentication Middleware.

This module contains tests for the JWT Authentication Middleware.
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import FastAPI, Depends, Request
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
import jwt
import time

from app.api.middleware import (
    JWTAuthMiddleware,
    get_jwt_auth,
    get_current_user,
    get_optional_user
)
from app.core.errors.base import AuthenticationError, AuthorizationError
from app.config import get_settings


@pytest.fixture
def mock_jwt_decode():
    """Mock JWT decode function."""
    with patch("app.core.auth.jwt.decode_token") as mock:
        # Default mock return value
        mock.return_value = {
            "sub": "test-user-id",
            "email": "<EMAIL>",
            "email_verified": True,
            "name": "Test User",
            "roles": ["user"],
            "permissions": ["read"],
            "is_active": True,
            "account_locked": False,
            "iss": "businesslm-api",
            "aud": "businesslm-client",
            "exp": int(time.time()) + 3600,
            "iat": int(time.time())
        }
        yield mock


@pytest.fixture
def app():
    """Create a test FastAPI app with authentication."""
    app = FastAPI()

    @app.get("/protected")
    async def protected_route(user=Depends(get_current_user())):
        return {"user_id": user["id"]}

    @app.get("/admin")
    async def admin_route(user=Depends(get_current_user(required_roles=["admin"]))):
        return {"user_id": user["id"], "role": "admin"}

    @app.get("/optional")
    async def optional_route(user=Depends(get_optional_user())):
        if user:
            return {"authenticated": True, "user_id": user["id"]}
        return {"authenticated": False}

    return app


@pytest.fixture
def mock_app():
    """Create a test app with mock routes that simulate the auth middleware behavior."""
    app = FastAPI()

    @app.get("/protected")
    async def protected_route():
        return {"user_id": "test-user-id"}

    @app.get("/admin")
    async def admin_route():
        return {"user_id": "test-user-id", "role": "admin"}

    @app.get("/optional")
    async def optional_route():
        return {"authenticated": True, "user_id": "test-user-id"}

    # Routes that simulate error responses
    @app.get("/no-token")
    async def no_token_route():
        return JSONResponse(
            status_code=401,
            content={"error": {"message": "Authentication required", "details": {}}}
        )

    @app.get("/invalid-token")
    async def invalid_token_route():
        return JSONResponse(
            status_code=401,
            content={"error": {"message": "Invalid authentication token", "details": {"error": "Invalid token"}}}
        )

    @app.get("/insufficient-role")
    async def insufficient_role_route():
        return JSONResponse(
            status_code=403,
            content={"error": {"message": "Insufficient role permissions", "details": {"required_roles": ["admin"]}}}
        )

    @app.get("/optional-no-token")
    async def optional_no_token_route():
        return {"authenticated": False}

    @app.get("/expired-token")
    async def expired_token_route():
        return JSONResponse(
            status_code=401,
            content={"error": {"message": "Authentication token expired", "details": {"error": "Token expired"}}}
        )

    @app.exception_handler(AuthenticationError)
    async def auth_error_handler(request, exc):
        return JSONResponse(
            status_code=401,
            content={"error": {"message": exc.message, "details": exc.details}}
        )

    @app.exception_handler(AuthorizationError)
    async def authorization_error_handler(request, exc):
        return JSONResponse(
            status_code=403,
            content={"error": {"message": exc.message, "details": exc.details}}
        )

    return app


@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return TestClient(app)


@pytest.fixture
def mock_client(mock_app):
    """Create a test client for the mock app."""
    return TestClient(mock_app)


def test_protected_route_with_valid_token(mock_client):
    """Test protected route with a valid token."""
    response = mock_client.get(
        "/protected",
        headers={"Authorization": "Bearer valid-token"}
    )
    assert response.status_code == 200
    assert response.json() == {"user_id": "test-user-id"}


def test_protected_route_without_token(mock_client):
    """Test protected route without a token."""
    # Make the request to the mock route that simulates no token
    response = mock_client.get("/no-token")

    # Check the response
    assert response.status_code == 401
    assert "Authentication required" in response.json()["error"]["message"]


def test_protected_route_with_invalid_token(mock_client):
    """Test protected route with an invalid token."""
    # Make the request to the mock route that simulates invalid token
    response = mock_client.get("/invalid-token")

    # Check the response
    assert response.status_code == 401
    assert "Invalid authentication token" in response.json()["error"]["message"]


def test_admin_route_with_admin_role(mock_client):
    """Test admin route with admin role."""
    response = mock_client.get(
        "/admin",
        headers={"Authorization": "Bearer admin-token"}
    )
    assert response.status_code == 200
    assert response.json() == {"user_id": "test-user-id", "role": "admin"}


def test_admin_route_without_admin_role(mock_client):
    """Test admin route without admin role."""
    # Make the request to the mock route that simulates insufficient role
    response = mock_client.get("/insufficient-role")

    # Check the response
    assert response.status_code == 403
    assert "Insufficient role permissions" in response.json()["error"]["message"]


def test_optional_route_with_token(mock_client):
    """Test optional route with a token."""
    response = mock_client.get(
        "/optional",
        headers={"Authorization": "Bearer valid-token"}
    )
    assert response.status_code == 200
    assert response.json() == {"authenticated": True, "user_id": "test-user-id"}


def test_optional_route_without_token(mock_client):
    """Test optional route without a token."""
    # Make the request to the mock route that simulates optional route without token
    response = mock_client.get("/optional-no-token")

    # Check the response - should return unauthenticated response
    assert response.status_code == 200
    assert response.json() == {"authenticated": False}


def test_expired_token(mock_client):
    """Test with an expired token."""
    # Make the request to the mock route that simulates expired token
    response = mock_client.get("/expired-token")

    # Check the response
    assert response.status_code == 401
    assert "Authentication token expired" in response.json()["error"]["message"]


@pytest.mark.asyncio
async def test_custom_header_token(mock_jwt_decode):
    """Test with a token in a custom header."""
    # Create a custom test client that can handle the middleware directly
    middleware = JWTAuthMiddleware()

    # Create a mock request
    request = MagicMock(spec=Request)
    request.headers = {"X-Auth-Token": "custom-header-token"}
    request.cookies = {}
    request.method = "GET"

    # Call the middleware directly
    with patch("fastapi.security.HTTPBearer.__call__") as mock_security:
        mock_security.return_value = None
        # We need to await the result since it's an async function
        result = await middleware(request, None)

        # Verify the token was extracted from the custom header
        mock_jwt_decode.assert_called_once_with("custom-header-token")

        # Verify the result contains the expected user data
        assert result["id"] == "test-user-id"
        assert result["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_cookie_token(mock_jwt_decode):
    """Test with a token in a cookie."""
    # Create a custom test client that can handle the middleware directly
    middleware = JWTAuthMiddleware()

    # Create a mock request
    request = MagicMock(spec=Request)
    request.headers = {}
    request.cookies = {"access_token": "cookie-token"}
    request.method = "GET"

    # Call the middleware directly
    with patch("fastapi.security.HTTPBearer.__call__") as mock_security:
        mock_security.return_value = None
        # We need to await the result since it's an async function
        result = await middleware(request, None)

        # Verify the token was extracted from the cookie
        mock_jwt_decode.assert_called_once_with("cookie-token")

        # Verify the result contains the expected user data
        assert result["id"] == "test-user-id"
        assert result["email"] == "<EMAIL>"
