"""
LLM Adapter Tests

This module contains comprehensive tests for the LLM adapter implementations.
It includes both formal pytest-based tests and simpler functional tests.
"""
import pytest
import logging
import sys
from unittest.mock import patch, MagicMock, AsyncMock

# Disable noisy logging during tests
logging.disable(logging.CRITICAL)

# Add the parent directory to sys.path to allow importing from app
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Create fixtures for common test setup
@pytest.fixture(autouse=True)
def no_retries():
    """Disable tenacity retries for faster tests."""
    with patch("app.core.llm.TENACITY_AVAILABLE", False):
        yield

from app.core.llm import (
    OpenAIAdapter,
    AnthropicAdapter,
    MockAdapter,
    get_llm_adapter,
    get_llm_adapter_factory,
    register_adapter,
    ADAPTER_REGISTRY,
    OPENAI_AVAILABLE,
    ANTHROPIC_AVAILABLE,
    TIKTOKEN_AVAILABLE,
)


@pytest.mark.asyncio
async def test_mock_adapter():
    """Test that the mock adapter works correctly."""
    # Create the adapter
    adapter = MockAdapter()

    # Test with a simple message
    messages = [{"role": "user", "content": "Hello, how are you?"}]
    response = await adapter.chat(messages)

    # Verify the response
    assert response == "This is a mock response."

    # Test with a finance-related message
    messages = [{"role": "user", "content": "What is our finance strategy?"}]
    response = await adapter.chat(messages)

    # Verify the response
    assert response == "This is a mock response about finance."

    # Test token counting
    prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)
    assert prompt_tokens > 0
    assert estimated_response_tokens == 50  # Fixed value for mock


@pytest.mark.asyncio
@pytest.mark.skipif(not OPENAI_AVAILABLE, reason="OpenAI package not installed")
async def test_openai_adapter_with_mock():
    """Test the OpenAI adapter with mocked API calls."""
    # Mock the OpenAI client
    mock_response = MagicMock()
    mock_response.choices = [MagicMock()]
    mock_response.choices[0].message.content = "This is a test response from OpenAI."

    # Create a mock client
    mock_client = AsyncMock()

    # Mock the OpenAI client
    with patch("app.core.llm.openai_adapter.AsyncOpenAI", return_value=mock_client):
        adapter = OpenAIAdapter(api_key="test_key")

        # Configure the mock client to return the mock response
        mock_client.chat.completions.create.return_value = mock_response

        # Test with a simple message
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = await adapter.chat(messages)

        # Verify the response
        assert response == "This is a test response from OpenAI."

        # Verify the API call
        mock_client.chat.completions.create.assert_awaited_once()
        _, kwargs = mock_client.chat.completions.create.call_args
        assert kwargs["model"] == adapter.model  # Use the model from the adapter
        assert kwargs["messages"] == messages

        # Test with additional parameters
        await adapter.chat(messages, temperature=0.8, max_tokens=100)

        # Verify the second API call had the right parameters
        assert mock_client.chat.completions.create.await_count == 2
        _, kwargs = mock_client.chat.completions.create.call_args
        assert kwargs["temperature"] == 0.8
        assert kwargs["max_tokens"] == 100


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_adapter_with_mock():
    """Test the Anthropic adapter with mocked API calls."""
    # Mock the Anthropic client (non-streaming response)
    mock_non_stream_response = MagicMock()
    mock_non_stream_response.content = [MagicMock()]
    mock_non_stream_response.content[0].text = "This is a test response from Claude."

    # Create a mock client
    mock_client = AsyncMock()
    mock_client.messages.create = AsyncMock(return_value=mock_non_stream_response)

    # Mock the Anthropic client
    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="test_key")

        # Test with a simple message
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = await adapter.chat(messages)

        # Verify the response
        assert response == "This is a test response from Claude."

        # Verify the API call
        mock_client.messages.create.assert_awaited_once()

        # Test the message conversion with system message
        system_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ]
        anthropic_messages = adapter._convert_to_anthropic_format(system_messages)

        # Verify the conversion includes the system message in the user message
        assert len(anthropic_messages) > 0
        assert anthropic_messages[0]["role"] == "user"
        assert "System:" in anthropic_messages[0]["content"]
        assert "You are a helpful assistant" in anthropic_messages[0]["content"]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "provider,fallback,expected_type,should_raise",
    [
        ("mock", True, MockAdapter, False),  # Mock adapter works
        ("unknown", True, MockAdapter, False),  # Unknown with fallback uses mock
        ("unknown", False, None, True),  # Unknown without fallback raises error
    ],
    ids=["mock", "unknown_fallback", "unknown_no_fallback"]
)
async def test_get_llm_adapter_factory(provider, fallback, expected_type, should_raise):
    """Test the get_llm_adapter factory function with different providers."""
    if should_raise:
        with pytest.raises(ValueError):
            get_llm_adapter(provider, fallback=fallback)
    else:
        adapter = get_llm_adapter(provider, fallback=fallback)
        assert isinstance(adapter, expected_type)

        # Test with empty messages (should handle gracefully)
        if provider == "mock" or (provider == "unknown" and fallback):
            empty_messages = []
            response = await adapter.chat(empty_messages)
            assert response is not None  # Should return a default response


@pytest.mark.asyncio
async def test_adapter_registry():
    """Test the adapter registry functionality."""
    # Create a simple custom adapter for testing
    class CustomAdapter(MockAdapter):
        """Custom adapter for testing the registry."""
        pass

    # Clear any existing registration with the same name
    if "custom" in ADAPTER_REGISTRY:
        del ADAPTER_REGISTRY["custom"]

    # Register the custom adapter
    register_adapter("custom", CustomAdapter)

    # Verify it's in the registry
    assert "custom" in ADAPTER_REGISTRY
    assert ADAPTER_REGISTRY["custom"] == CustomAdapter

    # Get the factory and verify our adapter is included
    factory = get_llm_adapter_factory()
    assert "custom" in factory
    assert factory["custom"] == CustomAdapter

    # Create an adapter using the factory
    adapter = get_llm_adapter("custom")
    assert isinstance(adapter, CustomAdapter)

    # Test the adapter works
    messages = [{"role": "user", "content": "Hello"}]
    response = await adapter.chat(messages)
    assert response is not None


@pytest.mark.asyncio
@pytest.mark.skipif(not OPENAI_AVAILABLE or not TIKTOKEN_AVAILABLE, reason="OpenAI or tiktoken package not installed")
async def test_openai_token_counting():
    """Test OpenAI token counting functionality."""
    # Mock tiktoken
    mock_encoding = MagicMock()
    mock_encoding.encode.side_effect = lambda text: [0] * (len(text) // 2)  # ~2 chars per token

    with patch("app.core.llm.openai_adapter.tiktoken.encoding_for_model", return_value=mock_encoding):
        adapter = OpenAIAdapter(api_key="test_key")

        # Test token counting
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ]

        prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)

        # Verify token counts
        assert prompt_tokens > 0
        assert estimated_response_tokens > 0
        assert mock_encoding.encode.call_count >= 2  # Called for each message content


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_token_counting():
    """Test Anthropic token counting functionality."""
    # Mock the Anthropic client's count_tokens method
    mock_client = AsyncMock()
    mock_client.count_tokens = MagicMock(return_value=100)  # Return a fixed token count

    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="test_key")

        # Test token counting
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ]

        prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)

        # Verify token counts
        assert prompt_tokens == 100  # Should match our mocked value
        assert estimated_response_tokens > 0

        # Verify the client method was called
        mock_client.count_tokens.assert_called_once()


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_token_counting_fallback():
    """Test Anthropic token counting fallback when SDK method fails."""
    # Mock the Anthropic client
    mock_client = AsyncMock()
    mock_client.count_tokens = MagicMock(side_effect=Exception("Simulated token count failure"))

    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="test_key")

        # Test token counting
        messages = [
            {"role": "system", "content": "System prompt"},
            {"role": "user", "content": "Fallback test message."}
        ]

        prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)

        # Fallback returns estimate, so should still be > 0
        assert prompt_tokens > 0
        assert estimated_response_tokens > 0

        # Verify the client method was called
        mock_client.count_tokens.assert_called_once()


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_token_counting_logs_fallback(caplog):
    """Ensure the fallback logs are emitted if token count fails."""
    mock_client = AsyncMock()
    mock_client.count_tokens = MagicMock(side_effect=Exception("Simulated error"))

    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="test_key")
        caplog.set_level(logging.DEBUG)

        messages = [{"role": "user", "content": "Hello"}]
        await adapter.get_token_count(messages)

        assert "using character-based heuristic" in caplog.text


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_multiple_system_messages_warning(caplog):
    """Test AnthropicAdapter logs warning for multiple system messages."""
    mock_client = AsyncMock()
    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="dummy_key")

        messages = [
            {"role": "system", "content": "System message 1"},
            {"role": "system", "content": "System message 2"},
            {"role": "user", "content": "Hello"}
        ]

        caplog.set_level(logging.WARNING)
        anthropic_format = adapter._convert_to_anthropic_format(messages)

        # Ensure warning was logged
        assert "Multiple system messages found" in caplog.text
        assert len(anthropic_format) > 0

        # Verify only the first system message was used
        user_message = next((m for m in anthropic_format if m["role"] == "user"), None)
        assert user_message is not None
        assert "System message 1" in user_message["content"]
        assert "System message 2" not in user_message["content"]


@pytest.mark.asyncio
@pytest.mark.skipif(not OPENAI_AVAILABLE, reason="OpenAI package not installed")
async def test_azure_openai_configuration():
    """Test Azure OpenAI configuration."""
    # Mock the OpenAI client
    mock_client = AsyncMock()

    # Mock the OpenAI client
    with patch("app.core.llm.openai_adapter.AsyncOpenAI", return_value=mock_client):
        try:
            azure_adapter = get_llm_adapter(
                "openai",
                model="gpt-4",
                base_url="https://example.openai.azure.com",
                api_version="2023-05-15"
            )
            assert azure_adapter.base_url == "https://example.openai.azure.com"
            assert azure_adapter.model == "gpt-4"
            assert azure_adapter.api_version == "2023-05-15"
        except Exception as e:
            pytest.skip(f"Azure OpenAI configuration test failed: {e}")


@pytest.mark.asyncio
async def test_adapter_with_kwargs():
    """Test adapters with additional kwargs."""
    # Test MockAdapter with temperature parameter
    mock_adapter = MockAdapter()
    messages = [{"role": "user", "content": "What is our finance strategy?"}]
    response = await mock_adapter.chat(messages, temperature=0.8)  # Should accept kwargs
    assert response == "This is a mock response about finance."

    # Test token counting
    prompt_tokens, estimated_response_tokens = await mock_adapter.get_token_count(messages)
    assert prompt_tokens > 0
    assert estimated_response_tokens == 50  # Fixed value for mock


@pytest.mark.asyncio
async def test_get_stream_flag():
    """Test the get_stream_flag method."""
    # Create adapters
    mock_adapter = MockAdapter()

    # Test with streaming requested and supported
    assert mock_adapter.supports_streaming == True
    assert mock_adapter.get_stream_flag(True) == True

    # Test with streaming not requested
    assert mock_adapter.get_stream_flag(False) == False

    # Create a custom adapter that doesn't support streaming
    class NonStreamingAdapter(MockAdapter):
        @property
        def supports_streaming(self) -> bool:
            return False

    non_streaming_adapter = NonStreamingAdapter()

    # Test with streaming requested but not supported
    assert non_streaming_adapter.supports_streaming == False
    assert non_streaming_adapter.get_stream_flag(True) == False

    # Test with streaming not requested and not supported
    assert non_streaming_adapter.get_stream_flag(False) == False


@pytest.mark.asyncio
@pytest.mark.skipif(not OPENAI_AVAILABLE, reason="OpenAI package not installed")
async def test_openai_streaming():
    """Test OpenAI streaming functionality."""
    # Create mock chunks for streaming
    chunk1 = MagicMock()
    chunk1.choices = [MagicMock()]
    chunk1.choices[0].delta.content = "Hello"

    chunk2 = MagicMock()
    chunk2.choices = [MagicMock()]
    chunk2.choices[0].delta.content = " world"

    # Create a mock async iterator
    async def mock_stream():
        yield chunk1
        yield chunk2

    # Create the mock client
    mock_client = AsyncMock()
    mock_client.chat.completions.create = AsyncMock(return_value=mock_stream())

    # Mock the OpenAI client
    with patch("app.core.llm.openai_adapter.AsyncOpenAI", return_value=mock_client):
        adapter = OpenAIAdapter(api_key="test_key")

        # Test streaming
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        chunks = []

        # Get the streaming generator
        stream_generator = await adapter.chat(messages, stream=True)

        # Iterate through the generator
        async for chunk in stream_generator:
            chunks.append(chunk)

        # Verify chunks were received correctly
        assert len(chunks) == 2
        assert chunks[0] == "Hello"
        assert chunks[1] == " world"
        assert "".join(chunks) == "Hello world"

        # Verify the API call
        mock_client.chat.completions.create.assert_awaited_once()
        _, kwargs = mock_client.chat.completions.create.call_args
        assert kwargs["stream"] == True


@pytest.mark.asyncio
@pytest.mark.skipif(not ANTHROPIC_AVAILABLE, reason="Anthropic package not installed")
async def test_anthropic_streaming():
    """Test Anthropic streaming functionality."""
    # Create mock chunks for streaming
    chunk1 = MagicMock()
    chunk1.type = "content_block_delta"
    chunk1.delta = MagicMock()
    chunk1.delta.text = "Hello"

    chunk2 = MagicMock()
    chunk2.type = "content_block_delta"
    chunk2.delta = MagicMock()
    chunk2.delta.text = " Claude"

    # Create a mock async iterator
    async def mock_stream():
        yield chunk1
        yield chunk2

    # Create the mock client
    mock_client = AsyncMock()
    mock_client.messages.create = AsyncMock(return_value=mock_stream())

    # Mock the Anthropic client
    with patch("app.core.llm.anthropic_adapter.AsyncAnthropic", return_value=mock_client):
        adapter = AnthropicAdapter(api_key="test_key")

        # Test streaming
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        chunks = []

        # Get the streaming generator
        stream_generator = await adapter.chat(messages, stream=True)

        # Iterate through the generator
        async for chunk in stream_generator:
            chunks.append(chunk)

        # Verify chunks were received correctly
        assert len(chunks) == 2
        assert chunks[0] == "Hello"
        assert chunks[1] == " Claude"
        assert "".join(chunks) == "Hello Claude"

        # Verify the API call
        mock_client.messages.create.assert_awaited_once()
        _, kwargs = mock_client.messages.create.call_args
        assert kwargs["stream"] == True


if __name__ == "__main__":
    # Run the tests using pytest
    try:
        # Try to use pytest.main() for more consistent test execution
        print("Running LLM adapter tests with pytest...")
        exit_code = pytest.main(['-xvs', __file__])
        sys.exit(exit_code)  # Exit with the same code as pytest
    except Exception as e:
        print(f"Error running pytest: {e}")
        print("Falling back to subprocess call...")

        # Fall back to subprocess call if pytest.main() fails
        import subprocess
        try:
            subprocess.run([sys.executable, "-m", "pytest", "-xvs", __file__], check=True)
        except subprocess.CalledProcessError as e:
            print(f"Tests failed with exit code: {e.returncode}")
            sys.exit(e.returncode)
