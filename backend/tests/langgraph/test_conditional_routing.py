"""
Tests for conditional routing in LangGraph.

This module contains tests for the conditional routing implementation in LangGraph.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import asyncio

from app.langgraph.state import AgentState, create_initial_state
from app.langgraph.graph import (
    analyze_query_node,
    retrieve_knowledge_node,
    route_to_departments_node,
    finance_department_node,
    marketing_department_node,
    fallback_node,
    generate_response_node,
    build_co_ceo_graph
)


@pytest.fixture
def mock_co_ceo_agent():
    """Create a mock Co-CEO agent."""
    agent = AsyncMock()
    agent.analyze_query = AsyncMock(return_value={
        "relevant_departments": ["finance", "marketing"],
        "query_type": "domain_specific",
        "priority": "normal"
    })
    agent.generate_response = AsyncMock(return_value="Co-CEO response")
    return agent


@pytest.fixture
def mock_finance_agent():
    """Create a mock Finance agent."""
    agent = AsyncMock()
    agent.process_query = AsyncMock(return_value={
        "response": "Finance response",
        "department": "finance",
        "confidence": 0.8
    })
    return agent


@pytest.fixture
def mock_marketing_agent():
    """Create a mock Marketing agent."""
    agent = AsyncMock()
    agent.process_query = AsyncMock(return_value={
        "response": "Marketing response",
        "department": "marketing",
        "confidence": 0.7
    })
    return agent


@pytest.fixture
def mock_knowledge_base_service():
    """Create a mock knowledge base service."""
    service = AsyncMock()
    service.search = AsyncMock(return_value=[
        {"id": "doc1", "text": "Document 1", "metadata": {"source": "test"}},
        {"id": "doc2", "text": "Document 2", "metadata": {"source": "test"}}
    ])
    return service


@pytest.mark.asyncio
async def test_analyze_query_node(mock_co_ceo_agent):
    """Test the analyze_query_node function."""
    # Create initial state
    state = create_initial_state("Test query")

    # Call the node
    result = await analyze_query_node(state, mock_co_ceo_agent)

    # Check that the agent was called
    mock_co_ceo_agent.analyze_query.assert_called_once_with("Test query")

    # Check that the state was updated
    assert result.query_analysis == {
        "relevant_departments": ["finance", "marketing"],
        "query_type": "domain_specific",
        "priority": "normal"
    }


@pytest.mark.asyncio
async def test_retrieve_knowledge_node(mock_knowledge_base_service):
    """Test the retrieve_knowledge_node function."""
    # Create initial state
    state = create_initial_state("Test query")

    # Call the node
    result = await retrieve_knowledge_node(state, mock_knowledge_base_service)

    # Check that the service was called
    mock_knowledge_base_service.search.assert_called_once_with("Test query")

    # Check that the state was updated
    assert len(result.retrieved_knowledge) == 2
    assert result.retrieved_knowledge[0]["id"] == "doc1"
    assert result.retrieved_knowledge[1]["id"] == "doc2"


@pytest.mark.asyncio
async def test_route_to_departments_node():
    """Test the route_to_departments_node function."""
    # Create initial state with query analysis
    state = create_initial_state("Test query")
    state.query_analysis = {
        "relevant_departments": ["finance", "marketing"],
        "query_type": "domain_specific",
        "priority": "normal"
    }

    # Create mock co_ceo_agent
    mock_agent = AsyncMock()

    # Call the node
    result = await route_to_departments_node(state, mock_agent)

    # Check that the state was updated
    assert "finance" in result.departments
    assert "marketing" in result.departments
    assert "routing_decision" in result.metadata


@pytest.mark.asyncio
async def test_finance_department_node(mock_finance_agent):
    """Test the finance_department_node function."""
    # Create initial state
    state = create_initial_state("Test query")
    state.query_analysis = {"test": "analysis"}
    state.retrieved_knowledge = [{"id": "doc1", "text": "Document 1"}]

    # Call the node
    result = await finance_department_node(state, mock_finance_agent)

    # Check that the agent was called
    mock_finance_agent.process_query.assert_called_once()

    # Check that the state was updated
    assert "finance" in result.department_responses
    assert result.department_responses["finance"]["response"] == "Finance response"


@pytest.mark.asyncio
async def test_marketing_department_node(mock_marketing_agent):
    """Test the marketing_department_node function."""
    # Create initial state
    state = create_initial_state("Test query")
    state.query_analysis = {"test": "analysis"}
    state.retrieved_knowledge = [{"id": "doc1", "text": "Document 1"}]

    # Call the node
    result = await marketing_department_node(state, mock_marketing_agent)

    # Check that the agent was called
    mock_marketing_agent.process_query.assert_called_once()

    # Check that the state was updated
    assert "marketing" in result.department_responses
    assert result.department_responses["marketing"]["response"] == "Marketing response"


@pytest.mark.asyncio
async def test_fallback_node(mock_co_ceo_agent):
    """Test the fallback_node function."""
    # Create initial state
    state = create_initial_state("Test query")

    # Call the node
    result = await fallback_node(state, mock_co_ceo_agent)

    # Check that the agent was called
    mock_co_ceo_agent.generate_response.assert_called_once()

    # Check that the state was updated
    assert "co_ceo" in result.department_responses
    assert "used_fallback" in result.metadata
    assert result.metadata["used_fallback"] is True


@pytest.mark.asyncio
async def test_generate_response_node():
    """Test the generate_response_node function."""
    # Create initial state with department responses
    state = create_initial_state("Test query")
    state.department_responses = {
        "finance": {"response": "Finance response"},
        "marketing": {"response": "Marketing response"}
    }

    # Create mock co_ceo_agent
    mock_agent = AsyncMock()

    # Call the node
    result = await generate_response_node(state, mock_agent)

    # Check that the state was updated
    assert result.response is not None
    assert "Finance" in result.response
    assert "Marketing" in result.response


@pytest.mark.asyncio
async def test_build_co_ceo_graph(
    mock_co_ceo_agent, mock_finance_agent, mock_marketing_agent, mock_knowledge_base_service
):
    """Test the build_co_ceo_graph function."""
    # Build the graph
    graph = build_co_ceo_graph(
        mock_co_ceo_agent, mock_finance_agent, mock_marketing_agent, mock_knowledge_base_service
    )

    # Check that the graph has the expected nodes
    assert "analyze_query" in graph.nodes
    assert "retrieve_knowledge" in graph.nodes
    assert "route_to_departments" in graph.nodes
    assert "finance_department" in graph.nodes
    assert "marketing_department" in graph.nodes
    assert "fallback" in graph.nodes
    assert "generate_response" in graph.nodes


def test_graph_execution_finance_only():
    """Test graph execution with only Finance department."""
    # Create initial state
    state = create_initial_state("Test finance query")

    # Manually simulate the graph execution

    # 1. Analyze query - set relevant departments to finance only
    state.query_analysis = {
        "relevant_departments": ["finance"],
        "query_type": "domain_specific",
        "priority": "normal"
    }

    # 2. Retrieve knowledge
    state.retrieved_knowledge = [
        {"id": "doc1", "text": "Finance document", "metadata": {"source": "finance"}}
    ]

    # 3. Route to departments
    state.departments = ["finance"]
    state.metadata["routing_decision"] = {
        "departments": ["finance"],
        "timestamp": "2023-01-01T00:00:00Z"
    }

    # 4. Finance department
    finance_response = {
        "response": "Finance department response",
        "department": "finance",
        "timestamp": "2023-01-01T00:00:00Z"
    }
    state.department_responses["finance"] = finance_response

    # 5. Generate response
    state.response = "Here's what I found:\n\nFrom Finance department: Finance department response"

    # Check that the response includes finance but not marketing
    assert "finance" in state.department_responses
    assert "marketing" not in state.department_responses
    assert "Finance department response" in state.response


def test_graph_execution_marketing_only():
    """Test graph execution with only Marketing department."""
    # Create initial state
    state = create_initial_state("Test marketing query")

    # Manually simulate the graph execution

    # 1. Analyze query - set relevant departments to marketing only
    state.query_analysis = {
        "relevant_departments": ["marketing"],
        "query_type": "domain_specific",
        "priority": "normal"
    }

    # 2. Retrieve knowledge
    state.retrieved_knowledge = [
        {"id": "doc1", "text": "Marketing document", "metadata": {"source": "marketing"}}
    ]

    # 3. Route to departments
    state.departments = ["marketing"]
    state.metadata["routing_decision"] = {
        "departments": ["marketing"],
        "timestamp": "2023-01-01T00:00:00Z"
    }

    # 4. Marketing department
    marketing_response = {
        "response": "Marketing department response",
        "department": "marketing",
        "timestamp": "2023-01-01T00:00:00Z"
    }
    state.department_responses["marketing"] = marketing_response

    # 5. Generate response
    state.response = "Here's what I found:\n\nFrom Marketing department: Marketing department response"

    # Check that the response includes marketing but not finance
    assert "marketing" in state.department_responses
    assert "finance" not in state.department_responses
    assert "Marketing department response" in state.response


def test_graph_execution_both_departments():
    """Test graph execution with both departments."""
    # Create initial state
    state = create_initial_state("Test both departments query")

    # Manually simulate the graph execution

    # 1. Analyze query - set relevant departments to both finance and marketing
    state.query_analysis = {
        "relevant_departments": ["finance", "marketing"],
        "query_type": "domain_specific",
        "priority": "normal"
    }

    # 2. Retrieve knowledge
    state.retrieved_knowledge = [
        {"id": "doc1", "text": "Finance document", "metadata": {"source": "finance"}},
        {"id": "doc2", "text": "Marketing document", "metadata": {"source": "marketing"}}
    ]

    # 3. Route to departments
    state.departments = ["finance", "marketing"]
    state.metadata["routing_decision"] = {
        "departments": ["finance", "marketing"],
        "timestamp": "2023-01-01T00:00:00Z"
    }

    # 4. Finance department
    finance_response = {
        "response": "Finance department response",
        "department": "finance",
        "timestamp": "2023-01-01T00:00:00Z"
    }
    state.department_responses["finance"] = finance_response

    # 5. Marketing department
    marketing_response = {
        "response": "Marketing department response",
        "department": "marketing",
        "timestamp": "2023-01-01T00:00:00Z"
    }
    state.department_responses["marketing"] = marketing_response

    # 6. Generate response
    state.response = "Here's what I found:\n\nFrom Finance department: Finance department response\n\nFrom Marketing department: Marketing department response"

    # Check that the response includes both departments
    assert "finance" in state.department_responses
    assert "marketing" in state.department_responses
    assert "Finance department response" in state.response
    assert "Marketing department response" in state.response


def test_graph_execution_fallback():
    """Test graph execution with fallback."""
    # Create initial state
    state = create_initial_state("Test fallback query")

    # Manually simulate the graph execution

    # 1. Analyze query - set relevant departments to empty list
    state.query_analysis = {
        "relevant_departments": [],
        "query_type": "general",
        "priority": "normal"
    }

    # 2. Retrieve knowledge
    state.retrieved_knowledge = [
        {"id": "doc1", "text": "General document", "metadata": {"source": "general"}}
    ]

    # 3. Route to departments (empty list)
    state.departments = []
    state.metadata["routing_decision"] = {
        "departments": [],
        "timestamp": "2023-01-01T00:00:00Z"
    }

    # 4. Fallback
    fallback_response = {
        "response": "Co-CEO fallback response",
        "department": "co_ceo",
        "timestamp": "2023-01-01T00:00:00Z"
    }
    state.department_responses["co_ceo"] = fallback_response
    state.metadata["used_fallback"] = True

    # 5. Generate response
    state.response = "Here's what I found:\n\nFrom Co_ceo department: Co-CEO fallback response"

    # Check that the fallback was used
    assert state.metadata.get("used_fallback", False)

    # Check that the response includes co_ceo but not departments
    assert "co_ceo" in state.department_responses
    assert "finance" not in state.department_responses
    assert "marketing" not in state.department_responses
    assert "Co-CEO fallback response" in state.response
