"""
Tests for timeout and retry mechanisms in LangGraph.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock
import time

from app.core.errors.base import TimeoutError
from app.langgraph.timeout import (
    with_timeout_and_retry,
    with_co_ceo_timeout,
    with_finance_department_timeout,
    with_marketing_department_timeout
)
from app.langgraph.state import AgentState


class TestTimeoutRetry:
    """Tests for timeout and retry mechanisms."""

    @pytest.mark.asyncio
    async def test_basic_timeout_configuration(self):
        """Test that timeout can be configured and works as expected."""
        # Create a function that takes longer than the timeout
        async def slow_function():
            await asyncio.sleep(0.5)  # This should timeout
            return "This should not be returned"

        # Verify that the function times out
        with pytest.raises(TimeoutError):
            await with_timeout_and_retry(
                slow_function,
                timeout_seconds=0.1,
                max_attempts=1
            )

    @pytest.mark.asyncio
    async def test_retry_logic(self):
        """Test that retry logic works as expected."""
        # Create a mock that fails twice then succeeds
        mock_function = AsyncMock()
        mock_function.side_effect = [
            Exception("First failure"),
            Exception("Second failure"),
            "Success"
        ]

        # Create a function that uses the mock
        async def retried_function():
            return await mock_function()

        # Verify that the function retries and eventually succeeds
        result = await with_timeout_and_retry(
            retried_function,
            timeout_seconds=1.0,
            max_attempts=3,
            retry_on=[Exception]
        )
        assert result == "Success"
        assert mock_function.call_count == 3  # Called 3 times (1 initial + 2 retries)

    @pytest.mark.asyncio
    async def test_department_specific_timeouts(self):
        """Test that department-specific timeout decorators work."""
        # Create a test state
        state = AgentState(query="Test query")

        # Create mock functions for each department
        @with_co_ceo_timeout()
        async def co_ceo_function(state):
            await asyncio.sleep(0.1)  # Short enough to not timeout
            return state.model_copy(deep=True)

        @with_finance_department_timeout()
        async def finance_function(state):
            await asyncio.sleep(0.1)  # Short enough to not timeout
            return state.model_copy(deep=True)

        @with_marketing_department_timeout()
        async def marketing_function(state):
            await asyncio.sleep(0.1)  # Short enough to not timeout
            return state.model_copy(deep=True)

        # Verify that all functions complete without timing out
        result_co_ceo = await co_ceo_function(state)
        result_finance = await finance_function(state)
        result_marketing = await marketing_function(state)

        assert isinstance(result_co_ceo, AgentState)
        assert isinstance(result_finance, AgentState)
        assert isinstance(result_marketing, AgentState)

    @pytest.mark.asyncio
    async def test_retry_with_exponential_backoff(self):
        """Test that retry uses exponential backoff."""
        retry_delays = []

        # Create a function to track retry delays
        async def track_time():
            retry_delays.append(time.time())
            raise Exception("Simulated failure")

        # Verify that the function retries with increasing delays
        with pytest.raises(Exception):
            await with_timeout_and_retry(
                track_time,
                timeout_seconds=1.0,
                max_attempts=3,
                retry_on=[Exception]
            )

        assert len(retry_delays) == 3  # Initial call + 2 retries

        # Calculate delays between retries
        delays = [retry_delays[i+1] - retry_delays[i] for i in range(len(retry_delays)-1)]

        # Verify that second delay is longer than first (exponential backoff)
        # We can't be too precise due to asyncio scheduling, but the pattern should be clear
        assert delays[1] > delays[0], "Second delay should be longer than first (exponential backoff)"
