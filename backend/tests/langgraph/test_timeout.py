"""
Tests for LangGraph timeout utilities.

This module contains tests for the timeout and retry utilities for LangGraph nodes.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, patch

from app.langgraph.state import AgentState, create_initial_state
from app.langgraph.timeout import (
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout
)
from app.core.errors.base import TimeoutError, RateLimitError


# Test node function that succeeds
async def node_success(state: AgentState) -> AgentState:
    return state


# Test node function that fails with an exception
async def node_failure(state: AgentState) -> AgentState:
    raise ValueError("Node failure")


# Test node function that times out
async def node_timeout(state: AgentState) -> AgentState:
    await asyncio.sleep(10)
    return state


@pytest.fixture
def test_state():
    """Create a test state for node functions."""
    return create_initial_state(
        query="Test query",
        user_id="test_user",
        thread_id="test_thread"
    )


@pytest.mark.asyncio
async def test_with_node_timeout_and_retry_success(test_state):
    """Test that with_node_timeout_and_retry succeeds with a fast function."""
    result = await with_node_timeout_and_retry(
        node_success,
        test_state,
        timeout_seconds=1,
        max_attempts=2
    )
    assert result == test_state
    assert "errors" not in result.metadata


@pytest.mark.asyncio
async def test_with_node_timeout_and_retry_failure(test_state):
    """Test that with_node_timeout_and_retry handles exceptions gracefully."""
    result = await with_node_timeout_and_retry(
        node_failure,
        test_state,
        timeout_seconds=1,
        max_attempts=2,
        node_name="test_node"
    )

    # Should return a state with error information
    assert result != test_state
    assert "errors" in result.metadata
    assert len(result.metadata["errors"]) == 1
    assert result.metadata["errors"][0]["type"] == "exception"
    assert result.metadata["errors"][0]["node"] == "test_node"

    # Should add an error message to messages
    assert len(result.messages) == 1
    assert "Error occurred in test_node" in result.messages[0]["content"]


@pytest.mark.asyncio
async def test_with_node_timeout_and_retry_timeout(test_state):
    """Test that with_node_timeout_and_retry handles timeouts gracefully."""
    result = await with_node_timeout_and_retry(
        node_timeout,
        test_state,
        timeout_seconds=0.1,
        max_attempts=1,
        node_name="test_node"
    )

    # Should return a state with error information
    assert result != test_state
    assert "errors" in result.metadata
    assert len(result.metadata["errors"]) == 1
    assert result.metadata["errors"][0]["type"] == "timeout"
    assert result.metadata["errors"][0]["node"] == "test_node"

    # Should add a timeout message to messages
    assert len(result.messages) == 1
    assert "timed out" in result.messages[0]["content"]


@pytest.mark.asyncio
async def test_with_node_timeout_decorator(test_state):
    """Test that with_node_timeout decorator works correctly."""
    # Create a decorated function
    @with_node_timeout(timeout_seconds=0.1, node_name="decorated_node")
    async def decorated_timeout_node(state: AgentState) -> AgentState:
        await asyncio.sleep(1)  # Will timeout
        return state

    # Call the decorated function
    result = await decorated_timeout_node(test_state)

    # Should return a state with error information
    assert result != test_state
    assert "errors" in result.metadata
    assert len(result.metadata["errors"]) == 1
    assert result.metadata["errors"][0]["type"] == "timeout"
    assert result.metadata["errors"][0]["node"] == "decorated_node"


@pytest.mark.asyncio
async def test_with_analyze_query_timeout(test_state):
    """Test that with_analyze_query_timeout decorator works correctly."""
    # Create a decorated function
    @with_analyze_query_timeout()
    async def analyze_query_node(state: AgentState) -> AgentState:
        return state

    # Call the decorated function
    result = await analyze_query_node(test_state)

    # Should return the original state
    assert result == test_state


@pytest.mark.asyncio
async def test_with_retrieve_knowledge_timeout(test_state):
    """Test that with_retrieve_knowledge_timeout decorator works correctly."""
    # Create a decorated function
    @with_retrieve_knowledge_timeout()
    async def retrieve_knowledge_node(state: AgentState) -> AgentState:
        return state

    # Call the decorated function
    result = await retrieve_knowledge_node(test_state)

    # Should return the original state
    assert result == test_state


@pytest.mark.asyncio
async def test_with_route_to_departments_timeout(test_state):
    """Test that with_route_to_departments_timeout decorator works correctly."""
    # Create a decorated function
    @with_route_to_departments_timeout()
    async def route_to_departments_node(state: AgentState) -> AgentState:
        return state

    # Call the decorated function
    result = await route_to_departments_node(test_state)

    # Should return the original state
    assert result == test_state


@pytest.mark.asyncio
async def test_with_generate_response_timeout(test_state):
    """Test that with_generate_response_timeout decorator works correctly."""
    # Create a decorated function
    @with_generate_response_timeout()
    async def generate_response_node(state: AgentState) -> AgentState:
        return state

    # Call the decorated function
    result = await generate_response_node(test_state)

    # Should return the original state
    assert result == test_state
