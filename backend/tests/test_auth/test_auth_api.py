"""
Authentication API Tests

This module contains tests for the authentication API endpoints.
"""

import pytest
from fastapi.testclient import TestClient
import jwt
from datetime import datetime

from app.main import app
from app.core.db.models import User
from app.core.auth.password import hash_password
from app.config import get_settings


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def test_db(monkeypatch):
    """Create a test database session with a test user."""
    # Mock the get_db dependency
    from app.core.db.database import get_db, SessionLocal
    
    # Create a test database session
    test_db = SessionLocal()
    
    # Override the get_db dependency
    def override_get_db():
        try:
            yield test_db
        finally:
            test_db.close()
    
    # Apply the monkeypatch
    monkeypatch.setattr("app.api.routes.auth.get_db", override_get_db)
    
    # Create a test user
    test_user = User(
        email="<EMAIL>",
        hashed_password=hash_password("password123"),
        roles=["user"],
        is_active=True
    )
    
    # Add the test user to the database
    test_db.add(test_user)
    test_db.commit()
    
    yield test_db
    
    # Clean up
    test_db.query(User).filter(User.email == "<EMAIL>").delete()
    test_db.commit()


def test_login_success(client, test_db):
    """Test successful login."""
    response = client.post(
        "/api/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "password123"
        }
    )
    
    # Check response status code
    assert response.status_code == 200
    
    # Check response data
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data
    
    # Verify the token
    token = data["access_token"]
    decoded = jwt.decode(
        token,
        get_settings().JWT_SECRET_KEY,
        algorithms=[get_settings().JWT_ALGORITHM]
    )
    
    # Check token data
    user = test_db.query(User).filter(User.email == "<EMAIL>").first()
    assert decoded["sub"] == str(user.id)
    assert decoded["email"] == "<EMAIL>"
    assert "exp" in decoded
    assert decoded["token_type"] == "access"


def test_login_invalid_credentials(client, test_db):
    """Test login with invalid credentials."""
    response = client.post(
        "/api/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "wrong_password"
        }
    )
    
    # Check response status code
    assert response.status_code == 401
    
    # Check error message
    data = response.json()
    assert "detail" in data
    assert "Invalid credentials" in data["detail"]


def test_login_inactive_user(client, test_db):
    """Test login with an inactive user."""
    # Make the test user inactive
    user = test_db.query(User).filter(User.email == "<EMAIL>").first()
    user.is_active = False
    test_db.commit()
    
    response = client.post(
        "/api/auth/login",
        data={
            "username": "<EMAIL>",
            "password": "password123"
        }
    )
    
    # Check response status code
    assert response.status_code == 401
    
    # Check error message
    data = response.json()
    assert "detail" in data
    assert "inactive" in data["detail"].lower()
    
    # Restore the user's active status
    user.is_active = True
    test_db.commit()
