"""
JWT Authentication Tests

This module contains tests for JWT authentication functionality.
"""

import time
import pytest
from datetime import timedelta
import jwt

from app.core.auth.jwt import create_access_token, decode_token
from app.config import get_settings


def test_create_access_token():
    """Test creating a JWT access token."""
    # Create a token with test data
    test_data = {"sub": "test-user", "email": "<EMAIL>"}
    token = create_access_token(test_data)
    
    # Verify the token is a string
    assert isinstance(token, str)
    
    # Decode the token and verify the data
    decoded = jwt.decode(
        token,
        get_settings().JWT_SECRET_KEY,
        algorithms=[get_settings().JWT_ALGORITHM]
    )
    
    # Check that the data is in the token
    assert decoded["sub"] == "test-user"
    assert decoded["email"] == "<EMAIL>"
    
    # Check that expiration is set
    assert "exp" in decoded
    
    # Check that token type is set
    assert decoded["token_type"] == "access"


def test_token_expiration():
    """Test that tokens expire correctly."""
    # Create a token that expires in 1 second
    test_data = {"sub": "test-user"}
    token = create_access_token(test_data, expires_delta=timedelta(seconds=1))
    
    # Token should be valid initially
    decoded = decode_token(token)
    assert decoded["sub"] == "test-user"
    
    # Wait for the token to expire
    time.sleep(2)
    
    # Token should now be expired
    with pytest.raises(Exception) as excinfo:
        decode_token(token)
    
    # Check that the error message mentions expiration
    assert "expired" in str(excinfo.value).lower()


def test_invalid_token():
    """Test that invalid tokens are rejected."""
    # Create an invalid token
    invalid_token = "invalid.token.string"
    
    # Decoding should fail
    with pytest.raises(Exception) as excinfo:
        decode_token(invalid_token)
    
    # Check that the error message mentions invalid token
    assert "invalid" in str(excinfo.value).lower()


def test_token_with_custom_expiration():
    """Test creating a token with a custom expiration time."""
    # Create a token with a custom expiration (5 minutes)
    test_data = {"sub": "test-user"}
    token = create_access_token(test_data, expires_delta=timedelta(minutes=5))
    
    # Decode the token
    decoded = decode_token(token)
    
    # Check that the token has the correct data
    assert decoded["sub"] == "test-user"
    
    # Check that the token has an expiration time
    assert "exp" in decoded
