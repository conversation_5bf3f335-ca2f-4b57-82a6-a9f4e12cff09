"""
Tests for the RAG retriever component.

This module contains tests for the retriever component of the RAG system,
including the Retriever base class, HybridRetriever, ContextWindowManager,
and QueryRewriter.
"""

import pytest
from unittest.mock import AsyncMock, patch

from app.rag.retriever import (
    Retriever,
    HybridRetriever,
    ContextWindowManager,
    QueryRewriter
)


class TestRetrieverBase:
    """Tests for the Retriever base class."""

    def test_abstract_methods(self):
        """Test that abstract methods raise TypeError when instantiating without implementation."""

        # Create a subclass that doesn't implement abstract methods
        class IncompleteRetriever(Retriever):
            pass

        # Verify that instantiating raises TypeError
        with pytest.raises(TypeError):
            IncompleteRetriever()


class TestHybridRetriever:
    """Tests for the HybridRetriever class."""

    @pytest.fixture
    def mock_query_analyzer(self):
        """Create a mock query analyzer."""
        mock = AsyncMock()
        mock.analyze.return_value = {
            "retrieval_strategy": {
                "search_type": "hybrid",
                "limit": 5,
                "filters": {"department": "finance"},
                "weights": {"vector": 0.7, "keyword": 0.3}
            }
        }
        return mock

    @pytest.fixture
    def mock_knowledge_base_service(self):
        """Create a mock knowledge base service."""
        mock = AsyncMock()
        mock.search.return_value = [
            {
                "id": "doc1",
                "text": "This is document 1",
                "metadata": {"source": "finance_docs"},
                "score": 0.9
            },
            {
                "id": "doc2",
                "text": "This is document 2",
                "metadata": {"source": "finance_docs"},
                "score": 0.8
            }
        ]
        return mock

    @pytest.fixture
    def mock_context_window_manager(self):
        """Create a mock context window manager."""
        mock = AsyncMock()
        mock.fit_to_context_window.return_value = [
            {
                "id": "doc1",
                "text": "This is document 1",
                "metadata": {"source": "finance_docs"},
                "score": 0.9
            }
        ]
        return mock

    @pytest.fixture
    def mock_query_rewriter(self):
        """Create a mock query rewriter."""
        mock = AsyncMock()
        mock.rewrite_query.return_value = "rewritten query"
        return mock

    @pytest.fixture
    def hybrid_retriever(self, mock_query_analyzer, mock_knowledge_base_service):
        """Create a HybridRetriever instance with mock dependencies."""
        return HybridRetriever(
            query_analyzer=mock_query_analyzer,
            knowledge_base_service=mock_knowledge_base_service,
            default_vector_weight=0.7,
            default_keyword_weight=0.3
        )

    @pytest.fixture
    def hybrid_retriever_with_all(
        self,
        mock_query_analyzer,
        mock_knowledge_base_service,
        mock_context_window_manager,
        mock_query_rewriter
    ):
        """Create a HybridRetriever instance with all mock dependencies."""
        return HybridRetriever(
            query_analyzer=mock_query_analyzer,
            knowledge_base_service=mock_knowledge_base_service,
            default_vector_weight=0.7,
            default_keyword_weight=0.3,
            context_window_manager=mock_context_window_manager,
            query_rewriter=mock_query_rewriter
        )

    @pytest.mark.asyncio
    async def test_retrieve_basic(self, hybrid_retriever, mock_knowledge_base_service):
        """Test basic retrieval functionality."""
        # Call retrieve
        results = await hybrid_retriever.retrieve("test query")

        # Verify knowledge base service was called
        mock_knowledge_base_service.search.assert_called_once()

        # Verify results
        assert len(results) == 2
        assert results[0]["id"] == "doc1"
        assert results[1]["id"] == "doc2"

    @pytest.mark.asyncio
    async def test_retrieve_with_strategy(self, hybrid_retriever, mock_knowledge_base_service):
        """Test retrieval with provided strategy."""
        # Create a strategy
        strategy = {
            "search_type": "vector",
            "limit": 3,
            "filters": {"department": "marketing"},
            "weights": {"vector": 0.8, "keyword": 0.2}
        }

        # Call retrieve with strategy
        await hybrid_retriever.retrieve("test query", retrieval_strategy=strategy)

        # Verify knowledge base service was called with correct parameters
        mock_knowledge_base_service.search.assert_called_once_with(
            query="test query",
            collection="knowledge",
            limit=3,
            search_type="vector",
            filters={"department": "marketing"},
            vector_weight=0.8,
            keyword_weight=0.2
        )

    @pytest.mark.asyncio
    async def test_retrieve_with_query_analyzer(
        self, hybrid_retriever, mock_query_analyzer, mock_knowledge_base_service
    ):
        """Test retrieval using query analyzer."""
        # Call retrieve without strategy
        await hybrid_retriever.retrieve("test query")

        # Verify query analyzer was called
        mock_query_analyzer.analyze.assert_called_once_with("test query")

        # Verify knowledge base service was called with strategy from analyzer
        mock_knowledge_base_service.search.assert_called_once_with(
            query="test query",
            collection="knowledge",
            limit=5,
            search_type="hybrid",
            filters={"department": "finance"},
            vector_weight=0.7,
            keyword_weight=0.3
        )

    @pytest.mark.asyncio
    async def test_retrieve_with_query_rewriter(
        self, hybrid_retriever_with_all, mock_query_rewriter, mock_knowledge_base_service
    ):
        """Test retrieval with query rewriting."""
        # Call retrieve with a strategy that includes department information
        strategy = {
            "search_type": "hybrid",
            "limit": 5,
            "filters": {"department": "finance"},
            "weights": {"vector": 0.7, "keyword": 0.3}
        }

        await hybrid_retriever_with_all.retrieve("test query", retrieval_strategy=strategy)

        # Verify query rewriter was called with context
        mock_query_rewriter.rewrite_query.assert_called_once()

        # Check that the context was passed to the rewriter
        call_args = mock_query_rewriter.rewrite_query.call_args
        assert len(call_args) >= 2  # At least query and context
        assert call_args[0][0] == "test query"  # First arg is query

        # Check that context contains department information
        context = call_args[0][1]
        assert "department" in context
        assert context["department"] == "finance"

        # Verify retrieval strategy was included in context
        assert "retrieval_strategy" in context

        # Verify knowledge base service was called with rewritten query
        mock_knowledge_base_service.search.assert_called_once()
        call_args = mock_knowledge_base_service.search.call_args[1]
        assert call_args["query"] == "rewritten query"

    @pytest.mark.asyncio
    async def test_retrieve_with_context_window_manager(
        self, hybrid_retriever_with_all, mock_context_window_manager
    ):
        """Test retrieval with context window management."""
        # Call retrieve
        results = await hybrid_retriever_with_all.retrieve("test query")

        # Verify context window manager was called
        mock_context_window_manager.fit_to_context_window.assert_called_once()

        # Verify results are from context window manager
        assert len(results) == 1
        assert results[0]["id"] == "doc1"

    @pytest.mark.asyncio
    async def test_retrieve_error_handling(self, hybrid_retriever, mock_knowledge_base_service):
        """Test error handling in retrieve method."""
        # Make knowledge base service raise an exception
        mock_knowledge_base_service.search.side_effect = Exception("Search failed")

        # Call retrieve
        results = await hybrid_retriever.retrieve("test query")

        # Verify empty results are returned
        assert results == []

    @pytest.mark.asyncio
    async def test_retrieve_query_analyzer_error(
        self, hybrid_retriever, mock_query_analyzer, mock_knowledge_base_service
    ):
        """Test handling of query analyzer errors."""
        # Make query analyzer raise an exception
        mock_query_analyzer.analyze.side_effect = Exception("Analysis failed")

        # Call retrieve
        await hybrid_retriever.retrieve("test query")

        # Verify knowledge base service was still called with default strategy
        mock_knowledge_base_service.search.assert_called_once()
        call_args = mock_knowledge_base_service.search.call_args[1]
        assert call_args["search_type"] == "hybrid"
        assert call_args["vector_weight"] == 0.7
        assert call_args["keyword_weight"] == 0.3


class TestContextWindowManager:
    """Tests for the ContextWindowManager class."""

    @pytest.fixture
    def mock_llm_adapter(self):
        """Create a mock LLM adapter."""
        mock = AsyncMock()
        mock.get_token_count.return_value = (10, None)  # (token_count, cost)
        return mock

    @pytest.fixture
    def context_window_manager(self, mock_llm_adapter):
        """Create a ContextWindowManager instance."""
        return ContextWindowManager(
            llm_adapter=mock_llm_adapter,
            max_tokens=100,
            token_buffer=20
        )

    @pytest.mark.asyncio
    async def test_count_tokens_with_tiktoken(self, context_window_manager):
        """Test token counting with tiktoken."""
        # Mock tiktoken
        with patch('app.rag.retriever.HAS_TIKTOKEN', True):
            with patch.object(context_window_manager, 'has_tiktoken', True):
                with patch.object(context_window_manager, 'tokenizer') as mock_tokenizer:
                    mock_tokenizer.encode.return_value = [1, 2, 3, 4, 5]  # 5 tokens

                    # Count tokens
                    token_count = await context_window_manager._count_tokens("test text")

                    # Verify result
                    assert token_count == 5
                    mock_tokenizer.encode.assert_called_once_with("test text")

    @pytest.mark.asyncio
    async def test_count_tokens_with_llm_adapter(self, context_window_manager, mock_llm_adapter):
        """Test token counting with LLM adapter."""
        # Mock tiktoken as unavailable
        with patch('app.rag.retriever.HAS_TIKTOKEN', False):
            with patch.object(context_window_manager, 'has_tiktoken', False):
                # Count tokens
                token_count = await context_window_manager._count_tokens("test text")

                # Verify result
                assert token_count == 10
                mock_llm_adapter.get_token_count.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_tokens_fallback(self, context_window_manager, mock_llm_adapter):
        """Test token counting fallback to character estimation."""
        # Mock tiktoken as unavailable and LLM adapter failing
        with patch('app.rag.retriever.HAS_TIKTOKEN', False):
            with patch.object(context_window_manager, 'has_tiktoken', False):
                mock_llm_adapter.get_token_count.side_effect = Exception("Token counting failed")

                # Count tokens for a 20-character string
                token_count = await context_window_manager._count_tokens("12345678901234567890")

                # Verify result (20 chars / 4 = 5 tokens)
                assert token_count == 5

    @pytest.mark.asyncio
    async def test_truncate_text_with_tiktoken(self, context_window_manager):
        """Test text truncation with tiktoken."""
        # Mock tiktoken
        with patch('app.rag.retriever.HAS_TIKTOKEN', True):
            with patch.object(context_window_manager, 'has_tiktoken', True):
                with patch.object(context_window_manager, 'tokenizer') as mock_tokenizer:
                    mock_tokenizer.encode.return_value = [1, 2, 3, 4, 5]  # 5 tokens
                    mock_tokenizer.decode.return_value = "truncated text"

                    # Truncate text
                    truncated = await context_window_manager._truncate_text("original text", 3)

                    # Verify result
                    assert truncated == "truncated text"
                    mock_tokenizer.encode.assert_called_once_with("original text")
                    mock_tokenizer.decode.assert_called_once_with([1, 2, 3])

    @pytest.mark.asyncio
    async def test_truncate_text_character_based(self, context_window_manager):
        """Test text truncation with character-based estimation."""
        # Mock tiktoken as unavailable
        with patch('app.rag.retriever.HAS_TIKTOKEN', False):
            with patch.object(context_window_manager, 'has_tiktoken', False):
                # Truncate text (3 tokens * 4 chars = 12 chars)
                truncated = await context_window_manager._truncate_text("this is a long text", 3)

                # Verify result (12 chars + ellipsis)
                assert truncated == "this is a lo..."

    @pytest.mark.asyncio
    async def test_fit_to_context_window(self, context_window_manager):
        """Test fitting documents to context window."""
        # Mock token counting to return specific values for each call
        async def count_tokens_side_effect(text):
            if text == "query":
                return 10
            elif text == "Document 1":
                return 30
            elif text == "Document 2":
                return 30
            elif text == "Document 3":
                return 30
            else:
                return 5  # Default for any other text

        # Apply the mocks
        with patch.object(context_window_manager, '_count_tokens', side_effect=count_tokens_side_effect):
            with patch.object(context_window_manager, '_truncate_text', return_value="Truncated document"):
                # Create test documents
                documents = [
                    {"id": "doc1", "text": "Document 1", "score": 0.9},
                    {"id": "doc2", "text": "Document 2", "score": 0.8},
                    {"id": "doc3", "text": "Document 3", "score": 0.7}
                ]

                # Fit to context window (100 - 10 - 20 = 70 tokens available)
                fitted = await context_window_manager.fit_to_context_window("query", documents)

                # Verify result (should fit 2 full documents and 1 truncated)
                assert len(fitted) == 3
                assert fitted[0]["id"] == "doc1"
                assert fitted[1]["id"] == "doc2"
                assert fitted[2]["id"] == "doc3"
                assert fitted[2]["truncated"] is True

    @pytest.mark.asyncio
    async def test_fit_to_context_window_with_truncation(self, context_window_manager):
        """Test fitting documents to context window with truncation."""
        # Mock token counting to return specific values for each call
        async def count_tokens_side_effect(text):
            if text == "query":
                return 10
            elif text == "Document 1":
                return 30
            elif text == "Document 2":
                return 30
            elif text == "Document 3":
                return 30
            elif text == "Truncated document":
                return 20
            else:
                return 5  # Default for any other text

        # Apply the mocks
        with patch.object(context_window_manager, '_count_tokens', side_effect=count_tokens_side_effect):
            with patch.object(context_window_manager, '_truncate_text', return_value="Truncated document"):
                # Create test documents
                documents = [
                    {"id": "doc1", "text": "Document 1", "score": 0.9},
                    {"id": "doc2", "text": "Document 2", "score": 0.8},
                    {"id": "doc3", "text": "Document 3", "score": 0.7}
                ]

                # Fit to context window (100 - 10 - 20 = 70 tokens available)
                fitted = await context_window_manager.fit_to_context_window("query", documents)

                # Verify result (should fit 2 full documents and 1 truncated: 30 + 30 + 20 = 80 tokens)
                assert len(fitted) == 3
                assert fitted[0]["id"] == "doc1"
                assert fitted[1]["id"] == "doc2"
                assert fitted[2]["id"] == "doc3"
                assert fitted[2]["text"] == "Truncated document"
                assert fitted[2]["truncated"] is True


class TestQueryRewriter:
    """Tests for the QueryRewriter class."""

    @pytest.fixture
    def mock_llm_adapter(self):
        """Create a mock LLM adapter."""
        mock = AsyncMock()
        mock.chat.return_value = "rewritten query with expanded terms"
        return mock

    @pytest.fixture
    def mock_embedding_model(self):
        """Create a mock embedding model."""
        mock = AsyncMock()
        mock.embed_query.return_value = [0.1, 0.2, 0.3]
        return mock

    @pytest.fixture
    def mock_knowledge_base_service(self):
        """Create a mock knowledge base service."""
        mock = AsyncMock()
        mock.search.return_value = [
            {
                "id": "doc1",
                "text": "This is a document about quarterly financial reports and ROI analysis",
                "metadata": {
                    "title": "Q2 Financial Analysis",
                    "department": "finance"
                },
                "score": 0.9
            }
        ]
        return mock

    @pytest.fixture
    def query_rewriter(self, mock_llm_adapter):
        """Create a basic QueryRewriter instance."""
        return QueryRewriter(llm_adapter=mock_llm_adapter)

    @pytest.fixture
    def enhanced_query_rewriter(self, mock_llm_adapter, mock_embedding_model, mock_knowledge_base_service):
        """Create an enhanced QueryRewriter instance with all dependencies."""
        return QueryRewriter(
            llm_adapter=mock_llm_adapter,
            embedding_model=mock_embedding_model,
            knowledge_base_service=mock_knowledge_base_service
        )

    @pytest.mark.asyncio
    async def test_rewrite_query_basic(self, query_rewriter, mock_llm_adapter):
        """Test basic query rewriting."""
        # Rewrite query
        rewritten = await query_rewriter.rewrite_query("original query")

        # Verify result
        assert rewritten == "rewritten query with expanded terms"
        mock_llm_adapter.chat.assert_called_once()

    @pytest.mark.asyncio
    async def test_rewrite_query_with_context(self, query_rewriter, mock_llm_adapter):
        """Test query rewriting with context."""
        # Create context
        context = {
            "department": "finance",
            "user_info": "CFO of the company"
        }

        # Rewrite query with context
        await query_rewriter.rewrite_query("budget analysis", context)

        # Verify LLM was called with context in the prompt
        assert mock_llm_adapter.chat.called
        prompt = mock_llm_adapter.chat.call_args[0][0]
        assert "Department Focus: finance" in prompt[0]["content"]
        assert "User Information: CFO of the company" in prompt[0]["content"]

    @pytest.mark.asyncio
    async def test_rule_based_enhancements(self, query_rewriter):
        """Test rule-based query enhancements."""
        # Test acronym expansion
        enhanced = await query_rewriter._apply_rule_based_enhancements("What is our ROI for Q2?")

        # Verify acronyms were expanded
        assert "return on investment" in enhanced.lower()
        assert "second quarter" in enhanced.lower() or "quarter" in enhanced.lower()

    @pytest.mark.asyncio
    async def test_context_aware_rewrite(self, enhanced_query_rewriter, mock_knowledge_base_service):
        """Test context-aware query rewriting."""
        # Create context
        context = {"department": "finance"}

        # Call context-aware rewrite
        rewritten = await enhanced_query_rewriter._context_aware_rewrite("quarterly report", context)

        # Verify knowledge base was searched
        mock_knowledge_base_service.search.assert_called_once()
        assert "department" in mock_knowledge_base_service.search.call_args[1]["filters"]

        # Verify rewritten query contains terms from the search results
        assert rewritten is not None
        assert "quarterly report" in rewritten
        assert "financial" in rewritten.lower() or "analysis" in rewritten.lower()

    @pytest.mark.asyncio
    async def test_extract_terms(self, query_rewriter):
        """Test term extraction."""
        # Extract terms from text
        terms = query_rewriter._extract_terms("This is a test document about financial analysis and quarterly reports")

        # Verify common terms are filtered out
        assert "this" not in terms
        assert "is" not in terms
        assert "a" not in terms

        # Verify meaningful terms are extracted
        assert "test" in terms or "document" in terms
        assert "financial" in terms or "analysis" in terms
        assert "quarterly" in terms or "reports" in terms

        # Verify multi-word phrases
        assert any("financial analysis" in term for term in terms) or any("quarterly reports" in term for term in terms)

    @pytest.mark.asyncio
    async def test_is_significantly_different(self, query_rewriter):
        """Test detection of significant query differences."""
        # Test identical queries
        assert not query_rewriter._is_significantly_different("test query", "test query")

        # Test length difference
        assert query_rewriter._is_significantly_different("short", "this is a much longer query with many more words")

        # Test term difference
        assert query_rewriter._is_significantly_different(
            "financial analysis for Q2",
            "marketing campaign performance metrics"
        )

        # Test similar queries
        assert not query_rewriter._is_significantly_different(
            "financial analysis for Q2",
            "Q2 financial analysis"
        )

    @pytest.mark.asyncio
    async def test_rewrite_query_error_handling(self, query_rewriter, mock_llm_adapter):
        """Test error handling in query rewriting."""
        # Make LLM adapter raise an exception
        mock_llm_adapter.chat.side_effect = Exception("Chat failed")

        # Rewrite query
        rewritten = await query_rewriter.rewrite_query("original query")

        # Verify fallback mechanism was used
        assert rewritten == "original query"

    @pytest.mark.asyncio
    async def test_fallback_rewrite(self, query_rewriter):
        """Test fallback query rewriting."""
        # Test with acronyms
        fallback = await query_rewriter._fallback_rewrite("ROI for Q2 and YOY growth")

        # Verify acronyms were expanded
        assert "return on investment" in fallback.lower()
        assert "second quarter" in fallback.lower() or "quarter" in fallback.lower()
        assert "year over year" in fallback.lower()

        # Test without acronyms
        fallback = await query_rewriter._fallback_rewrite("financial analysis")

        # Verify original query is returned
        assert fallback == "financial analysis"
