"""
Test script for vector store implementations.

This script demonstrates the basic usage of the vector store implementations.
"""

import asyncio
import random
import os
import shutil
import pytest
from typing import List

from app.rag.vector_store import (
    get_vector_store,
    FAISSVectorStore,
    ChromaVectorStore,
)


def generate_random_embeddings(count: int, dimension: int = 384) -> List[List[float]]:
    """Generate random embeddings for testing."""
    return [[random.random() for _ in range(dimension)] for _ in range(count)]


def generate_test_data(count: int, dimension: int = 384):
    """Generate test data for vector store."""
    embeddings = generate_random_embeddings(count, dimension)
    texts = [f"Document {i}" for i in range(count)]
    metadatas = [{"index": i, "category": f"cat_{i % 5}"} for i in range(count)]
    return embeddings, texts, metadatas


@pytest.mark.asyncio
async def test_faiss_vector_store():
    """Test FAISS vector store implementation."""
    print("\n=== Testing FAISS Vector Store ===")

    # Create vector store
    store = FAISSVectorStore(dimension=384)
    print(f"Created FAISS vector store with dimension {store.dimension}")

    # Add embeddings
    embeddings, texts, metadatas = generate_test_data(100)
    ids = await store.add_embeddings(embeddings, texts, metadatas)
    print(f"Added {len(ids)} embeddings to the store")

    # Check count
    print(f"Store contains {store.count} embeddings")

    # Search
    query_embedding = generate_random_embeddings(1)[0]
    results = await store.search(query_embedding, limit=5)
    print(f"Search returned {len(results)} results")
    for i, result in enumerate(results):
        print(f"  Result {i+1}: score={result['score']:.4f}, text={result['text']}")

    # Filter search
    filtered_results = await store.search(
        query_embedding, limit=5, filters={"category": "cat_1"}
    )
    print(f"Filtered search returned {len(filtered_results)} results")

    # Get by ID
    retrieved = await store.get([ids[0], ids[1]])
    print(f"Retrieved {len(retrieved)} embeddings by ID")

    # Memory usage
    memory_usage = store.estimate_memory_usage()
    print(f"Estimated memory usage: {memory_usage / 1024 / 1024:.2f} MB")

    # Save and load
    save_path = "./temp_faiss_store"
    await store.save(save_path)
    print(f"Saved vector store to {save_path}")

    # Create a new store and load
    new_store = FAISSVectorStore(dimension=384)
    await new_store.load(save_path)
    print(f"Loaded vector store from {save_path}")
    print(f"Loaded store contains {new_store.count} embeddings")

    # Clean up
    if os.path.exists(f"{save_path}.index"):
        os.remove(f"{save_path}.index")
    if os.path.exists(f"{save_path}.meta"):
        os.remove(f"{save_path}.meta")
    # Remove the directory if it was created
    save_dir = os.path.dirname(save_path)
    if save_dir and os.path.exists(save_dir) and not os.listdir(save_dir):
        os.rmdir(save_dir)

    # Delete embeddings
    await store.delete([ids[0]])
    print(f"Deleted 1 embedding, store now contains {store.count} embeddings")

    # Clear store
    await store.clear()
    print(f"Cleared store, now contains {store.count} embeddings")


@pytest.mark.asyncio
async def test_chroma_vector_store():
    """Test Chroma vector store implementation."""
    print("\n=== Testing Chroma Vector Store ===")

    # Create temporary directory for persistence
    persist_dir = "./temp_chroma_store"
    if os.path.exists(persist_dir):
        shutil.rmtree(persist_dir)
    os.makedirs(persist_dir)

    # Create vector store
    store = ChromaVectorStore(
        collection_name="test_collection",
        persist_directory=persist_dir
    )
    print(f"Created Chroma vector store with persistence at {persist_dir}")

    # Add embeddings
    embeddings, texts, metadatas = generate_test_data(100)
    ids = await store.add_embeddings(embeddings, texts, metadatas)
    print(f"Added {len(ids)} embeddings to the store")

    # Check count
    print(f"Store contains {store.count} embeddings")

    # Search
    query_embedding = generate_random_embeddings(1)[0]
    results = await store.search(query_embedding, limit=5)
    print(f"Search returned {len(results)} results")
    for i, result in enumerate(results):
        print(f"  Result {i+1}: score={result['score']:.4f}, text={result['text']}")

    # Filter search
    filtered_results = await store.search(
        query_embedding, limit=5, filters={"category": "cat_1"}
    )
    print(f"Filtered search returned {len(filtered_results)} results")

    # Get by ID
    retrieved = await store.get([ids[0], ids[1]])
    print(f"Retrieved {len(retrieved)} embeddings by ID")

    # Memory usage
    memory_usage = store.estimate_memory_usage()
    print(f"Estimated memory usage: {memory_usage / 1024 / 1024:.2f} MB")

    # Delete embeddings
    await store.delete([ids[0]])
    print(f"Deleted 1 embedding, store now contains {store.count} embeddings")

    # Clear store
    await store.clear()
    print(f"Cleared store, now contains {store.count} embeddings")

    # Clean up
    if os.path.exists(persist_dir):
        shutil.rmtree(persist_dir)


@pytest.mark.asyncio
async def test_factory_function():
    """Test the factory function."""
    print("\n=== Testing Factory Function ===")

    # Create FAISS store
    faiss_store = get_vector_store("faiss", dimension=384)
    print(f"Created {type(faiss_store).__name__} with factory function")

    # Create Chroma store
    chroma_store = get_vector_store("chroma", collection_name="test_factory")
    print(f"Created {type(chroma_store).__name__} with factory function")

    # Try invalid type
    try:
        get_vector_store("invalid_type")
    except ValueError as e:
        print(f"Correctly raised ValueError: {e}")


async def main():
    """Run all tests."""
    print("=== Vector Store Tests ===")

    await test_faiss_vector_store()
    await test_chroma_vector_store()
    await test_factory_function()

    print("\n=== All Tests Completed ===")


if __name__ == "__main__":
    asyncio.run(main())
