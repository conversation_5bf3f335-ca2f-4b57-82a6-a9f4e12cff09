"""
Test script for the enhanced QueryAnalyzer.

This script demonstrates the basic usage of the enhanced QueryAnalyzer
with embedding-based similarity and hybrid detection approach.
"""

import asyncio
import logging
import pytest
from unittest.mock import MagicMock, AsyncMock
from app.rag.query_analyzer import QueryAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@pytest.mark.asyncio
async def test_query_analyzer():
    """Test the enhanced QueryAnalyzer."""
    print("\n=== Testing Enhanced QueryAnalyzer ===")

    # Create mock LLM adapter
    async def mock_chat(_):
        return '{"finance": 0.8, "marketing": 0.6}'

    llm_adapter = MagicMock()
    llm_adapter.chat = mock_chat

    # Create mock embedding model
    async def mock_embed_query(_):
        return [0.1, 0.2, 0.3, 0.4, 0.5] * 100  # 500-dim vector

    embedding_model = MagicMock()
    embedding_model.embed_query = mock_embed_query
    embedding_model.dimension = 500
    print("Created mock embedding model")

    # Create query analyzer
    query_analyzer = QueryAnalyzer(
        llm_adapter=llm_adapter,
        embedding_model=embedding_model
    )

    # Wait for embeddings to initialize if needed
    if embedding_model:
        print("Waiting for embeddings to initialize...")
        await asyncio.sleep(2)

    # Test queries
    test_queries = [
        "What is our marketing budget for Q2?",
        "How much revenue did we generate last quarter?",
        "Can you explain our social media strategy?",
        "What are the projected expenses for next year?",
        "How does our brand positioning compare to competitors?"
    ]

    for query in test_queries:
        print(f"\nQuery: {query}")

        # Analyze query
        result = await query_analyzer.analyze(query)

        # Print results
        print(f"Query Type: {result['query_type']}")

        # Print intent
        print("\nIntent:")
        print(f"  Primary: {result['intent']['primary']} (confidence: {result['intent']['confidence']:.2f})")
        if result['intent']['secondary']:
            print(f"  Secondary: {', '.join(result['intent']['secondary'])}")

        # Print departments
        print("\nRelevant Departments:")
        for dept in result["relevant_departments"]:
            methods_str = ", ".join(dept.get("methods", []))
            print(f"  - {dept['id']} (confidence: {dept['confidence']:.2f}, methods: {methods_str})")
            if "matched_keywords" in dept:
                print(f"    Matched keywords: {', '.join(dept['matched_keywords'])}")

        # Print sections
        if result["relevant_sections"]:
            print("\nRelevant Sections:")
            for section in result["relevant_sections"]:
                print(f"  - {section['id']} (confidence: {section['confidence']:.2f})")
                print(f"    Description: {section['description']}")

        # Print retrieval strategy
        print("\nRetrieval Strategy:")
        strategy = result["retrieval_strategy"]
        print(f"  Search Type: {strategy['search_type']}")
        print(f"  Limit: {strategy['limit']}")
        print(f"  Reranking: {strategy['reranking']}")
        print(f"  Weights: vector={strategy['weights']['vector']:.2f}, keyword={strategy['weights']['keyword']:.2f}")

        # Print preprocessed text
        print("\nPreprocessed Text:")
        print(f"  Processed: {result['preprocessed']['processed_text']}")
        print(f"  Important Terms: {', '.join(result['preprocessed']['important_terms'][:5])}")

async def main():
    """Run all tests."""
    await test_query_analyzer()

if __name__ == "__main__":
    asyncio.run(main())
