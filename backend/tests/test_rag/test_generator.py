"""
Tests for the RAG generator component.

This module contains tests for the generator component of the RAG system,
including the Generator base class and RAGGenerator implementation.
"""

import pytest
from unittest.mock import AsyncMock

from app.rag.generator import Generator, RAGGenerator


class TestGeneratorBase:
    """Tests for the Generator base class."""

    def test_abstract_methods(self):
        """Test that abstract methods raise TypeError when instantiating without implementation."""

        # Create a subclass that doesn't implement abstract methods
        class IncompleteGenerator(Generator):
            pass

        # Verify that instantiating raises TypeError
        with pytest.raises(TypeError):
            IncompleteGenerator()


class TestRAGGenerator:
    """Tests for the RAGGenerator class."""

    @pytest.fixture
    def mock_llm_adapter(self):
        """Create a mock LLM adapter."""
        mock = AsyncMock()
        mock.chat.return_value = "Generated response with citations [1], [2]."
        return mock

    @pytest.fixture
    def mock_query_rewriter(self):
        """Create a mock query rewriter."""
        mock = AsyncMock()
        mock.rewrite_query.return_value = "enhanced marketing strategy query with additional terms"
        return mock

    @pytest.fixture
    def rag_generator(self, mock_llm_adapter):
        """Create a RAGGenerator instance with mock dependencies."""
        return RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=True,
            citation_format="inline"
        )

    @pytest.fixture
    def rag_generator_with_rewriter(self, mock_llm_adapter, mock_query_rewriter):
        """Create a RAGGenerator instance with query rewriter."""
        return RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=True,
            citation_format="inline",
            query_rewriter=mock_query_rewriter
        )

    @pytest.fixture
    def sample_context(self):
        """Create a sample context for testing."""
        return [
            {
                "id": "doc1",
                "text": "Our Q2 marketing strategy focuses on digital channels.",
                "metadata": {
                    "source": "Marketing Plan",
                    "title": "Q2 Marketing Strategy",
                    "section": "Overview",
                    "department": "marketing"
                },
                "score": 0.92
            },
            {
                "id": "doc2",
                "text": "The Q2 campaign will target millennials and Gen Z.",
                "metadata": {
                    "source": "Campaign Brief",
                    "title": "Q2 Campaign Targeting",
                    "section": "Audience Strategy",
                    "department": "marketing"
                },
                "score": 0.85
            }
        ]

    def test_init_with_invalid_citation_format(self, mock_llm_adapter):
        """Test initialization with invalid citation format."""
        # Create generator with invalid citation format
        generator = RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=True,
            citation_format="invalid"
        )

        # Verify that citation format is set to default
        assert generator.citation_format == "inline"

    def test_format_context_empty(self, rag_generator):
        """Test context formatting with empty context."""
        # Format empty context
        formatted = rag_generator._format_context([])

        # Verify result
        assert formatted == "No relevant information found."

    def test_format_context(self, rag_generator, sample_context):
        """Test context formatting with sample context."""
        # Format context
        formatted = rag_generator._format_context(sample_context)

        # Verify result contains document text and citations
        assert "Document 1:" in formatted
        assert "Our Q2 marketing strategy focuses on digital channels." in formatted
        assert "Citation: [1] Q2 Marketing Strategy, Overview (Source: Marketing Plan)" in formatted

        assert "Document 2:" in formatted
        assert "The Q2 campaign will target millennials and Gen Z." in formatted
        assert "Citation: [2] Q2 Campaign Targeting, Audience Strategy (Source: Campaign Brief)" in formatted

    def test_format_context_with_truncated_doc(self, rag_generator):
        """Test context formatting with truncated document."""
        # Create context with truncated document
        context = [
            {
                "id": "doc1",
                "text": "Truncated document text.",
                "metadata": {"title": "Truncated Doc"},
                "truncated": True
            }
        ]

        # Format context
        formatted = rag_generator._format_context(context)

        # Verify result indicates truncation
        assert "Document 1: [truncated]" in formatted

    def test_format_context_with_missing_metadata(self, rag_generator):
        """Test context formatting with missing metadata."""
        # Create context with missing metadata
        context = [
            {
                "id": "doc1",
                "text": "Document with missing metadata."
            }
        ]

        # Format context
        formatted = rag_generator._format_context(context)

        # Verify result handles missing metadata
        assert "Document 1:" in formatted
        assert "Document with missing metadata." in formatted
        assert "Citation: [1] Untitled (Source: Unknown)" in formatted

    def test_construct_prompt_with_citations(self, rag_generator):
        """Test prompt construction with citations."""
        # Construct prompt
        prompt = rag_generator._construct_prompt(
            query="What is our marketing strategy?",
            formatted_context="Document 1: Sample text\nCitation: [1] Sample Doc"
        )

        # Verify prompt structure
        assert len(prompt) == 2
        assert prompt[0]["role"] == "system"
        assert prompt[1]["role"] == "user"

        # Verify system message includes citation instructions
        assert "Include citations" in prompt[0]["content"]
        assert "Use inline citations" in prompt[0]["content"]

        # Verify user message includes query and context
        assert "Question: What is our marketing strategy?" in prompt[1]["content"]
        assert "Document 1: Sample text" in prompt[1]["content"]

    def test_construct_prompt_without_citations(self, mock_llm_adapter):
        """Test prompt construction without citations."""
        # Create generator without citations
        generator = RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=False
        )

        # Construct prompt
        prompt = generator._construct_prompt(
            query="What is our marketing strategy?",
            formatted_context="Document 1: Sample text\nCitation: [1] Sample Doc"
        )

        # Verify system message does not include citation instructions
        assert "Include citations" not in prompt[0]["content"]

    @pytest.mark.asyncio
    async def test_generate(self, rag_generator, mock_llm_adapter, sample_context):
        """Test response generation."""
        # Generate response
        response = await rag_generator.generate(
            query="What is our marketing strategy?",
            context=sample_context
        )

        # Verify LLM adapter was called
        mock_llm_adapter.chat.assert_called_once()

        # Verify response
        assert response == "Generated response with citations [1], [2]."

    @pytest.mark.asyncio
    async def test_generate_with_stream(self, rag_generator, mock_llm_adapter, sample_context):
        """Test streaming response generation."""
        # Set up mock streaming response
        mock_stream = AsyncMock()
        mock_llm_adapter.chat.return_value = mock_stream

        # Generate streaming response
        stream = await rag_generator.generate(
            query="What is our marketing strategy?",
            context=sample_context,
            stream=True
        )

        # Verify LLM adapter was called with stream=True
        mock_llm_adapter.chat.assert_called_once()
        call_kwargs = mock_llm_adapter.chat.call_args[1]
        assert call_kwargs.get("stream") is True

        # Verify stream is returned
        assert stream == mock_stream

    @pytest.mark.asyncio
    async def test_generate_with_query_rewriter(
        self, rag_generator_with_rewriter, mock_query_rewriter, mock_llm_adapter, sample_context
    ):
        """Test response generation with query rewriter."""
        # Generate response
        await rag_generator_with_rewriter.generate(
            query="What is our marketing strategy?",
            context=sample_context
        )

        # Verify query rewriter was called
        mock_query_rewriter.rewrite_query.assert_called_once()

        # Check that the query was passed to the rewriter
        call_args = mock_query_rewriter.rewrite_query.call_args
        assert call_args[0][0] == "What is our marketing strategy?"

        # Check that context was extracted and passed to the rewriter
        context_arg = call_args[0][1]
        assert "department" in context_arg

        # Verify LLM adapter was called with enhanced query
        mock_llm_adapter.chat.assert_called_once()
        prompt = mock_llm_adapter.chat.call_args[0][0]
        assert "Enhanced Question: enhanced marketing strategy query with additional terms" in prompt[1]["content"]

    @pytest.mark.asyncio
    async def test_generate_with_query_rewriter_error(
        self, rag_generator_with_rewriter, mock_query_rewriter, mock_llm_adapter, sample_context
    ):
        """Test response generation with query rewriter error."""
        # Make query rewriter raise an exception
        mock_query_rewriter.rewrite_query.side_effect = Exception("Rewriting failed")

        # Generate response
        await rag_generator_with_rewriter.generate(
            query="What is our marketing strategy?",
            context=sample_context
        )

        # Verify LLM adapter was still called with original query
        mock_llm_adapter.chat.assert_called_once()
        prompt = mock_llm_adapter.chat.call_args[0][0]
        assert "Question: What is our marketing strategy?" in prompt[1]["content"]

    @pytest.mark.asyncio
    async def test_generate_error_handling(self, rag_generator, mock_llm_adapter, sample_context):
        """Test error handling in generate method."""
        # Make LLM adapter raise an exception
        mock_llm_adapter.chat.side_effect = Exception("Chat failed")

        # Generate response
        response = await rag_generator.generate(
            query="What is our marketing strategy?",
            context=sample_context
        )

        # Verify error message is returned
        assert "I encountered an error" in response
        assert "Chat failed" in response

    @pytest.fixture
    def enhanced_rag_generator(self, mock_llm_adapter):
        """Create an enhanced RAGGenerator instance with mock dependencies."""
        return RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=True,
            citation_format="inline",
            max_context_tokens=2000,
            token_buffer=500,
            structured_output=True,
            fallback_to_summarization=True
        )

    @pytest.mark.asyncio
    async def test_context_window_management(self, enhanced_rag_generator):
        """Test context window management."""
        # Create a very large context that exceeds the token limit
        large_context = []
        for i in range(10):
            doc = {
                "id": f"doc{i}",
                "text": f"This is document {i} with a lot of content " * 50,  # Make it large
                "metadata": {
                    "source": "Test Source",
                    "title": f"Document {i}"
                }
            }
            # Manually set token count to ensure it exceeds the limit
            doc["token_count"] = 500  # Each doc is 500 tokens
            large_context.append(doc)

        # Manage context window
        managed_context, context_info = enhanced_rag_generator._manage_context_window(
            large_context,
            model_name="gpt-3.5-turbo"
        )

        # Verify context was truncated
        assert context_info["truncated"] == True
        assert context_info["original_docs"] > context_info["used_docs"]
        assert len(managed_context) < len(large_context)

    @pytest.mark.asyncio
    async def test_structured_output_instructions(self, enhanced_rag_generator):
        """Test adding structured output instructions."""
        # Create a basic prompt
        prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is the capital of France?"}
        ]

        # Add structured output instructions
        modified_prompt = enhanced_rag_generator._add_structured_output_instructions(prompt)

        # Verify instructions were added
        assert "Structure your response" in modified_prompt[0]["content"]
        assert "headings" in modified_prompt[0]["content"]

    @pytest.mark.asyncio
    async def test_ensure_citations(self, enhanced_rag_generator, sample_context):
        """Test ensuring citations are included in the response."""
        # Create a response without citations
        response = "Paris is the capital of France."

        # Ensure citations
        modified_response = enhanced_rag_generator._ensure_citations(response, sample_context)

        # Verify citations were added
        assert "References" in modified_response

    @pytest.mark.asyncio
    async def test_add_fallback_notices(self, enhanced_rag_generator):
        """Test adding fallback notices."""
        # Create a response
        response = "Paris is the capital of France."

        # Create fallbacks and context info
        applied_fallbacks = ["context_truncation", "query_enhancement"]
        context_info = {"original_docs": 5, "used_docs": 2}

        # Add fallback notices
        modified_response = enhanced_rag_generator._add_fallback_notices(
            response,
            applied_fallbacks,
            context_info
        )

        # Verify notices were added
        assert "Note:" in modified_response
        assert "2 of 5" in modified_response
