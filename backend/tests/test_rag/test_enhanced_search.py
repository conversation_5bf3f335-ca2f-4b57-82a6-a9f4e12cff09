"""
Tests for the enhanced search capabilities in the RAG system.

This module tests the enhanced hybrid search and reranking capabilities
of the knowledge base service.
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch

from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService
from app.rag.reranker import <PERSON>EncoderReranker, DummyReranker


# Firebase client fixture removed - no longer needed


@pytest.fixture
def mock_embedding_model():
    """Create a mock embedding model."""
    model = MagicMock()
    model.embed_query.return_value = asyncio.Future()
    model.embed_query.return_value.set_result([0.1, 0.2, 0.3, 0.4])
    model.embed_documents.return_value = asyncio.Future()
    model.embed_documents.return_value.set_result([[0.1, 0.2, 0.3, 0.4], [0.2, 0.3, 0.4, 0.5]])
    return model


@pytest.fixture
def mock_vector_store():
    """Create a mock vector store."""
    store = MagicMock()
    store.search.return_value = asyncio.Future()
    store.search.return_value.set_result([
        {
            "id": "doc1_0",
            "text": "This is the introduction section.",
            "score": 0.9,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section1",
                "section_title": "Introduction",
                "department": "marketing"
            }
        },
        {
            "id": "doc1_1",
            "text": "This is the details section.",
            "score": 0.8,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section2",
                "section_title": "Details",
                "department": "marketing"
            }
        }
    ])
    return store


@pytest.fixture
def mock_reranker():
    """Create a mock reranker."""
    reranker = MagicMock()
    reranker.rerank.return_value = asyncio.Future()
    reranker.rerank.return_value.set_result([
        {
            "id": "doc1_1",
            "text": "This is the details section.",
            "score": 0.95,
            "cross_encoder_score": 0.95,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section2",
                "section_title": "Details",
                "department": "marketing"
            }
        },
        {
            "id": "doc1_0",
            "text": "This is the introduction section.",
            "score": 0.85,
            "cross_encoder_score": 0.85,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section1",
                "section_title": "Introduction",
                "department": "marketing"
            }
        }
    ])
    return reranker


@pytest.fixture
def knowledge_base_service(mock_embedding_model, mock_vector_store, mock_reranker):
    """Create a knowledge base service with mocked dependencies."""
    return PgVectorKnowledgeBaseService(
        embedding_model=mock_embedding_model,
        vector_store=mock_vector_store,
        reranker=mock_reranker,
        use_reranking=True,
        reranking_candidates=10
    )


@pytest.mark.asyncio
async def test_hybrid_search_with_reranking(knowledge_base_service):
    """Test hybrid search with reranking."""
    # Perform hybrid search with reranking
    results = await knowledge_base_service.search(
        query="test query",
        search_type="hybrid",
        limit=5,
        use_reranking=True
    )

    # Verify that reranking was applied
    assert len(results) > 0
    assert "cross_encoder_score" in results[0]

    # Verify that the results are sorted by the reranked score
    assert results[0]["score"] >= results[1]["score"] if len(results) > 1 else True


@pytest.mark.asyncio
async def test_hybrid_search_with_section_boost(knowledge_base_service):
    """Test hybrid search with section boosting."""
    # Define section boost factors
    section_boost = {
        "introduction": 1.5,
        "details": 1.0
    }

    # Perform hybrid search with section boosting
    results = await knowledge_base_service.search(
        query="test query",
        search_type="hybrid",
        limit=5,
        section_boost=section_boost
    )

    # Verify that section boosting was applied
    assert len(results) > 0
    assert "boosted" in results[0] if "introduction" in results[0]["metadata"].get("section_title", "").lower() else True


@pytest.mark.asyncio
async def test_section_aware_search(knowledge_base_service):
    """Test section-aware search."""
    # Perform section-aware search
    results = await knowledge_base_service.search_sections(
        query="test query",
        limit=5
    )

    # Verify that section information is included
    assert len(results) > 0
    assert "section_title" in results[0]["metadata"]
    assert "section_id" in results[0]["metadata"]


@pytest.mark.asyncio
async def test_dynamic_weighting(knowledge_base_service):
    """Test dynamic weighting based on query characteristics."""
    # Test with a short query (should favor keyword search)
    short_query_results = await knowledge_base_service.search(
        query="test",
        search_type="hybrid",
        limit=5
    )

    # Test with a long query (should favor vector search)
    long_query_results = await knowledge_base_service.search(
        query="this is a longer query with multiple words to test dynamic weighting",
        search_type="hybrid",
        limit=5
    )

    # We can't directly verify the weights, but we can check that results are returned
    assert len(short_query_results) > 0
    assert len(long_query_results) > 0


@pytest.mark.asyncio
async def test_diversity_filtering(knowledge_base_service):
    """Test diversity filtering to avoid too many results from the same source."""
    # Mock the vector store to return multiple results from the same document
    knowledge_base_service.vector_store.search.return_value = asyncio.Future()
    knowledge_base_service.vector_store.search.return_value.set_result([
        {
            "id": "doc1_0",
            "text": "This is the introduction section.",
            "score": 0.9,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section1",
                "section_title": "Introduction",
                "department": "marketing"
            }
        },
        {
            "id": "doc1_1",
            "text": "This is the details section.",
            "score": 0.8,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section2",
                "section_title": "Details",
                "department": "marketing"
            }
        },
        {
            "id": "doc2_0",
            "text": "This is another document.",
            "score": 0.7,
            "metadata": {
                "document_id": "doc2",
                "document_title": "Another Document",
                "section_id": "section1",
                "section_title": "Introduction",
                "department": "marketing"
            }
        }
    ])

    # Perform search with diversity filtering
    results = await knowledge_base_service.search(
        query="test query",
        search_type="vector",
        limit=3
    )

    # Verify that diversity filtering was applied
    # We should have results from both documents
    doc_ids = set(result["metadata"]["document_id"] for result in results)
    assert len(doc_ids) >= 2  # Should have at least 2 different document IDs


@pytest.mark.asyncio
async def test_cross_encoder_reranker():
    """Test the CrossEncoderReranker directly."""
    # Create a dummy reranker for testing
    reranker = DummyReranker()

    # Create some test results
    results = [
        {
            "id": "doc1_0",
            "text": "This is the introduction section.",
            "score": 0.9,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section1",
                "section_title": "Introduction"
            }
        },
        {
            "id": "doc1_1",
            "text": "This is the details section.",
            "score": 0.8,
            "metadata": {
                "document_id": "doc1",
                "document_title": "Test Document",
                "section_id": "section2",
                "section_title": "Details"
            }
        }
    ]

    # Rerank the results
    reranked_results = await reranker.rerank(
        query="test query",
        results=results,
        top_n=2
    )

    # Verify that the reranker returns the original results
    assert reranked_results == results


@pytest.mark.asyncio
async def test_error_handling(knowledge_base_service):
    """Test error handling during search."""
    # Make the vector store raise an exception
    knowledge_base_service.vector_store.search.side_effect = Exception("Test error")

    # Perform search, which should fall back to keyword search
    results = await knowledge_base_service.search(
        query="test query",
        search_type="hybrid",
        limit=5
    )

    # Verify that we got results despite the error
    assert len(results) > 0
