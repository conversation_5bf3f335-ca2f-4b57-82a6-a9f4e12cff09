"""
Tests for the RAG Embedding Models

WHAT IS THIS FILE?
----------------
This file contains tests that verify our embedding models work correctly.
Think of it like a quality control system that makes sure each part of our
embedding system functions as expected.

WHY DO WE NEED TESTS?
------------------
1. Catch bugs early: Tests help us find problems before they affect users
2. Document behavior: Tests show how code is supposed to work
3. Enable refactoring: Tests give confidence when changing code
4. Prevent regressions: Tests ensure fixed bugs stay fixed

HOW ARE THE TESTS ORGANIZED?
-------------------------
We have separate test classes for each component:

1. TestEmbeddingModel:
   - Tests that the abstract base class properly enforces its contract
   - Makes sure incomplete implementations can't be instantiated

2. TestHuggingFaceEmbedding:
   - Tests the local embedding model implementation
   - Verifies it correctly processes queries and documents
   - Checks special handling for instructor models
   - Confirms dimension property works correctly

3. TestOpenAIEmbedding:
   - Tests the API-based embedding model implementation
   - Verifies it correctly calls the OpenAI API
   - Checks custom dimension handling
   - Confirms proper error handling for API issues

4. TestEmbeddingFactory:
   - Tests the factory that creates embedding models
   - Verifies it creates the correct type based on provider
   - Tests fallback behavior when dependencies are missing
   - Checks error handling for unknown providers

TESTING TECHNIQUES USED:
---------------------
1. Mocking External Dependencies:
   We use @patch and MagicMock to replace real external services (like OpenAI's API)
   with fake versions we can control. This makes tests:
   - Fast (no real API calls or model loading)
   - Reliable (not affected by network issues)
   - Predictable (we control exactly what the "API" returns)

2. Testing Async Code:
   We use pytest.mark.asyncio to properly test async functions, ensuring
   they work correctly with Python's async/await system.

3. Testing Error Handling:
   We use pytest.raises to verify that our code raises appropriate
   errors when things go wrong (like missing dependencies).

4. Table-Driven Tests:
   Some tests use similar patterns with different inputs, showing
   how the code handles various scenarios.

HOW TO RUN THESE TESTS:
--------------------
Run all tests:
```
pytest backend/tests/test_rag_embeddings.py -v
```

Run a specific test class:
```
pytest backend/tests/test_rag_embeddings.py::TestHuggingFaceEmbedding -v
```

Run a specific test:
```
pytest backend/tests/test_rag_embeddings.py::TestHuggingFaceEmbedding::test_embed_query -v
```
"""

import os
import pytest
import numpy as np
from unittest.mock import patch, MagicMock, AsyncMock

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.rag.embeddings import (
    EmbeddingModel,
    HuggingFaceEmbedding,
    OpenAIEmbedding,
    EmbeddingFactory,
)


class TestEmbeddingModel:
    """
    Tests for the EmbeddingModel abstract base class.

    WHAT DOES THIS TEST?
    -----------------
    This test class verifies that the EmbeddingModel abstract base class
    correctly enforces its contract - meaning that any class that inherits
    from it MUST implement all the required methods.

    It's like testing that a building inspector actually checks for all
    required safety features before approving a building.
    """

    def test_abstract_methods(self):
        """
        Test that Python prevents instantiating incomplete embedding models.

        WHAT THIS TESTS:
        -------------
        This test verifies that Python correctly prevents us from creating
        an instance of a class that inherits from EmbeddingModel but doesn't
        implement all the required methods.

        HOW IT WORKS:
        ----------
        1. We create a new class (IncompleteEmbedding) that inherits from
           EmbeddingModel but doesn't implement any of its abstract methods

        2. We try to create an instance of this incomplete class

        3. We verify that Python raises a TypeError, which is the standard
           error when trying to instantiate a class with unimplemented
           abstract methods

        WHY THIS MATTERS:
        --------------
        This test ensures that our abstract base class is working correctly
        to enforce the contract. If this test passes, we know that Python
        will prevent developers from accidentally creating incomplete
        embedding model implementations.
        """
        # Create a subclass that inherits from EmbeddingModel but doesn't
        # implement any of the required methods (embed_query, embed_documents, dimension)
        class IncompleteEmbedding(EmbeddingModel):
            pass  # This class is intentionally empty - it's missing all required methods

        # Try to instantiate the incomplete class - this should fail
        # with a TypeError because abstract methods aren't implemented
        with pytest.raises(TypeError):
            IncompleteEmbedding()  # This should raise TypeError


class TestHuggingFaceEmbedding:
    """Tests for the HuggingFaceEmbedding class."""

    @pytest.mark.asyncio
    @patch("sentence_transformers.SentenceTransformer", autospec=True)
    async def test_embed_query(self, mock_sentence_transformer):
        """Test embedding a single query."""
        # Setup mock
        mock_model = MagicMock()
        mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
        mock_model.get_sentence_embedding_dimension.return_value = 3
        mock_sentence_transformer.return_value = mock_model

        # Create embedding model
        embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

        # Embed query
        result = await embedding_model.embed_query("test query")

        # Verify result
        assert isinstance(result, list)
        assert len(result) == 3
        assert result == [0.1, 0.2, 0.3]
        mock_model.encode.assert_called_once()

    @pytest.mark.asyncio
    @patch("sentence_transformers.SentenceTransformer", autospec=True)
    async def test_embed_documents(self, mock_sentence_transformer):
        """Test embedding multiple documents."""
        # Setup mock
        mock_model = MagicMock()
        mock_model.encode.return_value = np.array([[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]])
        mock_model.get_sentence_embedding_dimension.return_value = 3
        mock_sentence_transformer.return_value = mock_model

        # Create embedding model
        embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

        # Embed documents
        result = await embedding_model.embed_documents(["doc1", "doc2"])

        # Verify result
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0] == [0.1, 0.2, 0.3]
        assert result[1] == [0.4, 0.5, 0.6]
        mock_model.encode.assert_called_once()

    @pytest.mark.asyncio
    @patch("sentence_transformers.SentenceTransformer", autospec=True)
    async def test_dimension_property(self, mock_sentence_transformer):
        """Test the dimension property."""
        # Setup mock
        mock_model = MagicMock()
        mock_model.get_sentence_embedding_dimension.return_value = 768
        mock_sentence_transformer.return_value = mock_model

        # Create embedding model
        embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

        # Get dimension
        result = embedding_model.dimension

        # Verify result
        assert result == 768

    @pytest.mark.asyncio
    @patch("sentence_transformers.SentenceTransformer", autospec=True)
    async def test_instructor_model(self, mock_sentence_transformer):
        """Test embedding with an instructor model."""
        # Setup mock
        mock_model = MagicMock()
        mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
        mock_model.get_sentence_embedding_dimension.return_value = 3
        mock_sentence_transformer.return_value = mock_model

        # Create embedding model with instructor in the name
        embedding_model = HuggingFaceEmbedding(model_name="instructor-xl")

        # Embed query
        await embedding_model.embed_query("test query")

        # Verify that the query was formatted for instructor models
        args, _ = mock_model.encode.call_args
        assert "Represent this sentence for retrieval:" in args[0]


class TestOpenAIEmbedding:
    """Tests for the OpenAIEmbedding class."""

    @pytest.mark.asyncio
    @patch("openai.AsyncOpenAI", autospec=True)
    async def test_embed_query(self, mock_async_openai):
        """Test embedding a single query."""
        # Setup mock
        mock_client = MagicMock()
        mock_embeddings = MagicMock()
        mock_client.embeddings = mock_embeddings
        mock_async_openai.return_value = mock_client

        # Mock response
        mock_data = MagicMock()
        mock_data.embedding = [0.1, 0.2, 0.3]
        mock_response = MagicMock()
        mock_response.data = [mock_data]

        # Make the create method an AsyncMock
        mock_embeddings.create = AsyncMock(return_value=mock_response)

        # Create embedding model
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            embedding_model = OpenAIEmbedding(model_name="text-embedding-3-small")

        # Embed query
        result = await embedding_model.embed_query("test query")

        # Verify result
        assert isinstance(result, list)
        assert len(result) == 3
        assert result == [0.1, 0.2, 0.3]
        mock_embeddings.create.assert_called_once()

    @pytest.mark.asyncio
    @patch("openai.AsyncOpenAI", autospec=True)
    async def test_embed_documents(self, mock_async_openai):
        """Test embedding multiple documents."""
        # Setup mock
        mock_client = MagicMock()
        mock_embeddings = MagicMock()
        mock_client.embeddings = mock_embeddings
        mock_async_openai.return_value = mock_client

        # Mock response
        mock_data1 = MagicMock()
        mock_data1.embedding = [0.1, 0.2, 0.3]
        mock_data1.index = 0
        mock_data2 = MagicMock()
        mock_data2.embedding = [0.4, 0.5, 0.6]
        mock_data2.index = 1
        mock_response = MagicMock()
        mock_response.data = [mock_data1, mock_data2]

        # Make the create method an AsyncMock
        mock_embeddings.create = AsyncMock(return_value=mock_response)

        # Create embedding model
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            embedding_model = OpenAIEmbedding(model_name="text-embedding-3-small")

        # Embed documents
        result = await embedding_model.embed_documents(["doc1", "doc2"])

        # Verify result
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0] == [0.1, 0.2, 0.3]
        assert result[1] == [0.4, 0.5, 0.6]
        mock_embeddings.create.assert_called_once()

    @pytest.mark.asyncio
    @patch("openai.AsyncOpenAI", autospec=True)
    async def test_dimension_property(self, mock_async_openai):
        """Test the dimension property."""
        # Setup mock
        mock_client = MagicMock()
        mock_async_openai.return_value = mock_client

        # Create embedding model
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            embedding_model = OpenAIEmbedding(model_name="text-embedding-3-small")

        # Get dimension
        result = embedding_model.dimension

        # Verify result
        assert result == 1024

    @pytest.mark.asyncio
    @patch("openai.AsyncOpenAI", autospec=True)
    async def test_custom_dimensions(self, mock_async_openai):
        """Test embedding with custom dimensions."""
        # Setup mock
        mock_client = MagicMock()
        mock_embeddings = MagicMock()
        mock_client.embeddings = mock_embeddings
        mock_async_openai.return_value = mock_client

        # Mock response
        mock_data = MagicMock()
        mock_data.embedding = [0.1, 0.2, 0.3]
        mock_response = MagicMock()
        mock_response.data = [mock_data]

        # Make the create method an AsyncMock
        mock_embeddings.create = AsyncMock(return_value=mock_response)

        # Create embedding model with custom dimensions
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            embedding_model = OpenAIEmbedding(
                model_name="text-embedding-3-small", dimensions=512
            )

        # Embed query
        await embedding_model.embed_query("test query")

        # Verify that dimensions were passed to the API
        _, kwargs = mock_embeddings.create.call_args  # Use _ for unused positional args
        assert kwargs["dimensions"] == 512  # Check that dimensions parameter was passed correctly


class TestEmbeddingFactory:
    """Tests for the EmbeddingFactory class."""

    @patch("app.rag.embeddings.HuggingFaceEmbedding", autospec=True)
    def test_create_huggingface(self, mock_huggingface):
        """Test creating a HuggingFace embedding model."""
        # Setup mock
        mock_huggingface.return_value = MagicMock()

        # Create embedding model
        result = EmbeddingFactory.create(provider="huggingface")

        # Verify result
        assert result == mock_huggingface.return_value
        mock_huggingface.assert_called_once()

    @patch("app.rag.embeddings.OpenAIEmbedding", autospec=True)
    def test_create_openai(self, mock_openai):
        """Test creating an OpenAI embedding model."""
        # Setup mock
        mock_openai.return_value = MagicMock()

        # Create embedding model
        result = EmbeddingFactory.create(provider="openai")

        # Verify result
        assert result == mock_openai.return_value
        mock_openai.assert_called_once()

    @patch("app.rag.embeddings.HuggingFaceEmbedding", autospec=True)
    def test_create_unknown_provider_with_fallback(self, mock_huggingface):
        """Test creating an embedding model with an unknown provider and fallback enabled."""
        # Setup mock
        mock_huggingface.return_value = MagicMock()

        # Create embedding model
        result = EmbeddingFactory.create(provider="unknown", fallback=True)

        # Verify result
        assert result == mock_huggingface.return_value
        mock_huggingface.assert_called_once()

    def test_create_unknown_provider_without_fallback(self):
        """Test creating an embedding model with an unknown provider and fallback disabled."""
        # Create embedding model
        with pytest.raises(ValueError):
            EmbeddingFactory.create(provider="unknown", fallback=False)

    @patch("app.rag.embeddings.HuggingFaceEmbedding", side_effect=ImportError)
    @patch("app.rag.embeddings.OpenAIEmbedding", autospec=True)
    def test_fallback_to_openai(self, mock_openai, mock_huggingface):
        """Test fallback to OpenAI when HuggingFace is not available."""
        # Setup mock
        mock_openai.return_value = MagicMock()

        # Create embedding model
        result = EmbeddingFactory.create(provider="huggingface", fallback=True)

        # Verify result
        assert result == mock_openai.return_value
        mock_huggingface.assert_called_once()
        mock_openai.assert_called_once()

    @patch("app.rag.embeddings.OpenAIEmbedding", side_effect=ImportError)
    @patch("app.rag.embeddings.HuggingFaceEmbedding", autospec=True)
    def test_fallback_to_huggingface(self, mock_huggingface, mock_openai):
        """Test fallback to HuggingFace when OpenAI is not available."""
        # Setup mock
        mock_huggingface.return_value = MagicMock()

        # Create embedding model
        result = EmbeddingFactory.create(provider="openai", fallback=True)

        # Verify result
        assert result == mock_huggingface.return_value
        mock_openai.assert_called_once()
        mock_huggingface.assert_called_once()

    @patch("app.rag.embeddings.HuggingFaceEmbedding", side_effect=ImportError)
    @patch("app.rag.embeddings.OpenAIEmbedding", side_effect=ImportError)
    def test_no_providers_available(self, mock_openai, mock_huggingface):
        """Test error when no providers are available."""
        # Create embedding model
        with pytest.raises(ImportError):
            EmbeddingFactory.create(provider="huggingface", fallback=True)

        # Verify calls
        mock_huggingface.assert_called_once()
        mock_openai.assert_called_once()
