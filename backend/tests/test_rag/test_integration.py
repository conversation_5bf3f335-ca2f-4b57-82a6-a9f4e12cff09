import pytest
import asyncio
from unittest.mock import MagicMock, patch
from typing import Dict, List, Any

from app.rag.generator import RAGGenerator
from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService
from app.agents.department_agent import DepartmentAgent, ResponseStyle, CitationFormat


class TestRAGIntegration:
    """Integration tests for the RAG system components."""

    @pytest.fixture
    def mock_llm_adapter(self):
        """Create a mock LLM adapter."""
        adapter = MagicMock()
        adapter.chat.return_value = "This is a test response with [1] citation."
        return adapter

    # Firebase client fixture removed - no longer needed

    @pytest.fixture
    def mock_embedding_model(self):
        """Create a mock embedding model."""
        model = MagicMock()
        model.embed_query.return_value = [0.1, 0.2, 0.3]
        return model

    @pytest.fixture
    def mock_vector_store(self):
        """Create a mock vector store."""
        store = MagicMock()
        store.search.return_value = [
            {"id": "doc1", "text": "Test document 1", "score": 0.9},
            {"id": "doc2", "text": "Test document 2", "score": 0.8},
        ]
        return store

    @pytest.fixture
    def mock_reranker(self):
        """Create a mock reranker."""
        reranker = MagicMock()
        reranker.rerank.return_value = [
            {"id": "doc1", "text": "Test document 1", "score": 0.95, "final_score": 0.95},
            {"id": "doc2", "text": "Test document 2", "score": 0.85, "final_score": 0.85},
        ]
        return reranker

    @pytest.fixture
    def knowledge_base(self, mock_embedding_model, mock_vector_store, mock_reranker):
        """Create a PgVectorKnowledgeBaseService instance with mock dependencies."""
        return PgVectorKnowledgeBaseService(
            embedding_model=mock_embedding_model,
            vector_store=mock_vector_store,
            reranker=mock_reranker,
            use_reranking=True
        )

    @pytest.fixture
    def rag_generator(self, mock_llm_adapter):
        """Create a RAGGenerator instance with mock dependencies."""
        return RAGGenerator(
            llm_adapter=mock_llm_adapter,
            include_citations=True,
            citation_format="inline",
            structured_output=True
        )

    @pytest.fixture
    def department_agent(self, mock_llm_adapter, knowledge_base):
        """Create a DepartmentAgent instance with mock dependencies."""
        return DepartmentAgent(
            department="marketing",
            llm_adapter=mock_llm_adapter,
            knowledge_base_service=knowledge_base,
            default_response_style=ResponseStyle.CONCISE,
            default_citation_format=CitationFormat.INLINE
        )

    @pytest.mark.asyncio
    async def test_knowledge_base_to_generator_integration(self, knowledge_base, rag_generator):
        """Test integration between PgVectorKnowledgeBaseService and RAGGenerator."""
        # Mock the search method to return test results
        search_results = [
            {
                "id": "doc1",
                "text": "Marketing strategy for Q2 includes social media campaigns.",
                "score": 0.95,
                "metadata": {
                    "source": "Marketing Plan",
                    "title": "Q2 Strategy",
                    "department": "marketing"
                }
            },
            {
                "id": "doc2",
                "text": "Budget allocation for Q2 marketing is $500,000.",
                "score": 0.85,
                "metadata": {
                    "source": "Financial Report",
                    "title": "Q2 Budget",
                    "department": "finance"
                }
            }
        ]

        # Set up the mock LLM adapter to return a specific response
        rag_generator.llm_adapter.chat.return_value = "Our Q2 marketing strategy includes social media campaigns [1]."

        # Patch the search method
        with patch.object(knowledge_base, 'search', return_value=search_results):
            # Search for documents
            results = await knowledge_base.search(
                query="What is our marketing strategy for Q2?",
                search_type="hybrid",
                limit=5
            )

            # Generate response using the search results
            response = await rag_generator.generate(
                query="What is our marketing strategy for Q2?",
                context=results
            )

            # Verify response contains expected content
            assert response is not None
            assert isinstance(response, str)
            assert len(response) > 0

            # Verify the LLM adapter was called
            assert rag_generator.llm_adapter.chat.called

            # Get the arguments passed to the LLM adapter
            call_args = rag_generator.llm_adapter.chat.call_args[0][0]

            # Verify the prompt structure
            assert len(call_args) == 2  # System and user messages
            assert "marketing strategy" in call_args[1]["content"]

    @pytest.mark.skip(reason="Requires more complex mocking of department agent")
    @pytest.mark.asyncio
    async def test_full_rag_pipeline(self):
        """Test the full RAG pipeline from knowledge base to department agent."""
        # This test is skipped for now as it requires more complex mocking
        pass

    @pytest.mark.asyncio
    async def test_rag_generator_with_large_context(self, rag_generator):
        """Test RAGGenerator with a large context that requires context window management."""
        # Set up the mock LLM adapter to return a specific response
        rag_generator.llm_adapter.chat.return_value = "This is a response based on the large context."

        # Create a large context that will exceed token limits
        large_context = []
        for i in range(20):
            doc = {
                "id": f"doc{i}",
                "text": f"This is document {i} with a lot of content " * 50,  # Make it large
                "score": 0.9 - (i * 0.02),  # Decreasing relevance
                "metadata": {
                    "source": "Test Source",
                    "title": f"Document {i}",
                    "department": "marketing"
                }
            }
            # Pre-compute token count to avoid estimation issues in tests
            doc["token_count"] = 500  # Simulate token count
            large_context.append(doc)

        # Generate response with the large context
        response = await rag_generator.generate(
            query="What is our marketing strategy?",
            context=large_context
        )

        # Verify response contains expected content
        assert response is not None
        assert isinstance(response, str)
        assert len(response) > 0

        # Verify the LLM adapter was called
        assert rag_generator.llm_adapter.chat.called

        # Verify that context window management was applied
        # The number of documents used should be less than the original
        call_args = rag_generator.llm_adapter.chat.call_args[0][0]
        user_message = call_args[1]["content"]

        # Context window management should have added a note about truncation
        assert "context limitations" in user_message.lower() or "token limit" in user_message.lower()

    @pytest.mark.skip(reason="Requires more complex mocking of department agent")
    @pytest.mark.asyncio
    async def test_department_agent_with_rag_generator(self):
        """Test DepartmentAgent using RAGGenerator for response generation."""
        # This test is skipped for now as it requires more complex mocking
        pass
