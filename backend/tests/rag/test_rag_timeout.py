"""
Tests for RAG timeout utilities.

This module contains tests for the timeout and retry utilities for RAG components.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, patch

from app.rag.timeout import (
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,
    with_embedding_timeout_decorator,
    with_vector_search_timeout_decorator,
    with_keyword_search_timeout_decorator,
    with_hybrid_search_timeout_decorator
)
from app.core.errors.base import TimeoutError, RateLimitError


# Test async function that succeeds
async def async_success(*args, **kwargs):
    return "success"


# Test async function that fails with an exception
async def async_failure(*args, **kwargs):
    raise ValueError("Failure")


# Test async function that times out
async def async_timeout(*args, **kwargs):
    await asyncio.sleep(10)
    return "timeout"


# Test fallback function
async def async_fallback(*args, **kwargs):
    return "fallback"


@pytest.mark.asyncio
async def test_with_embedding_timeout_success():
    """Test that with_embedding_timeout succeeds with a fast function."""
    result = await with_embedding_timeout(
        async_success,
        timeout_seconds=1,
        max_attempts=2
    )
    assert result == "success"


@pytest.mark.asyncio
async def test_with_embedding_timeout_failure():
    """Test that with_embedding_timeout propagates exceptions after all retries fail."""
    with pytest.raises(ValueError):
        await with_embedding_timeout(
            async_failure,
            timeout_seconds=1,
            max_attempts=2
        )


@pytest.mark.asyncio
async def test_with_embedding_timeout_timeout():
    """Test that with_embedding_timeout raises TimeoutError after all retries time out."""
    with pytest.raises(TimeoutError):
        await with_embedding_timeout(
            async_timeout,
            timeout_seconds=0.1,
            max_attempts=1
        )


@pytest.mark.asyncio
async def test_with_vector_search_timeout_success():
    """Test that with_vector_search_timeout succeeds with a fast function."""
    result = await with_vector_search_timeout(
        async_success,
        timeout_seconds=1,
        max_attempts=2
    )
    assert result == "success"


@pytest.mark.asyncio
async def test_with_vector_search_timeout_fallback():
    """Test that with_vector_search_timeout uses fallback when function fails."""
    result = await with_vector_search_timeout(
        async_failure,
        timeout_seconds=1,
        max_attempts=1,
        fallback_func=async_fallback
    )
    assert result == "fallback"


@pytest.mark.asyncio
async def test_with_vector_search_timeout_timeout_fallback():
    """Test that with_vector_search_timeout uses fallback when function times out."""
    result = await with_vector_search_timeout(
        async_timeout,
        timeout_seconds=0.1,
        max_attempts=1,
        fallback_func=async_fallback
    )
    assert result == "fallback"


@pytest.mark.asyncio
async def test_with_rag_timeout_decorator():
    """Test that with_rag_timeout decorator works correctly."""
    # Create a decorated function
    @with_rag_timeout(operation_type="embedding")
    async def decorated_function(*args, **kwargs):
        return "decorated success"

    # Call the decorated function
    result = await decorated_function()

    # Should return the result
    assert result == "decorated success"


@pytest.mark.asyncio
async def test_with_rag_timeout_decorator_fallback():
    """Test that with_rag_timeout decorator uses fallback when function fails."""
    # Create a decorated function with fallback
    @with_rag_timeout(
        operation_type="vector_search",
        fallback_func=async_fallback
    )
    async def decorated_failure(*args, **kwargs):
        raise ValueError("Decorated failure")

    # Call the decorated function
    result = await decorated_failure()

    # Should use fallback
    assert result == "fallback"


@pytest.mark.asyncio
async def test_with_embedding_timeout_decorator():
    """Test that with_embedding_timeout_decorator works correctly."""
    # Create a decorated function
    @with_embedding_timeout_decorator()
    async def decorated_function(*args, **kwargs):
        return "embedding success"

    # Call the decorated function
    result = await decorated_function()

    # Should return the result
    assert result == "embedding success"


@pytest.mark.asyncio
async def test_with_vector_search_timeout_decorator():
    """Test that with_vector_search_timeout_decorator works correctly."""
    # Create a decorated function
    @with_vector_search_timeout_decorator(fallback_func=async_fallback)
    async def decorated_timeout(*args, **kwargs):
        await asyncio.sleep(10)  # Will timeout
        return "vector search"

    # Call the decorated function with a short timeout
    with patch('app.rag.timeout.RAG_TIMEOUTS', {'vector_search': 0.1, 'default': 1.0}):
        result = await decorated_timeout()

    # Should use fallback
    assert result == "fallback"


@pytest.mark.asyncio
async def test_with_keyword_search_timeout_decorator():
    """Test that with_keyword_search_timeout_decorator works correctly."""
    # Create a decorated function
    @with_keyword_search_timeout_decorator()
    async def decorated_function(*args, **kwargs):
        return "keyword search success"

    # Call the decorated function
    result = await decorated_function()

    # Should return the result
    assert result == "keyword search success"


@pytest.mark.asyncio
async def test_with_hybrid_search_timeout_decorator():
    """Test that with_hybrid_search_timeout_decorator works correctly."""
    # Create a decorated function
    @with_hybrid_search_timeout_decorator()
    async def decorated_function(*args, **kwargs):
        return "hybrid search success"

    # Call the decorated function
    result = await decorated_function()

    # Should return the result
    assert result == "hybrid search success"
