"""
Tests for the DepartmentAgent base class.

This module contains tests for the DepartmentAgent base class functionality,
including enhanced knowledge retrieval and response generation capabilities.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime

from app.agents.department_agent import DepartmentAgent, ResponseStyle, CitationFormat


class TestDepartmentAgent:
    """Tests for the DepartmentAgent class."""

    def setup_method(self):
        """Set up the test environment."""
        # Create mock dependencies
        self.mock_llm_adapter = MagicMock()
        self.mock_llm_adapter.chat = AsyncMock(return_value="Test response")

        self.mock_knowledge_base_service = MagicMock()
        self.mock_knowledge_base_service.retrieve = AsyncMock(return_value=[
            {"id": "doc1", "content": "Test document", "source": "Test source", "score": 0.85}
        ])

        # Create a test department agent with default settings
        self.test_agent = DepartmentAgent(
            self.mock_llm_adapter,
            self.mock_knowledge_base_service,
            department="test_department"
        )

        # Create a test department agent with custom settings
        self.custom_agent = DepartmentAgent(
            self.mock_llm_adapter,
            self.mock_knowledge_base_service,
            department="test_department",
            default_response_style=ResponseStyle.CONCISE,
            default_citation_format=CitationFormat.FOOTNOTE,
            default_retrieval_params={
                "top_k": 3,
                "similarity_threshold": 0.8,
                "use_hybrid_search": False
            }
        )

    @pytest.mark.asyncio
    async def test_process_query_default(self):
        """Test processing a query with default settings."""
        # Reset mocks to ensure clean state
        self.mock_llm_adapter.chat.reset_mock()
        self.mock_knowledge_base_service.retrieve.reset_mock()

        # Process a test query
        result = await self.test_agent.process_query("Test query")

        # Check the result structure
        assert "response" in result
        assert "query" in result
        assert "tools_used" in result
        assert "knowledge_sources" in result
        assert "timestamp" in result
        assert "department" in result
        assert "knowledge_count" in result
        assert "processing_time" in result
        assert "response_style" in result
        assert "citation_format" in result
        assert "metadata" in result

        # Check specific values
        assert "Test response" in result["response"]
        assert result["query"] == "Test query"
        assert result["tools_used"] == []
        assert result["department"] == "test_department"
        assert len(result["knowledge_sources"]) == 1
        assert result["knowledge_sources"][0] == "Test source"
        assert result["knowledge_count"] == 1
        assert result["response_style"] == "detailed"
        assert result["citation_format"] == "inline"
        assert "expanded_queries" in result["metadata"]
        assert "retrieval_params" in result["metadata"]

    @pytest.mark.asyncio
    async def test_process_query_custom_params(self):
        """Test processing a query with custom parameters."""
        # Reset mocks to ensure clean state
        self.mock_llm_adapter.chat.reset_mock()
        self.mock_knowledge_base_service.retrieve.reset_mock()

        # Custom retrieval parameters
        custom_retrieval_params = {
            "top_k": 10,
            "similarity_threshold": 0.9,
            "use_hybrid_search": False
        }

        # Process a test query with custom parameters
        result = await self.test_agent.process_query(
            "Test query",
            response_style=ResponseStyle.ANALYTICAL,
            citation_format=CitationFormat.ENDNOTE,
            retrieval_params=custom_retrieval_params
        )

        # Check specific values
        assert "Test response" in result["response"]
        assert result["response_style"] == "analytical"
        assert result["citation_format"] == "endnote"

        # Check that custom retrieval parameters were used
        assert result["metadata"]["retrieval_params"]["top_k"] == 10
        assert result["metadata"]["retrieval_params"]["similarity_threshold"] == 0.9
        assert result["metadata"]["retrieval_params"]["use_hybrid_search"] is False

    @pytest.mark.asyncio
    async def test_retrieve_department_knowledge_default(self):
        """Test retrieving department knowledge with default parameters."""
        # Reset mock to ensure clean state
        self.mock_knowledge_base_service.retrieve.reset_mock()

        # Retrieve knowledge
        results = await self.test_agent._retrieve_department_knowledge("Test query")

        # Check that the knowledge base service was called correctly
        # Note: We're only checking the first call since query expansion will make multiple calls
        call_args = self.mock_knowledge_base_service.retrieve.call_args_list[0][1]
        assert call_args["metadata_filter"] == {"department": "test_department"}
        assert call_args["top_k"] == 5
        assert call_args["similarity_threshold"] == 0.7
        assert call_args["use_hybrid_search"] is True

        # Check the results
        assert len(results) == 1
        assert results[0]["content"] == "Test document"
        assert results[0]["source"] == "Test source"

    @pytest.mark.asyncio
    async def test_retrieve_department_knowledge_custom(self):
        """Test retrieving department knowledge with custom parameters."""
        # Reset mock to ensure clean state
        self.mock_knowledge_base_service.retrieve.reset_mock()

        # Custom retrieval parameters
        custom_params = {
            "top_k": 10,
            "similarity_threshold": 0.9,
            "use_hybrid_search": False,
            "additional_filters": {"document_type": "report"}
        }

        # Retrieve knowledge with custom parameters
        results = await self.test_agent._retrieve_department_knowledge("Test query", custom_params)

        # Check that the knowledge base service was called correctly
        # Note: We're only checking the first call since query expansion will make multiple calls
        call_args = self.mock_knowledge_base_service.retrieve.call_args_list[0][1]
        assert call_args["metadata_filter"] == {"department": "test_department", "document_type": "report"}
        assert call_args["top_k"] == 10
        assert call_args["similarity_threshold"] == 0.9
        assert call_args["use_hybrid_search"] is False

        # Check the results
        assert len(results) == 1
        assert results[0]["content"] == "Test document"
        assert results[0]["source"] == "Test source"

    @pytest.mark.asyncio
    async def test_expand_query(self):
        """Test query expansion functionality."""
        # Test with a query containing a finance keyword
        finance_query = "What is our budget for Q2?"
        self.test_agent.department = "finance"
        expanded_finance = await self.test_agent._expand_query(finance_query)

        # Should include original query and expanded version
        assert finance_query in expanded_finance
        assert len(expanded_finance) > 1

        # Test with a query containing a marketing keyword
        marketing_query = "How is our latest campaign performing?"
        self.test_agent.department = "marketing"
        expanded_marketing = await self.test_agent._expand_query(marketing_query)

        # Should include original query and expanded version
        assert marketing_query in expanded_marketing
        assert len(expanded_marketing) > 1

    @pytest.mark.asyncio
    async def test_generate_department_response_default(self):
        """Test generating a department response with default settings."""
        # Reset mock to ensure clean state
        self.mock_llm_adapter.chat.reset_mock()

        # Generate a response
        knowledge_results = [{"content": "Test document", "source": "Test source"}]
        context = {"additional_instructions": "Be concise."}

        response = await self.test_agent._generate_department_response(
            "Test query", knowledge_results, context
        )

        # Check that the LLM adapter was called correctly
        self.mock_llm_adapter.chat.assert_called_once()

        # Check the response
        assert "Test response" in response

        # Check that the prompt includes the default response style (DETAILED)
        call_args = self.mock_llm_adapter.chat.call_args[0][0]
        user_message = call_args[1]["content"]
        assert "response style: detailed" in user_message.lower()

    @pytest.mark.asyncio
    async def test_generate_department_response_custom_style(self):
        """Test generating a department response with custom response style."""
        # Reset mock to ensure clean state
        self.mock_llm_adapter.chat.reset_mock()

        # Generate a response with custom style
        knowledge_results = [{"content": "Test document", "source": "Test source"}]
        context = {
            "response_style": ResponseStyle.CONCISE,
            "additional_instructions": "Focus on key points."
        }

        response = await self.test_agent._generate_department_response(
            "Test query", knowledge_results, context
        )

        # Check the response
        assert "Test response" in response

        # Check that the prompt includes the custom response style
        call_args = self.mock_llm_adapter.chat.call_args[0][0]
        user_message = call_args[1]["content"]
        assert "response style: concise" in user_message.lower()
        assert "brief and to the point" in user_message.lower()

    @pytest.mark.asyncio
    async def test_format_citations(self):
        """Test citation formatting."""
        # Test data
        knowledge_results = [
            {"content": "First document content", "source": "Source A"},
            {"content": "Second document content", "source": "Source B"}
        ]

        # Test inline citations
        inline_context, inline_citations = self.test_agent._format_citations(
            knowledge_results, CitationFormat.INLINE
        )
        assert "[Source 1]" in inline_context
        assert "[Source 2]" in inline_context
        assert "Source A" in inline_citations
        assert "Source B" in inline_citations

        # Test footnote citations
        footnote_context, footnote_citations = self.test_agent._format_citations(
            knowledge_results, CitationFormat.FOOTNOTE
        )
        assert "^1" in footnote_context
        assert "^2" in footnote_context
        assert "^1: Source A" in footnote_citations
        assert "^2: Source B" in footnote_citations

        # Test endnote citations
        endnote_context, endnote_citations = self.test_agent._format_citations(
            knowledge_results, CitationFormat.ENDNOTE
        )
        assert "First document content" in endnote_context
        assert "Second document content" in endnote_context
        assert "[1] Source A" in endnote_citations
        assert "[2] Source B" in endnote_citations

        # Test no citations
        none_context, none_citations = self.test_agent._format_citations(
            knowledge_results, CitationFormat.NONE
        )
        assert "First document content" in none_context
        assert "Second document content" in none_context
        assert none_citations == ""

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in process_query."""
        # Create a new test agent with a mock that raises an exception
        # We need to mock the _retrieve_department_knowledge method directly
        original_method = DepartmentAgent._retrieve_department_knowledge

        try:
            # Replace the method with a mock that raises an exception
            DepartmentAgent._retrieve_department_knowledge = AsyncMock(side_effect=Exception("Test error"))

            # Process a query
            result = await self.test_agent.process_query("Test query")

            # Check that an error response was returned
            assert "response" in result

            # The error field should be present
            assert "error" in result, f"Error field missing in result: {result}"
            assert "Test error" in result["error"]
        finally:
            # Restore the original method
            DepartmentAgent._retrieve_department_knowledge = original_method
