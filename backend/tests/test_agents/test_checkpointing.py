"""
Tests for the checkpointing module.

This module contains tests for the checkpointing functionality.
"""

import os
import pytest
import tempfile
from datetime import datetime
from typing import Dict, List, Any

from app.langgraph.state import AgentState, create_initial_state
from app.langgraph.checkpointing import (
    CheckpointerInterface,
    SQLiteCheckpointer,
    get_checkpointer
)


class TestSQLiteCheckpointer:
    """Tests for the SQLiteCheckpointer class."""

    @pytest.fixture
    def temp_db_path(self):
        """Create a temporary database path."""
        with tempfile.NamedTemporaryFile(suffix=".db") as temp_file:
            db_path = temp_file.name
        return db_path

    @pytest.fixture
    def checkpointer(self, temp_db_path):
        """Create a SQLiteCheckpointer instance."""
        return SQLiteCheckpointer(temp_db_path)

    @pytest.fixture
    def sample_state(self):
        """Create a sample state for testing."""
        state = create_initial_state(
            "What is the budget for Q2?",
            user_id="user123",
            thread_id="thread456"
        )
        state.add_message("user", "What is the budget for Q2?")
        state.add_department("finance")
        state.update_analysis("finance", 0.8)
        return state

    def test_init(self, temp_db_path):
        """Test initialization."""
        checkpointer = SQLiteCheckpointer(temp_db_path)

        assert checkpointer.db_path == temp_db_path
        assert os.path.exists(temp_db_path)

    def test_save_and_load_checkpoint(self, checkpointer, sample_state):
        """Test saving and loading a checkpoint."""
        thread_id = "thread456"

        # Save checkpoint
        success = checkpointer.save_checkpoint(thread_id, sample_state)
        assert success is True

        # Load checkpoint
        loaded_state = checkpointer.load_checkpoint(thread_id)

        assert loaded_state is not None
        assert loaded_state.query == sample_state.query
        assert loaded_state.departments == sample_state.departments
        assert loaded_state.analysis == sample_state.analysis
        assert len(loaded_state.messages) == len(sample_state.messages)
        assert loaded_state.messages[0]["role"] == sample_state.messages[0]["role"]
        assert loaded_state.messages[0]["content"] == sample_state.messages[0]["content"]

    def test_load_nonexistent_checkpoint(self, checkpointer):
        """Test loading a nonexistent checkpoint."""
        thread_id = "nonexistent"

        loaded_state = checkpointer.load_checkpoint(thread_id)

        assert loaded_state is None

    def test_list_checkpoints(self, checkpointer, sample_state):
        """Test listing checkpoints."""
        thread_id = "thread456"

        # Save checkpoint
        checkpointer.save_checkpoint(thread_id, sample_state)

        # List checkpoints
        checkpoints = checkpointer.list_checkpoints(thread_id)

        assert len(checkpoints) == 1
        assert checkpoints[0]["thread_id"] == thread_id
        assert "timestamp" in checkpoints[0]
        assert "query" in checkpoints[0]
        assert checkpoints[0]["query"] == sample_state.query

    def test_delete_checkpoint(self, checkpointer, sample_state):
        """Test deleting a checkpoint."""
        thread_id = "thread456"

        # Save checkpoint
        checkpointer.save_checkpoint(thread_id, sample_state)

        # Get checkpoint ID
        checkpoints = checkpointer.list_checkpoints(thread_id)
        checkpoint_id = checkpoints[0]["id"]

        # Delete checkpoint
        success = checkpointer.delete_checkpoint(thread_id, checkpoint_id)
        assert success is True

        # Verify checkpoint is deleted
        checkpoints = checkpointer.list_checkpoints(thread_id)
        assert len(checkpoints) == 0

    def test_delete_all_checkpoints(self, checkpointer, sample_state):
        """Test deleting all checkpoints for a thread."""
        thread_id = "thread456"

        # Save first checkpoint
        checkpointer.save_checkpoint(thread_id, sample_state)

        # Create a modified state with a different ID to ensure it's saved as a separate checkpoint
        modified_state = create_initial_state(
            "What is the marketing plan?",
            user_id="user123",
            thread_id=thread_id
        )
        modified_state.add_message("user", "What is the marketing plan?")
        modified_state.add_department("marketing")

        # Save the modified state
        checkpointer.save_checkpoint(thread_id, modified_state)

        # Verify multiple checkpoints exist
        checkpoints = checkpointer.list_checkpoints(thread_id)
        assert len(checkpoints) == 2

        # Delete all checkpoints
        success = checkpointer.delete_checkpoint(thread_id)
        assert success is True

        # Verify all checkpoints are deleted
        checkpoints = checkpointer.list_checkpoints(thread_id)
        assert len(checkpoints) == 0

    def test_multiple_threads(self, checkpointer, sample_state):
        """Test handling multiple threads."""
        thread1 = "thread1"
        thread2 = "thread2"

        # Create states for different threads
        state1 = sample_state
        state2 = create_initial_state(
            "What is the marketing plan?",
            user_id="user456",
            thread_id=thread2
        )
        state2.add_message("user", "What is the marketing plan?")
        state2.add_department("marketing")

        # Save checkpoints for both threads
        checkpointer.save_checkpoint(thread1, state1)
        checkpointer.save_checkpoint(thread2, state2)

        # Load checkpoints for both threads
        loaded_state1 = checkpointer.load_checkpoint(thread1)
        loaded_state2 = checkpointer.load_checkpoint(thread2)

        # Verify correct states are loaded
        assert loaded_state1.query == state1.query
        assert loaded_state2.query == state2.query
        assert loaded_state1.departments == ["finance"]
        assert loaded_state2.departments == ["marketing"]


class TestGetCheckpointer:
    """Tests for the get_checkpointer function."""

    def test_get_checkpointer_default(self, monkeypatch):
        """Test getting the default checkpointer."""
        # Clear environment variables
        monkeypatch.delenv("CHECKPOINTER_TYPE", raising=False)

        checkpointer = get_checkpointer()

        assert isinstance(checkpointer, SQLiteCheckpointer)

    def test_get_checkpointer_sqlite(self, monkeypatch):
        """Test getting the SQLite checkpointer."""
        # Set environment variables
        monkeypatch.setenv("CHECKPOINTER_TYPE", "sqlite")

        checkpointer = get_checkpointer()

        assert isinstance(checkpointer, SQLiteCheckpointer)

    def test_get_checkpointer_with_path(self, monkeypatch, tmp_path):
        """Test getting the SQLite checkpointer with a custom path."""
        # Set environment variables
        db_path = str(tmp_path / "custom.db")
        monkeypatch.setenv("CHECKPOINTER_TYPE", "sqlite")
        monkeypatch.setenv("SQLITE_CHECKPOINTER_PATH", db_path)

        checkpointer = get_checkpointer()

        assert isinstance(checkpointer, SQLiteCheckpointer)
        assert checkpointer.db_path == db_path
