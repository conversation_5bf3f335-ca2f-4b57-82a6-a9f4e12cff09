"""
Tests for agent tools integration.

This module contains tests for the integration between agents and tools.
"""

import pytest
from unittest.mock import MagicMock

from app.agents import initialize_agents
from app.agents.registry import agent_registry
from app.core.tools import BaseTool, register_tool, TOOL_REGISTRY


class MockTool(BaseTool):
    """Mock tool for testing."""
    
    def __init__(self, name, department):
        """Initialize the mock tool."""
        super().__init__(
            name=name,
            description=f"Mock {name} tool for {department}",
            department=department
        )
    
    async def run(self, **kwargs):
        """Run the mock tool."""
        return {"result": f"Mock {self.name} result"}


class TestAgentTools:
    """Tests for agent tools integration."""
    
    def setup_method(self):
        """Set up the test environment."""
        # Clear the registries before each test
        agent_registry.agents = {}
        agent_registry.tools = {}
        TOOL_REGISTRY.clear()
        
        # Register some mock tools
        self.finance_tool1 = MockTool("finance_tool1", "finance")
        self.finance_tool2 = MockTool("finance_tool2", "finance")
        self.marketing_tool1 = MockTool("marketing_tool1", "marketing")
        self.marketing_tool2 = MockTool("marketing_tool2", "marketing")
        
        register_tool(self.finance_tool1)
        register_tool(self.finance_tool2)
        register_tool(self.marketing_tool1)
        register_tool(self.marketing_tool2)
    
    def test_tools_assigned_to_agents(self):
        """Test that tools are correctly assigned to agents."""
        # Create mock dependencies
        mock_llm_adapter = MagicMock()
        mock_knowledge_base_service = MagicMock()
        
        # Initialize agents
        agents = initialize_agents(mock_llm_adapter, mock_knowledge_base_service)
        
        # Check that finance tools are assigned to finance agent
        finance_agent = agents["finance"]
        assert "finance.finance_tool1" in finance_agent.tools
        assert "finance.finance_tool2" in finance_agent.tools
        assert finance_agent.tools["finance.finance_tool1"] is self.finance_tool1
        assert finance_agent.tools["finance.finance_tool2"] is self.finance_tool2
        
        # Check that marketing tools are assigned to marketing agent
        marketing_agent = agents["marketing"]
        assert "marketing.marketing_tool1" in marketing_agent.tools
        assert "marketing.marketing_tool2" in marketing_agent.tools
        assert marketing_agent.tools["marketing.marketing_tool1"] is self.marketing_tool1
        assert marketing_agent.tools["marketing.marketing_tool2"] is self.marketing_tool2
        
        # Check that finance tools are not assigned to marketing agent
        assert "finance.finance_tool1" not in marketing_agent.tools
        assert "finance.finance_tool2" not in marketing_agent.tools
        
        # Check that marketing tools are not assigned to finance agent
        assert "marketing.marketing_tool1" not in finance_agent.tools
        assert "marketing.marketing_tool2" not in finance_agent.tools
    
    def test_agent_registry_tools(self):
        """Test that tools are correctly registered in the agent registry."""
        # Create mock dependencies
        mock_llm_adapter = MagicMock()
        mock_knowledge_base_service = MagicMock()
        
        # Initialize agents
        initialize_agents(mock_llm_adapter, mock_knowledge_base_service)
        
        # Check that tools are registered in the agent registry
        finance_tools = agent_registry.get_tools("finance")
        marketing_tools = agent_registry.get_tools("marketing")
        
        assert len(finance_tools) == 2
        assert len(marketing_tools) == 2
        
        # Check that the correct tools are registered
        finance_tool_names = [tool.name for tool in finance_tools]
        marketing_tool_names = [tool.name for tool in marketing_tools]
        
        assert "finance_tool1" in finance_tool_names
        assert "finance_tool2" in finance_tool_names
        assert "marketing_tool1" in marketing_tool_names
        assert "marketing_tool2" in marketing_tool_names
    
    @pytest.mark.asyncio
    async def test_agent_can_use_tools(self):
        """Test that agents can use their assigned tools."""
        # Create mock dependencies
        mock_llm_adapter = MagicMock()
        mock_knowledge_base_service = MagicMock()
        
        # Initialize agents
        agents = initialize_agents(mock_llm_adapter, mock_knowledge_base_service)
        
        # Test that finance agent can use finance tools
        finance_agent = agents["finance"]
        result = await finance_agent.use_tool("finance.finance_tool1", {})
        assert result == {"result": "Mock finance_tool1 result"}
        
        # Test that marketing agent can use marketing tools
        marketing_agent = agents["marketing"]
        result = await marketing_agent.use_tool("marketing.marketing_tool1", {})
        assert result == {"result": "Mock marketing_tool1 result"}
        
        # Test that agents cannot use tools from other departments
        with pytest.raises(ValueError):
            await finance_agent.use_tool("marketing.marketing_tool1", {})
        
        with pytest.raises(ValueError):
            await marketing_agent.use_tool("finance.finance_tool1", {})
