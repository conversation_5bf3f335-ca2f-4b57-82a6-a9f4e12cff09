"""
Tests for the agent registry.

This module contains tests for the agent registry functionality.
"""

import pytest
from unittest.mock import MagicMock

from app.agents.registry import AgentRegistry, agent_registry, get_registry


class TestAgentRegistry:
    """Tests for the AgentRegistry class."""

    def setup_method(self):
        """Set up the test environment."""
        # Clear the registry before each test
        agent_registry.agents = {}
        agent_registry.tools = {}

    def test_singleton_access(self):
        """Test that the singleton registry is accessible."""
        registry1 = get_registry()
        registry2 = get_registry()

        assert registry1 is registry2
        assert registry1 is agent_registry

    def test_register_and_get_agent(self):
        """Test registering and retrieving an agent."""
        registry = agent_registry

        # Create a mock agent
        mock_agent = MagicMock()

        # Register the agent
        registry.register("test_agent", mock_agent)

        # Retrieve the agent
        retrieved_agent = registry.get_agent("test_agent")

        assert retrieved_agent is mock_agent

    def test_get_nonexistent_agent(self):
        """Test retrieving a nonexistent agent."""
        registry = agent_registry

        # Attempt to retrieve a nonexistent agent
        retrieved_agent = registry.get_agent("nonexistent_agent")

        assert retrieved_agent is None

    def test_list_agents(self):
        """Test listing all registered agents."""
        registry = agent_registry

        # Create mock agents
        mock_agent1 = MagicMock()
        mock_agent2 = MagicMock()

        # Register the agents
        registry.register("agent1", mock_agent1)
        registry.register("agent2", mock_agent2)

        # List all agents
        agent_names = registry.list_agents()

        assert len(agent_names) == 2
        assert "agent1" in agent_names
        assert "agent2" in agent_names

    def test_iteration(self):
        """Test iterating over the registry."""
        registry = agent_registry

        # Create mock agents
        mock_agent1 = MagicMock()
        mock_agent2 = MagicMock()

        # Register the agents
        registry.register("agent1", mock_agent1)
        registry.register("agent2", mock_agent2)

        # Iterate over the registry
        agents_dict = dict(registry)

        assert len(agents_dict) == 2
        assert agents_dict["agent1"] is mock_agent1
        assert agents_dict["agent2"] is mock_agent2
