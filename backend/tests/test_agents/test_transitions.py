"""
Tests for the state transition logic.

This module contains tests for the state transition functions.
"""

import pytest
from datetime import datetime
from typing import Dict, List, Any

from app.langgraph.state import AgentState, RagContext, create_initial_state
from app.langgraph.transitions import (
    validate_state,
    transition_add_rag_documents,
    transition_add_message,
    transition_add_department,
    transition_update_analysis,
    transition_set_response,
    transition_update_metadata,
    create_transition
)


class TestValidateState:
    """Tests for the validate_state function."""

    def test_validate_valid_state(self):
        """Test validating a valid state."""
        state = create_initial_state("test query")

        is_valid, error = validate_state(state)

        assert is_valid is True
        assert error is None

    def test_validate_missing_query(self):
        """Test validating a state with missing query."""
        state = AgentState(query="")  # Empty query

        is_valid, error = validate_state(state)

        assert is_valid is False
        assert "Query is required" in error

    def test_validate_missing_metadata(self):
        """Test validating a state with missing metadata."""
        # Note: The validator in AgentState automatically adds required metadata fields,
        # so we need to modify the test to check that validation passes with empty metadata
        state = AgentState(query="test")
        state.metadata = {}  # Empty metadata

        # The validator should automatically add the required fields
        is_valid, error = validate_state(state)

        # Validation should pass because the validator adds the required fields
        assert is_valid is True
        assert error is None

    def test_validate_inconsistent_rag(self):
        """Test validating a state with inconsistent RAG context."""
        state = create_initial_state("test query")

        # Add docs without scores
        state.rag.docs = state.rag.docs + ["doc1"]

        is_valid, error = validate_state(state)

        assert is_valid is False
        assert "Number of documents and scores must match" in error


class TestTransitionAddRagDocuments:
    """Tests for the transition_add_rag_documents function."""

    def test_add_rag_documents(self):
        """Test adding RAG documents."""
        state = create_initial_state("test query")

        docs = ["doc1", "doc2"]
        cites = [{"source": "source1"}, {"source": "source2"}]
        scores = [0.8, 0.6]

        new_state = transition_add_rag_documents(state, docs, cites, scores)

        assert new_state.rag.docs == docs
        assert new_state.rag.cites == cites
        assert new_state.rag.scores == scores

    def test_add_rag_documents_append(self):
        """Test appending RAG documents."""
        state = create_initial_state("test query")

        # Add initial documents
        docs1 = ["doc1"]
        cites1 = [{"source": "source1"}]
        scores1 = [0.8]

        state = transition_add_rag_documents(state, docs1, cites1, scores1)

        # Add more documents
        docs2 = ["doc2"]
        cites2 = [{"source": "source2"}]
        scores2 = [0.6]

        new_state = transition_add_rag_documents(state, docs2, cites2, scores2)

        assert new_state.rag.docs == docs1 + docs2
        assert new_state.rag.cites == cites1 + cites2
        assert new_state.rag.scores == scores1 + scores2

    def test_add_rag_documents_validation(self):
        """Test validation during adding RAG documents."""
        state = create_initial_state("test query")

        docs = ["doc1", "doc2"]
        cites = [{"source": "source1"}, {"source": "source2"}]
        scores = [0.8]  # Only one score for two docs

        with pytest.raises(ValueError, match="Number of documents and scores must match"):
            transition_add_rag_documents(state, docs, cites, scores)


class TestTransitionAddMessage:
    """Tests for the transition_add_message function."""

    def test_add_message(self):
        """Test adding a message."""
        state = create_initial_state("test query")

        new_state = transition_add_message(state, "user", "Hello")

        assert len(new_state.messages) == 1
        assert new_state.messages[0]["role"] == "user"
        assert new_state.messages[0]["content"] == "Hello"
        assert "timestamp" in new_state.messages[0]

    def test_add_message_with_additional_fields(self):
        """Test adding a message with additional fields."""
        state = create_initial_state("test query")

        new_state = transition_add_message(
            state,
            "assistant",
            "Hello there",
            department="finance",
            confidence=0.9
        )

        assert len(new_state.messages) == 1
        assert new_state.messages[0]["role"] == "assistant"
        assert new_state.messages[0]["content"] == "Hello there"
        assert new_state.messages[0]["department"] == "finance"
        assert new_state.messages[0]["confidence"] == 0.9

    def test_add_multiple_messages(self):
        """Test adding multiple messages."""
        state = create_initial_state("test query")

        # Add first message
        state = transition_add_message(state, "user", "Hello")

        # Add second message
        new_state = transition_add_message(state, "assistant", "Hi there")

        assert len(new_state.messages) == 2
        assert new_state.messages[0]["role"] == "user"
        assert new_state.messages[0]["content"] == "Hello"
        assert new_state.messages[1]["role"] == "assistant"
        assert new_state.messages[1]["content"] == "Hi there"


class TestTransitionAddDepartment:
    """Tests for the transition_add_department function."""

    def test_add_department(self):
        """Test adding a department."""
        state = create_initial_state("test query")

        new_state = transition_add_department(state, "finance")

        assert new_state.departments == ["finance"]

    def test_add_duplicate_department(self):
        """Test adding a duplicate department."""
        state = create_initial_state("test query")

        # Add department
        state = transition_add_department(state, "finance")

        # Add same department again
        new_state = transition_add_department(state, "finance")

        assert new_state.departments == ["finance"]  # No duplicates

    def test_add_multiple_departments(self):
        """Test adding multiple departments."""
        state = create_initial_state("test query")

        # Add first department
        state = transition_add_department(state, "finance")

        # Add second department
        new_state = transition_add_department(state, "marketing")

        assert new_state.departments == ["finance", "marketing"]


class TestTransitionUpdateAnalysis:
    """Tests for the transition_update_analysis function."""

    def test_update_analysis(self):
        """Test updating analysis."""
        state = create_initial_state("test query")

        new_state = transition_update_analysis(state, "finance", 0.8)

        assert new_state.analysis == {"finance": 0.8}

    def test_update_existing_analysis(self):
        """Test updating existing analysis."""
        state = create_initial_state("test query")

        # Add initial analysis
        state = transition_update_analysis(state, "finance", 0.8)

        # Update analysis
        new_state = transition_update_analysis(state, "finance", 0.9)

        assert new_state.analysis == {"finance": 0.9}

    def test_update_multiple_departments(self):
        """Test updating multiple departments."""
        state = create_initial_state("test query")

        # Add first department
        state = transition_update_analysis(state, "finance", 0.8)

        # Add second department
        new_state = transition_update_analysis(state, "marketing", 0.7)

        assert new_state.analysis == {"finance": 0.8, "marketing": 0.7}


class TestTransitionSetResponse:
    """Tests for the transition_set_response function."""

    def test_set_response(self):
        """Test setting the response."""
        state = create_initial_state("test query")

        new_state = transition_set_response(state, "This is a response.")

        assert new_state.response == "This is a response."

    def test_update_response(self):
        """Test updating the response."""
        state = create_initial_state("test query")

        # Set initial response
        state = transition_set_response(state, "This is a response.")

        # Update response
        new_state = transition_set_response(state, "This is an updated response.")

        assert new_state.response == "This is an updated response."


class TestTransitionUpdateMetadata:
    """Tests for the transition_update_metadata function."""

    def test_update_metadata(self):
        """Test updating metadata."""
        state = create_initial_state("test query")

        new_state = transition_update_metadata(state, user_id="user123")

        assert "user_id" in new_state.metadata
        assert new_state.metadata["user_id"] == "user123"

    def test_update_existing_metadata(self):
        """Test updating existing metadata."""
        state = create_initial_state("test query")

        # Add initial metadata
        state = transition_update_metadata(state, user_id="user123")

        # Update metadata
        new_state = transition_update_metadata(state, user_id="user456")

        assert new_state.metadata["user_id"] == "user456"

    def test_update_multiple_metadata_fields(self):
        """Test updating multiple metadata fields."""
        state = create_initial_state("test query")

        new_state = transition_update_metadata(
            state,
            user_id="user123",
            session_id="session456",
            client_info={"browser": "Chrome"}
        )

        assert new_state.metadata["user_id"] == "user123"
        assert new_state.metadata["session_id"] == "session456"
        assert new_state.metadata["client_info"] == {"browser": "Chrome"}


class TestCreateTransition:
    """Tests for the create_transition function."""

    def test_create_transition_valid(self):
        """Test creating a transition with valid state."""
        # Create a simple transition function
        def simple_transition(state: AgentState, value: str) -> AgentState:
            state.update_metadata(test_value=value)
            return state

        # Wrap it with validation
        validated_transition = create_transition(simple_transition)

        # Test with valid state
        state = create_initial_state("test query")
        new_state = validated_transition(state, "test value")

        assert new_state.metadata["test_value"] == "test value"

    def test_create_transition_invalid_before(self):
        """Test creating a transition with invalid state before."""
        # Create a simple transition function
        def simple_transition(state: AgentState, value: str) -> AgentState:
            state.update_metadata(test_value=value)
            return state

        # Wrap it with validation
        validated_transition = create_transition(simple_transition)

        # Test with invalid state (empty query)
        state = AgentState(query="")

        with pytest.raises(ValueError, match="Invalid state before transition"):
            validated_transition(state, "test value")

    def test_create_transition_invalid_after(self):
        """Test creating a transition with invalid state after."""
        # Create a transition function that produces invalid state
        def invalid_transition(state: AgentState) -> AgentState:
            state.query = ""  # Make the state invalid
            return state

        # Wrap it with validation
        validated_transition = create_transition(invalid_transition)

        # Test with valid state that becomes invalid
        state = create_initial_state("test query")

        with pytest.raises(ValueError, match="Invalid state after transition"):
            validated_transition(state)
