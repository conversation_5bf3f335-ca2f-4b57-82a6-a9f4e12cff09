"""
Tests for agent initialization.

This module contains tests for the agent initialization functionality.
"""

import pytest
from unittest.mock import MagicMock

from app.agents import initialize_agents
from app.agents.co_ceo import CoCEOAgent
from app.agents.finance import FinanceAgent
from app.agents.marketing import MarketingAgent
from app.agents.registry import agent_registry


class TestAgentInitialization:
    """Tests for agent initialization."""

    def setup_method(self):
        """Set up the test environment."""
        # Clear the registry before each test
        agent_registry.agents = {}
        agent_registry.tools = {}

    def test_initialize_agents(self):
        """Test initializing agents."""
        # Create mock dependencies
        mock_llm_adapter = MagicMock()
        mock_knowledge_base_service = MagicMock()

        # Initialize agents
        agents = initialize_agents(mock_llm_adapter, mock_knowledge_base_service)

        # Check that agents were created and returned
        assert "co_ceo" in agents
        assert "finance" in agents
        assert "marketing" in agents

        assert isinstance(agents["co_ceo"], CoCEOAgent)
        assert isinstance(agents["finance"], FinanceAgent)
        assert isinstance(agents["marketing"], MarketingAgent)

        # Check that agents were registered
        assert agent_registry.get_agent("co_ceo") is agents["co_ceo"]
        assert agent_registry.get_agent("finance") is agents["finance"]
        assert agent_registry.get_agent("marketing") is agents["marketing"]
