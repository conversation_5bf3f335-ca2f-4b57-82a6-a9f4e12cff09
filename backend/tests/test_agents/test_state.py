"""
Tests for the state management module.

This module contains tests for the state models and their reducers.
"""

import pytest
from datetime import datetime
from typing import Dict, List, Any

from app.langgraph.state import (
    AgentState,
    RagContext,
    append_unique,
    create_initial_state,
    STATE_MODEL_VERSION
)


class TestRagContext:
    """Tests for the RagContext class."""

    def test_init_default(self):
        """Test initialization with default values."""
        context = RagContext()
        assert context.docs == []
        assert context.cites == []
        assert context.scores == []

    def test_init_with_values(self):
        """Test initialization with values."""
        docs = ["doc1", "doc2"]
        cites = [{"source": "source1"}, {"source": "source2"}]
        scores = [0.8, 0.6]

        context = RagContext(docs=docs, cites=cites, scores=scores)

        assert context.docs == docs
        assert context.cites == cites
        assert context.scores == scores

    def test_add_reducer_docs(self):
        """Test the add reducer for docs."""
        context = RagContext()
        new_docs = ["doc1", "doc2"]

        # Use the reducer to add docs
        context.docs = context.docs + new_docs

        assert context.docs == new_docs

        # Add more docs
        more_docs = ["doc3"]
        context.docs = context.docs + more_docs

        assert context.docs == new_docs + more_docs

    def test_add_reducer_cites(self):
        """Test the add reducer for cites."""
        context = RagContext()
        new_cites = [{"source": "source1"}, {"source": "source2"}]

        # Use the reducer to add cites
        context.cites = context.cites + new_cites

        assert context.cites == new_cites

        # Add more cites
        more_cites = [{"source": "source3"}]
        context.cites = context.cites + more_cites

        assert context.cites == new_cites + more_cites

    def test_add_reducer_scores(self):
        """Test the add reducer for scores."""
        context = RagContext()
        new_scores = [0.8, 0.6]

        # Use the reducer to add scores
        context.scores = context.scores + new_scores

        assert context.scores == new_scores

        # Add more scores
        more_scores = [0.7]
        context.scores = context.scores + more_scores

        assert context.scores == new_scores + more_scores


class TestAppendUnique:
    """Tests for the append_unique function."""

    def test_append_unique_empty(self):
        """Test append_unique with empty lists."""
        existing = []
        new_items = ["item1", "item2"]

        result = append_unique(existing, new_items)

        assert result == new_items

    def test_append_unique_with_duplicates(self):
        """Test append_unique with duplicates."""
        existing = ["item1", "item2"]
        new_items = ["item2", "item3"]

        result = append_unique(existing, new_items)

        assert result == ["item1", "item2", "item3"]
        assert result.count("item2") == 1  # No duplicates

    def test_append_unique_preserves_order(self):
        """Test append_unique preserves order."""
        existing = ["item1", "item2"]
        new_items = ["item3", "item4"]

        result = append_unique(existing, new_items)

        assert result == ["item1", "item2", "item3", "item4"]


class TestAgentState:
    """Tests for the AgentState class."""

    def test_init_required_fields(self):
        """Test initialization with required fields."""
        with pytest.raises(ValueError):
            # query is required
            AgentState()

    def test_init_with_query(self):
        """Test initialization with query."""
        query = "What is the budget for Q2?"
        state = AgentState(query=query)

        assert state.query == query
        assert isinstance(state.rag, RagContext)
        assert state.messages == []
        assert state.departments == []
        assert state.analysis == {}
        assert state.response is None
        assert "timestamp" in state.metadata
        assert "version" in state.metadata
        assert "id" in state.metadata
        assert state.metadata["version"] == STATE_MODEL_VERSION

    def test_init_with_all_fields(self):
        """Test initialization with all fields."""
        query = "What is the budget for Q2?"
        rag = RagContext(
            docs=["doc1"],
            cites=[{"source": "source1"}],
            scores=[0.8]
        )
        messages = [{"role": "user", "content": "Hello"}]
        departments = ["finance"]
        analysis = {"finance": 0.8}
        response = "The budget for Q2 is $1M."
        metadata = {"user_id": "user123"}

        state = AgentState(
            query=query,
            rag=rag,
            messages=messages,
            departments=departments,
            analysis=analysis,
            response=response,
            metadata=metadata
        )

        assert state.query == query
        assert state.rag == rag
        assert state.messages == messages
        assert state.departments == departments
        assert state.analysis == analysis
        assert state.response == response
        assert "user_id" in state.metadata
        assert state.metadata["user_id"] == "user123"
        assert "timestamp" in state.metadata
        assert "version" in state.metadata
        assert "id" in state.metadata

    def test_add_message(self):
        """Test adding a message."""
        state = AgentState(query="test")

        # Add a message
        state.add_message("user", "Hello")

        assert len(state.messages) == 1
        assert state.messages[0]["role"] == "user"
        assert state.messages[0]["content"] == "Hello"
        assert "timestamp" in state.messages[0]

        # Add another message
        state.add_message("assistant", "Hi there", department="general")

        assert len(state.messages) == 2
        assert state.messages[1]["role"] == "assistant"
        assert state.messages[1]["content"] == "Hi there"
        assert state.messages[1]["department"] == "general"

    def test_add_department(self):
        """Test adding a department."""
        state = AgentState(query="test")

        # Add a department
        state.add_department("finance")

        assert state.departments == ["finance"]

        # Add the same department again
        state.add_department("finance")

        assert state.departments == ["finance"]  # No duplicates

        # Add another department
        state.add_department("marketing")

        assert state.departments == ["finance", "marketing"]

    def test_update_analysis(self):
        """Test updating analysis."""
        state = AgentState(query="test")

        # Update analysis
        state.update_analysis("finance", 0.8)

        assert state.analysis == {"finance": 0.8}

        # Update the same department
        state.update_analysis("finance", 0.9)

        assert state.analysis == {"finance": 0.9}

        # Add another department
        state.update_analysis("marketing", 0.7)

        assert state.analysis == {"finance": 0.9, "marketing": 0.7}

    def test_set_response(self):
        """Test setting the response."""
        state = AgentState(query="test")

        # Set response
        state.set_response("This is a response.")

        assert state.response == "This is a response."

        # Update response
        state.set_response("This is an updated response.")

        assert state.response == "This is an updated response."

    def test_update_metadata(self):
        """Test updating metadata."""
        state = AgentState(query="test")

        # Update metadata
        state.update_metadata(user_id="user123")

        assert "user_id" in state.metadata
        assert state.metadata["user_id"] == "user123"

        # Update existing field
        state.update_metadata(user_id="user456")

        assert state.metadata["user_id"] == "user456"

        # Add another field
        state.update_metadata(session_id="session123")

        assert "session_id" in state.metadata
        assert state.metadata["session_id"] == "session123"
        assert state.metadata["user_id"] == "user456"  # Existing field preserved


class TestCreateInitialState:
    """Tests for the create_initial_state function."""

    def test_create_initial_state_minimal(self):
        """Test creating an initial state with minimal parameters."""
        query = "What is the budget for Q2?"

        state = create_initial_state(query)

        assert state.query == query
        assert isinstance(state.rag, RagContext)
        assert state.messages == []
        assert state.departments == []
        assert state.analysis == {}
        assert state.response is None
        assert "timestamp" in state.metadata
        assert "version" in state.metadata
        assert "thread_id" in state.metadata
        assert "id" in state.metadata

    def test_create_initial_state_with_ids(self):
        """Test creating an initial state with user_id and thread_id."""
        query = "What is the budget for Q2?"
        user_id = "user123"
        thread_id = "thread456"

        state = create_initial_state(query, user_id=user_id, thread_id=thread_id)

        assert state.query == query
        assert "user_id" in state.metadata
        assert state.metadata["user_id"] == user_id
        assert "thread_id" in state.metadata
        assert state.metadata["thread_id"] == thread_id

    def test_create_initial_state_with_additional_metadata(self):
        """Test creating an initial state with additional metadata."""
        query = "What is the budget for Q2?"

        state = create_initial_state(
            query,
            session_id="session123",
            client_info={"browser": "Chrome"}
        )

        assert state.query == query
        assert "session_id" in state.metadata
        assert state.metadata["session_id"] == "session123"
        assert "client_info" in state.metadata
        assert state.metadata["client_info"] == {"browser": "Chrome"}
