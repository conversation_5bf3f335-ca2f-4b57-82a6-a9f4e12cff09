"""
Tests for the agent template functionality.
"""

import os
import pytest
from app.agents.templates import get_agent_prompt, load_template, render_template


def test_load_template():
    """Test loading a template."""
    template = load_template("co_ceo")
    assert template is not None
    assert len(template) > 0
    assert "Co-CEO Agent" in template


def test_load_nonexistent_template():
    """Test loading a nonexistent template."""
    template = load_template("nonexistent")
    assert template is not None
    assert "Nonexistent" in template


def test_render_template():
    """Test rendering a template with parameters."""
    template = "Hello, {name}!"
    rendered = render_template(template, name="World")
    assert rendered == "Hello, World!"


def test_get_agent_prompt():
    """Test getting an agent prompt."""
    prompt = get_agent_prompt("co_ceo")
    assert prompt is not None
    assert len(prompt) > 0
    assert "Co-CEO Agent" in prompt


def test_get_agent_prompt_with_params():
    """Test getting an agent prompt with parameters."""
    # Create a temporary template with parameters
    temp_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                           "app", "agents", "templates", "test")
    os.makedirs(temp_dir, exist_ok=True)
    
    with open(os.path.join(temp_dir, "system_prompt.txt"), "w") as f:
        f.write("Hello, {name}!")
    
    try:
        prompt = get_agent_prompt("test", name="World")
        assert prompt == "Hello, World!"
    finally:
        # Clean up
        os.remove(os.path.join(temp_dir, "system_prompt.txt"))
        os.rmdir(temp_dir)
