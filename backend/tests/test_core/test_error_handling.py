"""
Tests for the error handling utilities.

This module contains tests for the error handling utilities in app.core.errors.base.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch
import asyncio

from app.core.errors.base import (
    AppError, ValidationError, AuthenticationError, ResourceNotFoundError,
    <PERSON>rror<PERSON>ategory, ErrorSeverity, handle_errors, handle_async_errors,
    convert_exception_to_app_error, format_error_for_response,
    log_error, create_error_response, ErrorContext
)


class TestAppError:
    """Tests for the AppError class."""

    def test_init(self):
        """Test initializing an AppError."""
        error = AppError(
            message="Test error",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            details={"field": "test_field"},
            status_code=400
        )

        assert error.message == "Test error"
        assert error.category == ErrorCategory.VALIDATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.details == {"field": "test_field"}
        assert error.status_code == 400
        assert error.timestamp is not None

    def test_to_dict(self):
        """Test converting an AppError to a dictionary."""
        error = AppError(
            message="Test error",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            details={"field": "test_field"},
            status_code=400
        )

        error_dict = error.to_dict()

        assert error_dict["message"] == "Test error"
        assert error_dict["category"] == "validation"
        assert error_dict["severity"] == "warning"
        assert error_dict["details"] == {"field": "test_field"}
        assert error_dict["status_code"] == 400
        assert "timestamp" in error_dict

    def test_str(self):
        """Test string representation of an AppError."""
        error = AppError(
            message="Test error",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING
        )

        assert str(error) == "VALIDATION (warning): Test error"


class TestSpecificErrors:
    """Tests for specific error classes."""

    def test_validation_error(self):
        """Test ValidationError."""
        error = ValidationError(
            message="Invalid value",
            field="username",
            value="invalid@value"
        )

        assert error.message == "Invalid value"
        assert error.category == ErrorCategory.VALIDATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["field"] == "username"
        assert error.details["value"] == "invalid@value"
        assert error.status_code == 400

    def test_authentication_error(self):
        """Test AuthenticationError."""
        error = AuthenticationError()

        assert error.message == "Authentication failed"
        assert error.category == ErrorCategory.AUTHENTICATION
        assert error.severity == ErrorSeverity.WARNING
        assert error.status_code == 401

    def test_resource_not_found_error(self):
        """Test ResourceNotFoundError."""
        error = ResourceNotFoundError(
            resource_type="user",
            resource_id="123"
        )

        assert error.message == "Resource not found"
        assert error.category == ErrorCategory.RESOURCE_NOT_FOUND
        assert error.severity == ErrorSeverity.WARNING
        assert error.details["resource_type"] == "user"
        assert error.details["resource_id"] == "123"
        assert error.status_code == 404


class TestErrorDecorators:
    """Tests for error handling decorators."""

    def test_handle_errors(self):
        """Test the handle_errors decorator."""
        # Define a test function that raises an exception
        @handle_errors(fallback_return="fallback")
        def test_function(should_raise=True):
            if should_raise:
                raise ValueError("Test error")
            return "success"

        # Test with an exception
        result = test_function()
        assert result == "fallback"

        # Test without an exception
        result = test_function(should_raise=False)
        assert result == "success"

    def test_handle_errors_with_handler(self):
        """Test the handle_errors decorator with a custom error handler."""
        # Define a custom error handler
        def custom_handler(error, context):
            return f"Handled: {error}"

        # Define a test function with the custom handler
        @handle_errors(error_handler=custom_handler)
        def test_function():
            raise ValueError("Test error")

        # Test with an exception
        result = test_function()
        assert result.startswith("Handled:")

    def test_handle_errors_reraise(self):
        """Test the handle_errors decorator with reraise=True."""
        # Define a test function that reraises exceptions
        @handle_errors(reraise=True)
        def test_function():
            raise ValueError("Test error")

        # Test with an exception
        with pytest.raises(ValueError):
            test_function()

    @pytest.mark.asyncio
    async def test_handle_async_errors(self):
        """Test the handle_async_errors decorator."""
        # Define a test async function that raises an exception
        @handle_async_errors(fallback_return="fallback")
        async def test_function(should_raise=True):
            if should_raise:
                raise ValueError("Test error")
            return "success"

        # Test with an exception
        result = await test_function()
        assert result == "fallback"

        # Test without an exception
        result = await test_function(should_raise=False)
        assert result == "success"


class TestUtilityFunctions:
    """Tests for utility functions."""

    def test_convert_exception_to_app_error(self):
        """Test converting standard exceptions to AppErrors."""
        # Test ValueError
        value_error = ValueError("Invalid value")
        app_error = convert_exception_to_app_error(value_error)
        assert isinstance(app_error, ValidationError)
        assert app_error.message == "Invalid value"

        # Test KeyError
        key_error = KeyError("missing_key")
        app_error = convert_exception_to_app_error(key_error)
        assert isinstance(app_error, ResourceNotFoundError)

        # Test generic exception
        generic_error = Exception("Generic error")
        app_error = convert_exception_to_app_error(generic_error)
        assert isinstance(app_error, AppError)
        assert app_error.category == ErrorCategory.INTERNAL

    def test_format_error_for_response(self):
        """Test formatting errors for API responses."""
        # Test with AppError
        app_error = ValidationError(
            message="Invalid value",
            field="username"
        )
        response = format_error_for_response(app_error)
        assert response["message"] == "Invalid value"
        assert response["category"] == "validation"

        # Test with standard exception
        std_error = ValueError("Invalid value")
        response = format_error_for_response(std_error)
        assert response["message"] == "Invalid value"
        assert response["category"] == "validation"

    @patch("app.core.errors.base.logger")
    def test_log_error(self, mock_logger):
        """Test logging errors."""
        # Test with AppError
        app_error = ValidationError(
            message="Invalid value",
            field="username"
        )
        log_error(app_error, {"user_id": "123"})

        # Check that logger was called
        mock_logger.log.assert_called_once()

        # Test with standard exception
        mock_logger.reset_mock()
        std_error = ValueError("Invalid value")
        log_error(std_error)

        # Check that logger was called
        mock_logger.log.assert_called_once()

    def test_create_error_response(self):
        """Test creating standardized error responses."""
        # Test with AppError
        app_error = ValidationError(
            message="Invalid value",
            field="username"
        )
        response = create_error_response(app_error)

        assert response["success"] is False
        assert response["error"]["message"] == "Invalid value"
        assert response["error"]["type"] == "validation"
        assert response["error"]["code"] == 400
        assert "details" in response["error"]

        # Test without details
        response = create_error_response(app_error, include_details=False)
        assert "details" not in response["error"]

        # Test with standard exception
        std_error = ValueError("Invalid value")
        response = create_error_response(std_error)

        assert response["success"] is False
        assert response["error"]["message"] == "Invalid value"
        assert response["error"]["type"] == "validation"


class TestErrorContext:
    """Tests for the ErrorContext context manager."""

    def test_no_error(self):
        """Test ErrorContext with no error."""
        with ErrorContext("Test operation") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_with_error(self):
        """Test ErrorContext with an error."""
        with pytest.raises(ValueError):
            with ErrorContext("Test operation") as ctx:
                raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, AppError)

    def test_suppress_error(self):
        """Test ErrorContext with error suppression."""
        with ErrorContext("Test operation", reraise=False) as ctx:
            raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, AppError)
