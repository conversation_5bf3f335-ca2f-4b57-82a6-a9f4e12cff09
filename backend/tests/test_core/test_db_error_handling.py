"""
Tests for the database error handling utilities.

This module contains tests for the database error handling utilities in app.core.errors.db.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch

from app.core.errors.db import (
    handle_db_errors, handle_async_db_errors, DBErrorContext
)
from app.core.errors.base import (
    ValidationError, ResourceNotFoundError, DatabaseError
)


class TestDBErrorDecorators:
    """Tests for the database error handling decorators."""

    def test_handle_db_errors(self):
        """Test the handle_db_errors decorator."""
        # Define a test function that raises an exception
        @handle_db_errors(fallback_return="fallback")
        def test_function(should_raise=True, exception_type=ValueError):
            if should_raise:
                raise exception_type("Test error")
            return "success"

        # Test with an exception
        result = test_function()
        assert result == "fallback"

        # Test without an exception
        result = test_function(should_raise=False)
        assert result == "success"

    def test_handle_db_errors_with_integrity_error(self):
        """Test the handle_db_errors decorator with an integrity error."""
        # Create a mock SQLAlchemy IntegrityError
        class MockIntegrityError(Exception):
            pass

        MockIntegrityError.__module__ = "sqlalchemy.exc"
        MockIntegrityError.__name__ = "IntegrityError"

        # Define a test function that raises an integrity error
        @handle_db_errors(fallback_return="fallback")
        def test_function():
            raise MockIntegrityError("Unique constraint violation")

        # Test with an integrity error
        result = test_function()
        assert result == "fallback"

    def test_handle_db_errors_with_not_found_error(self):
        """Test the handle_db_errors decorator with a not found error."""
        # Create a mock SQLAlchemy NoResultFound
        class MockNoResultFound(Exception):
            pass

        MockNoResultFound.__module__ = "sqlalchemy.exc"
        MockNoResultFound.__name__ = "NoResultFound"

        # Define a test function that raises a not found error
        @handle_db_errors(fallback_return="fallback")
        def test_function():
            raise MockNoResultFound("No result found")

        # Test with a not found error
        result = test_function()
        assert result == "fallback"

    @pytest.mark.asyncio
    async def test_handle_async_db_errors(self):
        """Test the handle_async_db_errors decorator."""
        # Define a test async function that raises an exception
        @handle_async_db_errors(fallback_return="fallback")
        async def test_function(should_raise=True):
            if should_raise:
                raise ValueError("Test error")
            return "success"

        # Test with an exception
        result = await test_function()
        assert result == "fallback"

        # Test without an exception
        result = await test_function(should_raise=False)
        assert result == "success"


class TestDBErrorContext:
    """Tests for the DBErrorContext context manager."""

    def test_no_error(self):
        """Test DBErrorContext with no error."""
        with DBErrorContext("Test operation") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_with_error(self):
        """Test DBErrorContext with an error."""
        with pytest.raises(ValueError):
            with DBErrorContext("Test operation") as ctx:
                raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, DatabaseError)

    def test_with_integrity_error(self):
        """Test DBErrorContext with an integrity error."""
        # Create a mock SQLAlchemy IntegrityError
        class MockIntegrityError(Exception):
            pass

        MockIntegrityError.__module__ = "sqlalchemy.exc"
        MockIntegrityError.__name__ = "IntegrityError"

        with pytest.raises(MockIntegrityError):
            with DBErrorContext("Test operation") as ctx:
                raise MockIntegrityError("Unique constraint violation")

        assert isinstance(ctx.error, MockIntegrityError)
        assert isinstance(ctx.app_error, ValidationError)

    def test_with_not_found_error(self):
        """Test DBErrorContext with a not found error."""
        # Create a mock SQLAlchemy NoResultFound
        class MockNoResultFound(Exception):
            pass

        MockNoResultFound.__module__ = "sqlalchemy.exc"
        MockNoResultFound.__name__ = "NoResultFound"

        with pytest.raises(MockNoResultFound):
            with DBErrorContext("Test operation") as ctx:
                raise MockNoResultFound("No result found")

        assert isinstance(ctx.error, MockNoResultFound)
        assert isinstance(ctx.app_error, ResourceNotFoundError)

    def test_suppress_error(self):
        """Test DBErrorContext with error suppression."""
        with DBErrorContext("Test operation", reraise=False) as ctx:
            raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, DatabaseError)
