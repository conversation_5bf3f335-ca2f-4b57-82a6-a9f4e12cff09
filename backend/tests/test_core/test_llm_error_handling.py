"""
Tests for the LLM error handling utilities.

This module contains tests for the LLM error handling utilities in app.core.errors.llm.
"""
import pytest
import logging
from unittest.mock import MagicMock, patch
import asyncio

from app.core.errors.llm import (
    LLMError, LLMTimeoutError, LLMRateLimitError,
    handle_llm_errors, handle_async_llm_errors, LLMErrorContext
)


class TestLLMErrorClasses:
    """Tests for the LLM error classes."""

    def test_llm_error(self):
        """Test LLMError."""
        error = LLMError(
            message="Test error",
            provider="openai",
            model="gpt-4",
            operation="chat"
        )

        assert error.message == "Test error"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["operation"] == "chat"
        assert error.status_code == 502  # From ExternalServiceError

    def test_llm_timeout_error(self):
        """Test LLMTimeoutError."""
        error = LLMTimeoutError(
            message="Request timed out",
            provider="openai",
            model="gpt-4",
            operation="chat",
            timeout_seconds=30
        )

        assert error.message == "Request timed out"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["operation"] == "chat"
        assert error.details["timeout_seconds"] == 30
        assert error.status_code == 504  # From TimeoutError

    def test_llm_rate_limit_error(self):
        """Test LLMRateLimitError."""
        error = LLMRateLimitError(
            message="Rate limit exceeded",
            provider="openai",
            model="gpt-4",
            limit=100,
            reset_time="2023-01-01T00:00:00Z"
        )

        assert error.message == "Rate limit exceeded"
        assert error.details["provider"] == "openai"
        assert error.details["model"] == "gpt-4"
        assert error.details["limit"] == 100
        assert error.details["reset_time"] == "2023-01-01T00:00:00Z"
        assert error.status_code == 429  # From RateLimitError


class TestLLMErrorDecorators:
    """Tests for the LLM error handling decorators."""

    def test_handle_llm_errors(self):
        """Test the handle_llm_errors decorator."""
        # Define a test function that raises an exception
        @handle_llm_errors(fallback_return="fallback")
        def test_function(should_raise=True):
            if should_raise:
                raise ValueError("Test error")
            return "success"

        # Test with an exception
        result = test_function()
        assert result == "fallback"

        # Test without an exception
        result = test_function(should_raise=False)
        assert result == "success"

    @pytest.mark.asyncio
    async def test_handle_async_llm_errors(self):
        """Test the handle_async_llm_errors decorator."""
        # Define a test async function that raises an exception
        @handle_async_llm_errors(fallback_return="fallback")
        async def test_function(should_raise=True):
            if should_raise:
                raise ValueError("Test error")
            return "success"

        # Test with an exception
        result = await test_function()
        assert result == "fallback"

        # Test without an exception
        result = await test_function(should_raise=False)
        assert result == "success"

    @pytest.mark.asyncio
    async def test_handle_async_llm_errors_with_retry(self):
        """Test the handle_async_llm_errors decorator with retry."""
        # This test is simplified since the retry logic is now handled by tenacity
        # Define a test async function that raises an exception
        @handle_async_llm_errors(
            fallback_return="fallback"
        )
        async def test_function():
            raise asyncio.TimeoutError("Test timeout")

        # Test with an exception
        result = await test_function()

        # Check that fallback was returned
        assert result == "fallback"


class TestLLMErrorContext:
    """Tests for the LLMErrorContext context manager."""

    def test_no_error(self):
        """Test LLMErrorContext with no error."""
        with LLMErrorContext("Test operation", provider="openai", model="gpt-4") as ctx:
            pass

        assert ctx.error is None
        assert ctx.app_error is None

    def test_with_error(self):
        """Test LLMErrorContext with an error."""
        with pytest.raises(ValueError):
            with LLMErrorContext("Test operation", provider="openai", model="gpt-4") as ctx:
                raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, LLMError)
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_with_timeout_error(self):
        """Test LLMErrorContext with a timeout error."""
        with pytest.raises(asyncio.TimeoutError):
            with LLMErrorContext("Test operation", provider="openai", model="gpt-4") as ctx:
                raise asyncio.TimeoutError("Test timeout")

        assert isinstance(ctx.error, asyncio.TimeoutError)
        assert isinstance(ctx.app_error, LLMTimeoutError)
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"

    def test_suppress_error(self):
        """Test LLMErrorContext with error suppression."""
        with LLMErrorContext("Test operation", provider="openai", model="gpt-4", reraise=False) as ctx:
            raise ValueError("Test error")

        assert isinstance(ctx.error, ValueError)
        assert isinstance(ctx.app_error, LLMError)
        assert ctx.app_error.details["provider"] == "openai"
        assert ctx.app_error.details["model"] == "gpt-4"
