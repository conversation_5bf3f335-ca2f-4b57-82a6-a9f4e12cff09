"""Add query_logs table

Revision ID: xxxx
Revises: previous_revision_id
Create Date: 2023-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID
from app.core.db.pgvector import Vector
from app.config import get_settings

# revision identifiers, used by Alembic.
revision = 'xxxx'
down_revision = 'previous_revision_id'  # Update this to your actual previous revision ID
branch_labels = None
depends_on = None


def upgrade():
    # Create pgvector extension if it doesn't exist
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    
    # Create query_logs table
    op.create_table(
        'query_logs',
        sa.Column('id', UUID, primary_key=True, server_default=sa.text("gen_random_uuid()")),
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('user_id', UUID, sa.<PERSON>('users.id', ondelete='SET NULL'), nullable=True),
        sa.Column('thread_id', sa.String(), nullable=True),
        sa.Column('session_id', sa.String(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('department', sa.String(), nullable=True),
        sa.Column('top_results', JSONB, nullable=True),
        sa.Column('execution_time_ms', sa.Integer(), nullable=True),
        sa.Column('embedding', Vector(get_settings().VECTOR_DIMENSION), nullable=True),
        sa.Column('metadata', JSONB, server_default=sa.text("'{}'::jsonb"), nullable=False),
    )

    # Create indexes for efficient querying
    op.create_index('idx_query_logs_timestamp', 'query_logs', ['timestamp'])
    op.create_index('idx_query_logs_user_id', 'query_logs', ['user_id'])
    op.create_index('idx_query_logs_thread_id', 'query_logs', ['thread_id'])
    op.create_index('idx_query_logs_session_id', 'query_logs', ['session_id'])
    op.create_index('idx_query_logs_department', 'query_logs', ['department'])
    
    # Create vector index for similarity search
    op.execute(
        f"CREATE INDEX idx_query_logs_embedding ON query_logs USING hnsw (embedding vector_cosine_ops)"
    )


def downgrade():
    # Drop indexes
    op.execute("DROP INDEX IF EXISTS idx_query_logs_embedding")
    op.drop_index('idx_query_logs_department')
    op.drop_index('idx_query_logs_session_id')
    op.drop_index('idx_query_logs_thread_id')
    op.drop_index('idx_query_logs_user_id')
    op.drop_index('idx_query_logs_timestamp')

    # Drop table
    op.drop_table('query_logs')
