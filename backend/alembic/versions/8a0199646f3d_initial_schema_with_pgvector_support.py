"""Initial schema with pgvector support

Revision ID: 8a0199646f3d
Revises:
Create Date: 2025-05-10 18:40:36.380715

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, ARRAY, JSONB
import uuid


# revision identifiers, used by Alembic.
revision: str = '8a0199646f3d'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create pgvector extension if it doesn't exist
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')

    # Create users table
    op.create_table(
        'users',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('email', sa.String(255), unique=True, nullable=False),
        sa.Column('hashed_password', sa.String(255), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), default=True),
        sa.Column('email_verified', sa.Boolean(), default=False),
        sa.Column('roles', ARRAY(sa.String), default=[]),
        sa.Column('permissions', ARRAY(sa.String), default=[]),
        sa.Column('profile_data', JSONB, default={}),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.Column('account_locked', sa.Boolean(), default=False),
        sa.Column('lock_reason', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now())
    )

    # Create refresh_tokens table
    op.create_table(
        'refresh_tokens',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('token', sa.String(255), unique=True, nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_revoked', sa.Boolean(), default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    )

    # Create documents table
    op.create_table(
        'documents',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('metadata', JSONB, default={}),
        sa.Column('user_id', UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    )

    # Create document_chunks table with pgvector
    op.create_table(
        'document_chunks',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('document_id', UUID(as_uuid=True), nullable=False),
        sa.Column('chunk_index', sa.Integer(), nullable=False),
        sa.Column('text', sa.Text(), nullable=False),
        sa.Column('metadata', JSONB, default={}),
        sa.Column('embedding', sa.String(), nullable=True),  # Will be converted to vector type
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='CASCADE'),
    )

    # Add text search index to document_chunks
    op.execute(
        'ALTER TABLE document_chunks ADD COLUMN text_search tsvector '
        'GENERATED ALWAYS AS (to_tsvector(\'english\', text)) STORED'
    )
    op.create_index(
        'idx_document_chunks_text_search',
        'document_chunks',
        ['text_search'],
        postgresql_using='gin'
    )

    # Convert embedding column to vector type
    op.execute('ALTER TABLE document_chunks ALTER COLUMN embedding TYPE vector(768) USING embedding::vector')

    # Create HNSW index on embeddings
    op.execute(
        'CREATE INDEX idx_document_chunks_embedding ON document_chunks '
        'USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64)'
    )

    # Create conversation_checkpoints table
    op.create_table(
        'conversation_checkpoints',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('thread_id', sa.String(255), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), nullable=True),
        sa.Column('checkpoint_data', JSONB, nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    )

    # Create indexes
    op.create_index('idx_users_email', 'users', ['email'])
    op.create_index('idx_refresh_tokens_token', 'refresh_tokens', ['token'])
    op.create_index('idx_refresh_tokens_user_id', 'refresh_tokens', ['user_id'])
    op.create_index('idx_documents_user_id', 'documents', ['user_id'])
    op.create_index('idx_document_chunks_document_id', 'document_chunks', ['document_id'])
    op.create_index('idx_conversation_checkpoints_thread_id', 'conversation_checkpoints', ['thread_id'])
    op.create_index('idx_conversation_checkpoints_user_id', 'conversation_checkpoints', ['user_id'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop tables in reverse order
    op.drop_table('conversation_checkpoints')
    op.drop_table('document_chunks')
    op.drop_table('documents')
    op.drop_table('refresh_tokens')
    op.drop_table('users')

    # We don't drop the pgvector extension as it might be used by other applications
