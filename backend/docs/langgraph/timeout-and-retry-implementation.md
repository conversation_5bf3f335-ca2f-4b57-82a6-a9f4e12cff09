# 🛡️ Timeout and Retry Implementation

This document outlines the architecture and implementation details for the timeout and retry system in BusinessLM. The implementation provides comprehensive timeout and retry logic for LLM API calls, LangGraph node execution, RAG operations, and external service calls, ensuring system resilience and responsiveness.

> **Note**: This documentation serves as a comprehensive reference for the timeout and retry implementation in BusinessLM. It covers both the architectural design and implementation details to provide a complete understanding of the system.

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🧩 Components](#-components)
3. [⚙️ Configuration](#-configuration)
4. [🧪 Testing](#-testing)
5. [📊 Error Handling and Reporting](#-error-handling-and-reporting)
6. [🔄 Circuit Breaker Pattern](#-circuit-breaker-pattern)
7. [🚀 Usage Examples](#-usage-examples)
8. [📈 Performance Considerations](#-performance-considerations)
9. [📦 Deployment Considerations](#-deployment-considerations)
10. [📝 Conclusion](#-conclusion)

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 🔍 Overview

### Purpose

The timeout and retry system serves as a critical reliability component of BusinessLM, enabling:

1. Prevention of system hangs due to unresponsive external services
2. Graceful handling of temporary failures through retry mechanisms
3. Intelligent backoff strategies to avoid overwhelming services
4. Circuit breaking to prevent cascading failures
5. Comprehensive error reporting for observability

### Key Features

The timeout and retry implementation provides the following key features:

- **Configurable Timeouts**: Timeout durations configurable via environment variables
- **Exponential Backoff**: Retry with exponential backoff to avoid overwhelming services
- **Circuit Breaking**: Circuit breaker pattern to prevent cascading failures
- **Error Mapping**: Mapping of timeout and retry errors to appropriate error types
- **Fallback Mechanisms**: Fallback mechanisms for timeout and retry failures
- **Observability**: Comprehensive logging and metrics for timeout and retry events

### Core Components

The system consists of the following key components:

- **Core Timeout Utilities**: Generic timeout and retry utilities for use across the codebase
- **LangGraph Node Timeout**: Specialized timeout and retry utilities for LangGraph nodes
- **RAG Timeout Handling**: Timeout and retry utilities for RAG operations
- **Circuit Breaker**: Circuit breaker implementation for external service calls
- **Error Handling**: Comprehensive error handling and reporting

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 🚀 Quickstart (Local Development)

To use the timeout and retry utilities in your code:

```python
# Import the utilities (new modular structure)
from app.core.timeout import with_timeout_and_retry
from app.core.error_handling import TimeoutError

# Or using the deprecated compatibility module
# from app.core.timeout_utils import with_timeout_and_retry
# from app.core.error_handling import TimeoutError

# Use the timeout and retry wrapper
async def example_function():
    try:
        result = await with_timeout_and_retry(
            async_function,
            *args,
            timeout_seconds=10,
            operation_name="example_operation",
            max_attempts=3,
            **kwargs
        )
        return result
    except TimeoutError as e:
        # Handle timeout error
        logger.error(f"Operation timed out: {str(e)}")
        return fallback_result
```

Requirements:
- Python 3.10+
- asyncio
- tenacity (optional, falls back to manual implementation if not available)

> The timeout and retry utilities are designed to work with or without the tenacity library, providing a consistent interface regardless of the underlying implementation.

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 🧩 Components

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────┐
│                  Application Components                  │
├─────────────┬─────────────┬─────────────┬───────────────┤
│  LangGraph  │     RAG     │    LLM      │   External    │
│    Nodes    │ Operations  │   Calls     │   Services    │
└──────┬──────┴──────┬──────┴──────┬──────┴───────┬───────┘
       │             │             │              │
       ▼             ▼             ▼              ▼
┌─────────────┬─────────────┬─────────────┬───────────────┐
│  Node       │  RAG        │  LLM        │  Service      │
│  Timeout    │  Timeout    │  Timeout    │  Circuit      │
│  Decorators │  Utilities  │  Utilities  │  Breaker      │
└──────┬──────┴──────┬──────┴──────┬──────┴───────┬───────┘
       │             │             │              │
       └─────────────┼─────────────┼──────────────┘
                     ▼             ▼
         ┌───────────────────────────────────┐
         │       Core Timeout Utilities      │
         ├───────────────┬───────────────────┤
         │ with_timeout  │  with_retry       │
         └───────┬───────┴─────────┬─────────┘
                 │                 │
                 └────────┬────────┘
                          ▼
                ┌───────────────────┐
                │ with_timeout_and_ │
                │      retry        │
                └─────────┬─────────┘
                          │
                          ▼
                ┌───────────────────┐
                │   Error Handling  │
                │    & Reporting    │
                └───────────────────┘
```

### Implementation Structure

> **Note**: The timeout and retry system has been reorganized into a more modular structure. See [Timeout Utilities](../python-migration/timeout-utilities.md) for details on the new structure. The information below reflects the original structure but the functionality remains the same.

The timeout and retry system is implemented in the following files:

**Original Structure (Deprecated):**
```
backend/app/
├── core/
│   ├── __init__.py       # Exports public symbols
│   ├── timeout_utils.py  # Core timeout and retry utilities
│   └── error_handling.py # Error handling and reporting
├── langgraph/
│   ├── __init__.py       # Exports public symbols
│   └── timeout.py        # LangGraph node timeout decorators
└── rag/
    ├── __init__.py       # Exports public symbols
    └── timeout.py        # RAG timeout utilities
```

**New Modular Structure:**
```
backend/app/
├── core/
│   ├── __init__.py       # Exports public symbols
│   ├── timeout_utils.py  # Compatibility module (deprecated)
│   ├── error_handling.py # Error handling and reporting
│   └── timeout/          # New modular timeout utilities
│       ├── __init__.py   # Package exports
│       ├── base.py       # Base timeout and retry utilities
│       ├── llm.py        # LLM-specific timeout utilities
│       ├── rag.py        # RAG-specific timeout utilities
│       └── graph.py      # LangGraph-specific timeout utilities
├── langgraph/
│   ├── __init__.py       # Exports public symbols
│   └── timeout.py        # Compatibility module (deprecated)
└── rag/
    ├── __init__.py       # Exports public symbols
    └── timeout.py        # Compatibility module (deprecated)
```

### Core Timeout Utilities

The core timeout utilities are implemented in `backend/app/core/timeout/base.py` (previously in `backend/app/core/timeout_utils.py`) and provide:

- **Configurable Timeout Parameters**: Default timeout values for different operations that can be overridden via environment variables.
- **Async Timeout Wrapper**: Generic async function timeout wrapper using `asyncio.timeout`.
- **Retry Decorators**: Unified retry decorators using tenacity with exponential backoff.
- **Circuit Breaker Implementation**: State tracking for external services with failure threshold and recovery period.

```python
# Example: Using the timeout wrapper
result = await with_timeout(
    async_function,
    *args,
    timeout_seconds=10,
    operation_name="example_operation",
    **kwargs
)

# Example: Using the retry decorator
@with_retry(
    retry_on=[TimeoutError, RateLimitError],
    max_attempts=3,
    min_wait=1.0,
    max_wait=10.0
)
async def my_function():
    # Function implementation
    pass

# Example: Using timeout and retry together
result = await with_timeout_and_retry(
    async_function,
    *args,
    timeout_seconds=10,
    operation_name="example_operation",
    max_attempts=3,
    **kwargs
)

# Example: Using circuit breaker
cb = CircuitBreaker("example_service", failure_threshold=5, recovery_timeout=60.0)
result = await cb.execute(
    async_function,
    *args,
    fallback=fallback_function,
    **kwargs
)
```

### LangGraph Node Timeout

The LangGraph node timeout utilities are implemented in `backend/app/core/timeout/graph.py` (previously in `backend/app/langgraph/timeout.py`) and provide:

- **Node Timeout Wrapper**: Timeout wrapper for LangGraph node functions.
- **Node Retry Logic**: Retry mechanism for node execution failures.
- **State Management Integration**: Integration with state management for error reporting.

```python
# Example: Using the node timeout wrapper
result = await with_node_timeout_and_retry(
    node_function,
    state,
    *args,
    timeout_seconds=30,
    max_attempts=2,
    node_name="example_node",
    **kwargs
)

# Example: Using the node timeout decorator
@with_node_timeout(timeout_seconds=30, max_attempts=2, node_name="example_node")
async def my_node(state: AgentState) -> AgentState:
    # Node implementation
    return state

# Example: Using specific node timeout decorators
@with_analyze_query_timeout()
async def analyze_query_node(state: AgentState) -> AgentState:
    # Node implementation
    return state
```

### RAG Timeout Handling

The RAG timeout utilities are implemented in `backend/app/core/timeout/rag.py` (previously in `backend/app/rag/timeout.py`) and provide:

- **Embedding Generation Timeout**: Timeout wrapper for embedding generation.
- **Vector Search Timeout**: Timeout handling for vector search operations.
- **Fallback Mechanisms**: Fallback mechanisms for timeout scenarios.

```python
# Example: Using the embedding timeout wrapper
embeddings = await with_embedding_timeout(
    embedding_function,
    text,
    timeout_seconds=15,
    max_attempts=2,
    operation_name="generate_embeddings"
)

# Example: Using the vector search timeout wrapper
results = await with_vector_search_timeout(
    search_function,
    query,
    timeout_seconds=10,
    max_attempts=2,
    operation_name="vector_search",
    fallback_func=fallback_search_function
)

# Example: Using RAG timeout decorators
@with_embedding_timeout_decorator()
async def generate_embeddings(text: str) -> List[float]:
    # Embedding generation implementation
    pass

@with_vector_search_timeout_decorator(fallback_func=fallback_search_function)
async def search_vectors(query: str) -> List[Dict[str, Any]]:
    # Vector search implementation
    pass
```

## ⚙️ Configuration

The timeout and retry implementation uses the following configuration parameters:

### Default Timeout Values

```python
DEFAULT_TIMEOUTS = {
    "llm": int(os.getenv("TIMEOUT_LLM", "30")),
    "rag_search": int(os.getenv("TIMEOUT_RAG_SEARCH", "10")),
    "rag_embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "agent_node": int(os.getenv("TIMEOUT_AGENT_NODE", "40")),
    "external_service": int(os.getenv("TIMEOUT_EXTERNAL_SERVICE", "10")),
    "database": int(os.getenv("TIMEOUT_DATABASE", "5")),
}
```

### Default Retry Configuration

```python
DEFAULT_RETRY_CONFIG = {
    "max_attempts": int(os.getenv("RETRY_MAX_ATTEMPTS", "3")),
    "min_wait": float(os.getenv("RETRY_MIN_WAIT", "1.0")),
    "max_wait": float(os.getenv("RETRY_MAX_WAIT", "10.0")),
    "multiplier": float(os.getenv("RETRY_MULTIPLIER", "2.0")),
}
```

### Node-Specific Timeout Values

```python
NODE_TIMEOUTS = {
    "analyze_query": int(os.getenv("TIMEOUT_NODE_ANALYZE_QUERY", "30")),
    "retrieve_knowledge": int(os.getenv("TIMEOUT_NODE_RETRIEVE_KNOWLEDGE", "20")),
    "route_to_departments": int(os.getenv("TIMEOUT_NODE_ROUTE_TO_DEPARTMENTS", "40")),
    "generate_response": int(os.getenv("TIMEOUT_NODE_GENERATE_RESPONSE", "30")),
    "default": int(os.getenv("TIMEOUT_NODE_DEFAULT", "40")),
}
```

### RAG-Specific Timeout Values

```python
RAG_TIMEOUTS = {
    "embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "vector_search": int(os.getenv("TIMEOUT_RAG_VECTOR_SEARCH", "10")),
    "keyword_search": int(os.getenv("TIMEOUT_RAG_KEYWORD_SEARCH", "5")),
    "hybrid_search": int(os.getenv("TIMEOUT_RAG_HYBRID_SEARCH", "12")),
    "rerank": int(os.getenv("TIMEOUT_RAG_RERANK", "8")),
    "default": int(os.getenv("TIMEOUT_RAG_DEFAULT", "10")),
}
```

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 🧪 Testing

The timeout and retry implementation includes comprehensive testing to ensure correct behavior across different scenarios.

### Testing Strategy

The testing strategy for timeout and retry follows these principles:

1. **Isolation**: Tests are isolated from external dependencies for deterministic results
2. **Comprehensive Coverage**: Tests cover both happy paths and error scenarios
3. **Realistic Scenarios**: Tests simulate realistic timeout and retry scenarios
4. **Performance Validation**: Tests verify that timeout and retry mechanisms don't add significant overhead

### Test Implementation

The tests are implemented in the following files:

```
backend/tests/
├── core/
│   └── test_timeout_utils.py  # Tests for core timeout utilities
├── langgraph/
│   └── test_timeout_retry.py  # Tests for LangGraph node timeout decorators
└── rag/
    └── test_rag_timeout.py    # Tests for RAG timeout utilities
```

### Core Timeout Utilities Tests

The core timeout utilities tests verify:

```python
# Test basic timeout functionality
@pytest.mark.asyncio
async def test_basic_timeout_configuration():
    """Test that timeout can be configured and works as expected."""
    # Create a function that takes longer than the timeout
    async def slow_function():
        await asyncio.sleep(0.5)  # This should timeout
        return "This should not be returned"

    # Verify that the function times out
    with pytest.raises(TimeoutError):
        await with_timeout_and_retry(
            slow_function,
            timeout_seconds=0.1,
            max_attempts=1
        )

# Test retry logic
@pytest.mark.asyncio
async def test_retry_logic():
    """Test that retry logic works as expected."""
    # Create a mock that fails twice then succeeds
    mock_function = AsyncMock()
    mock_function.side_effect = [
        Exception("First failure"),
        Exception("Second failure"),
        "Success"
    ]

    # Create a function that uses the mock
    async def retried_function():
        return await mock_function()

    # Verify that the function retries and eventually succeeds
    result = await with_timeout_and_retry(
        retried_function,
        timeout_seconds=1.0,
        max_attempts=3,
        retry_on=[Exception]
    )
    assert result == "Success"
    assert mock_function.call_count == 3  # Called 3 times (1 initial + 2 retries)
```

### LangGraph Node Timeout Tests

The LangGraph node timeout tests verify:

1. **Department-Specific Timeouts**: Tests that department-specific timeout decorators work
2. **State Updates on Timeout**: Tests that state is properly updated when timeouts occur
3. **Error Propagation**: Tests that timeout errors are properly propagated and handled

### RAG Timeout Tests

The RAG timeout tests verify:

1. **Embedding Timeouts**: Tests that embedding generation timeouts are properly handled
2. **Vector Search Timeouts**: Tests that vector search timeouts are properly handled
3. **Fallback Mechanisms**: Tests that fallback mechanisms work as expected

## 📊 Error Handling and Reporting

The timeout and retry implementation integrates with the existing error handling framework:

- **Error Mapping**: Maps timeout and retry errors to appropriate `AppError` subclasses.
- **Error Logging**: Logs timeout and retry errors with appropriate context.
- **Error Reporting**: Reports timeout and retry errors through the state object.

## 🔄 Circuit Breaker Pattern

The circuit breaker pattern is implemented to prevent repeated calls to failing external services:

- **Failure Tracking**: Tracks consecutive failures for each service.
- **Circuit Opening**: Opens the circuit when a failure threshold is reached.
- **Recovery Period**: Allows a recovery period before testing if the service is back online.
- **Fallback Mechanisms**: Provides fallback mechanisms for when the circuit is open.

## 🚀 Usage Examples

### LangGraph Node with Timeout

```python
@with_analyze_query_timeout()
async def analyze_query_node(state: AgentState, co_ceo_agent) -> AgentState:
    """
    Node for analyzing the user query.

    Args:
        state: The current state
        co_ceo_agent: The Co-CEO agent

    Returns:
        Updated state
    """
    logger.info(f"Analyzing query: {state.query}")

    # Analyze the query
    analysis = await co_ceo_agent.analyze_query(state.query)

    # Update the state
    return AgentState(
        **state.model_dump(),
        query_analysis=analysis,
    )
```

### RAG Retriever with Timeout

```python
@with_hybrid_search_timeout_decorator(
    timeout_seconds=12,  # 12 seconds timeout for hybrid search
    max_attempts=2,      # 2 retry attempts
    fallback_func=None   # No fallback function, will return empty list on failure
)
async def retrieve(
    self,
    query: str,
    retrieval_strategy: Optional[Dict[str, Any]] = None,
    limit: int = 5,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve relevant documents using hybrid search.
    """
    # Implementation
    pass
```

### Process Query with Timeout

```python
async def process_query(
    query: str,
    co_ceo_agent,
    finance_agent,
    marketing_agent,
    knowledge_base_service,
    user_id: Optional[str] = None,
    thread_id: Optional[str] = None,
    timeout_seconds: Optional[float] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process a user query through the agent graph.
    """
    # Create initial state
    state = create_initial_state(query, user_id, thread_id, **kwargs)

    # Build the graph
    graph = build_co_ceo_graph(co_ceo_agent, finance_agent, marketing_agent, knowledge_base_service)

    try:
        # Execute the graph with timeout if specified
        if timeout_seconds:
            async with asyncio.timeout(timeout_seconds):
                result = await graph.ainvoke(state)
        else:
            result = await graph.ainvoke(state)

        return result
    except asyncio.TimeoutError:
        # Handle timeout
        pass
    except Exception as e:
        # Handle other exceptions
        pass
```

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 📈 Performance Considerations

The timeout and retry implementation includes several performance optimizations:

1. **Minimal Overhead**: The timeout and retry mechanisms add minimal overhead to operations
2. **Efficient Error Handling**: Error handling is designed to be efficient and non-blocking
3. **Configurable Parameters**: Timeout and retry parameters can be tuned for optimal performance
4. **Circuit Breaking**: Circuit breaking prevents wasting resources on known-failing services
5. **Fallback Mechanisms**: Fallback mechanisms provide responses even when operations fail

### Potential Bottlenecks

Potential performance bottlenecks to be aware of:

1. **Retry Overhead**: Multiple retries can add significant latency to operations
2. **Logging Overhead**: Excessive logging of timeout and retry events can impact performance
3. **Circuit Breaker State Management**: Managing circuit breaker state adds some overhead

### Optimization Strategies

Strategies for optimizing performance:

1. **Tuned Timeouts**: Configure appropriate timeout values for different operations
2. **Limited Retries**: Limit the number of retries to avoid excessive latency
3. **Selective Retry**: Only retry operations that are likely to succeed on retry
4. **Efficient Logging**: Use sampling and level-based logging to reduce logging overhead
5. **Caching**: Cache results to avoid repeated operations

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 📦 Deployment Considerations

When deploying the timeout and retry system to production, consider the following:

1. **Environment Variables**: Configure timeout and retry parameters via environment variables
2. **Monitoring**: Add monitoring for timeout and retry events
3. **Alerting**: Set up alerts for excessive timeouts or retries
4. **Circuit Breaker Tuning**: Tune circuit breaker parameters for production workloads
5. **Fallback Quality**: Ensure fallback mechanisms provide acceptable responses

### Production Readiness Checklist

- [ ] Configure appropriate timeout values for production environment
- [ ] Set up monitoring for timeout and retry events
- [ ] Configure alerting for excessive timeouts or retries
- [ ] Tune circuit breaker parameters for production workloads
- [ ] Test fallback mechanisms under production-like conditions
- [ ] Document timeout and retry configuration options
- [ ] Create runbooks for handling timeout and retry issues

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>

## 📝 Conclusion

The timeout and retry implementation provides comprehensive timeout and retry logic for the BusinessLM system. It ensures that the system remains responsive even when components experience delays or failures, and provides appropriate fallback mechanisms for timeout scenarios.

### Key Achievements

1. **Comprehensive Coverage**: Timeout and retry logic for all critical components
2. **Configurable Parameters**: Timeout and retry parameters configurable via environment variables
3. **Robust Error Handling**: Comprehensive error handling and reporting
4. **Circuit Breaking**: Circuit breaker pattern to prevent cascading failures
5. **Fallback Mechanisms**: Fallback mechanisms for timeout and retry failures
6. **Thorough Testing**: Comprehensive testing of timeout and retry mechanisms

### Design Principles

The implementation is designed to be:

- **Configurable**: Timeout and retry parameters can be configured via environment variables
- **Extensible**: The implementation can be extended to support additional components
- **Robust**: The implementation includes comprehensive error handling and reporting
- **Testable**: The implementation includes comprehensive tests
- **Maintainable**: The implementation follows clean code principles and is well-documented

### Path Forward

The timeout and retry system provides a solid foundation for system reliability. Future work should focus on:

1. **Enhanced Monitoring**: Add more detailed monitoring and metrics for timeout and retry events
2. **Adaptive Timeouts**: Implement adaptive timeouts based on historical performance
3. **Improved Fallbacks**: Enhance fallback mechanisms to provide better responses
4. **Circuit Breaker Enhancements**: Add more sophisticated circuit breaker logic
5. **Performance Optimization**: Optimize timeout and retry mechanisms for better performance

By continuing to improve and extend the timeout and retry system, BusinessLM can provide increasingly reliable and responsive interactions to users, even in the face of external service failures or delays.

<div align="right"><a href="#-timeout-and-retry-implementation">⬆️ Back to top</a></div>
