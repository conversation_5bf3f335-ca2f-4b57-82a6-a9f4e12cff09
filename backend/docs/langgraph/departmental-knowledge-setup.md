# 🧠 Departmental Knowledge Setup

This document provides a detailed explanation of how departmental knowledge boundaries are implemented in the BusinessLM system. It serves as a reference for understanding and modifying departmental knowledge and behavior, ensuring proper query routing and specialized responses.

> **Note**: This documentation details the knowledge domain boundaries between agents in the BusinessLM system. It explains how knowledge is partitioned, how queries are routed, and how to modify these boundaries to adapt the system to changing requirements.

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🧩 Knowledge Boundary Mechanisms](#-knowledge-boundary-mechanisms)
3. [👑 Co-CEO Agent Knowledge](#-co-ceo-agent-knowledge)
4. [💰 Finance Agent Knowledge](#-finance-agent-knowledge)
5. [📊 Marketing Agent Knowledge](#-marketing-agent-knowledge)
6. [🔄 Query Routing System](#-query-routing-system)
7. [🔎 Knowledge Retrieval Filtering](#-knowledge-retrieval-filtering)
8. [📝 Response Generation](#-response-generation)
9. [🛠️ Modifying Knowledge Boundaries](#-modifying-knowledge-boundaries)
10. [✅ Best Practices](#-best-practices)

## 🔍 Overview

### Purpose

The BusinessLM system uses a multi-agent architecture where each agent has specific knowledge boundaries. These boundaries serve several critical purposes:

1. **Specialization**: Enable agents to develop deep expertise in specific domains
2. **Efficient Routing**: Direct queries to the most appropriate department
3. **Collaboration**: Facilitate cross-departmental knowledge sharing for complex queries
4. **Coherent Responses**: Ensure consistent and authoritative answers within each domain
5. **Scalability**: Allow new departments to be added without disrupting existing ones

### Knowledge Domain Structure

Knowledge boundaries in the system determine:

1. **Access Scope**: What knowledge each agent can access and utilize
2. **Query Handling**: Which types of queries each agent should process
3. **Collaboration Patterns**: How agents share information to answer complex queries
4. **Response Authority**: Which agent has final say on specific topics

### Implementation Approach

The system implements knowledge boundaries through several complementary mechanisms, creating a robust framework for departmental specialization while enabling cross-departmental collaboration. This approach ensures:

- **Clear Responsibility**: Each department has well-defined areas of expertise
- **Minimal Redundancy**: Knowledge is primarily stored in one department
- **Flexible Routing**: Queries can be routed to multiple departments when needed
- **Coherent Synthesis**: Information from multiple departments is integrated seamlessly

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 🧩 Knowledge Boundary Mechanisms

The system uses the following mechanisms to implement knowledge boundaries:

### Multi-Layered Approach

Knowledge boundaries are enforced through a multi-layered approach:

1. **System Prompts**: Explicit knowledge domain definitions in agent prompt templates
   - **Files**:
     - `backend/app/agents/templates/co_ceo/system_prompt.txt`
     - `backend/app/agents/templates/finance/system_prompt.txt`
     - `backend/app/agents/templates/marketing/system_prompt.txt`
     - `backend/app/agents/templates/__init__.py`
   - **Key Functions**:
     - `get_agent_prompt(agent_type)` in `templates/__init__.py`

2. **Department Descriptions**: Semantic descriptions for embedding-based routing
   - **Files**:
     - `backend/app/agents/co_ceo.py`
   - **Key Components**:
     - `DEPARTMENT_DESCRIPTIONS` dictionary
     - `_calculate_department_similarity(query)` function

3. **Metadata Filtering**: Department-specific document filtering in knowledge retrieval
   - **Files**:
     - `backend/app/agents/finance.py`
     - `backend/app/agents/marketing.py`
   - **Key Functions**:
     - `_retrieve_finance_knowledge(query)` with `metadata_filter = {"department": "finance"}`
     - `_retrieve_marketing_knowledge(query)` with `metadata_filter = {"department": "marketing"}`

4. **Query Analysis**: Hybrid approach combining embedding similarity and LLM classification
   - **Files**:
     - `backend/app/agents/co_ceo.py`
   - **Key Functions**:
     - `analyze_query(query)`: Main orchestration function
     - `_calculate_department_similarity(query)`: Embedding-based similarity
     - `_llm_analyze_query(query)`: LLM-based classification
     - `_combine_analysis_results(embedding_scores, llm_analysis)`: Merges approaches

5. **Response Synthesis**: Department-specific response generation with specialized knowledge
   - **Files**:
     - `backend/app/agents/co_ceo.py`
     - `backend/app/agents/finance.py`
     - `backend/app/agents/marketing.py`
   - **Key Functions**:
     - `generate_response(query, analysis, department_responses)` in `co_ceo.py`
     - `_generate_direct_response(query, analysis)` in `co_ceo.py`
     - `_generate_finance_response(query, knowledge_results, context)` in `finance.py`
     - `_generate_marketing_response(query, knowledge_results, context)` in `marketing.py`

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                     Knowledge Boundaries                    │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│  ┌─────────────┐          ┌─────────────┐          ┌─────────────┐
│  │   Co-CEO    │◄────────►│   Finance   │◄────────►│  Marketing  │
│  │   Domain    │          │   Domain    │          │   Domain    │
│  └─────────────┘          └─────────────┘          └─────────────┘
│                                                             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Implementation Layers                    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ │   System    │ │ Department  │ │  Metadata   │ │    Query    │
│ │   Prompts   │ │Descriptions │ │  Filtering  │ │  Analysis   │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
└─────────────────────────────────────────────────────────────┘
```

### Implementation Strategy

The knowledge boundary implementation follows these principles:

1. **Explicit Definition**: Knowledge domains are explicitly defined in system prompts
2. **Semantic Routing**: Queries are routed based on semantic similarity to department descriptions
3. **Filtered Retrieval**: Document retrieval is filtered by department metadata
4. **Hybrid Analysis**: Query analysis combines embedding similarity with LLM classification
5. **Specialized Generation**: Response generation leverages department-specific knowledge

This multi-layered approach ensures that knowledge boundaries are maintained throughout the query processing pipeline, from initial routing to final response generation.

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 👑 Co-CEO Agent Knowledge

### Knowledge Domain Definition

The Co-CEO agent serves as the central coordinator with broad business knowledge and the ability to delegate specialized queries to department agents. Its knowledge domain is explicitly defined in `backend/app/agents/templates/co_ceo/system_prompt.txt`:

```
# KNOWLEDGE BOUNDARIES
You have broad knowledge of business operations and strategy, including:
- Company overview and strategic direction
- Cross-departmental knowledge and coordination
- General business principles and best practices

You should defer to specialized department agents for domain-specific details:
- Finance Agent: For detailed financial analysis, budgeting, forecasting, expense management
- Marketing Agent: For marketing campaigns, market research, brand strategy, content planning
```

### Implementation Details

The Co-CEO agent serves as the central coordinator with:

1. **Broad Knowledge**: General business knowledge that spans departments
2. **Coordination Knowledge**: Understanding of how departments interact
3. **Delegation Logic**: Knowledge of when to defer to specialized departments
4. **Synthesis Capability**: Ability to integrate information from multiple departments

### Knowledge Boundary Enforcement

The Co-CEO agent's knowledge boundaries are enforced through:

1. **Explicit Instructions**: Clear instructions in the system prompt about when to defer to specialists
2. **Example-Based Guidance**: Examples in the prompt that demonstrate proper delegation
3. **Query Analysis Logic**: Code that determines which departments should handle a query
4. **Response Synthesis**: Logic that integrates department responses while preserving attribution

### Code Implementation

The Co-CEO agent's knowledge boundaries are implemented in several key components:

#### System Prompt Definition

Located in `backend/app/agents/templates/co_ceo/system_prompt.txt`, this file contains explicit knowledge boundary definitions and examples of proper delegation.

#### Query Analysis Method

```python
async def analyze_query(self, query: str) -> Dict[str, Any]:
    """
    Analyze a query to determine its intent and relevant departments.

    This function uses a hybrid approach:
    1. Embedding-based similarity to calculate relevance scores
    2. LLM-based classification for more nuanced understanding
    """
    # Step 1: Calculate embedding-based similarity scores
    embedding_scores = await self._calculate_department_similarity(query)

    # Step 2: Use LLM to analyze the query
    llm_analysis = await self._llm_analyze_query(query)

    # Step 3: Combine the results
    combined_analysis = self._combine_analysis_results(embedding_scores, llm_analysis)

    return combined_analysis
```

#### Department Routing Method

```python
async def route_to_departments(self, query: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Route a query to relevant department agents.
    """
    logger.info(f"Routing query to departments: {analysis['relevant_departments']}")

    # Get the agent registry
    from app.agents.registry import agent_registry

    department_responses = []
    for dept in analysis["relevant_departments"]:
        # Get the department agent from the registry
        agent = agent_registry.get_agent(dept)

        if agent:
            # Create context with analysis results
            context = {
                "analysis": analysis,
                "additional_instructions": f"This query was routed to you by the Co-CEO agent..."
            }

            # Process the query with the department agent
            response = await agent.process_query(query, context)
            department_responses.append(response)

    return department_responses
```

#### Response Synthesis Method

```python
async def generate_response(
    self,
    query: str,
    analysis: Dict[str, Any],
    department_responses: List[Dict[str, Any]]
) -> str:
    """
    Generate a comprehensive response based on department inputs.
    """
    # If no department responses, generate a direct response
    if not department_responses:
        return await self._generate_direct_response(query, analysis)

    # Create the prompt for response synthesis
    system_prompt = get_agent_prompt("co_ceo")

    # Format department responses
    dept_responses_text = ""
    for resp in department_responses:
        dept_responses_text += f"\n## {resp['department'].title()} Department Response:\n{resp['response']}\n"

    # Generate synthesized response that integrates department knowledge
    # while maintaining proper attribution
    # ...
```

### Quickstart: Modifying Co-CEO Knowledge

To adjust the Co-CEO agent's knowledge boundaries:

1. Edit `backend/app/agents/templates/co_ceo/system_prompt.txt` to modify the knowledge domain definition
2. Update the `DEPARTMENT_DESCRIPTIONS` dictionary in `backend/app/agents/co_ceo.py` to adjust routing
3. Modify the relevance threshold in `_combine_analysis_results` to change routing sensitivity

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 💰 Finance Agent Knowledge

### Knowledge Domain Definition

The Finance agent specializes in financial matters and provides expert analysis and recommendations on financial topics. Its knowledge domain is explicitly defined in `backend/app/agents/templates/finance/system_prompt.txt`:

```
# KNOWLEDGE DOMAIN
You have specialized knowledge in:
- Budgeting and financial planning
- Financial reporting and analysis
- Forecasting and projections
- Expense management and cost control
- Investment analysis and capital allocation
- Cash flow management
- Financial metrics and KPIs
- Tax planning and compliance
- Risk management and financial controls
```

### Implementation Details

The Finance agent specializes in financial matters with:

1. **Domain-Specific Knowledge**: Detailed financial expertise across multiple areas
2. **Financial Analysis**: Ability to analyze financial data and metrics with precision
3. **Financial Recommendations**: Capability to provide actionable financial advice
4. **Financial Terminology**: Expertise in using appropriate financial terminology
5. **Financial Context**: Understanding of financial implications across business functions

### Knowledge Boundary Enforcement

The Finance agent's knowledge boundaries are enforced through:

1. **Specialized Prompt**: Detailed financial knowledge domain in the system prompt
2. **Example-Based Guidance**: Examples in the prompt that demonstrate financial analysis
3. **Metadata Filtering**: Document retrieval filtered to finance-specific content
4. **Specialized Response Format**: Financial response formatting with appropriate metrics and terminology

### Code Implementation

The Finance agent's knowledge boundaries are implemented in several key components:

#### System Prompt Definition

Located in `backend/app/agents/templates/finance/system_prompt.txt`, this file contains the detailed financial knowledge domain and examples of financial analysis.

#### Knowledge Retrieval Method

```python
async def _retrieve_finance_knowledge(self, query: str) -> List[Dict[str, Any]]:
    """
    Retrieve finance-related knowledge for the query.
    """
    logger.info(f"Retrieving finance knowledge for: {query}")

    # Filter for finance-related documents
    metadata_filter = {"department": "finance"}

    try:
        results = await self.knowledge_base_service.retrieve(
            query=query,
            top_k=5,
            metadata_filter=metadata_filter
        )

        logger.info(f"Retrieved {len(results)} finance documents")
        return results
    except Exception as e:
        logger.error(f"Error retrieving finance knowledge: {e}", exc_info=True)
        # Return an empty list if retrieval fails
        return []
```

#### Response Generation Method

```python
async def _generate_finance_response(
    self,
    query: str,
    knowledge_results: List[Dict[str, Any]],
    context: Dict[str, Any]
) -> str:
    """
    Generate a finance-specific response using the LLM.
    """
    # Get the finance agent prompt
    system_prompt = get_agent_prompt("finance")

    # Format the knowledge context
    knowledge_context = ""
    for i, doc in enumerate(knowledge_results, 1):
        content = doc.get("content", "")
        source = doc.get("source", "Unknown source")
        knowledge_context += f"\n[Document {i}] {content}\nSource: {source}\n"

    # Create the user prompt with finance-specific instructions
    user_prompt = f"""
    Please respond to the following finance-related query using the provided knowledge context.

    Query: "{query}"

    Knowledge Context:
    {knowledge_context if knowledge_context else "No specific finance documents found for this query."}

    Please provide a comprehensive, accurate response that:
    1. Directly addresses the finance query
    2. Uses the provided knowledge context
    3. Cites sources where appropriate
    4. Provides financial analysis and insights
    5. Uses appropriate financial terminology
    6. Includes relevant financial metrics and data
    7. Acknowledges any limitations in the available information
    """

    # Call the LLM with finance-specific prompt
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

### Quickstart: Modifying Finance Knowledge

To adjust the Finance agent's knowledge boundaries:

1. Edit `backend/app/agents/templates/finance/system_prompt.txt` to modify the financial knowledge domain
2. Update the metadata filter in `_retrieve_finance_knowledge` to adjust document retrieval
3. Modify the response generation instructions in `_generate_finance_response` to change response format

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 📊 Marketing Agent Knowledge

### Knowledge Domain Definition

The Marketing agent specializes in marketing strategy, campaign analysis, and market research. Its knowledge domain is explicitly defined in `backend/app/agents/templates/marketing/system_prompt.txt`:

```
# KNOWLEDGE DOMAIN
You have specialized knowledge in:
- Marketing strategy and planning
- Campaign analysis and optimization
- Market research and competitive analysis
- Brand strategy and positioning
- Content marketing and creation
- Digital marketing channels (social media, email, SEO, PPC)
- Customer segmentation and targeting
- Marketing metrics and KPIs
- Marketing budget allocation
- Marketing trends and best practices
```

### Implementation Details

The Marketing agent specializes in marketing matters with:

1. **Domain-Specific Knowledge**: Detailed marketing expertise across multiple channels and strategies
2. **Campaign Analysis**: Ability to analyze marketing campaigns and metrics with precision
3. **Marketing Recommendations**: Capability to provide actionable marketing advice
4. **Marketing Terminology**: Expertise in using appropriate marketing terminology
5. **Market Context**: Understanding of market dynamics and competitive landscapes

### Knowledge Boundary Enforcement

The Marketing agent's knowledge boundaries are enforced through:

1. **Specialized Prompt**: Detailed marketing knowledge domain in the system prompt
2. **Example-Based Guidance**: Examples in the prompt that demonstrate marketing analysis
3. **Metadata Filtering**: Document retrieval filtered to marketing-specific content
4. **Specialized Response Format**: Marketing response formatting with appropriate metrics and terminology

### Code Implementation

The Marketing agent's knowledge boundaries are implemented in several key components:

#### System Prompt Definition

Located in `backend/app/agents/templates/marketing/system_prompt.txt`, this file contains the detailed marketing knowledge domain and examples of marketing analysis.

#### Knowledge Retrieval Method

```python
async def _retrieve_marketing_knowledge(self, query: str) -> List[Dict[str, Any]]:
    """
    Retrieve marketing-related knowledge for the query.
    """
    logger.info(f"Retrieving marketing knowledge for: {query}")

    # Filter for marketing-related documents
    metadata_filter = {"department": "marketing"}

    try:
        results = await self.knowledge_base_service.retrieve(
            query=query,
            top_k=5,
            metadata_filter=metadata_filter
        )

        logger.info(f"Retrieved {len(results)} marketing documents")
        return results
    except Exception as e:
        logger.error(f"Error retrieving marketing knowledge: {e}", exc_info=True)
        # Return an empty list if retrieval fails
        return []
```

#### Response Generation Method

```python
async def _generate_marketing_response(
    self,
    query: str,
    knowledge_results: List[Dict[str, Any]],
    context: Dict[str, Any]
) -> str:
    """
    Generate a marketing-specific response using the LLM.
    """
    # Get the marketing agent prompt
    system_prompt = get_agent_prompt("marketing")

    # Format the knowledge context
    knowledge_context = ""
    for i, doc in enumerate(knowledge_results, 1):
        content = doc.get("content", "")
        source = doc.get("source", "Unknown source")
        knowledge_context += f"\n[Document {i}] {content}\nSource: {source}\n"

    # Create the user prompt with marketing-specific instructions
    user_prompt = f"""
    Please respond to the following marketing-related query using the provided knowledge context.

    Query: "{query}"

    Knowledge Context:
    {knowledge_context if knowledge_context else "No specific marketing documents found for this query."}

    Please provide a comprehensive, accurate response that:
    1. Directly addresses the marketing query
    2. Uses the provided knowledge context
    3. Cites sources where appropriate
    4. Provides marketing analysis and insights
    5. Uses appropriate marketing terminology
    6. Includes relevant marketing metrics and data
    7. Acknowledges any limitations in the available information
    """

    # Call the LLM with marketing-specific prompt
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

### Quickstart: Modifying Marketing Knowledge

To adjust the Marketing agent's knowledge boundaries:

1. Edit `backend/app/agents/templates/marketing/system_prompt.txt` to modify the marketing knowledge domain
2. Update the metadata filter in `_retrieve_marketing_knowledge` to adjust document retrieval
3. Modify the response generation instructions in `_generate_marketing_response` to change response format

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 🔄 Query Routing System

The system uses a sophisticated query routing mechanism to direct queries to the appropriate departments, ensuring that each query is handled by the most relevant specialized agents.

### Architecture Overview

The query routing system follows this flow:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Query     │────▶│   Analyze   │────▶│    Route    │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
                    ┌─────────────────────────┼─────────────────────────┐
                    │                         │                         │
                    ▼                         ▼                         ▼
            ┌─────────────┐           ┌─────────────┐           ┌─────────────┐
            │   Finance   │           │  Marketing  │           │    Other    │
            │    Agent    │           │    Agent    │           │    Agents   │
            └─────────────┘           └─────────────┘           └─────────────┘
                    │                         │                         │
                    └─────────────────────────┼─────────────────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │  Synthesize │
                                      │  Response   │
                                      └─────────────┘
```

### Department Descriptions

Department descriptions for embedding-based similarity are defined in `backend/app/agents/co_ceo.py`:

```python
# Department descriptions for embedding-based similarity
DEPARTMENT_DESCRIPTIONS = {
    "finance": "Financial analysis, budgeting, forecasting, expense management, financial reporting, cash flow, investments, financial metrics, profit and loss, balance sheet, financial planning",
    "marketing": "Marketing campaigns, market research, brand strategy, content planning, digital marketing, social media, email marketing, SEO, advertising, customer acquisition, marketing metrics, marketing ROI"
}
```

These descriptions serve as semantic anchors for the embedding-based similarity calculation, providing a rich representation of each department's knowledge domain.

### Hybrid Query Analysis

The Co-CEO agent uses a hybrid approach to analyze queries, combining the strengths of embedding-based similarity and LLM-based classification:

#### Embedding-Based Similarity

Calculates semantic similarity between the query and department descriptions using vector embeddings:

```python
async def _calculate_department_similarity(self, query: str) -> Dict[str, float]:
    """
    Calculate similarity scores between the query and department descriptions.
    """
    # Get embeddings for the query and department descriptions
    query_embedding = await self.knowledge_base_service.embedding_model.embed_query(query)

    department_embeddings = {}
    for dept, desc in DEPARTMENT_DESCRIPTIONS.items():
        dept_embedding = await self.knowledge_base_service.embedding_model.embed_query(desc)
        department_embeddings[dept] = dept_embedding

    # Calculate similarity scores
    similarity_scores = {}
    for dept, embedding in department_embeddings.items():
        similarity = cosine_similarity(query_embedding, embedding)
        similarity_scores[dept] = float(similarity)

    return similarity_scores
```

#### LLM-Based Classification

Uses the LLM to analyze the query and determine relevant departments based on semantic understanding:

```python
async def _llm_analyze_query(self, query: str) -> Dict[str, Any]:
    """
    Use the LLM to analyze the query for intent and relevant departments.
    """
    # Create the prompt for query analysis
    system_prompt = get_agent_prompt("co_ceo")

    user_prompt = f"""
    Please analyze the following query and determine:
    1. The query type (informational, analytical, strategic, etc.)
    2. The relevant departments that should be consulted
    3. The priority level of the query (low, normal, high)

    Query: "{query}"

    Return your analysis as a JSON object with the following structure:
    {{
      "query_type": "string",
      "relevant_departments": ["department1", "department2"],
      "priority": "string",
      "topics": ["topic1", "topic2"],
      "explanation": "string"
    }}

    Available departments: {", ".join(DEPARTMENTS)}
    """

    # Call the LLM and parse the response
    # ...
```

#### Combined Analysis

The system combines both approaches to get the benefits of each:

```python
def _combine_analysis_results(
    self,
    embedding_scores: Dict[str, float],
    llm_analysis: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Combine embedding-based similarity scores with LLM analysis.
    """
    # Start with the LLM analysis
    combined = llm_analysis.copy()

    # Add embedding scores
    combined["embedding_scores"] = embedding_scores

    # Determine relevant departments based on both methods
    llm_departments = set(llm_analysis.get("relevant_departments", []))

    # Add departments with high embedding scores
    embedding_departments = {
        dept for dept, score in embedding_scores.items()
        if score > 0.35  # Threshold for relevance
    }

    # Combine departments from both methods
    all_departments = llm_departments.union(embedding_departments)

    # If no departments were selected, pick the one with the highest embedding score
    if not all_departments and embedding_scores:
        top_dept = max(embedding_scores.items(), key=lambda x: x[1])[0]
        all_departments = {top_dept}

    combined["relevant_departments"] = list(all_departments)

    return combined
```

### Configurable Relevance Threshold

The system uses a configurable threshold to determine if a department is relevant:

```python
# Add departments with high embedding scores
embedding_departments = {
    dept for dept, score in embedding_scores.items()
    if score > 0.35  # Threshold for relevance
}
```

This threshold can be adjusted to make the routing more or less aggressive:
- Lower threshold (e.g., 0.25): More departments will be consulted (higher recall)
- Higher threshold (e.g., 0.45): Fewer departments will be consulted (higher precision)

### Fallback Logic

If no departments are deemed relevant, the system selects the department with the highest similarity score:

```python
# If no departments were selected, pick the one with the highest embedding score
if not all_departments and embedding_scores:
    top_dept = max(embedding_scores.items(), key=lambda x: x[1])[0]
    all_departments = {top_dept}
```

This ensures that every query is routed to at least one department, even if the relevance scores are low.

### Routing Implementation

Once the relevant departments are determined, the Co-CEO agent routes the query to those departments:

```python
async def route_to_departments(self, query: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Route a query to relevant department agents.
    """
    logger.info(f"Routing query to departments: {analysis['relevant_departments']}")

    # Get the agent registry
    from app.agents.registry import agent_registry

    department_responses = []
    for dept in analysis["relevant_departments"]:
        # Get the department agent from the registry
        agent = agent_registry.get_agent(dept)

        if agent:
            # Process the query with the department agent
            response = await agent.process_query(query, context)
            department_responses.append(response)

    return department_responses
```

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 🔎 Knowledge Retrieval Filtering

Each department agent implements specialized knowledge retrieval that filters documents by department, ensuring that agents only access information within their knowledge domain.

### Metadata-Based Filtering Architecture

The knowledge retrieval system uses metadata filtering to enforce knowledge boundaries:

```
┌─────────────────────────────────────────────────────────────┐
│                    Knowledge Base Service                    │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     Document Collection                      │
│                                                             │
│  ┌─────────────┐          ┌─────────────┐          ┌─────────────┐
│  │  Finance    │          │  Marketing  │          │    Other    │
│  │ Documents   │          │ Documents   │          │ Documents   │
│  │             │          │             │          │             │
│  │ metadata:   │          │ metadata:   │          │ metadata:   │
│  │ department: │          │ department: │          │ department: │
│  │  "finance"  │          │ "marketing" │          │   "other"   │
│  └─────────────┘          └─────────────┘          └─────────────┘
│                                                             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Department-Specific                       │
│                    Knowledge Retrieval                       │
└─────────────────────────────────────────────────────────────┘
```

### Finance Knowledge Retrieval

The Finance agent retrieves only finance-related documents using metadata filtering:

```python
async def _retrieve_finance_knowledge(self, query: str) -> List[Dict[str, Any]]:
    """
    Retrieve finance-related knowledge for the query.
    """
    logger.info(f"Retrieving finance knowledge for: {query}")

    # Filter for finance-related documents
    metadata_filter = {"department": "finance"}

    try:
        results = await self.knowledge_base_service.retrieve(
            query=query,
            top_k=5,
            metadata_filter=metadata_filter
        )

        logger.info(f"Retrieved {len(results)} finance documents")
        return results
    except Exception as e:
        logger.error(f"Error retrieving finance knowledge: {e}", exc_info=True)
        # Return an empty list if retrieval fails
        return []
```

### Marketing Knowledge Retrieval

Similarly, the Marketing agent retrieves only marketing-related documents:

```python
async def _retrieve_marketing_knowledge(self, query: str) -> List[Dict[str, Any]]:
    """
    Retrieve marketing-related knowledge for the query.
    """
    logger.info(f"Retrieving marketing knowledge for: {query}")

    # Filter for marketing-related documents
    metadata_filter = {"department": "marketing"}

    try:
        results = await self.knowledge_base_service.retrieve(
            query=query,
            top_k=5,
            metadata_filter=metadata_filter
        )

        logger.info(f"Retrieved {len(results)} marketing documents")
        return results
    except Exception as e:
        logger.error(f"Error retrieving marketing knowledge: {e}", exc_info=True)
        # Return an empty list if retrieval fails
        return []
```

### Advanced Filtering Options

The metadata filtering system supports more complex filtering beyond just department:

```python
# Example of more complex filtering
metadata_filter = {
    "department": "finance",
    "document_type": "report",
    "confidentiality": "public"
}

# Example of date-based filtering
metadata_filter = {
    "department": "marketing",
    "created_after": "2023-01-01",
    "campaign": "summer_promotion"
}
```

### Knowledge Base Service Implementation

The `KnowledgeBaseService` implements the retrieval functionality with metadata filtering:

```python
async def retrieve(
    self,
    query: str,
    top_k: int = 5,
    metadata_filter: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve relevant documents for a query with optional metadata filtering.

    Args:
        query: The search query
        top_k: Number of documents to retrieve
        metadata_filter: Optional metadata filters to apply

    Returns:
        List of relevant documents with content and metadata
    """
    # Generate query embedding
    query_embedding = await self.embedding_model.embed_query(query)

    # Apply metadata filter if provided
    filter_condition = None
    if metadata_filter:
        filter_condition = self._build_filter_condition(metadata_filter)

    # Perform vector search with metadata filtering
    results = self.vector_store.search(
        query_embedding,
        top_k=top_k,
        filter=filter_condition
    )

    return results
```

### Document Ingestion with Metadata

When documents are ingested into the knowledge base, they are tagged with appropriate metadata:

```python
async def add_documents(
    self,
    documents: List[str],
    metadata: List[Dict[str, Any]]
) -> List[str]:
    """
    Add documents to the knowledge base with metadata.

    Args:
        documents: List of document texts
        metadata: List of metadata dictionaries for each document

    Returns:
        List of document IDs
    """
    # Ensure each document has department metadata
    for meta in metadata:
        if "department" not in meta:
            raise ValueError("Each document must have a 'department' metadata field")

    # Generate embeddings
    embeddings = await self.embedding_model.embed_documents(documents)

    # Add to vector store with metadata
    doc_ids = self.vector_store.add(
        embeddings=embeddings,
        documents=documents,
        metadata=metadata
    )

    return doc_ids
```

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 📝 Response Generation

Each agent generates responses that reflect its specialized knowledge domain, ensuring that the information provided stays within appropriate knowledge boundaries while providing comprehensive answers.

### Response Generation Architecture

The response generation system follows this flow:

```
┌─────────────────────────────────────────────────────────────┐
│                    Knowledge Boundaries                      │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Department-Specific                       │
│                    Response Generation                       │
│                                                             │
│  ┌─────────────┐          ┌─────────────┐          ┌─────────────┐
│  │   Finance   │          │  Marketing  │          │    Other    │
│  │  Response   │          │  Response   │          │  Response   │
│  └─────────────┘          └─────────────┘          └─────────────┘
│                                                             │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Co-CEO Response                           │
│                    Synthesis                                 │
└─────────────────────────────────────────────────────────────┘
```

### Department-Specific Response Generation

#### Finance Response Generation

The Finance agent generates responses that incorporate financial expertise and terminology:

```python
async def _generate_finance_response(
    self,
    query: str,
    knowledge_results: List[Dict[str, Any]],
    context: Dict[str, Any]
) -> str:
    """
    Generate a finance-specific response using the LLM.
    """
    # Get the finance agent prompt
    system_prompt = get_agent_prompt("finance")

    # Format the knowledge context
    knowledge_context = ""
    for i, doc in enumerate(knowledge_results, 1):
        content = doc.get("content", "")
        source = doc.get("source", "Unknown source")
        knowledge_context += f"\n[Document {i}] {content}\nSource: {source}\n"

    # Create the user prompt with finance-specific instructions
    user_prompt = f"""
    Please respond to the following finance-related query using the provided knowledge context.

    Query: "{query}"

    Knowledge Context:
    {knowledge_context if knowledge_context else "No specific finance documents found for this query."}

    Please provide a comprehensive, accurate response that:
    1. Directly addresses the finance query
    2. Uses the provided knowledge context
    3. Cites sources where appropriate
    4. Provides financial analysis and insights
    5. Uses appropriate financial terminology
    6. Includes relevant financial metrics and data
    7. Acknowledges any limitations in the available information
    """

    # Call the LLM with finance-specific prompt
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

#### Marketing Response Generation

The Marketing agent generates responses that incorporate marketing expertise and terminology:

```python
async def _generate_marketing_response(
    self,
    query: str,
    knowledge_results: List[Dict[str, Any]],
    context: Dict[str, Any]
) -> str:
    """
    Generate a marketing-specific response using the LLM.
    """
    # Get the marketing agent prompt
    system_prompt = get_agent_prompt("marketing")

    # Format the knowledge context
    knowledge_context = ""
    for i, doc in enumerate(knowledge_results, 1):
        content = doc.get("content", "")
        source = doc.get("source", "Unknown source")
        knowledge_context += f"\n[Document {i}] {content}\nSource: {source}\n"

    # Create the user prompt with marketing-specific instructions
    user_prompt = f"""
    Please respond to the following marketing-related query using the provided knowledge context.

    Query: "{query}"

    Knowledge Context:
    {knowledge_context if knowledge_context else "No specific marketing documents found for this query."}

    Please provide a comprehensive, accurate response that:
    1. Directly addresses the marketing query
    2. Uses the provided knowledge context
    3. Cites sources where appropriate
    4. Provides marketing analysis and insights
    5. Uses appropriate marketing terminology
    6. Includes relevant marketing metrics and data
    7. Acknowledges any limitations in the available information
    """

    # Call the LLM with marketing-specific prompt
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

### Co-CEO Response Synthesis

The Co-CEO agent synthesizes responses from department agents, integrating their specialized knowledge while maintaining a coherent narrative:

```python
async def generate_response(
    self,
    query: str,
    analysis: Dict[str, Any],
    department_responses: List[Dict[str, Any]]
) -> str:
    """
    Generate a comprehensive response based on department inputs.
    """
    # If no department responses, generate a direct response
    if not department_responses:
        return await self._generate_direct_response(query, analysis)

    # Create the prompt for response synthesis
    system_prompt = get_agent_prompt("co_ceo")

    # Format department responses
    dept_responses_text = ""
    for resp in department_responses:
        dept_responses_text += f"\n## {resp['department'].title()} Department Response:\n{resp['response']}\n"

    user_prompt = f"""
    I need you to synthesize a comprehensive response to the user's query based on the information provided by different departments.

    User Query: "{query}"

    Query Analysis:
    - Type: {analysis.get('query_type', 'general')}
    - Topics: {', '.join(analysis.get('topics', []))}
    - Priority: {analysis.get('priority', 'normal')}

    Department Responses:
    {dept_responses_text}

    Please synthesize a coherent, comprehensive response that:
    1. Addresses the user's query directly
    2. Integrates information from all departments
    3. Resolves any contradictions or inconsistencies
    4. Provides clear, actionable insights
    5. Uses a professional, helpful tone
    6. Maintains proper attribution to departments where appropriate
    7. Presents a unified organizational voice
    """

    # Call the LLM with synthesis prompt
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

### Direct Response Generation

When no departments are consulted, the Co-CEO agent generates a direct response:

```python
async def _generate_direct_response(self, query: str, analysis: Dict[str, Any]) -> str:
    """
    Generate a direct response when no departments are consulted.
    """
    system_prompt = get_agent_prompt("co_ceo")

    user_prompt = f"""
    Please respond directly to the following user query:

    "{query}"

    Since this query doesn't require specific department expertise, provide a helpful,
    general response based on your knowledge as the Co-CEO Agent.

    Please ensure your response:
    1. Addresses the query directly
    2. Provides helpful information within your general knowledge domain
    3. Acknowledges any limitations in your knowledge
    4. Suggests consulting specific departments if the query might benefit from specialized expertise
    """

    # Call the LLM
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    response = await self.llm_adapter.chat(messages)
    return response
```

### Response Formatting Standards

To maintain consistency across departments while preserving their unique expertise, the system follows these response formatting standards:

1. **Clear Section Headers**: All responses use consistent section headers
2. **Citation Format**: Sources are cited in a standard format: `[Source: Document Title]`
3. **Confidence Indicators**: Uncertain information includes confidence levels
4. **Department Attribution**: Information from specific departments is clearly attributed
5. **Consistent Terminology**: Core business terms are used consistently across departments
6. **Structured Format**: Responses follow a consistent structure:
   - Summary
   - Detailed Analysis
   - Recommendations
   - Limitations/Caveats

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## 🛠️ Modifying Knowledge Boundaries

This section provides detailed instructions for modifying the knowledge boundaries of the system to adapt to changing requirements or to add new departments.

### Modification Architecture

The knowledge boundary modification process follows this flow:

```
┌─────────────────────────────────────────────────────────────┐
│                    Knowledge Boundary                        │
│                    Modification Points                       │
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │  System Prompts │ │   Department    │ │    Metadata     │
    │                 │ │  Descriptions   │ │     Filters     │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
                │               │               │
                └───────────────┼───────────────┘
                                │
                                ▼
    ┌─────────────────────────────────────────────────────────┐
    │                    Testing and                           │
    │                    Validation                            │
    └─────────────────────────────────────────────────────────┘
```

### 1. System Prompts

To change an agent's knowledge domain:

1. Edit the corresponding system prompt file:
   - Co-CEO: `backend/app/agents/templates/co_ceo/system_prompt.txt`
   - Finance: `backend/app/agents/templates/finance/system_prompt.txt`
   - Marketing: `backend/app/agents/templates/marketing/system_prompt.txt`

2. Modify the knowledge domain section to add, remove, or refine areas of expertise:

```
# KNOWLEDGE DOMAIN
You have specialized knowledge in:
- [Add new area of expertise]
- [Remove outdated area]
- [Refine existing area with more specific description]
```

3. Update examples to reflect the modified knowledge domain:

```
## Example: [New Area]
User: "[Example query related to new area]"
Response:
```

### 2. Department Descriptions

To adjust the embedding-based routing:

1. Edit the `DEPARTMENT_DESCRIPTIONS` dictionary in `backend/app/agents/co_ceo.py`:

```python
# Department descriptions for embedding-based similarity
DEPARTMENT_DESCRIPTIONS = {
    "finance": "Financial analysis, budgeting, forecasting, expense management, financial reporting, cash flow, investments, financial metrics, profit and loss, balance sheet, financial planning, [add new financial areas]",
    "marketing": "Marketing campaigns, market research, brand strategy, content planning, digital marketing, social media, email marketing, SEO, advertising, customer acquisition, marketing metrics, marketing ROI, [add new marketing areas]"
}
```

2. Ensure the descriptions are comprehensive but focused:
   - Include key terminology that might appear in user queries
   - Focus on the core responsibilities of each department
   - Use domain-specific terminology that will match user queries
   - Keep descriptions to 100-200 words for optimal embedding performance

### 3. Relevance Threshold

To change how aggressively queries are routed to departments:

1. Modify the threshold value in the `_combine_analysis_results` method in `backend/app/agents/co_ceo.py`:

```python
# Add departments with high embedding scores
embedding_departments = {
    dept for dept, score in embedding_scores.items()
    if score > 0.35  # Adjust this threshold
}
```

Threshold adjustment guidelines:
- **Lower values** (e.g., 0.25): More departments will be consulted (higher recall)
  - Good for complex queries that might span multiple domains
  - May result in unnecessary department consultations
  - Increases computational load and response time
- **Higher values** (e.g., 0.45): Fewer departments will be consulted (higher precision)
  - Good for focused queries with clear departmental alignment
  - May miss relevant departments for ambiguous queries
  - Reduces computational load and response time

2. Consider implementing dynamic thresholds based on query complexity:

```python
# Example of dynamic threshold based on query length
threshold = 0.40 if len(query.split()) < 10 else 0.30
embedding_departments = {
    dept for dept, score in embedding_scores.items()
    if score > threshold
}
```

### 4. Metadata Filters

To change how documents are filtered for each department:

1. Modify the metadata filter in the department's knowledge retrieval method:

```python
# Basic department filter
metadata_filter = {"department": "finance"}

# Enhanced filter with additional criteria
metadata_filter = {
    "department": "finance",
    "category": "reports",
    "confidentiality": "public",
    "date_range": {"start": "2023-01-01", "end": "2023-12-31"}
}
```

2. Update the document ingestion process to include the new metadata fields:

```python
# When adding documents to the knowledge base
metadata = {
    "department": "finance",
    "category": "reports",
    "confidentiality": "public",
    "date": "2023-06-15",
    "author": "Finance Team"
}
```

### 5. Adding a New Department

To add a new department:

1. Create a new system prompt file:

```bash
mkdir -p backend/app/agents/templates/operations
touch backend/app/agents/templates/operations/system_prompt.txt
```

2. Add the department to the `DEPARTMENTS` list in `backend/app/agents/co_ceo.py`:

```python
# Available departments
DEPARTMENTS = ["finance", "marketing", "operations"]
```

3. Add a description to `DEPARTMENT_DESCRIPTIONS` in `backend/app/agents/co_ceo.py`:

```python
# Department descriptions for embedding-based similarity
DEPARTMENT_DESCRIPTIONS = {
    "finance": "Financial analysis, budgeting...",
    "marketing": "Marketing campaigns, market research...",
    "operations": "Supply chain management, logistics, inventory control, production planning, quality assurance, facility management, process optimization, operational efficiency, resource allocation, vendor management"
}
```

4. Create a new agent class in `backend/app/agents/operations.py`:

```python
"""
Operations Agent Implementation

This module implements the Operations agent, which specializes in
operations-related queries and knowledge.
"""

from typing import Dict, List, Any
import logging
from datetime import datetime

from app.agents.base import BaseAgent
from app.agents.utils import get_agent_prompt

logger = logging.getLogger(__name__)

class OperationsAgent(BaseAgent):
    """
    Operations Agent that specializes in operations-related queries.

    This agent provides expertise on supply chain, logistics, inventory,
    production planning, and other operational areas.
    """

    async def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process an operations-related query."""
        context = context or {}

        try:
            # Retrieve operations knowledge
            knowledge_results = await self._retrieve_operations_knowledge(query)

            # Generate response
            response = await self._generate_operations_response(
                query, knowledge_results, context
            )

            return {
                "department": "operations",
                "response": response,
                "query": query,
                "confidence": 0.9,  # Can be adjusted based on knowledge match
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error processing operations query: {e}", exc_info=True)
            return {
                "department": "operations",
                "response": "I encountered an error while processing your operations query.",
                "query": query,
                "confidence": 0.0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _retrieve_operations_knowledge(self, query: str) -> List[Dict[str, Any]]:
        """Retrieve operations-related knowledge."""
        metadata_filter = {"department": "operations"}

        try:
            results = await self.knowledge_base_service.retrieve(
                query=query,
                top_k=5,
                metadata_filter=metadata_filter
            )

            logger.info(f"Retrieved {len(results)} operations documents")
            return results
        except Exception as e:
            logger.error(f"Error retrieving operations knowledge: {e}", exc_info=True)
            return []

    async def _generate_operations_response(
        self,
        query: str,
        knowledge_results: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> str:
        """Generate an operations-specific response."""
        system_prompt = get_agent_prompt("operations")

        # Format knowledge context
        knowledge_context = ""
        for i, doc in enumerate(knowledge_results, 1):
            content = doc.get("content", "")
            source = doc.get("source", "Unknown source")
            knowledge_context += f"\n[Document {i}] {content}\nSource: {source}\n"

        # Create user prompt
        user_prompt = f"""
        Please respond to the following operations-related query using the provided knowledge context.

        Query: "{query}"

        Knowledge Context:
        {knowledge_context if knowledge_context else "No specific operations documents found for this query."}

        Please provide a comprehensive, accurate response that:
        1. Directly addresses the operations query
        2. Uses the provided knowledge context
        3. Cites sources where appropriate
        4. Provides operational analysis and insights
        5. Uses appropriate operations terminology
        6. Includes relevant operational metrics and data
        7. Acknowledges any limitations in the available information
        """

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await self.llm_adapter.chat(messages)
        return response
```

5. Update the agent registry in `backend/app/agents/__init__.py`:

```python
# Add import
from app.agents.operations import OperationsAgent

# Update __all__
__all__ = [
    # Existing imports...
    "OperationsAgent",
    # ...
]

# Update initialize_agents function
def initialize_agents(llm_adapter, knowledge_base_service):
    """Initialize and register all agents."""
    # Create agent instances
    co_ceo_agent = CoCEOAgent(llm_adapter, knowledge_base_service)
    finance_agent = FinanceAgent(llm_adapter, knowledge_base_service)
    marketing_agent = MarketingAgent(llm_adapter, knowledge_base_service)
    operations_agent = OperationsAgent(llm_adapter, knowledge_base_service)

    # Register agents
    agent_registry.register("co_ceo", co_ceo_agent)
    agent_registry.register("finance", finance_agent)
    agent_registry.register("marketing", marketing_agent)
    agent_registry.register("operations", operations_agent)

    return {
        "co_ceo": co_ceo_agent,
        "finance": finance_agent,
        "marketing": marketing_agent,
        "operations": operations_agent,
    }
```

### 6. Testing Knowledge Boundary Changes

After modifying knowledge boundaries, it's essential to test the changes:

1. **Query Routing Test**: Test with various queries to ensure proper routing:

```python
async def test_routing():
    """Test query routing with various queries."""
    test_queries = [
        "What was our Q2 financial performance?",  # Should route to Finance
        "How did our last email campaign perform?",  # Should route to Marketing
        "What's our inventory turnover rate?",  # Should route to Operations (new)
        "How would increasing marketing budget affect our Q3 financials?",  # Should route to both Finance and Marketing
    ]

    for query in test_queries:
        analysis = await co_ceo_agent.analyze_query(query)
        print(f"Query: {query}")
        print(f"Departments: {analysis['relevant_departments']}")
        print(f"Scores: {analysis['embedding_scores']}")
        print("---")
```

2. **Knowledge Retrieval Test**: Test document retrieval with the new metadata filters:

```python
async def test_knowledge_retrieval():
    """Test knowledge retrieval with metadata filters."""
    query = "inventory management"

    # Test with different metadata filters
    filters = [
        {"department": "operations"},
        {"department": "operations", "category": "reports"},
        {"department": "operations", "date_range": {"start": "2023-01-01"}}
    ]

    for filter in filters:
        results = await knowledge_base_service.retrieve(
            query=query,
            top_k=5,
            metadata_filter=filter
        )
        print(f"Filter: {filter}")
        print(f"Results: {len(results)}")
        for doc in results:
            print(f"- {doc['source']}: {doc['content'][:50]}...")
        print("---")
```

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>

## ✅ Best Practices

When modifying knowledge boundaries, follow these best practices to ensure optimal system performance and maintainability.

### Knowledge Domain Definition

1. **Maintain Clear Boundaries**: Keep knowledge domains clearly defined with minimal overlap
   - Clearly articulate each department's core responsibilities
   - Minimize redundancy between departments
   - Define explicit handoff points for cross-department topics

2. **Balance Specificity and Breadth**: Find the right balance in knowledge domain definitions
   - Too specific: May miss relevant queries
   - Too broad: May lead to inappropriate routing
   - Ideal: Focused on core competencies with appropriate breadth

3. **Use Domain-Specific Terminology**: Include specialized terminology in domain definitions
   - Include industry-standard terms and acronyms
   - Use terminology that matches how users phrase queries
   - Define terms consistently across all departments

### Implementation Best Practices

4. **Provide Examples**: Include examples in system prompts to guide agent behavior
   - Include examples of both in-domain and boundary cases
   - Show examples of proper delegation and collaboration
   - Update examples when knowledge domains change

5. **Consistent Formatting**: Maintain consistent formatting across all departments
   - Use the same section headers in all system prompts
   - Follow consistent citation and attribution patterns
   - Maintain consistent response structures

6. **Version Control**: Track changes to knowledge boundaries
   - Document major changes in git commit messages
   - Include rationale for boundary modifications
   - Keep a changelog of knowledge domain evolution

### Testing and Maintenance

7. **Test Thoroughly**: Test with various queries to ensure proper routing and response generation
   - Create a test suite with diverse query types
   - Include edge cases that span multiple departments
   - Test with real user queries from logs

8. **Monitor Performance**: Track routing and response quality metrics
   - Monitor routing accuracy over time
   - Track user satisfaction with responses
   - Identify patterns of misrouting or confusion

9. **Regular Review**: Periodically review and refine knowledge boundaries
   - Schedule quarterly reviews of knowledge domains
   - Analyze logs to identify routing issues
   - Update boundaries based on new business requirements

10. **Document Changes**: Update this documentation when making significant changes
    - Document both what changed and why
    - Include examples of before and after behavior
    - Note any impacts on routing or response generation

### Scaling Considerations

11. **Plan for Growth**: Design knowledge boundaries with future expansion in mind
    - Leave room for new sub-domains within departments
    - Consider how new departments might interact with existing ones
    - Design metadata schemas that can accommodate new fields

12. **Manage Complexity**: As the system grows, manage increasing complexity
    - Consider hierarchical department structures for large domains
    - Implement sub-specialties within departments
    - Use metadata to create finer-grained routing within departments

13. **Cross-Department Collaboration**: Design for effective collaboration
    - Define clear protocols for multi-department queries
    - Establish priority and authority rules for overlapping domains
    - Create templates for integrated responses

By following these guidelines, you can effectively maintain and modify the departmental knowledge boundaries in the BusinessLM system, ensuring optimal performance as the system evolves and grows.

<div align="right"><a href="#-departmental-knowledge-setup">⬆️ Back to top</a></div>
