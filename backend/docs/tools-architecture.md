# Tools Architecture

This document describes the architecture of the tool system in the application.

## Overview

The tool system is designed to provide a flexible and extensible way to implement functionality that can be used by agents. Tools are organized into a core infrastructure layer and department-specific implementations.

## Architecture

The tool system is organized into two main components:

1. **Core Tool Infrastructure** (`app.core.tools`)
   - Provides the base classes and interfaces for all tools
   - Manages the global tool registry
   - Provides utility functions for accessing tools

2. **Department-Specific Tool Implementations** (`app.agents.tools`)
   - Contains concrete tool implementations for each department
   - Organized by department (e.g., `finance.py`, `marketing.py`)
   - Integrates with the core tool registry

## Core Tool Infrastructure

The core tool infrastructure is defined in `app.core.tools` and includes:

- `BaseTool`: Abstract base class that all tools must inherit from
- `TOOL_REGISTRY`: Global registry of all tools
- Utility functions:
  - `register_tool()`: Register a tool in the global registry
  - `get_tool()`: Get a tool by ID
  - `get_tools_by_department()`: Get all tools for a department
  - `list_all_tools()`: Get a dictionary of all registered tools

## Department-Specific Tool Implementations

Department-specific tool implementations are organized in the `app.agents.tools` package:

- `finance.py`: Tools for the Finance department
- `marketing.py`: Tools for the Marketing department
- `registry.py`: Integration with the core tool registry

Each department file contains tool implementations specific to that department. The `registry.py` file is responsible for registering all tools with the core registry.

## Tool Registration Process

Tools are registered with the core registry through the following process:

1. Department-specific tool classes are defined in their respective files
2. Tool instances are created in `app.agents.tools.registry`
3. The `register_tools()` function in `app.agents.tools.registry` registers all tools with the core registry
4. The registration happens automatically when the `app.agents.tools` package is imported

## Creating New Tools

To create a new tool:

1. Identify the appropriate department for the tool
2. Create a new tool class in the corresponding department file (e.g., `finance.py`)
3. Inherit from `BaseTool` and implement the required methods
4. Add the tool to the `ALL_TOOLS` list in `registry.py`

Example:

```python
# In app/agents/tools/finance.py
from app.core.tools import BaseTool
from pydantic import BaseModel, Field

class MyNewToolArgs(BaseModel):
    param1: str = Field(..., description="Description of param1")
    param2: int = Field(..., description="Description of param2")

class MyNewToolOutput(BaseModel):
    result: str = Field(..., description="The result")
    data: dict = Field(..., description="Additional data")

class MyNewTool(BaseTool):
    """
    My new finance tool.
    """
    
    name = "my_new_tool"
    description = "Description of what my tool does"
    args_schema = MyNewToolArgs
    output_schema = MyNewToolOutput
    
    async def _run(self, args: MyNewToolArgs) -> dict:
        # Implement the tool logic here
        return {
            "result": f"Processed {args.param1}",
            "data": {"value": args.param2 * 2}
        }
```

Then add it to the registry:

```python
# In app/agents/tools/registry.py
from .finance import MyNewTool

# Initialize tool instances
my_new_tool = MyNewTool()

# Add to ALL_TOOLS list
ALL_TOOLS = [
    # Existing tools...
    my_new_tool,
]
```

## Using Tools in Agents

Agents can use tools through the `use_tool` method provided by the `BaseAgent` class:

```python
# In an agent implementation
result = await self.use_tool("finance.my_new_tool", {
    "param1": "value1",
    "param2": 42
})
```

Alternatively, agents can get tools directly from the registry:

```python
from app.core.tools import get_tool

tool = get_tool("finance.my_new_tool")
result = await tool.run(param1="value1", param2=42)
```

## Best Practices

1. **Keep tools focused**: Each tool should do one thing well
2. **Use proper typing**: Define input and output schemas for all tools
3. **Handle errors gracefully**: Tools should handle errors and return meaningful error messages
4. **Document tools thoroughly**: Include detailed docstrings for all tools
5. **Follow naming conventions**: Use consistent naming for tools and their parameters
6. **Organize by department**: Keep tools organized by department
7. **Test tools thoroughly**: Write unit tests for all tools
