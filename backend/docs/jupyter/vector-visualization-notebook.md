# Vector Visualization Jupyter Notebook

This document provides a guide to using the Vector Visualization Jupyter notebook for exploring and analyzing vector embeddings stored in PostgreSQL+pgvector. This notebook allows you to visualize document clusters, explore the vector space, and analyze query results.

## Table of Contents

1. [Overview](#overview)
2. [Setup](#setup)
3. [Loading Vector Data](#loading-vector-data)
4. [Document Cluster Visualization](#document-cluster-visualization)
5. [Query Visualization](#query-visualization)
6. [Vector Space Exploration](#vector-space-exploration)
7. [Embedding Analysis](#embedding-analysis)
8. [Integration with Tracing](#integration-with-tracing)
9. [Exporting Results](#exporting-results)
10. [Example Notebooks](#example-notebooks)
11. [Troubleshooting](#troubleshooting)

## Overview

The Vector Visualization Jupyter notebook provides a powerful environment for exploring and analyzing vector embeddings stored in PostgreSQL+pgvector. It allows you to:

- Visualize document clusters in 2D or 3D
- Explore the vector space using dimensionality reduction
- Analyze query results and their relevance
- Examine embedding statistics and distributions
- Integrate with tracing data for comprehensive analysis

## Setup

### Prerequisites

- Python 3.10+
- Jupyter Notebook or JupyterLab
- PostgreSQL with pgvector extension
- Required Python packages:
  - pandas
  - numpy
  - matplotlib
  - plotly
  - scikit-learn
  - umap-learn
  - sqlalchemy
  - psycopg2-binary
  - pgvector

### Installation

```bash
# Install Jupyter
pip install jupyter

# Install required packages
pip install pandas numpy matplotlib plotly scikit-learn umap-learn sqlalchemy psycopg2-binary pgvector

# Start Jupyter
jupyter notebook
```

### Loading the Vector Visualization Module

```python
# Import the vector visualization module
from app.notebooks.vector_visualization import (
    load_vectors,
    visualize_clusters,
    visualize_query,
    explore_vector_space,
    analyze_embeddings
)
```

## Loading Vector Data

### Loading from PostgreSQL

```python
# Load vector data from PostgreSQL
from app.notebooks.vector_visualization import load_vectors_from_db

# Load all vectors
vectors, metadata = load_vectors_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm"
)

# Load vectors with filter
vectors, metadata = load_vectors_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm",
    filter_condition="meta_info->>'department' = 'marketing'"
)

# Load limited number of vectors
vectors, metadata = load_vectors_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm",
    limit=1000
)
```

### Loading from a File

```python
# Load vector data from a file
from app.notebooks.vector_visualization import load_vectors_from_file

# Load from JSON file
vectors, metadata = load_vectors_from_file("vectors.json")

# Load from CSV file
vectors, metadata = load_vectors_from_file("vectors.csv", format="csv")
```

## Document Cluster Visualization

### Basic Cluster Visualization

```python
# Visualize document clusters
from app.notebooks.vector_visualization import visualize_clusters
import plotly.graph_objects as go

# Visualize clusters using PCA
fig = visualize_clusters(vectors, metadata, method="pca", n_components=3)

# Display visualization
fig.show()

# Visualize clusters using UMAP
fig = visualize_clusters(vectors, metadata, method="umap", n_components=3)

# Display visualization
fig.show()
```

### Colored by Metadata

```python
# Visualize clusters colored by metadata
fig = visualize_clusters(
    vectors,
    metadata,
    method="pca",
    n_components=3,
    color_by="department"
)

# Display visualization
fig.show()
```

### Interactive 3D Visualization

```python
# Create interactive 3D visualization
fig = visualize_clusters(
    vectors,
    metadata,
    method="pca",
    n_components=3,
    interactive=True
)

# Display visualization
fig.show()
```

## Query Visualization

### Visualizing Query Results

```python
# Visualize query results
from app.notebooks.vector_visualization import visualize_query_results

# Generate embedding for query
query_text = "What is our marketing budget?"
query_embedding = get_embedding_for_text(query_text)

# Visualize query results
fig = visualize_query_results(
    query_embedding,
    vectors,
    metadata,
    method="pca",
    n_components=3,
    top_k=20
)

# Display visualization
fig.show()
```

### Query Playground

```python
# Create a query playground
from app.notebooks.vector_visualization import create_query_playground

# Create playground
playground = create_query_playground(
    connection_string="postgresql://username:password@localhost:5432/businesslm"
)

# Query vectors
results = playground["query_vectors"]("What is our marketing budget?", top_k=10)

# Display results
print(results)

# Visualize query results
fig, results = playground["visualize_query_results"]("What is our marketing budget?", top_k=50)

# Display visualization
fig.show()
```

## Vector Space Exploration

### Exploring the Vector Space

```python
# Explore the vector space
from app.notebooks.vector_visualization import explore_vector_space
import plotly.graph_objects as go

# Create vector space explorer
explorer = explore_vector_space(vectors, metadata)

# Display explorer
explorer.show()
```

### Vector Space Statistics

```python
# Get vector space statistics
from app.notebooks.vector_visualization import get_vector_space_stats
import pandas as pd

# Get statistics
stats = get_vector_space_stats(vectors)

# Display statistics
print(f"Dimension: {stats['dimension']}")
print(f"Number of Vectors: {stats['count']}")
print(f"Average L2 Norm: {stats['avg_norm']}")
print(f"Min Distance: {stats['min_distance']}")
print(f"Max Distance: {stats['max_distance']}")
print(f"Average Distance: {stats['avg_distance']}")

# Plot distance distribution
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 6))
plt.hist(stats['distance_distribution'], bins=50)
plt.title("Distance Distribution")
plt.xlabel("Distance")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()
```

## Embedding Analysis

### Embedding Quality Analysis

```python
# Analyze embedding quality
from app.notebooks.vector_visualization import analyze_embedding_quality
import pandas as pd

# Analyze quality
quality = analyze_embedding_quality(vectors, metadata)

# Display quality metrics
print(f"Silhouette Score: {quality['silhouette_score']}")
print(f"Davies-Bouldin Index: {quality['davies_bouldin_index']}")
print(f"Calinski-Harabasz Index: {quality['calinski_harabasz_index']}")

# Plot quality metrics by department
departments = quality['department_metrics'].keys()
silhouette_scores = [m['silhouette_score'] for m in quality['department_metrics'].values()]

plt.figure(figsize=(10, 6))
plt.bar(departments, silhouette_scores)
plt.title("Silhouette Score by Department")
plt.xlabel("Department")
plt.ylabel("Silhouette Score")
plt.xticks(rotation=45)
plt.grid(True)
plt.tight_layout()
plt.show()
```

### Embedding Similarity Analysis

```python
# Analyze embedding similarity
from app.notebooks.vector_visualization import analyze_embedding_similarity
import pandas as pd
import seaborn as sns

# Analyze similarity
similarity = analyze_embedding_similarity(vectors, metadata)

# Create similarity matrix
similarity_matrix = similarity['similarity_matrix']

# Plot similarity heatmap
plt.figure(figsize=(12, 10))
sns.heatmap(similarity_matrix, cmap="viridis")
plt.title("Embedding Similarity Matrix")
plt.tight_layout()
plt.show()
```

## Integration with Tracing

### Combining Vector and Trace Data

```python
# Combine vector and trace data
from app.notebooks.vector_visualization import combine_vector_and_trace_data
import pandas as pd

# Load trace data
from app.notebooks.trace_analysis import load_trace
trace_data = load_trace("traces/trace-123456.json")

# Combine data
combined_data = combine_vector_and_trace_data(
    vectors,
    metadata,
    trace_data,
    connection_string="postgresql://username:password@localhost:5432/businesslm"
)

# Convert to DataFrame
df = pd.DataFrame(combined_data)

# Display combined data
print(df.head())

# Analyze retrieval performance
plt.figure(figsize=(10, 6))
plt.scatter(df["similarity_score"], df["retrieval_time"])
plt.title("Retrieval Time vs. Similarity Score")
plt.xlabel("Similarity Score")
plt.ylabel("Retrieval Time (ms)")
plt.grid(True)
plt.show()
```

## Exporting Results

### Exporting to HTML

```python
# Export visualization to HTML
from app.notebooks.vector_visualization import export_visualization_to_html

# Export visualization
html = export_visualization_to_html(fig)

# Save to file
with open("vector_visualization.html", "w") as f:
    f.write(html)
```

### Exporting to PNG

```python
# Export visualization to PNG
from app.notebooks.vector_visualization import export_visualization_to_png

# Export visualization
export_visualization_to_png(fig, "vector_visualization.png")
```

### Exporting to Interactive HTML

```python
# Export to interactive HTML
fig.write_html("vector_visualization_interactive.html")
```

## Example Notebooks

The BusinessLM Python backend includes several example notebooks for vector visualization:

1. **Basic Vector Visualization**: `notebooks/vector_visualization_basic.ipynb`
2. **Query Playground**: `notebooks/vector_query_playground.ipynb`
3. **Embedding Analysis**: `notebooks/embedding_analysis.ipynb`
4. **Vector and Trace Integration**: `notebooks/vector_trace_integration.ipynb`

## Troubleshooting

### Common Issues

1. **Notebook Not Loading Vector Data**
   - Verify that the PostgreSQL connection string is correct
   - Check that the pgvector extension is installed
   - Ensure that the vector table exists and contains data

2. **Visualization Not Displaying**
   - Verify that the required packages are installed
   - Check that the vector data is in the expected format
   - Try restarting the Jupyter kernel

3. **Dimensionality Reduction Issues**
   - For large datasets, try reducing the number of vectors
   - Adjust the parameters of the dimensionality reduction method
   - Try a different method (e.g., UMAP instead of PCA)

4. **Performance Issues**
   - Limit the number of vectors loaded from the database
   - Use a more efficient dimensionality reduction method
   - Reduce the number of components for visualization
