# Templates Architecture

This document describes the architecture of the template system in the application.

## Overview

The template system provides a flexible and maintainable way to manage prompt templates for different agents in the system. Templates are stored as files in the `app/agents/templates/` directory, making them easy to edit and version control.

## Directory Structure

```
backend/app/agents/templates/
├── __init__.py                  # Template loading and rendering utilities
├── co_ceo/                      # Co-CEO agent templates
│   └── system_prompt.txt        # System prompt for Co-CEO agent
├── finance/                     # Finance agent templates
│   └── system_prompt.txt        # System prompt for Finance agent
└── marketing/                   # Marketing agent templates
    └── system_prompt.txt        # System prompt for Marketing agent
```

## Template Format

Templates are stored as plain text files with a structured format:

1. **Header**: A brief description of the agent's role
2. **Sections**: Clearly defined sections with headings (e.g., `# ROLE AND RESPONSIBILITIES`)
3. **Examples**: Example interactions to guide the agent's behavior
4. **Formatting Guidelines**: Instructions for how the agent should format responses

## Template Loading

Templates are loaded using the utilities in `app/agents/templates/__init__.py`:

```python
from app.agents.templates import get_agent_prompt

# Load a template for a specific agent
prompt = get_agent_prompt("co_ceo")
```

The `get_agent_prompt` function loads the template from the appropriate file and optionally renders it with parameters:

```python
# Load a template with parameters
prompt = get_agent_prompt("co_ceo", name="BusinessLM", version="2.0")
```

## Template Rendering

Templates can include placeholders for dynamic content using Python's string formatting syntax:

```
You are the Co-CEO Agent for {company_name}, version {version}.
```

These placeholders are replaced with actual values when the template is rendered:

```python
from app.agents.templates import render_template

template = "Hello, {name}!"
rendered = render_template(template, name="World")
# Result: "Hello, World!"
```

## Adding New Templates

To add a new template:

1. Create a new directory for the agent type (e.g., `app/agents/templates/new_agent/`)
2. Add a `system_prompt.txt` file with the template content
3. Use the `get_agent_prompt` function to load the template in your code

## Best Practices

1. **Structured Format**: Use a consistent structure for all templates
2. **Clear Sections**: Divide templates into clear sections with headings
3. **Examples**: Include examples to guide the agent's behavior
4. **Parameterization**: Use placeholders for dynamic content
5. **Version Control**: Track changes to templates in version control
6. **Testing**: Write tests for template loading and rendering

## Testing

Templates are tested in `tests/test_agents/test_templates.py` to ensure they can be loaded and rendered correctly.
