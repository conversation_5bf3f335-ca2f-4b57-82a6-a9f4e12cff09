# CLI Visualization for Tracing

This document provides a guide to using the CLI visualization tools for tracing in the BusinessLM Python backend. These tools allow you to visualize trace data in the terminal, making it easier to understand the execution flow of the RAG and multi-agent orchestration systems.

## Table of Contents

1. [Overview](#overview)
2. [Basic Usage](#basic-usage)
3. [Visualization Formats](#visualization-formats)
4. [Filtering Traces](#filtering-traces)
5. [Exporting Visualizations](#exporting-visualizations)
6. [Integration with Makefile Commands](#integration-with-makefile-commands)
7. [Advanced Usage](#advanced-usage)
8. [Customization](#customization)
9. [Troubleshooting](#troubleshooting)

## Overview

The CLI visualization tools provide a way to visualize trace data in the terminal, making it easier to understand the execution flow of the RAG and multi-agent orchestration systems. These tools are built on top of the [Rich](https://github.com/Textualize/rich) library and provide a variety of visualization formats.

## Basic Usage

### Visualizing Traces

```python
from app.cli.tracing import visualize_trace

# Visualize traces from a TracingCollector
tracer = TracingCollector(session_id="session-123")
# ... add traces ...
visualize_trace(tracer.traces)

# Visualize traces from a file
visualize_trace_from_file("traces.json")
```

### Using the Trace Viewer CLI

```bash
# View traces from a file
python -m app.cli.trace_viewer view traces.json

# View traces with a specific format
python -m app.cli.trace_viewer view traces.json --format tree

# Filter traces by node_id
python -m app.cli.trace_viewer view traces.json --node-id analyze_query

# Filter traces by event_type
python -m app.cli.trace_viewer view traces.json --event-type llm_call
```

## Visualization Formats

The CLI visualization tools support multiple visualization formats:

### Text Format

The text format provides a simple text representation of the trace:

```bash
python -m app.cli.trace_viewer view traces.json --format text
```

Example output:
```
[2023-01-01T00:00:00] analyze_query (node_start)
  thread_id: thread-789
  user_id: user-456
  session_id: session-123
[2023-01-01T00:00:01] analyze_query (node_end)
  thread_id: thread-789
  user_id: user-456
  session_id: session-123
  duration: 1000ms
```

### Tree Format

The tree format provides a hierarchical view of the trace:

```bash
python -m app.cli.trace_viewer view traces.json --format tree
```

Example output:
```
Trace (session-123)
├── analyze_query
│   ├── Start: 2023-01-01T00:00:00
│   ├── End: 2023-01-01T00:00:01
│   ├── Duration: 1000ms
│   └── Metadata
│       ├── thread_id: thread-789
│       └── user_id: user-456
├── retrieve_knowledge
│   ├── Start: 2023-01-01T00:00:01
│   ├── End: 2023-01-01T00:00:02
│   ├── Duration: 1000ms
│   └── Metadata
│       ├── thread_id: thread-789
│       └── user_id: user-456
```

### Table Format

The table format provides a tabular representation of trace events:

```bash
python -m app.cli.trace_viewer view traces.json --format table
```

Example output:
```
┌────────────────────┬──────────────┬────────────┬────────────────┬──────────┐
│ Timestamp          │ Node         │ Event Type │ Duration       │ Metadata │
├────────────────────┼──────────────┼────────────┼────────────────┼──────────┤
│ 2023-01-01T00:00:00│ analyze_query│ node_start │ -              │ {...}    │
│ 2023-01-01T00:00:01│ analyze_query│ node_end   │ 1000ms         │ {...}    │
│ 2023-01-01T00:00:01│ retrieve_kno…│ node_start │ -              │ {...}    │
│ 2023-01-01T00:00:02│ retrieve_kno…│ node_end   │ 1000ms         │ {...}    │
└────────────────────┴──────────────┴────────────┴────────────────┴──────────┘
```

### Timeline Format

The timeline format provides a timeline view of trace events:

```bash
python -m app.cli.trace_viewer view traces.json --format timeline
```

Example output:
```
00:00:00 ┌─────────────────────────────┐
         │ analyze_query               │
00:00:01 └─────────────────────────────┘
         ┌─────────────────────────────┐
         │ retrieve_knowledge          │
00:00:02 └─────────────────────────────┘
```

## Filtering Traces

The CLI visualization tools support filtering traces by various criteria:

### Filtering by Node ID

```bash
# Filter traces by node_id
python -m app.cli.trace_viewer view traces.json --node-id analyze_query
```

### Filtering by Event Type

```bash
# Filter traces by event_type
python -m app.cli.trace_viewer view traces.json --event-type llm_call
```

### Filtering by Time Range

```bash
# Filter traces by time range
python -m app.cli.trace_viewer view traces.json --start-time 2023-01-01T00:00:00 --end-time 2023-01-01T00:00:10
```

### Filtering by Status

```bash
# Filter traces by status
python -m app.cli.trace_viewer view traces.json --status error
```

### Combining Filters

```bash
# Combine multiple filters
python -m app.cli.trace_viewer view traces.json --node-id analyze_query --event-type llm_call --status error
```

## Exporting Visualizations

The CLI visualization tools support exporting visualizations to various formats:

### Exporting to HTML

```bash
# Export visualization to HTML
python -m app.cli.trace_viewer view traces.json --format tree --export trace.html
```

### Exporting to SVG

```bash
# Export visualization to SVG
python -m app.cli.trace_viewer view traces.json --format timeline --export trace.svg
```

### Exporting to PNG

```bash
# Export visualization to PNG
python -m app.cli.trace_viewer view traces.json --format table --export trace.png
```

## Integration with Makefile Commands

The CLI visualization tools are integrated with the Makefile commands for testing RAG and multi-agent orchestration:

### Enabling Tracing in Makefile Commands

```bash
# Enable tracing in test-rag (using PostgreSQL by default)
make -f makefiles/backend-cli-testing/Makefile test-rag QUERY="What is our marketing budget?" TRACE=true

# Enable tracing in test-rag with in-memory storage
make -f makefiles/backend-cli-testing/Makefile test-rag QUERY="What is our marketing budget?" TRACE=true USE_POSTGRES=false

# Enable tracing in test-agents (using PostgreSQL by default)
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our marketing strategy?" TRACE=true

# Enable tracing in test-agents with in-memory storage
make -f makefiles/backend-cli-testing/Makefile test-agents QUERY="What is our marketing strategy?" TRACE=true USE_POSTGRES=false
```

### Viewing Traces from Makefile Commands

```bash
# View the most recent trace
make -f makefiles/backend-cli-testing/Makefile view-trace

# View a specific trace
make -f makefiles/backend-cli-testing/Makefile view-trace TRACE_FILE=traces/trace-123456.json
```

### PostgreSQL Integration Commands

The CLI visualization tools also support PostgreSQL integration for document storage and vector search:

```bash
# Verify pgvector extension is installed
make -f makefiles/backend-cli-testing/Makefile verify-pgvector

# Migrate documents to PostgreSQL
make -f makefiles/backend-cli-testing/Makefile migrate-docs-to-postgres

# List documents in PostgreSQL
make -f makefiles/backend-cli-testing/Makefile list-postgres-docs

# List document chunks in PostgreSQL
make -f makefiles/backend-cli-testing/Makefile list-postgres-chunks

# Test RAG with PostgreSQL
make -f makefiles/backend-cli-testing/Makefile test-postgres-rag QUERY="What is our marketing budget?"
```

## Advanced Usage

### Comparing Traces

```bash
# Compare two traces
python -m app.cli.trace_viewer compare trace1.json trace2.json

# Compare with specific format
python -m app.cli.trace_viewer compare trace1.json trace2.json --format table
```

### Analyzing Trace Performance

```bash
# Analyze trace performance
python -m app.cli.trace_viewer analyze traces.json

# Analyze specific node performance
python -m app.cli.trace_viewer analyze traces.json --node-id analyze_query
```

### Generating Statistics

```bash
# Generate trace statistics
python -m app.cli.trace_viewer stats traces.json

# Generate statistics for specific event type
python -m app.cli.trace_viewer stats traces.json --event-type llm_call
```

## Customization

### Customizing Visualization Colors

```python
from app.cli.tracing import set_visualization_colors

# Set custom colors for visualization
set_visualization_colors({
    "node_start": "green",
    "node_end": "blue",
    "error": "red",
    "llm_call": "magenta"
})
```

### Customizing Visualization Format

```python
from app.cli.tracing import set_default_visualization_format

# Set default visualization format
set_default_visualization_format("tree")
```

## Troubleshooting

### Common Issues

1. **Visualization Not Showing**
   - Verify that the trace file exists and is valid JSON
   - Check that the trace file contains valid trace events
   - Ensure that the terminal supports rich text formatting

2. **Missing Events in Visualization**
   - Check that the events were properly added to the tracer
   - Verify that the events match the filter criteria
   - Ensure that the trace file was properly exported

3. **Performance Issues**
   - Reduce the number of events being visualized
   - Use a simpler visualization format (e.g., text instead of tree)
   - Export the visualization to a file instead of displaying in the terminal
