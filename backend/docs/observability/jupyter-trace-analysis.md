# Jupyter Trace Analysis

This document provides a guide to using Jupyter notebooks for analyzing trace data in the BusinessLM Python backend. These notebooks allow you to perform advanced analysis and visualization of trace data, providing deeper insights into the execution of the RAG and multi-agent orchestration systems.

## Table of Contents

1. [Overview](#overview)
2. [Setup](#setup)
3. [Loading Trace Data](#loading-trace-data)
4. [Basic Analysis](#basic-analysis)
5. [Advanced Visualization](#advanced-visualization)
6. [Performance Analysis](#performance-analysis)
7. [Comparing Traces](#comparing-traces)
8. [Integration with PostgreSQL](#integration-with-postgresql)
9. [Exporting Results](#exporting-results)
10. [Example Notebooks](#example-notebooks)
11. [Troubleshooting](#troubleshooting)

## Overview

Jupyter notebooks provide a powerful environment for analyzing trace data from the BusinessLM Python backend. They allow you to:

- Load and explore trace data
- Visualize execution flow
- Analyze performance metrics
- Compare different traces
- Integrate with PostgreSQL for query analysis
- Export results for reporting

## Setup

### Prerequisites

- Python 3.10+
- Jupyter Notebook or JupyterLab
- Required Python packages:
  - pandas
  - numpy
  - matplotlib
  - plotly
  - networkx
  - sqlalchemy (for PostgreSQL integration)

### Installation

```bash
# Install Jupyter
pip install jupyter

# Install required packages
pip install pandas numpy matplotlib plotly networkx sqlalchemy

# Start Jupyter
jupyter notebook
```

### Loading the Trace Analysis Module

```python
# Import the trace analysis module
from app.notebooks.trace_analysis import (
    load_trace,
    analyze_trace,
    visualize_trace_flow,
    analyze_performance,
    compare_traces
)
```

## Loading Trace Data

### Loading from a File

```python
# Load trace data from a JSON file
trace_data = load_trace("traces/trace-123456.json")

# Load trace data from an NDJSON file
trace_data = load_trace("traces/trace-123456.ndjson", format="ndjson")

# Load trace data from a LangGraph format file
trace_data = load_trace("traces/langgraph-trace-123456.json", format="langgraph")
```

### Loading from PostgreSQL

```python
# Load trace data from PostgreSQL
from app.notebooks.trace_analysis import load_trace_from_db

# Load by session_id
trace_data = load_trace_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm",
    session_id="session-123"
)

# Load by thread_id
trace_data = load_trace_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm",
    thread_id="thread-789"
)

# Load by time range
trace_data = load_trace_from_db(
    connection_string="postgresql://username:password@localhost:5432/businesslm",
    start_time="2023-01-01T00:00:00",
    end_time="2023-01-02T00:00:00"
)
```

## Basic Analysis

### Trace Summary

```python
# Get a summary of the trace
summary = analyze_trace(trace_data)

# Display summary
print(f"Trace ID: {summary['trace_id']}")
print(f"Session ID: {summary['session_id']}")
print(f"User ID: {summary['user_id']}")
print(f"Thread ID: {summary['thread_id']}")
print(f"Start Time: {summary['start_time']}")
print(f"End Time: {summary['end_time']}")
print(f"Duration: {summary['duration']} ms")
print(f"Node Count: {summary['node_count']}")
print(f"Event Count: {summary['event_count']}")
print(f"Error Count: {summary['error_count']}")
```

### Event Distribution

```python
# Get event distribution
from app.notebooks.trace_analysis import get_event_distribution
import matplotlib.pyplot as plt

# Get event distribution
event_dist = get_event_distribution(trace_data)

# Plot event distribution
plt.figure(figsize=(10, 6))
plt.bar(event_dist.keys(), event_dist.values())
plt.title("Event Distribution")
plt.xlabel("Event Type")
plt.ylabel("Count")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

### Node Execution Timeline

```python
# Visualize node execution timeline
from app.notebooks.trace_analysis import visualize_timeline
import plotly.graph_objects as go

# Get timeline visualization
fig = visualize_timeline(trace_data)

# Display timeline
fig.show()
```

## Advanced Visualization

### Execution Flow Graph

```python
# Visualize execution flow as a graph
from app.notebooks.trace_analysis import visualize_trace_flow
import networkx as nx
import matplotlib.pyplot as plt

# Get flow graph
G = visualize_trace_flow(trace_data)

# Plot graph
plt.figure(figsize=(12, 8))
pos = nx.spring_layout(G)
nx.draw(G, pos, with_labels=True, node_color="skyblue", node_size=1500, edge_color="gray")
nx.draw_networkx_edge_labels(G, pos, edge_labels=nx.get_edge_attributes(G, "label"))
plt.title("Execution Flow Graph")
plt.tight_layout()
plt.show()
```

### Interactive Sankey Diagram

```python
# Create a Sankey diagram of execution flow
from app.notebooks.trace_analysis import create_sankey_diagram
import plotly.graph_objects as go

# Get Sankey diagram
fig = create_sankey_diagram(trace_data)

# Display diagram
fig.show()
```

### State Transition Visualization

```python
# Visualize state transitions
from app.notebooks.trace_analysis import visualize_state_transitions
import plotly.graph_objects as go

# Get state transition visualization
fig = visualize_state_transitions(trace_data)

# Display visualization
fig.show()
```

## Performance Analysis

### Node Execution Time

```python
# Analyze node execution time
from app.notebooks.trace_analysis import analyze_node_performance
import matplotlib.pyplot as plt

# Get node performance data
node_perf = analyze_node_performance(trace_data)

# Plot node execution time
plt.figure(figsize=(10, 6))
plt.bar(node_perf.keys(), [p["duration"] for p in node_perf.values()])
plt.title("Node Execution Time")
plt.xlabel("Node")
plt.ylabel("Duration (ms)")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

### LLM Call Analysis

```python
# Analyze LLM calls
from app.notebooks.trace_analysis import analyze_llm_calls
import pandas as pd

# Get LLM call data
llm_calls = analyze_llm_calls(trace_data)

# Convert to DataFrame for easier analysis
df = pd.DataFrame(llm_calls)

# Display summary statistics
print(df.describe())

# Plot token usage
plt.figure(figsize=(10, 6))
plt.bar(df["node_id"], df["token_count"])
plt.title("LLM Token Usage by Node")
plt.xlabel("Node")
plt.ylabel("Token Count")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

### Performance Bottleneck Identification

```python
# Identify performance bottlenecks
from app.notebooks.trace_analysis import identify_bottlenecks

# Get bottleneck analysis
bottlenecks = identify_bottlenecks(trace_data)

# Display bottlenecks
for b in bottlenecks:
    print(f"Bottleneck: {b['node_id']}")
    print(f"  Duration: {b['duration']} ms")
    print(f"  Percentage of Total Time: {b['percentage']:.2f}%")
    print(f"  Recommendation: {b['recommendation']}")
    print()
```

## Comparing Traces

### Basic Comparison

```python
# Compare two traces
from app.notebooks.trace_analysis import compare_traces
import pandas as pd

# Load two traces
trace1 = load_trace("traces/trace-123456.json")
trace2 = load_trace("traces/trace-789012.json")

# Compare traces
comparison = compare_traces(trace1, trace2)

# Display comparison
print(f"Trace 1 ID: {comparison['trace1_id']}")
print(f"Trace 2 ID: {comparison['trace2_id']}")
print(f"Trace 1 Duration: {comparison['trace1_duration']} ms")
print(f"Trace 2 Duration: {comparison['trace2_duration']} ms")
print(f"Duration Difference: {comparison['duration_diff']} ms ({comparison['duration_diff_percent']:.2f}%)")
print(f"Node Count Difference: {comparison['node_count_diff']}")
print(f"Event Count Difference: {comparison['event_count_diff']}")
print(f"Common Nodes: {comparison['common_nodes']}")
print(f"Unique Nodes in Trace 1: {comparison['unique_nodes_trace1']}")
print(f"Unique Nodes in Trace 2: {comparison['unique_nodes_trace2']}")
```

### Performance Comparison

```python
# Compare performance between two traces
from app.notebooks.trace_analysis import compare_performance
import matplotlib.pyplot as plt

# Compare performance
perf_comparison = compare_performance(trace1, trace2)

# Plot performance comparison
plt.figure(figsize=(12, 6))
nodes = list(perf_comparison.keys())
trace1_times = [p["trace1_duration"] for p in perf_comparison.values()]
trace2_times = [p["trace2_duration"] for p in perf_comparison.values()]

x = range(len(nodes))
width = 0.35

plt.bar([i - width/2 for i in x], trace1_times, width, label="Trace 1")
plt.bar([i + width/2 for i in x], trace2_times, width, label="Trace 2")

plt.xlabel("Node")
plt.ylabel("Duration (ms)")
plt.title("Performance Comparison")
plt.xticks(x, nodes, rotation=45)
plt.legend()
plt.tight_layout()
plt.show()
```

## Integration with PostgreSQL

### Combining Trace and Query Data

```python
# Combine trace data with query logs
from app.notebooks.trace_analysis import combine_trace_and_query_logs
import pandas as pd

# Combine data
combined_data = combine_trace_and_query_logs(
    trace_data,
    connection_string="postgresql://username:password@localhost:5432/businesslm"
)

# Convert to DataFrame
df = pd.DataFrame(combined_data)

# Display combined data
print(df.head())

# Analyze retrieval performance
plt.figure(figsize=(10, 6))
plt.scatter(df["doc_count"], df["retrieval_time"])
plt.title("Retrieval Time vs. Document Count")
plt.xlabel("Document Count")
plt.ylabel("Retrieval Time (ms)")
plt.grid(True)
plt.show()
```

## Exporting Results

### Exporting to HTML

```python
# Export analysis to HTML
from app.notebooks.trace_analysis import export_analysis_to_html

# Export analysis
html_report = export_analysis_to_html(trace_data)

# Save to file
with open("trace_analysis.html", "w") as f:
    f.write(html_report)
```

### Exporting to PDF

```python
# Export analysis to PDF
from app.notebooks.trace_analysis import export_analysis_to_pdf

# Export analysis
export_analysis_to_pdf(trace_data, "trace_analysis.pdf")
```

### Exporting to JSON

```python
# Export analysis to JSON
from app.notebooks.trace_analysis import export_analysis_to_json
import json

# Export analysis
analysis_json = export_analysis_to_json(trace_data)

# Save to file
with open("trace_analysis.json", "w") as f:
    json.dump(analysis_json, f, indent=2)
```

## Example Notebooks

The BusinessLM Python backend includes several example notebooks for trace analysis:

1. **Basic Trace Analysis**: `notebooks/trace_analysis_basic.ipynb`
2. **Performance Analysis**: `notebooks/trace_analysis_performance.ipynb`
3. **Trace Comparison**: `notebooks/trace_comparison.ipynb`
4. **PostgreSQL Integration**: `notebooks/trace_postgresql_integration.ipynb`

## Troubleshooting

### Common Issues

1. **Notebook Not Loading Trace Data**
   - Verify that the trace file exists and is valid JSON
   - Check that the trace file contains valid trace events
   - Ensure that the file path is correct

2. **Visualization Not Displaying**
   - Verify that the required packages are installed
   - Check that the trace data is in the expected format
   - Try restarting the Jupyter kernel

3. **PostgreSQL Integration Issues**
   - Verify that the connection string is correct
   - Check that the required tables exist in the database
   - Ensure that the user has the necessary permissions
