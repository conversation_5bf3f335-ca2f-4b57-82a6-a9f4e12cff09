# Step-by-Step Visualization Plan for BusinessLM Python Backend

This document provides a comprehensive and detailed implementation plan for adding traceability/observability and PostgreSQL+pgvector visualization capabilities to the BusinessLM Python backend. It serves as a complete reference guide to ensure consistent implementation and avoid detours, even if implementation spans multiple development sessions or threads.

**Project Context**: The BusinessLM Python backend includes a RAG system and multi-agent orchestration capabilities. This plan focuses on adding visualization tools to observe agent communications during multi-agent orchestration and to visualize vector embeddings stored in PostgreSQL+pgvector.

**Current System State**: The system currently has basic RAG and multi-agent orchestration capabilities, but lacks comprehensive traceability and visualization tools. The multi-agent orchestration is implemented using LangGraph, and document storage uses PostgreSQL+pgvector.

**Implementation Goals**:
1. Add real-time visualization of agent communications during CLI testing
2. Implement comprehensive tracing of the multi-agent orchestration process
3. Create visualization tools for PostgreSQL+pgvector vector embeddings
4. Integrate tracing and visualization with existing CLI testing tools

## Table of Contents

1. [Overview](#1-overview)
2. [Implementation Phases](#2-implementation-phases)
3. [Phase 1: Foundation Setup](#3-phase-1-foundation-setup)
4. [Phase 2: LangGraph Integration](#4-phase-2-langgraph-integration)
5. [Phase 3: Vector Store Integration](#5-phase-3-vector-store-integration)
6. [Phase 4: Jupyter Notebook Integration](#6-phase-4-jupyter-notebook-integration)
5. [Phase 5: Integration and Testing](#7-phase-5-integration-and-testing)
6. [Phase 6: Optimization and Refinement](#8-phase-6-optimization-and-refinement)
7. [Implementation Timeline](#9-implementation-timeline)
8. [Key Dependencies](#10-key-dependencies)
9. [Risk Mitigation](#11-risk-mitigation)

## 1. Overview

This implementation plan focuses on two key components:

1. **Traceability/Observability**: A system for tracking and visualizing the execution of the multi-agent orchestration system, with a particular focus on agent communications.

2. **PostgreSQL+pgvector Integration**: Tools for visualizing vector embeddings stored in PostgreSQL+pgvector, enabling better understanding of the RAG system.

```
┌─────────────────────────────────────────────────────────────────────────┐
│                                                                         │
│                      ┌───────────────────┐                              │
│                      │  TracingCollector │                              │
│                      └───────────────────┘                              │
│                               │                                         │
│                               ▼                                         │
│  ┌───────────────┐    ┌───────────────────┐    ┌───────────────────┐   │
│  │ LangGraph     │◄───┤  Trace Events     ├───►│ CLI Visualization │   │
│  │ Integration   │    └───────────────────┘    └───────────────────┘   │
│  └───────────────┘              │                        │             │
│         │                       │                        │             │
│         │                       ▼                        ▼             │
│         │              ┌───────────────────┐    ┌───────────────────┐  │
│         └─────────────►│  Agent Messages   │    │ Jupyter Notebooks │  │
│                        └───────────────────┘    └───────────────────┘  │
│                                 │                        │             │
│                                 └────────────┬───────────┘             │
│                                              ▼                         │
│                                 ┌───────────────────────┐              │
│                                 │ Trace Export Formats  │              │
│                                 └───────────────────────┘              │
│                                                                         │
└─────────────────────────────────────────────────────────────────────────┘
                  Traceability and Observability Architecture
```

## 2. Implementation Phases

The implementation is divided into six phases:

1. **Foundation Setup**: Core tracing infrastructure, database schema extensions, and basic CLI visualization
2. **LangGraph Integration**: LangGraph tracing hooks, agent communication tracing, and real-time CLI visualization
3. **Vector Store Integration**: Query logging, embedding statistics, and vector store CLI tools
4. **Jupyter Notebook Integration**: Trace analysis notebook, vector visualization notebook, and query playground
5. **Integration and Testing**: Makefile integration, end-to-end testing, and documentation finalization
6. **Optimization and Refinement**: Performance optimization, user experience refinement, and final integration

## 3. Phase 1: Foundation Setup

### 3.1 Core Tracing Infrastructure

#### Step 1: Create Directory Structure
```bash
mkdir -p app/core/tracing
touch app/core/tracing/__init__.py
touch app/core/tracing/collector.py
touch app/core/tracing/utils.py
```

#### Step 2: Implement TracingCollector Class

Create `TracingCollector` class in `app/core/tracing/collector.py`:

```python
import enum
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

class TracingLevel(enum.Enum):
    NONE = 0       # No tracing
    BASIC = 1      # Node transitions only
    STANDARD = 2   # Node transitions + key state changes
    DETAILED = 3   # Full state diffs + internal operations
    DEBUG = 4      # Everything including LLM calls and raw responses

class TracingCollector:
    """Collector for tracing events in the system."""

    def __init__(
        self,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        granularity: TracingLevel = TracingLevel.STANDARD
    ):
        """Initialize the tracing collector.

        Args:
            session_id: Unique identifier for the session
            user_id: Identifier for the user
            thread_id: Identifier for the conversation thread
            granularity: Level of detail to capture
        """
        self.session_id = session_id or str(uuid.uuid4())
        self.user_id = user_id
        self.thread_id = thread_id
        self.granularity = granularity
        self.traces: List[Dict[str, Any]] = []

    def add_trace(
        self,
        node_id: str,
        event_type: str,
        state_before: Optional[Any] = None,
        state_after: Optional[Any] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a trace event.

        Args:
            node_id: Identifier for the node
            event_type: Type of event (node_start, node_end, etc.)
            state_before: State before the event
            state_after: State after the event
            metadata: Additional metadata
        """
        if self.granularity == TracingLevel.NONE:
            return

        # Skip detailed events if granularity is too low
        if event_type in ["llm_call", "rag_retrieval"] and self.granularity < TracingLevel.DETAILED:
            return

        trace = {
            "timestamp": datetime.now().isoformat(),
            "correlation": {
                "thread_id": self.thread_id,
                "user_id": self.user_id,
                "session_id": self.session_id,
            },
            "node_id": node_id,
            "event_type": event_type,
            "metadata": metadata or {},
        }

        # Add state information based on granularity
        if self.granularity >= TracingLevel.STANDARD:
            if state_before is not None:
                trace["state_before"] = self._serialize_state(state_before)
            if state_after is not None:
                trace["state_after"] = self._serialize_state(state_after)

        self.traces.append(trace)

    def _serialize_state(self, state: Any) -> Dict[str, Any]:
        """Serialize state object to a dictionary.

        Args:
            state: State object to serialize

        Returns:
            Serialized state as a dictionary
        """
        # Handle Pydantic models
        if hasattr(state, "model_dump"):
            return state.model_dump()
        # Handle dataclasses
        if hasattr(state, "__dataclass_fields__"):
            return {field: getattr(state, field) for field in state.__dataclass_fields__}
        # Handle dictionaries
        if isinstance(state, dict):
            return state
        # Handle other objects
        return {"value": str(state)}

    def export_json(self, file_path: Optional[str] = None) -> str:
        """Export traces as JSON.

        Args:
            file_path: Path to save the JSON file

        Returns:
            JSON string of traces
        """
        json_str = json.dumps(self.traces, indent=2)

        if file_path:
            with open(file_path, "w") as f:
                f.write(json_str)

        return json_str

    def export_ndjson(self, file_path: Optional[str] = None) -> str:
        """Export traces as newline-delimited JSON.

        Args:
            file_path: Path to save the NDJSON file

        Returns:
            NDJSON string of traces
        """
        ndjson_lines = [json.dumps(trace) for trace in self.traces]
        ndjson_str = "\n".join(ndjson_lines)

        if file_path:
            with open(file_path, "w") as f:
                f.write(ndjson_str)

        return ndjson_str

    def export_langgraph_format(self, file_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """Export traces in LangGraph-compatible format.

        Args:
            file_path: Path to save the LangGraph format file

        Returns:
            List of traces in LangGraph format
        """
        langgraph_events = []

        for trace in self.traces:
            event = {
                "id": str(uuid.uuid4()),
                "type": "node_execution" if trace["event_type"] == "node_start" else "state_update",
                "name": trace["node_id"],
                "data": {
                    "inputs": trace.get("state_before"),
                    "outputs": trace.get("state_after"),
                    "metadata": trace.get("metadata", {})
                },
                "timestamp": trace["timestamp"]
            }
            langgraph_events.append(event)

        if file_path:
            with open(file_path, "w") as f:
                json.dump(langgraph_events, f, indent=2)

        return langgraph_events

    def clear(self) -> None:
        """Clear all traces."""
        self.traces = []
```

#### Step 3: Create Utility Functions

Implement trace storage and retrieval utilities in `app/core/tracing/utils.py`:

```python
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from app.core.tracing.collector import TracingCollector

def save_traces(
    traces: List[Dict[str, Any]],
    directory: str = "traces",
    prefix: str = "trace",
    format: str = "json"
) -> str:
    """Save traces to a file.

    Args:
        traces: List of trace events
        directory: Directory to save the file
        prefix: Prefix for the filename
        format: File format (json or ndjson)

    Returns:
        Path to the saved file
    """
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    filename = f"{prefix}-{timestamp}.{format}"
    file_path = os.path.join(directory, filename)

    # Save traces
    if format == "json":
        with open(file_path, "w") as f:
            json.dump(traces, f, indent=2)
    elif format == "ndjson":
        with open(file_path, "w") as f:
            for trace in traces:
                f.write(json.dumps(trace) + "\n")
    else:
        raise ValueError(f"Unsupported format: {format}")

    return file_path

def load_traces(file_path: str) -> List[Dict[str, Any]]:
    """Load traces from a file.

    Args:
        file_path: Path to the trace file

    Returns:
        List of trace events
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Trace file not found: {file_path}")

    # Determine format from file extension
    if file_path.endswith(".json"):
        with open(file_path, "r") as f:
            return json.load(f)
    elif file_path.endswith(".ndjson"):
        traces = []
        with open(file_path, "r") as f:
            for line in f:
                if line.strip():
                    traces.append(json.loads(line))
        return traces
    else:
        raise ValueError(f"Unsupported file format: {file_path}")

def filter_traces(
    traces: List[Dict[str, Any]],
    node_id: Optional[str] = None,
    event_type: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    thread_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """Filter traces based on criteria.

    Args:
        traces: List of trace events
        node_id: Filter by node ID
        event_type: Filter by event type
        start_time: Filter by start time (ISO format)
        end_time: Filter by end time (ISO format)
        session_id: Filter by session ID
        user_id: Filter by user ID
        thread_id: Filter by thread ID

    Returns:
        Filtered list of trace events
    """
    filtered_traces = traces

    if node_id:
        filtered_traces = [t for t in filtered_traces if t.get("node_id") == node_id]

    if event_type:
        filtered_traces = [t for t in filtered_traces if t.get("event_type") == event_type]

    if start_time:
        start_dt = datetime.fromisoformat(start_time)
        filtered_traces = [
            t for t in filtered_traces
            if datetime.fromisoformat(t.get("timestamp", "")) >= start_dt
        ]

    if end_time:
        end_dt = datetime.fromisoformat(end_time)
        filtered_traces = [
            t for t in filtered_traces
            if datetime.fromisoformat(t.get("timestamp", "")) <= end_dt
        ]

    if session_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("session_id") == session_id
        ]

    if user_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("user_id") == user_id
        ]

    if thread_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("thread_id") == thread_id
        ]

    return filtered_traces

def get_latest_trace_file(directory: str = "traces", prefix: str = "trace") -> Optional[str]:
    """Get the path to the latest trace file.

    Args:
        directory: Directory containing trace files
        prefix: Prefix for trace filenames

    Returns:
        Path to the latest trace file, or None if no files found
    """
    if not os.path.exists(directory):
        return None

    files = [
        os.path.join(directory, f)
        for f in os.listdir(directory)
        if f.startswith(prefix) and (f.endswith(".json") or f.endswith(".ndjson"))
    ]

    if not files:
        return None

    # Sort by modification time (newest first)
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    return files[0]
```

#### Step 4: Create Unit Tests

Create test directory and implement unit tests for `TracingCollector`:

```bash
mkdir -p tests/core/tracing
touch tests/core/tracing/__init__.py
touch tests/core/tracing/test_collector.py
touch tests/core/tracing/test_utils.py
```

Implement unit tests in `tests/core/tracing/test_collector.py`:

```python
import json
import os
import tempfile
import unittest
from datetime import datetime

from app.core.tracing.collector import TracingCollector, TracingLevel

class TestTracingCollector(unittest.TestCase):
    def setUp(self):
        self.tracer = TracingCollector(
            session_id="test-session",
            user_id="test-user",
            thread_id="test-thread"
        )

    def test_init(self):
        """Test initialization of TracingCollector."""
        self.assertEqual(self.tracer.session_id, "test-session")
        self.assertEqual(self.tracer.user_id, "test-user")
        self.assertEqual(self.tracer.thread_id, "test-thread")
        self.assertEqual(self.tracer.granularity, TracingLevel.STANDARD)
        self.assertEqual(len(self.tracer.traces), 0)

    def test_add_trace(self):
        """Test adding a trace event."""
        self.tracer.add_trace(
            node_id="test-node",
            event_type="node_start",
            metadata={"test": "data"}
        )

        self.assertEqual(len(self.tracer.traces), 1)
        trace = self.tracer.traces[0]
        self.assertEqual(trace["node_id"], "test-node")
        self.assertEqual(trace["event_type"], "node_start")
        self.assertEqual(trace["metadata"], {"test": "data"})
        self.assertEqual(trace["correlation"]["session_id"], "test-session")
        self.assertEqual(trace["correlation"]["user_id"], "test-user")
        self.assertEqual(trace["correlation"]["thread_id"], "test-thread")

    def test_granularity_filtering(self):
        """Test that granularity filters events correctly."""
        # Create a tracer with BASIC granularity
        basic_tracer = TracingCollector(
            session_id="test-session",
            granularity=TracingLevel.BASIC
        )

        # Add a detailed event
        basic_tracer.add_trace(
            node_id="test-node",
            event_type="llm_call",
            metadata={"prompt": "test"}
        )

        # Should be filtered out
        self.assertEqual(len(basic_tracer.traces), 0)

        # Add a basic event
        basic_tracer.add_trace(
            node_id="test-node",
            event_type="node_start"
        )

        # Should be included
        self.assertEqual(len(basic_tracer.traces), 1)

    def test_export_json(self):
        """Test exporting traces as JSON."""
        self.tracer.add_trace(
            node_id="test-node",
            event_type="node_start"
        )

        # Export to string
        json_str = self.tracer.export_json()
        traces = json.loads(json_str)

        self.assertEqual(len(traces), 1)
        self.assertEqual(traces[0]["node_id"], "test-node")

        # Export to file
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as f:
            file_path = f.name

        try:
            self.tracer.export_json(file_path)

            with open(file_path, "r") as f:
                file_traces = json.load(f)

            self.assertEqual(len(file_traces), 1)
            self.assertEqual(file_traces[0]["node_id"], "test-node")
        finally:
            os.unlink(file_path)

    def test_export_ndjson(self):
        """Test exporting traces as NDJSON."""
        self.tracer.add_trace(
            node_id="test-node-1",
            event_type="node_start"
        )
        self.tracer.add_trace(
            node_id="test-node-2",
            event_type="node_end"
        )

        # Export to string
        ndjson_str = self.tracer.export_ndjson()
        lines = ndjson_str.strip().split("\n")

        self.assertEqual(len(lines), 2)
        self.assertEqual(json.loads(lines[0])["node_id"], "test-node-1")
        self.assertEqual(json.loads(lines[1])["node_id"], "test-node-2")

        # Export to file
        with tempfile.NamedTemporaryFile(suffix=".ndjson", delete=False) as f:
            file_path = f.name

        try:
            self.tracer.export_ndjson(file_path)

            with open(file_path, "r") as f:
                file_lines = f.read().strip().split("\n")

            self.assertEqual(len(file_lines), 2)
            self.assertEqual(json.loads(file_lines[0])["node_id"], "test-node-1")
            self.assertEqual(json.loads(file_lines[1])["node_id"], "test-node-2")
        finally:
            os.unlink(file_path)

    def test_export_langgraph_format(self):
        """Test exporting traces in LangGraph format."""
        self.tracer.add_trace(
            node_id="test-node",
            event_type="node_start",
            state_before={"key": "value"},
            state_after={"key": "new-value"}
        )

        langgraph_events = self.tracer.export_langgraph_format()

        self.assertEqual(len(langgraph_events), 1)
        event = langgraph_events[0]
        self.assertEqual(event["type"], "node_execution")
        self.assertEqual(event["name"], "test-node")
        self.assertEqual(event["data"]["inputs"], {"key": "value"})
        self.assertEqual(event["data"]["outputs"], {"key": "new-value"})

    def test_clear(self):
        """Test clearing traces."""
        self.tracer.add_trace(
            node_id="test-node",
            event_type="node_start"
        )

        self.assertEqual(len(self.tracer.traces), 1)

        self.tracer.clear()

        self.assertEqual(len(self.tracer.traces), 0)

if __name__ == "__main__":
    unittest.main()
```

Implement unit tests in `tests/core/tracing/test_utils.py`:

```python
import json
import os
import tempfile
import time
import unittest
from datetime import datetime, timedelta

from app.core.tracing.collector import TracingCollector
from app.core.tracing.utils import (
    save_traces,
    load_traces,
    filter_traces,
    get_latest_trace_file
)

class TestTracingUtils(unittest.TestCase):
    def setUp(self):
        self.tracer = TracingCollector(
            session_id="test-session",
            user_id="test-user",
            thread_id="test-thread"
        )

        # Add some test traces
        self.tracer.add_trace(
            node_id="node-1",
            event_type="node_start",
            metadata={"timestamp": datetime.now().isoformat()}
        )

        time.sleep(0.1)  # Ensure different timestamps

        self.tracer.add_trace(
            node_id="node-2",
            event_type="node_start",
            metadata={"timestamp": datetime.now().isoformat()}
        )

        time.sleep(0.1)  # Ensure different timestamps

        self.tracer.add_trace(
            node_id="node-1",
            event_type="node_end",
            metadata={"timestamp": datetime.now().isoformat()}
        )

    def test_save_and_load_traces_json(self):
        """Test saving and loading traces in JSON format."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save traces
            file_path = save_traces(
                self.tracer.traces,
                directory=temp_dir,
                prefix="test",
                format="json"
            )

            # Verify file exists
            self.assertTrue(os.path.exists(file_path))

            # Load traces
            loaded_traces = load_traces(file_path)

            # Verify traces match
            self.assertEqual(len(loaded_traces), len(self.tracer.traces))
            for i, trace in enumerate(self.tracer.traces):
                self.assertEqual(loaded_traces[i]["node_id"], trace["node_id"])
                self.assertEqual(loaded_traces[i]["event_type"], trace["event_type"])

    def test_save_and_load_traces_ndjson(self):
        """Test saving and loading traces in NDJSON format."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save traces
            file_path = save_traces(
                self.tracer.traces,
                directory=temp_dir,
                prefix="test",
                format="ndjson"
            )

            # Verify file exists
            self.assertTrue(os.path.exists(file_path))

            # Load traces
            loaded_traces = load_traces(file_path)

            # Verify traces match
            self.assertEqual(len(loaded_traces), len(self.tracer.traces))
            for i, trace in enumerate(self.tracer.traces):
                self.assertEqual(loaded_traces[i]["node_id"], trace["node_id"])
                self.assertEqual(loaded_traces[i]["event_type"], trace["event_type"])

    def test_filter_traces_by_node_id(self):
        """Test filtering traces by node ID."""
        filtered = filter_traces(self.tracer.traces, node_id="node-1")

        self.assertEqual(len(filtered), 2)
        self.assertEqual(filtered[0]["node_id"], "node-1")
        self.assertEqual(filtered[1]["node_id"], "node-1")

    def test_filter_traces_by_event_type(self):
        """Test filtering traces by event type."""
        filtered = filter_traces(self.tracer.traces, event_type="node_start")

        self.assertEqual(len(filtered), 2)
        self.assertEqual(filtered[0]["event_type"], "node_start")
        self.assertEqual(filtered[1]["event_type"], "node_start")

    def test_filter_traces_by_time_range(self):
        """Test filtering traces by time range."""
        # Get timestamps from traces
        timestamps = [
            datetime.fromisoformat(trace["timestamp"])
            for trace in self.tracer.traces
        ]

        # Filter by start time (include all)
        start_time = (min(timestamps) - timedelta(seconds=1)).isoformat()
        filtered = filter_traces(self.tracer.traces, start_time=start_time)

        self.assertEqual(len(filtered), 3)

        # Filter by end time (include only first)
        end_time = (min(timestamps) + timedelta(milliseconds=50)).isoformat()
        filtered = filter_traces(self.tracer.traces, end_time=end_time)

        self.assertEqual(len(filtered), 1)

        # Filter by time range (include middle)
        start_time = (timestamps[0] + timedelta(milliseconds=50)).isoformat()
        end_time = (timestamps[2] - timedelta(milliseconds=50)).isoformat()
        filtered = filter_traces(
            self.tracer.traces,
            start_time=start_time,
            end_time=end_time
        )

        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0]["node_id"], "node-2")

    def test_get_latest_trace_file(self):
        """Test getting the latest trace file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create some trace files
            file1 = os.path.join(temp_dir, "trace-20230101-120000.json")
            file2 = os.path.join(temp_dir, "trace-20230101-120100.json")
            file3 = os.path.join(temp_dir, "other-20230101-120200.json")  # Different prefix

            # Create files with different modification times
            with open(file1, "w") as f:
                f.write("{}")

            time.sleep(0.1)

            with open(file2, "w") as f:
                f.write("{}")

            time.sleep(0.1)

            with open(file3, "w") as f:
                f.write("{}")

            # Get latest trace file
            latest = get_latest_trace_file(directory=temp_dir, prefix="trace")

            # Should be file2 (latest with correct prefix)
            self.assertEqual(latest, file2)

            # Get latest with different prefix
            latest = get_latest_trace_file(directory=temp_dir, prefix="other")

            # Should be file3
            self.assertEqual(latest, file3)

            # Get latest with non-existent prefix
            latest = get_latest_trace_file(directory=temp_dir, prefix="nonexistent")

            # Should be None
            self.assertIsNone(latest)

if __name__ == "__main__":
    unittest.main()
```

### 3.2 Database Schema Extensions

#### Step 1: Create Migration Script

Create a migration script for the `query_logs` table in `backend/migrations/versions/xxxx_add_query_logs_table.py`:

```python
"""Add query_logs table

Revision ID: xxxx
Revises: previous_revision_id
Create Date: 2023-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

# revision identifiers, used by Alembic.
revision = 'xxxx'
down_revision = 'previous_revision_id'
branch_labels = None
depends_on = None


def upgrade():
    # Create query_logs table
    op.create_table(
        'query_logs',
        sa.Column('id', UUID, primary_key=True, server_default=sa.text("gen_random_uuid()")),
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('user_id', UUID, sa.ForeignKey('users.id', ondelete='SET NULL'), nullable=True),
        sa.Column('thread_id', sa.String(), nullable=True),
        sa.Column('session_id', sa.String(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('department', sa.String(), nullable=True),
        sa.Column('top_results', JSONB, nullable=True),
        sa.Column('execution_time_ms', sa.Integer(), nullable=True),
        sa.Column('metadata', JSONB, server_default=sa.text("'{}'::jsonb"), nullable=False),
    )

    # Create indexes for efficient querying
    op.create_index('idx_query_logs_timestamp', 'query_logs', ['timestamp'])
    op.create_index('idx_query_logs_user_id', 'query_logs', ['user_id'])
    op.create_index('idx_query_logs_thread_id', 'query_logs', ['thread_id'])
    op.create_index('idx_query_logs_session_id', 'query_logs', ['session_id'])
    op.create_index('idx_query_logs_department', 'query_logs', ['department'])


def downgrade():
    # Drop indexes
    op.drop_index('idx_query_logs_department')
    op.drop_index('idx_query_logs_session_id')
    op.drop_index('idx_query_logs_thread_id')
    op.drop_index('idx_query_logs_user_id')
    op.drop_index('idx_query_logs_timestamp')

    # Drop table
    op.drop_table('query_logs')
```

Run the migration:

```bash
cd backend
alembic upgrade head
```

#### Step 2: Update Database Initialization

Update the database initialization script in `backend/app/db/init_db.py` to include the new tables:

```python
# Add this import
from app.models.query_log import QueryLog

# In the init_db function, add:
def init_db(db: Session) -> None:
    # existing initialization code...

    # Verify pgvector extension
    db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

    # Verify query_logs table
    tables = inspect(db.bind).get_table_names()
    if 'query_logs' not in tables:
        logger.warning("query_logs table not found, running migrations may be required")
```

Create the SQLAlchemy model for query logs in `backend/app/models/query_log.py`:

```python
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class QueryLog(Base):
    """Model for storing query logs."""

    __tablename__ = "query_logs"

    id = Column(PGUUID, primary_key=True, server_default=text("gen_random_uuid()"))
    query_text = Column(Text, nullable=False)
    user_id = Column(PGUUID, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    thread_id = Column(String, nullable=True)
    session_id = Column(String, nullable=True)
    timestamp = Column(DateTime(timezone=True), server_default=text("now()"), nullable=False)
    department = Column(String, nullable=True)
    top_results = Column(JSONB, nullable=True)
    execution_time_ms = Column(Integer, nullable=True)
    metadata = Column(JSONB, server_default=text("'{}'::jsonb"), nullable=False)

    # Relationships
    user = relationship("User", back_populates="query_logs")
```

Update the User model in `backend/app/models/user.py` to include the relationship:

```python
# Add this to the User class
query_logs = relationship("QueryLog", back_populates="user")
```

#### Step 3: Create Query Logging Utilities

Create the query logging module in `backend/app/rag/query_logging.py`:

```python
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import desc, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import async_session
from app.models.query_log import QueryLog


async def log_query(
    query_text: str,
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    department: Optional[str] = None,
    results: Optional[List[Dict[str, Any]]] = None,
    execution_time_ms: Optional[int] = None,
    metadata: Optional[Dict[str, Any]] = None,
) -> UUID:
    """Log a query to the database.

    Args:
        query_text: The text of the query
        user_id: ID of the user who made the query
        thread_id: ID of the conversation thread
        session_id: ID of the session
        department: Department the query was routed to
        results: Top results returned for the query
        execution_time_ms: Execution time in milliseconds
        metadata: Additional metadata

    Returns:
        ID of the created query log
    """
    async with async_session() as session:
        query_log = QueryLog(
            query_text=query_text,
            user_id=user_id,
            thread_id=thread_id,
            session_id=session_id,
            department=department,
            top_results=results,
            execution_time_ms=execution_time_ms,
            metadata=metadata or {},
        )

        session.add(query_log)
        await session.commit()
        await session.refresh(query_log)

        return query_log.id


async def get_query_logs(
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    department: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """Get query logs from the database.

    Args:
        user_id: Filter by user ID
        thread_id: Filter by thread ID
        session_id: Filter by session ID
        department: Filter by department
        start_time: Filter by start time
        end_time: Filter by end time
        limit: Maximum number of logs to return
        offset: Offset for pagination

    Returns:
        List of query logs
    """
    async with async_session() as session:
        query = select(QueryLog).order_by(desc(QueryLog.timestamp))

        if user_id:
            query = query.filter(QueryLog.user_id == user_id)

        if thread_id:
            query = query.filter(QueryLog.thread_id == thread_id)

        if session_id:
            query = query.filter(QueryLog.session_id == session_id)

        if department:
            query = query.filter(QueryLog.department == department)

        if start_time:
            query = query.filter(QueryLog.timestamp >= start_time)

        if end_time:
            query = query.filter(QueryLog.timestamp <= end_time)

        query = query.limit(limit).offset(offset)

        result = await session.execute(query)
        query_logs = result.scalars().all()

        return [
            {
                "id": str(log.id),
                "query_text": log.query_text,
                "user_id": str(log.user_id) if log.user_id else None,
                "thread_id": log.thread_id,
                "session_id": log.session_id,
                "timestamp": log.timestamp.isoformat(),
                "department": log.department,
                "top_results": log.top_results,
                "execution_time_ms": log.execution_time_ms,
                "metadata": log.metadata,
            }
            for log in query_logs
        ]


async def delete_query_logs(
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    older_than: Optional[datetime] = None,
) -> int:
    """Delete query logs from the database.

    Args:
        user_id: Delete logs for this user ID
        thread_id: Delete logs for this thread ID
        session_id: Delete logs for this session ID
        older_than: Delete logs older than this time

    Returns:
        Number of logs deleted
    """
    async with async_session() as session:
        query = select(QueryLog)

        if user_id:
            query = query.filter(QueryLog.user_id == user_id)

        if thread_id:
            query = query.filter(QueryLog.thread_id == thread_id)

        if session_id:
            query = query.filter(QueryLog.session_id == session_id)

        if older_than:
            query = query.filter(QueryLog.timestamp < older_than)

        result = await session.execute(query)
        query_logs = result.scalars().all()

        for log in query_logs:
            await session.delete(log)

        await session.commit()

        return len(query_logs)
```

#### Step 4: Test Database Schema

Create a test script in `backend/tests/rag/test_query_logging.py`:

```python
import asyncio
import uuid
from datetime import datetime, timedelta

import pytest
from sqlalchemy import select

from app.db.session import async_session
from app.models.query_log import QueryLog
from app.rag.query_logging import log_query, get_query_logs, delete_query_logs


@pytest.mark.asyncio
async def test_log_query():
    """Test logging a query."""
    # Generate unique IDs for testing
    user_id = str(uuid.uuid4())
    thread_id = f"thread-{uuid.uuid4()}"
    session_id = f"session-{uuid.uuid4()}"

    # Log a query
    query_id = await log_query(
        query_text="Test query",
        user_id=user_id,
        thread_id=thread_id,
        session_id=session_id,
        department="test",
        results=[{"id": "doc1", "score": 0.9}],
        execution_time_ms=100,
        metadata={"test": "metadata"}
    )

    # Verify query was logged
    async with async_session() as session:
        result = await session.execute(select(QueryLog).filter(QueryLog.id == query_id))
        query_log = result.scalars().first()

        assert query_log is not None
        assert query_log.query_text == "Test query"
        assert str(query_log.user_id) == user_id
        assert query_log.thread_id == thread_id
        assert query_log.session_id == session_id
        assert query_log.department == "test"
        assert query_log.top_results == [{"id": "doc1", "score": 0.9}]
        assert query_log.execution_time_ms == 100
        assert query_log.metadata == {"test": "metadata"}


@pytest.mark.asyncio
async def test_get_query_logs():
    """Test retrieving query logs."""
    # Generate unique IDs for testing
    user_id = str(uuid.uuid4())
    thread_id = f"thread-{uuid.uuid4()}"
    session_id = f"session-{uuid.uuid4()}"

    # Log multiple queries
    await log_query(
        query_text="Query 1",
        user_id=user_id,
        thread_id=thread_id,
        session_id=session_id,
        department="test1"
    )

    await log_query(
        query_text="Query 2",
        user_id=user_id,
        thread_id=thread_id,
        session_id=session_id,
        department="test2"
    )

    # Get logs by user ID
    logs = await get_query_logs(user_id=user_id)
    assert len(logs) == 2

    # Get logs by thread ID
    logs = await get_query_logs(thread_id=thread_id)
    assert len(logs) == 2

    # Get logs by session ID
    logs = await get_query_logs(session_id=session_id)
    assert len(logs) == 2

    # Get logs by department
    logs = await get_query_logs(department="test1")
    assert len(logs) == 1
    assert logs[0]["query_text"] == "Query 1"

    # Get logs with limit
    logs = await get_query_logs(user_id=user_id, limit=1)
    assert len(logs) == 1


@pytest.mark.asyncio
async def test_delete_query_logs():
    """Test deleting query logs."""
    # Generate unique IDs for testing
    user_id = str(uuid.uuid4())
    thread_id = f"thread-{uuid.uuid4()}"
    session_id = f"session-{uuid.uuid4()}"

    # Log a query
    await log_query(
        query_text="Test query",
        user_id=user_id,
        thread_id=thread_id,
        session_id=session_id
    )

    # Verify query was logged
    logs = await get_query_logs(user_id=user_id)
    assert len(logs) == 1

    # Delete the query log
    deleted = await delete_query_logs(user_id=user_id)
    assert deleted == 1

    # Verify query was deleted
    logs = await get_query_logs(user_id=user_id)
    assert len(logs) == 0


@pytest.mark.asyncio
async def test_query_logs_time_filtering():
    """Test filtering query logs by time."""
    # Generate unique IDs for testing
    user_id = str(uuid.uuid4())

    # Log queries with different timestamps
    now = datetime.now()

    # Create a query log with a timestamp 1 hour ago
    one_hour_ago = now - timedelta(hours=1)
    async with async_session() as session:
        query_log = QueryLog(
            query_text="Old query",
            user_id=user_id,
            timestamp=one_hour_ago
        )
        session.add(query_log)
        await session.commit()

    # Create a query log with current timestamp
    await log_query(
        query_text="New query",
        user_id=user_id
    )

    # Get logs from the last 30 minutes
    thirty_minutes_ago = now - timedelta(minutes=30)
    logs = await get_query_logs(
        user_id=user_id,
        start_time=thirty_minutes_ago
    )

    assert len(logs) == 1
    assert logs[0]["query_text"] == "New query"

    # Get logs older than 30 minutes
    logs = await get_query_logs(
        user_id=user_id,
        end_time=thirty_minutes_ago
    )

    assert len(logs) == 1
    assert logs[0]["query_text"] == "Old query"

    # Clean up
    await delete_query_logs(user_id=user_id)
```

Run the tests:

```bash
cd backend
pytest -xvs tests/rag/test_query_logging.py
```

### 3.3 Basic CLI Visualization

#### Step 1: Set Up CLI Directory Structure

```bash
# Create CLI directory structure
mkdir -p app/cli
touch app/cli/__init__.py
```

#### Step 2: Create Basic Formatting Utilities

Create `app/cli/formatting.py` for basic text formatting:

```python
"""Formatting utilities for CLI output."""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.tree import Tree

# Initialize console
console = Console()

def format_timestamp(timestamp: str) -> str:
    """Format ISO timestamp to a more readable format.

    Args:
        timestamp: ISO format timestamp

    Returns:
        Formatted timestamp string
    """
    dt = datetime.fromisoformat(timestamp)
    return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

def print_info(message: str) -> None:
    """Print an info message.

    Args:
        message: Message to print
    """
    console.print(f"[bold blue]INFO:[/bold blue] {message}")

def print_success(message: str) -> None:
    """Print a success message.

    Args:
        message: Message to print
    """
    console.print(f"[bold green]SUCCESS:[/bold green] {message}")

def print_warning(message: str) -> None:
    """Print a warning message.

    Args:
        message: Message to print
    """
    console.print(f"[bold yellow]WARNING:[/bold yellow] {message}")

def print_error(message: str) -> None:
    """Print an error message.

    Args:
        message: Message to print
    """
    console.print(f"[bold red]ERROR:[/bold red] {message}")

def print_json(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> None:
    """Print JSON data in a formatted way.

    Args:
        data: JSON data to print
    """
    console.print_json(json.dumps(data))

def create_table(title: str, columns: List[str]) -> Table:
    """Create a table with the given title and columns.

    Args:
        title: Table title
        columns: Column names

    Returns:
        Rich Table object
    """
    table = Table(title=title)

    for column in columns:
        table.add_column(column)

    return table
```

#### Step 3: Implement Basic Trace Visualization

Create `app/cli/tracing.py` for trace visualization:

```python
"""CLI visualization tools for tracing."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.tree import Tree

from app.cli.formatting import (
    console,
    create_table,
    format_timestamp,
    print_error,
    print_info,
    print_json,
    print_success,
    print_warning,
)
from app.core.tracing.utils import filter_traces, load_traces

def visualize_trace_text(
    traces: List[Dict[str, Any]],
    show_metadata: bool = True
) -> None:
    """Visualize traces in text format.

    Args:
        traces: List of trace events
        show_metadata: Whether to show metadata
    """
    if not traces:
        print_warning("No traces to visualize")
        return

    for trace in traces:
        # Format timestamp
        timestamp = format_timestamp(trace["timestamp"])

        # Format event
        node_id = trace["node_id"]
        event_type = trace["event_type"]

        console.print(f"[bold][{timestamp}][/bold] {node_id} ({event_type})")

        # Print correlation IDs
        correlation = trace.get("correlation", {})
        for key, value in correlation.items():
            if value:
                console.print(f"  {key}: {value}")

        # Print metadata if requested
        if show_metadata and trace.get("metadata"):
            console.print("  metadata:")
            for key, value in trace["metadata"].items():
                console.print(f"    {key}: {value}")

        console.print()

def visualize_trace_tree(traces: List[Dict[str, Any]]) -> None:
    """Visualize traces in tree format.

    Args:
        traces: List of trace events
    """
    if not traces:
        print_warning("No traces to visualize")
        return

    # Group traces by node_id
    nodes = {}
    for trace in traces:
        node_id = trace["node_id"]
        if node_id not in nodes:
            nodes[node_id] = []
        nodes[node_id].append(trace)

    # Create tree
    session_id = traces[0].get("correlation", {}).get("session_id", "unknown")
    tree = Tree(f"Trace (session_id: {session_id})")

    for node_id, node_traces in nodes.items():
        # Create node branch
        node_branch = tree.add(node_id)

        # Add start and end times
        start_traces = [t for t in node_traces if t["event_type"] == "node_start"]
        end_traces = [t for t in node_traces if t["event_type"] == "node_end"]

        if start_traces:
            start_time = format_timestamp(start_traces[0]["timestamp"])
            node_branch.add(f"Start: {start_time}")

        if end_traces:
            end_time = format_timestamp(end_traces[0]["timestamp"])
            node_branch.add(f"End: {end_time}")

        # Calculate duration if possible
        if start_traces and end_traces:
            start_dt = datetime.fromisoformat(start_traces[0]["timestamp"])
            end_dt = datetime.fromisoformat(end_traces[0]["timestamp"])
            duration_ms = (end_dt - start_dt).total_seconds() * 1000
            node_branch.add(f"Duration: {duration_ms:.2f}ms")

        # Add metadata
        metadata_branch = node_branch.add("Metadata")
        for trace in node_traces:
            for key, value in trace.get("metadata", {}).items():
                metadata_branch.add(f"{key}: {value}")

    # Print tree
    console.print(tree)

def visualize_trace_table(traces: List[Dict[str, Any]]) -> None:
    """Visualize traces in table format.

    Args:
        traces: List of trace events
    """
    if not traces:
        print_warning("No traces to visualize")
        return

    # Create table
    table = create_table(
        title="Trace Events",
        columns=["Timestamp", "Node", "Event Type", "Duration", "Metadata"]
    )

    # Add rows
    for trace in traces:
        timestamp = format_timestamp(trace["timestamp"])
        node_id = trace["node_id"]
        event_type = trace["event_type"]

        # Get duration from metadata if available
        duration = trace.get("metadata", {}).get("duration_ms", "-")
        if duration != "-":
            duration = f"{duration}ms"

        # Format metadata
        metadata = json.dumps(trace.get("metadata", {}))
        if len(metadata) > 20:
            metadata = metadata[:17] + "..."

        table.add_row(timestamp, node_id, event_type, duration, metadata)

    # Print table
    console.print(table)

def visualize_trace(
    traces: Union[List[Dict[str, Any]], str],
    format: str = "text",
    show_metadata: bool = True
) -> None:
    """Visualize traces in the specified format.

    Args:
        traces: List of trace events or path to trace file
        format: Visualization format (text, tree, table)
        show_metadata: Whether to show metadata (for text format)
    """
    # Load traces from file if string is provided
    if isinstance(traces, str):
        try:
            traces = load_traces(traces)
        except Exception as e:
            print_error(f"Failed to load traces: {e}")
            return

    # Sort traces by timestamp
    traces = sorted(traces, key=lambda t: t["timestamp"])

    # Visualize based on format
    if format == "tree":
        visualize_trace_tree(traces)
    elif format == "table":
        visualize_trace_table(traces)
    else:  # text
        visualize_trace_text(traces, show_metadata=show_metadata)

def visualize_agent_communication(
    traces: Union[List[Dict[str, Any]], str],
    show_content: bool = True
) -> None:
    """Visualize agent communication from traces.

    Args:
        traces: List of trace events or path to trace file
        show_content: Whether to show message content
    """
    # Load traces from file if string is provided
    if isinstance(traces, str):
        try:
            traces = load_traces(traces)
        except Exception as e:
            print_error(f"Failed to load traces: {e}")
            return

    # Filter agent message events
    message_traces = filter_traces(traces, event_type="agent_message")

    if not message_traces:
        print_warning("No agent communication found in traces")
        return

    # Sort by timestamp
    message_traces = sorted(message_traces, key=lambda t: t["timestamp"])

    # Print messages
    for trace in message_traces:
        timestamp = format_timestamp(trace["timestamp"])
        metadata = trace.get("metadata", {})

        sender = metadata.get("sender", "Unknown")
        recipient = metadata.get("recipient", "Unknown")

        # Format sender and recipient with emojis
        sender_emoji = "🤖"
        recipient_emoji = "🤖"

        if sender.lower() == "user":
            sender_emoji = "👤"
        elif "co-ceo" in sender.lower():
            sender_emoji = "🤵"
        elif "finance" in sender.lower():
            sender_emoji = "💰"
        elif "marketing" in sender.lower():
            sender_emoji = "📣"

        if recipient.lower() == "user":
            recipient_emoji = "👤"
        elif "co-ceo" in recipient.lower():
            recipient_emoji = "🤵"
        elif "finance" in recipient.lower():
            recipient_emoji = "💰"
        elif "marketing" in recipient.lower():
            recipient_emoji = "📣"

        # Print message header
        console.print(f"[bold][{timestamp}][/bold] {sender_emoji} {sender} → {recipient_emoji} {recipient}")

        # Print message content if requested
        if show_content and "content" in metadata:
            content = metadata["content"]
            console.print(f"  {content}")

        console.print()
```

#### Step 4: Create Command-Line Interface

Create `app/cli/trace_viewer.py` for the command-line interface:

```python
"""Command-line interface for viewing traces."""

import os
from datetime import datetime
from typing import List, Optional

import typer
from rich.console import Console

from app.cli.formatting import print_error, print_info, print_success, print_warning
from app.cli.tracing import visualize_agent_communication, visualize_trace
from app.core.tracing.utils import filter_traces, get_latest_trace_file, load_traces

# Create Typer app
app = typer.Typer(help="Trace viewer CLI")
console = Console()

@app.command()
def view(
    file_path: Optional[str] = typer.Argument(None, help="Path to trace file"),
    format: str = typer.Option("text", help="Visualization format (text, tree, table)"),
    node_id: Optional[str] = typer.Option(None, help="Filter by node ID"),
    event_type: Optional[str] = typer.Option(None, help="Filter by event type"),
    session_id: Optional[str] = typer.Option(None, help="Filter by session ID"),
    start_time: Optional[str] = typer.Option(None, help="Filter by start time (ISO format)"),
    end_time: Optional[str] = typer.Option(None, help="Filter by end time (ISO format)"),
    show_metadata: bool = typer.Option(True, help="Show metadata (for text format)"),
    latest: bool = typer.Option(False, help="View the latest trace file"),
):
    """View a trace file with various visualization options."""
    # Determine file path
    if latest:
        file_path = get_latest_trace_file()
        if not file_path:
            print_error("No trace files found")
            raise typer.Exit(1)
    elif not file_path:
        print_error("No file path provided. Use --latest to view the latest trace file.")
        raise typer.Exit(1)

    # Load traces
    try:
        traces = load_traces(file_path)
    except Exception as e:
        print_error(f"Failed to load traces: {e}")
        raise typer.Exit(1)

    print_info(f"Loaded {len(traces)} trace events from {file_path}")

    # Apply filters
    filtered_traces = traces

    if node_id:
        filtered_traces = filter_traces(filtered_traces, node_id=node_id)
        print_info(f"Filtered to {len(filtered_traces)} events with node_id={node_id}")

    if event_type:
        filtered_traces = filter_traces(filtered_traces, event_type=event_type)
        print_info(f"Filtered to {len(filtered_traces)} events with event_type={event_type}")

    if session_id:
        filtered_traces = filter_traces(filtered_traces, session_id=session_id)
        print_info(f"Filtered to {len(filtered_traces)} events with session_id={session_id}")

    if start_time:
        filtered_traces = filter_traces(filtered_traces, start_time=start_time)
        print_info(f"Filtered to {len(filtered_traces)} events after {start_time}")

    if end_time:
        filtered_traces = filter_traces(filtered_traces, end_time=end_time)
        print_info(f"Filtered to {len(filtered_traces)} events before {end_time}")

    # Visualize traces
    visualize_trace(filtered_traces, format=format, show_metadata=show_metadata)

@app.command()
def agents(
    file_path: Optional[str] = typer.Argument(None, help="Path to trace file"),
    show_content: bool = typer.Option(True, help="Show message content"),
    latest: bool = typer.Option(False, help="View the latest trace file"),
):
    """View agent communication from a trace file."""
    # Determine file path
    if latest:
        file_path = get_latest_trace_file()
        if not file_path:
            print_error("No trace files found")
            raise typer.Exit(1)
    elif not file_path:
        print_error("No file path provided. Use --latest to view the latest trace file.")
        raise typer.Exit(1)

    # Visualize agent communication
    visualize_agent_communication(file_path, show_content=show_content)

if __name__ == "__main__":
    app()
```

#### Step 5: Create Simple Test Script

Create `backend/scripts/test_trace_visualization.py`:

```python
"""Test script for trace visualization."""

import json
import os
import random
import time
from datetime import datetime, timedelta

from app.cli.tracing import visualize_agent_communication, visualize_trace
from app.core.tracing.collector import TracingCollector, TracingLevel
from app.core.tracing.utils import save_traces

def generate_sample_traces():
    """Generate sample traces for testing visualization."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Add node_start event
    tracer.add_trace(
        node_id="analyze_query",
        event_type="node_start",
        metadata={"query": "What is our marketing budget?"}
    )

    # Wait a bit
    time.sleep(0.1)

    # Add llm_call event
    tracer.add_trace(
        node_id="analyze_query",
        event_type="llm_call",
        metadata={
            "prompt": "Analyze the query: What is our marketing budget?",
            "response": "This query is about the marketing budget.",
            "duration_ms": 250
        }
    )

    # Wait a bit
    time.sleep(0.1)

    # Add node_end event
    tracer.add_trace(
        node_id="analyze_query",
        event_type="node_end",
        metadata={"duration_ms": 350}
    )

    # Add node_start event for retrieve_knowledge
    tracer.add_trace(
        node_id="retrieve_knowledge",
        event_type="node_start",
        metadata={"department": "marketing"}
    )

    # Wait a bit
    time.sleep(0.1)

    # Add rag_retrieval event
    tracer.add_trace(
        node_id="retrieve_knowledge",
        event_type="rag_retrieval",
        metadata={
            "query": "marketing budget",
            "document_ids": ["doc1", "doc2", "doc3"],
            "similarity_scores": [0.92, 0.87, 0.76],
            "duration_ms": 150
        }
    )

    # Wait a bit
    time.sleep(0.1)

    # Add node_end event for retrieve_knowledge
    tracer.add_trace(
        node_id="retrieve_knowledge",
        event_type="node_end",
        metadata={"duration_ms": 200}
    )

    # Add agent_message events
    tracer.add_trace(
        node_id="co_ceo_agent",
        event_type="agent_message",
        metadata={
            "sender": "CO-CEO",
            "recipient": "FINANCE",
            "content": "What is our marketing budget?",
            "timestamp": datetime.now().isoformat()
        }
    )

    # Wait a bit
    time.sleep(0.1)

    tracer.add_trace(
        node_id="finance_agent",
        event_type="agent_message",
        metadata={
            "sender": "FINANCE",
            "recipient": "CO-CEO",
            "content": "The marketing budget for Q2 is $1.2M.",
            "timestamp": datetime.now().isoformat()
        }
    )

    # Wait a bit
    time.sleep(0.1)

    tracer.add_trace(
        node_id="co_ceo_agent",
        event_type="agent_message",
        metadata={
            "sender": "CO-CEO",
            "recipient": "USER",
            "content": "The marketing budget for Q2 is $1.2M.",
            "timestamp": datetime.now().isoformat()
        }
    )

    return tracer.traces

def main():
    """Run the test script."""
    # Generate sample traces
    traces = generate_sample_traces()

    # Save traces to a file
    os.makedirs("traces", exist_ok=True)
    file_path = save_traces(traces, directory="traces", prefix="test")

    print(f"Saved {len(traces)} trace events to {file_path}")

    # Visualize traces in different formats
    print("\n=== Text Format ===\n")
    visualize_trace(traces, format="text")

    print("\n=== Tree Format ===\n")
    visualize_trace(traces, format="tree")

    print("\n=== Table Format ===\n")
    visualize_trace(traces, format="table")

    print("\n=== Agent Communication ===\n")
    visualize_agent_communication(traces)

if __name__ == "__main__":
    main()
```

#### Step 6: Test the CLI Visualization

Run the test script:

```bash
cd backend
python scripts/test_trace_visualization.py
```

Test the command-line interface:

```bash
# View the latest trace file
python -m app.cli.trace_viewer view --latest

# View in tree format
python -m app.cli.trace_viewer view traces/test-*.json --format tree

# View agent communication
python -m app.cli.trace_viewer agents --latest
```

#### Step 7: Add CLI to Makefile

Update `makefiles/backend-cli-testing/Makefile` to include trace visualization commands:

```makefile
# Add these commands to the Makefile

# View the latest trace
view-trace:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest

# View agent communication from the latest trace
view-agents:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer agents --latest

# Generate a test trace
test-trace:
	cd $(BACKEND_DIR) && python scripts/test_trace_visualization.py
```

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing
make test-trace
make view-trace
make view-agents
```

## 4. Phase 2: LangGraph Integration

### 4.1 LangGraph Tracing Hooks

#### Step 1: Create Tracing Decorator

Create a decorator for tracing node functions in `app/core/tracing/decorators.py`:

```python
"""Decorators for tracing."""

import functools
import inspect
import time
from typing import Any, Callable, Dict, Optional, TypeVar, cast

from app.core.tracing.collector import TracingCollector, TracingLevel

F = TypeVar("F", bound=Callable[..., Any])

def trace_node(func: F) -> F:
    """Decorator for tracing node functions.

    This decorator adds tracing to a node function, capturing the start and end
    of the function execution, as well as any state changes.

    Args:
        func: The node function to trace

    Returns:
        Decorated function
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Get node ID from function name
        node_id = func.__name__
        if node_id.endswith("_node"):
            node_id = node_id[:-5]  # Remove "_node" suffix

        # Get tracer from kwargs
        tracer = kwargs.get("tracer")
        if not tracer or not isinstance(tracer, TracingCollector):
            # No tracer provided, just call the function
            return await func(*args, **kwargs)

        # Get state from args or kwargs
        state = None
        if args and len(args) > 0:
            state = args[0]
        elif "state" in kwargs:
            state = kwargs["state"]

        # Add node_start trace
        tracer.add_trace(
            node_id=node_id,
            event_type="node_start",
            state_before=state,
            metadata={
                "function": func.__name__,
                "module": func.__module__,
            }
        )

        # Measure execution time
        start_time = time.time()

        try:
            # Call the function
            result = await func(*args, **kwargs)

            # Calculate execution time
            execution_time = time.time() - start_time

            # Add node_end trace
            tracer.add_trace(
                node_id=node_id,
                event_type="node_end",
                state_after=result,
                metadata={
                    "duration_ms": int(execution_time * 1000),
                    "status": "success",
                }
            )

            return result
        except Exception as e:
            # Calculate execution time
            execution_time = time.time() - start_time

            # Add error trace
            tracer.add_trace(
                node_id=node_id,
                event_type="error",
                state_before=state,
                metadata={
                    "duration_ms": int(execution_time * 1000),
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "status": "error",
                }
            )

            # Re-raise the exception
            raise

    return cast(F, wrapper)
```

#### Step 2: Modify LangGraph Graph Class

Update `app/langgraph/graph.py` to support tracing:

```python
"""LangGraph implementation for agent orchestration."""

import asyncio
import json
import logging
from typing import Any, Callable, Dict, List, Optional, Tuple, Type, Union, cast

import langgraph.graph as lg
from langgraph.checkpoint.base import BaseCheckpointService
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import create_agent_executor
from pydantic import BaseModel, Field

from app.core.tracing.collector import TracingCollector, TracingLevel

logger = logging.getLogger(__name__)

# Add this function to build a graph with tracing
async def build_graph_with_tracing(
    agents: Dict[str, Any],
    knowledge_base_service: Any,
    tracer: Optional[TracingCollector] = None,
    checkpoint_service: Optional[BaseCheckpointService] = None,
) -> StateGraph:
    """Build a graph with tracing.

    Args:
        agents: Dictionary of agents
        knowledge_base_service: Knowledge base service
        tracer: Tracing collector
        checkpoint_service: Checkpoint service

    Returns:
        StateGraph
    """
    # Create the original graph
    graph = build_graph(
        agents=agents,
        knowledge_base_service=knowledge_base_service,
        checkpoint_service=checkpoint_service
    )

    # If no tracer is provided, return the original graph
    if not tracer:
        return graph

    # Get the original invoke method
    original_invoke = graph.ainvoke

    # Create a new invoke method with tracing
    async def invoke_with_tracing(state: Any) -> Any:
        # Add trace for graph execution start
        tracer.add_trace(
            node_id="graph",
            event_type="graph_start",
            state_before=state,
            metadata={
                "thread_id": getattr(state, "thread_id", None),
                "user_id": getattr(state, "user_id", None),
                "session_id": getattr(state, "session_id", None),
            }
        )

        # Call the original invoke method
        try:
            result = await original_invoke(state)

            # Add trace for graph execution end
            tracer.add_trace(
                node_id="graph",
                event_type="graph_end",
                state_after=result,
                metadata={
                    "status": "success",
                }
            )

            return result
        except Exception as e:
            # Add trace for graph execution error
            tracer.add_trace(
                node_id="graph",
                event_type="error",
                state_before=state,
                metadata={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "status": "error",
                }
            )

            # Re-raise the exception
            raise

    # Replace the invoke method
    graph.ainvoke = invoke_with_tracing

    return graph

# Update the existing build_graph function to add tracing hooks
def build_graph(
    agents: Dict[str, Any],
    knowledge_base_service: Any,
    checkpoint_service: Optional[BaseCheckpointService] = None,
) -> StateGraph:
    """Build a graph for agent orchestration.

    Args:
        agents: Dictionary of agents
        knowledge_base_service: Knowledge base service
        checkpoint_service: Checkpoint service

    Returns:
        StateGraph
    """
    # Existing implementation...

    # Add tracing hooks to the graph
    # This is a placeholder for the actual implementation

    return graph
```

#### Step 3: Add Tracing to Node Functions

Update node functions in `app/langgraph/nodes.py` to use the tracing decorator:

```python
"""Node functions for LangGraph."""

from typing import Any, Dict, List, Optional, Tuple, Union

from app.core.tracing.decorators import trace_node
from app.core.tracing.collector import TracingCollector

# Add the trace_node decorator to node functions
@trace_node
async def analyze_query_node(state: Any, tracer: Optional[TracingCollector] = None) -> Any:
    """Analyze the query and determine relevant departments.

    Args:
        state: Current state
        tracer: Tracing collector

    Returns:
        Updated state
    """
    # Existing implementation...

    # Add custom trace for department routing decision
    if tracer:
        tracer.add_trace(
            node_id="analyze_query",
            event_type="decision",
            metadata={
                "query": state.query,
                "departments": departments,
                "confidence_scores": confidence_scores,
            }
        )

    return state

@trace_node
async def retrieve_knowledge_node(state: Any, tracer: Optional[TracingCollector] = None) -> Any:
    """Retrieve relevant knowledge for the query.

    Args:
        state: Current state
        tracer: Tracing collector

    Returns:
        Updated state
    """
    # Existing implementation...

    # Add custom trace for RAG retrieval
    if tracer:
        tracer.add_trace(
            node_id="retrieve_knowledge",
            event_type="rag_retrieval",
            metadata={
                "query": state.query,
                "department": state.department,
                "document_ids": [doc["id"] for doc in documents],
                "similarity_scores": [doc["score"] for doc in documents],
                "document_count": len(documents),
            }
        )

    return state

# Add tracing to other node functions...
```

#### Step 4: Implement Trace Context Propagation

Create a utility function for propagating trace context in `app/core/tracing/utils.py`:

```python
# Add this function to the existing utils.py file

def propagate_trace_context(state: Any, tracer: TracingCollector) -> Dict[str, Any]:
    """Propagate trace context to node functions.

    Args:
        state: Current state
        tracer: Tracing collector

    Returns:
        Dictionary with trace context
    """
    # Extract correlation IDs from state
    thread_id = getattr(state, "thread_id", None)
    user_id = getattr(state, "user_id", None)
    session_id = getattr(state, "session_id", None)

    # Update tracer with correlation IDs
    if thread_id and not tracer.thread_id:
        tracer.thread_id = thread_id

    if user_id and not tracer.user_id:
        tracer.user_id = user_id

    if session_id and not tracer.session_id:
        tracer.session_id = session_id

    # Return trace context
    return {
        "tracer": tracer,
        "thread_id": thread_id,
        "user_id": user_id,
        "session_id": session_id,
    }
```

Update the graph execution in `app/langgraph/executor.py`:

```python
"""LangGraph executor for agent orchestration."""

from typing import Any, Dict, List, Optional, Tuple, Union

from app.core.tracing.collector import TracingCollector
from app.core.tracing.utils import propagate_trace_context
from app.langgraph.graph import build_graph_with_tracing

async def execute_graph(
    state: Any,
    agents: Dict[str, Any],
    knowledge_base_service: Any,
    tracer: Optional[TracingCollector] = None,
    checkpoint_service: Optional[Any] = None,
) -> Any:
    """Execute the graph with the given state.

    Args:
        state: Initial state
        agents: Dictionary of agents
        knowledge_base_service: Knowledge base service
        tracer: Tracing collector
        checkpoint_service: Checkpoint service

    Returns:
        Final state
    """
    # Build graph with tracing
    graph = await build_graph_with_tracing(
        agents=agents,
        knowledge_base_service=knowledge_base_service,
        tracer=tracer,
        checkpoint_service=checkpoint_service
    )

    # Propagate trace context
    if tracer:
        propagate_trace_context(state, tracer)

    # Execute graph
    result = await graph.ainvoke(state)

    return result
```

#### Step 5: Create Test Script for LangGraph Tracing

Create a test script in `backend/scripts/test_langgraph_tracing.py`:

```python
"""Test script for LangGraph tracing."""

import asyncio
import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.core.tracing.collector import TracingCollector, TracingLevel
from app.core.tracing.utils import save_traces
from app.langgraph.executor import execute_graph
from app.langgraph.graph import build_graph_with_tracing
from app.langgraph.state import AgentState

# Define a simple state model for testing
class TestState(BaseModel):
    """Test state model."""

    query: str
    department: Optional[str] = None
    documents: List[Dict[str, Any]] = Field(default_factory=list)
    response: Optional[str] = None
    thread_id: str = "test-thread"
    user_id: str = "test-user"
    session_id: str = "test-session"

# Define simple node functions for testing
async def analyze_query(state: TestState, tracer: Optional[TracingCollector] = None) -> TestState:
    """Analyze the query and determine department."""
    if tracer:
        tracer.add_trace(
            node_id="analyze_query",
            event_type="node_start",
            state_before=state,
            metadata={"function": "analyze_query"}
        )

    # Simulate analysis
    if "marketing" in state.query.lower():
        state.department = "marketing"
    elif "finance" in state.query.lower():
        state.department = "finance"
    else:
        state.department = "general"

    if tracer:
        tracer.add_trace(
            node_id="analyze_query",
            event_type="node_end",
            state_after=state,
            metadata={
                "department": state.department,
                "duration_ms": 100
            }
        )

    return state

async def retrieve_documents(state: TestState, tracer: Optional[TracingCollector] = None) -> TestState:
    """Retrieve documents for the query."""
    if tracer:
        tracer.add_trace(
            node_id="retrieve_documents",
            event_type="node_start",
            state_before=state,
            metadata={"function": "retrieve_documents"}
        )

    # Simulate document retrieval
    state.documents = [
        {"id": "doc1", "content": f"Document about {state.department}", "score": 0.9},
        {"id": "doc2", "content": f"Another document about {state.department}", "score": 0.8}
    ]

    if tracer:
        tracer.add_trace(
            node_id="retrieve_documents",
            event_type="rag_retrieval",
            state_after=state,
            metadata={
                "document_count": len(state.documents),
                "department": state.department,
                "duration_ms": 150
            }
        )

    return state

async def generate_response(state: TestState, tracer: Optional[TracingCollector] = None) -> TestState:
    """Generate a response based on documents."""
    if tracer:
        tracer.add_trace(
            node_id="generate_response",
            event_type="node_start",
            state_before=state,
            metadata={"function": "generate_response"}
        )

    # Simulate response generation
    state.response = f"Based on the documents about {state.department}, here is the answer to your query: '{state.query}'"

    if tracer:
        tracer.add_trace(
            node_id="generate_response",
            event_type="node_end",
            state_after=state,
            metadata={
                "response_length": len(state.response),
                "duration_ms": 200
            }
        )

    return state

# Define a simple graph for testing
async def build_test_graph(tracer: Optional[TracingCollector] = None):
    """Build a test graph."""
    from langgraph.graph import StateGraph

    # Create a graph
    builder = StateGraph(TestState)

    # Add nodes
    builder.add_node("analyze_query", analyze_query)
    builder.add_node("retrieve_documents", retrieve_documents)
    builder.add_node("generate_response", generate_response)

    # Add edges
    builder.add_edge("analyze_query", "retrieve_documents")
    builder.add_edge("retrieve_documents", "generate_response")
    builder.add_edge("generate_response", "END")

    # Set entry point
    builder.set_entry_point("analyze_query")

    # Compile the graph
    graph = builder.compile()

    # If no tracer is provided, return the original graph
    if not tracer:
        return graph

    # Get the original invoke method
    original_invoke = graph.ainvoke

    # Create a new invoke method with tracing
    async def invoke_with_tracing(state: TestState) -> TestState:
        # Add trace for graph execution start
        tracer.add_trace(
            node_id="graph",
            event_type="graph_start",
            state_before=state,
            metadata={
                "thread_id": state.thread_id,
                "user_id": state.user_id,
                "session_id": state.session_id,
            }
        )

        # Call the original invoke method
        try:
            result = await original_invoke(state)

            # Add trace for graph execution end
            tracer.add_trace(
                node_id="graph",
                event_type="graph_end",
                state_after=result,
                metadata={
                    "status": "success",
                }
            )

            return result
        except Exception as e:
            # Add trace for graph execution error
            tracer.add_trace(
                node_id="graph",
                event_type="error",
                state_before=state,
                metadata={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "status": "error",
                }
            )

            # Re-raise the exception
            raise

    # Replace the invoke method
    graph.ainvoke = invoke_with_tracing

    return graph

async def main():
    """Run the test script."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create initial state
    state = TestState(query="What is our marketing budget?")

    # Build the graph
    graph = await build_test_graph(tracer=tracer)

    # Execute the graph
    result = await graph.ainvoke(state)

    # Print the result
    print(f"Query: {result.query}")
    print(f"Department: {result.department}")
    print(f"Documents: {len(result.documents)}")
    print(f"Response: {result.response}")

    # Save traces to a file
    os.makedirs("traces", exist_ok=True)
    file_path = save_traces(tracer.traces, directory="traces", prefix="langgraph")

    print(f"\nSaved {len(tracer.traces)} trace events to {file_path}")

    # Visualize traces
    from app.cli.tracing import visualize_trace

    print("\n=== Trace Visualization ===\n")
    visualize_trace(tracer.traces, format="tree")

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 6: Test LangGraph Integration

Run the test script:

```bash
cd backend
python scripts/test_langgraph_tracing.py
```

Verify that the traces are properly collected and visualized:

```bash
# View the latest trace file
python -m app.cli.trace_viewer view --latest

# View in tree format
python -m app.cli.trace_viewer view traces/langgraph-*.json --format tree
```

### 4.2 Agent Communication Tracing

#### Step 1: Add Agent Message Event Type

Update `app/core/tracing/collector.py` to include the agent message event type:

```python
# Add this to the TracingCollector class docstring
"""
Event Types:
    - node_start: Node execution started
    - node_end: Node execution completed
    - state_update: State was updated
    - decision: A routing decision was made
    - error: An error occurred
    - llm_call: An LLM was called
    - rag_retrieval: Documents were retrieved from the knowledge base
    - agent_message: Communication between agents
"""
```

Create a utility function for agent message tracing in `app/core/tracing/utils.py`:

```python
# Add this function to the existing utils.py file

def trace_agent_message(
    tracer: TracingCollector,
    sender: str,
    recipient: str,
    content: str,
    node_id: Optional[str] = None,
    message_type: str = "text",
    context: Optional[Dict[str, Any]] = None,
) -> None:
    """Trace an agent message.

    Args:
        tracer: Tracing collector
        sender: Sender agent ID
        recipient: Recipient agent ID
        content: Message content
        node_id: Node ID (defaults to sender)
        message_type: Message type (text, json, etc.)
        context: Additional context for the message
    """
    if not tracer:
        return

    # Use sender as node_id if not provided
    if not node_id:
        node_id = sender

    # Add trace for agent message
    tracer.add_trace(
        node_id=node_id,
        event_type="agent_message",
        metadata={
            "sender": sender,
            "recipient": recipient,
            "content": content,
            "message_type": message_type,
            "timestamp": datetime.now().isoformat(),
            "context": context or {},
        }
    )
```

#### Step 2: Create Agent Communication Interface

Create an interface for agent communication in `app/agents/communication.py`:

```python
"""Agent communication interface."""

from typing import Any, Dict, List, Optional, Union

from app.core.tracing.collector import TracingCollector
from app.core.tracing.utils import trace_agent_message

class AgentCommunication:
    """Interface for agent communication with tracing."""

    def __init__(self, agent_id: str, tracer: Optional[TracingCollector] = None):
        """Initialize the agent communication interface.

        Args:
            agent_id: Agent ID
            tracer: Tracing collector
        """
        self.agent_id = agent_id
        self.tracer = tracer

    async def send_message(
        self,
        recipient: str,
        content: str,
        message_type: str = "text",
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Send a message to another agent.

        Args:
            recipient: Recipient agent ID
            content: Message content
            message_type: Message type (text, json, etc.)
            context: Additional context for the message
        """
        # Trace the message
        if self.tracer:
            trace_agent_message(
                tracer=self.tracer,
                sender=self.agent_id,
                recipient=recipient,
                content=content,
                node_id=self.agent_id,
                message_type=message_type,
                context=context,
            )

        # Actual message sending would happen here
        # For now, we just trace the message

        return None

    async def receive_message(
        self,
        sender: str,
        content: str,
        message_type: str = "text",
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Receive a message from another agent.

        Args:
            sender: Sender agent ID
            content: Message content
            message_type: Message type (text, json, etc.)
            context: Additional context for the message
        """
        # Trace the message
        if self.tracer:
            trace_agent_message(
                tracer=self.tracer,
                sender=sender,
                recipient=self.agent_id,
                content=content,
                node_id=self.agent_id,
                message_type=message_type,
                context=context,
            )

        # Actual message processing would happen here
        # For now, we just trace the message

        return None
```

#### Step 3: Modify Agent Classes

Update the base agent class in `app/agents/base.py` to include communication capabilities:

```python
"""Base agent class."""

from typing import Any, Dict, List, Optional, Union

from app.core.tracing.collector import TracingCollector
from app.agents.communication import AgentCommunication

class BaseAgent:
    """Base class for all agents."""

    def __init__(
        self,
        agent_id: str,
        tracer: Optional[TracingCollector] = None,
    ):
        """Initialize the agent.

        Args:
            agent_id: Agent ID
            tracer: Tracing collector
        """
        self.agent_id = agent_id
        self.tracer = tracer
        self.communication = AgentCommunication(agent_id=agent_id, tracer=tracer)

    async def process_message(
        self,
        message: str,
        sender: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Process a message and generate a response.

        Args:
            message: Input message
            sender: Sender of the message
            context: Additional context

        Returns:
            Response message
        """
        # Trace message receipt if sender is provided
        if sender and self.tracer:
            await self.communication.receive_message(
                sender=sender,
                content=message,
                context=context,
            )

        # Process the message (to be implemented by subclasses)
        response = await self._process_message(message, context)

        # Trace response if sender is provided
        if sender and self.tracer:
            await self.communication.send_message(
                recipient=sender,
                content=response,
                context=context,
            )

        return response

    async def _process_message(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Process a message and generate a response (to be implemented by subclasses).

        Args:
            message: Input message
            context: Additional context

        Returns:
            Response message
        """
        raise NotImplementedError("Subclasses must implement this method")

    async def send_message(
        self,
        recipient: str,
        content: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Send a message to another agent.

        Args:
            recipient: Recipient agent ID
            content: Message content
            context: Additional context
        """
        await self.communication.send_message(
            recipient=recipient,
            content=content,
            context=context,
        )
```

Update the co-ceo agent class in `app/agents/co_ceo.py` to include multi-agent orchestration with tracing:

```python
"""Co-CEO agent implementation."""

from typing import Any, Dict, List, Optional, Union

from app.agents.base import BaseAgent
from app.core.tracing.collector import TracingCollector

class CoCEOAgent(BaseAgent):
    """Co-CEO agent with multi-agent orchestration capabilities."""

    def __init__(
        self,
        agent_id: str = "co-ceo",
        tracer: Optional[TracingCollector] = None,
        department_agents: Optional[Dict[str, BaseAgent]] = None,
    ):
        """Initialize the Co-CEO agent.

        Args:
            agent_id: Agent ID
            tracer: Tracing collector
            department_agents: Dictionary of department agents
        """
        super().__init__(agent_id=agent_id, tracer=tracer)
        self.department_agents = department_agents or {}

    async def _process_message(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Process a message and generate a response.

        Args:
            message: Input message
            context: Additional context

        Returns:
            Response message
        """
        # Analyze the query to determine relevant departments
        departments = await self._analyze_query(message)

        # If no departments are relevant, handle the query directly
        if not departments:
            return await self._generate_response(message, [])

        # Collect responses from department agents
        department_responses = {}
        for department in departments:
            if department in self.department_agents:
                # Send message to department agent
                await self.send_message(
                    recipient=department,
                    content=message,
                    context={"query": message},
                )

                # Get response from department agent
                response = await self.department_agents[department].process_message(
                    message=message,
                    sender=self.agent_id,
                    context={"query": message},
                )

                department_responses[department] = response

        # Generate final response
        return await self._generate_response(message, department_responses)

    async def _analyze_query(self, query: str) -> List[str]:
        """Analyze the query to determine relevant departments.

        Args:
            query: User query

        Returns:
            List of relevant department IDs
        """
        # Simple keyword-based analysis for testing
        departments = []

        if "finance" in query.lower() or "budget" in query.lower() or "cost" in query.lower():
            departments.append("finance")

        if "marketing" in query.lower() or "campaign" in query.lower() or "brand" in query.lower():
            departments.append("marketing")

        # Trace the decision
        if self.tracer:
            self.tracer.add_trace(
                node_id=self.agent_id,
                event_type="decision",
                metadata={
                    "query": query,
                    "departments": departments,
                }
            )

        return departments

    async def _generate_response(
        self,
        query: str,
        department_responses: Union[List[str], Dict[str, str]],
    ) -> str:
        """Generate a response based on department responses.

        Args:
            query: User query
            department_responses: Responses from department agents

        Returns:
            Final response
        """
        # For testing, just combine the responses
        if isinstance(department_responses, list):
            return f"I don't have specific information about that."

        if not department_responses:
            return f"I don't have specific information about that."

        response_parts = []
        for department, response in department_responses.items():
            response_parts.append(f"From {department}: {response}")

        return "\n\n".join(response_parts)
```

#### Step 4: Create Test Script for Agent Communication

Create a test script in `backend/scripts/test_agent_communication.py`:

```python
"""Test script for agent communication tracing."""

import asyncio
import os
from typing import Dict, Optional

from app.agents.base import BaseAgent
from app.agents.co_ceo import CoCEOAgent
from app.core.tracing.collector import TracingCollector, TracingLevel
from app.core.tracing.utils import save_traces
from app.cli.tracing import visualize_agent_communication

class TestAgent(BaseAgent):
    """Test agent implementation."""

    async def _process_message(self, message: str, context: Optional[Dict] = None) -> str:
        """Process a message and generate a response."""
        # Simple response for testing
        return f"Response from {self.agent_id}: I received your message: '{message}'"

async def main():
    """Run the test script."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create agents
    finance_agent = TestAgent(agent_id="finance", tracer=tracer)
    marketing_agent = TestAgent(agent_id="marketing", tracer=tracer)

    # Create co-ceo agent with department agents
    co_ceo_agent = CoCEOAgent(
        tracer=tracer,
        department_agents={
            "finance": finance_agent,
            "marketing": marketing_agent,
        }
    )

    # Process a query
    query = "What is our marketing budget and financial performance?"
    response = await co_ceo_agent.process_message(
        message=query,
        sender="user",
        context={"query_id": "test-query"}
    )

    # Print the response
    print(f"Query: {query}")
    print(f"Response: {response}")

    # Save traces to a file
    os.makedirs("traces", exist_ok=True)
    file_path = save_traces(tracer.traces, directory="traces", prefix="agent-communication")

    print(f"\nSaved {len(tracer.traces)} trace events to {file_path}")

    # Visualize agent communication
    print("\n=== Agent Communication ===\n")
    visualize_agent_communication(tracer.traces)

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 5: Test Agent Communication Tracing

Run the test script:

```bash
cd backend
python scripts/test_agent_communication.py
```

Verify that agent communications are properly traced and visualized:

```bash
# View the latest trace file
python -m app.cli.trace_viewer view --latest

# View agent communication
python -m app.cli.trace_viewer agents --latest
```

### 4.3 Real-Time CLI Visualization

#### Step 1: Create Live Visualization Module

Create `app/cli/live_visualization.py` for real-time visualization:

```python
"""Real-time CLI visualization for tracing."""

import asyncio
import threading
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Union

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.tree import Tree

from app.core.tracing.collector import TracingCollector
from app.cli.formatting import format_timestamp

# Initialize console
console = Console()

class LiveVisualization:
    """Live visualization for tracing."""

    def __init__(
        self,
        tracer: TracingCollector,
        update_interval: float = 0.1,
        max_messages: int = 50,
        auto_scroll: bool = True,
    ):
        """Initialize the live visualization.

        Args:
            tracer: Tracing collector
            update_interval: Update interval in seconds
            max_messages: Maximum number of messages to display
            auto_scroll: Whether to auto-scroll to the latest message
        """
        self.tracer = tracer
        self.update_interval = update_interval
        self.max_messages = max_messages
        self.auto_scroll = auto_scroll

        self.live = None
        self.running = False
        self.last_trace_count = 0
        self.seen_trace_ids: Set[str] = set()

        # State for keyboard interaction
        self.paused = False
        self.filter_agent = None
        self.show_content = True

    def start(self):
        """Start the live visualization."""
        if self.running:
            return

        self.running = True
        self.live = Live(
            self._generate_layout(),
            refresh_per_second=int(1 / self.update_interval),
            auto_refresh=False
        )

        # Start the live display
        self.live.start()

        # Start the update thread
        self.update_thread = threading.Thread(target=self._update_loop)
        self.update_thread.daemon = True
        self.update_thread.start()

    def stop(self):
        """Stop the live visualization."""
        self.running = False
        if self.live:
            self.live.stop()

    def _update_loop(self):
        """Update loop for the live visualization."""
        while self.running:
            # Check if there are new traces
            if len(self.tracer.traces) > self.last_trace_count and not self.paused:
                # Update the display
                self.live.update(self._generate_layout())
                self.last_trace_count = len(self.tracer.traces)

            # Sleep for the update interval
            time.sleep(self.update_interval)

    def _generate_layout(self):
        """Generate the layout for the live visualization.

        Returns:
            Rich renderable for the layout
        """
        # Create the main panel
        panel = Panel(
            self._generate_content(),
            title="[bold]Real-Time Agent Communication[/bold]",
            subtitle="Press 'p' to pause/resume, 'f' to filter by agent, 'c' to toggle content, 'q' to quit",
            border_style="blue"
        )

        return panel

    def _generate_content(self):
        """Generate the content for the live visualization.

        Returns:
            Rich renderable for the content
        """
        # Filter agent message events
        message_traces = [
            t for t in self.tracer.traces
            if t.get("event_type") == "agent_message"
        ]

        # Apply agent filter
        if self.filter_agent:
            message_traces = [
                t for t in message_traces
                if (t.get("metadata", {}).get("sender") == self.filter_agent or
                    t.get("metadata", {}).get("recipient") == self.filter_agent)
            ]

        # Sort by timestamp
        message_traces = sorted(message_traces, key=lambda t: t["timestamp"])

        # Limit to max_messages
        if len(message_traces) > self.max_messages:
            message_traces = message_traces[-self.max_messages:]

        # Create a table for the messages
        table = Table(show_header=False, box=None, padding=(0, 1))
        table.add_column("Messages", width=100)

        # Add messages to the table
        for trace in message_traces:
            metadata = trace.get("metadata", {})

            sender = metadata.get("sender", "Unknown")
            recipient = metadata.get("recipient", "Unknown")

            # Format sender and recipient with emojis
            sender_emoji = "🤖"
            recipient_emoji = "🤖"

            if sender.lower() == "user":
                sender_emoji = "👤"
            elif "co-ceo" in sender.lower():
                sender_emoji = "🤵"
            elif "finance" in sender.lower():
                sender_emoji = "💰"
            elif "marketing" in sender.lower():
                sender_emoji = "📣"

            if recipient.lower() == "user":
                recipient_emoji = "👤"
            elif "co-ceo" in recipient.lower():
                recipient_emoji = "🤵"
            elif "finance" in recipient.lower():
                recipient_emoji = "💰"
            elif "marketing" in recipient.lower():
                recipient_emoji = "📣"

            # Format timestamp
            timestamp = format_timestamp(trace["timestamp"])

            # Create message header
            header = Text()
            header.append(f"[{timestamp}] ", style="bold")
            header.append(f"{sender_emoji} {sender}", style="bold blue")
            header.append(" → ", style="bold")
            header.append(f"{recipient_emoji} {recipient}", style="bold green")

            # Add header to table
            table.add_row(header)

            # Add message content if requested
            if self.show_content and "content" in metadata:
                content = metadata["content"]
                table.add_row(f"  {content}")
                table.add_row("")  # Add empty row for spacing

        # Add status information
        status_text = Text()
        status_text.append("\n")
        status_text.append(f"Total Traces: {len(self.tracer.traces)}", style="bold")
        status_text.append(" | ")
        status_text.append(f"Agent Messages: {len(message_traces)}", style="bold")

        if self.paused:
            status_text.append(" | ")
            status_text.append("PAUSED", style="bold red")

        if self.filter_agent:
            status_text.append(" | ")
            status_text.append(f"Filtering by: {self.filter_agent}", style="bold yellow")

        # Combine table and status text
        content = Table.grid()
        content.add_row(table)
        content.add_row(status_text)

        return content

    def handle_key(self, key: str):
        """Handle a key press.

        Args:
            key: Key that was pressed
        """
        if key == "p":
            # Toggle pause
            self.paused = not self.paused
            if not self.paused:
                # Update the display
                self.live.update(self._generate_layout())
                self.last_trace_count = len(self.tracer.traces)

        elif key == "f":
            # Prompt for agent filter
            self.live.stop()
            self.filter_agent = console.input("Enter agent to filter by (empty to clear): ")
            if not self.filter_agent:
                self.filter_agent = None
            self.live.start()
            self.live.update(self._generate_layout())

        elif key == "c":
            # Toggle content display
            self.show_content = not self.show_content
            self.live.update(self._generate_layout())

        elif key == "q":
            # Quit
            self.stop()
            return True

        return False

async def visualize_live(
    tracer: TracingCollector,
    update_interval: float = 0.1,
    max_messages: int = 50,
    auto_scroll: bool = True,
):
    """Visualize traces in real-time.

    Args:
        tracer: Tracing collector
        update_interval: Update interval in seconds
        max_messages: Maximum number of messages to display
        auto_scroll: Whether to auto-scroll to the latest message
    """
    # Create the live visualization
    live_viz = LiveVisualization(
        tracer=tracer,
        update_interval=update_interval,
        max_messages=max_messages,
        auto_scroll=auto_scroll
    )

    # Start the visualization
    live_viz.start()

    try:
        # Wait for keyboard input
        while True:
            # Check for keyboard input
            if console.input_text(timeout=0.1):
                key = console.input_text()
                if live_viz.handle_key(key):
                    break

            # Sleep to avoid high CPU usage
            await asyncio.sleep(0.1)
    except KeyboardInterrupt:
        pass
    finally:
        # Stop the visualization
        live_viz.stop()
```

#### Step 2: Create Real-Time Visualization Command

Add a real-time visualization command to `app/cli/trace_viewer.py`:

```python
# Add this import
import asyncio

# Add this command to the app
@app.command()
def live(
    tracer_path: Optional[str] = typer.Option(None, help="Path to tracer module"),
    update_interval: float = typer.Option(0.1, help="Update interval in seconds"),
    max_messages: int = typer.Option(50, help="Maximum number of messages to display"),
    auto_scroll: bool = typer.Option(True, help="Whether to auto-scroll to the latest message"),
):
    """Visualize traces in real-time."""
    # Import the tracer module
    if not tracer_path:
        print_error("No tracer path provided")
        raise typer.Exit(1)

    try:
        # Import the module
        import importlib.util
        spec = importlib.util.spec_from_file_location("tracer_module", tracer_path)
        tracer_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(tracer_module)

        # Get the tracer
        tracer = getattr(tracer_module, "tracer", None)
        if not tracer:
            print_error(f"No tracer found in module: {tracer_path}")
            raise typer.Exit(1)

        # Import the live visualization module
        from app.cli.live_visualization import visualize_live

        # Run the live visualization
        asyncio.run(visualize_live(
            tracer=tracer,
            update_interval=update_interval,
            max_messages=max_messages,
            auto_scroll=auto_scroll
        ))
    except Exception as e:
        print_error(f"Failed to load tracer: {e}")
        raise typer.Exit(1)
```

#### Step 3: Modify Makefile for Real-Time Testing

Update `makefiles/backend-cli-testing/Makefile` to include real-time visualization:

```makefile
# Add these commands to the Makefile

# Test agents with real-time visualization
test-agents-live:
	cd $(BACKEND_DIR) && \
	python -c "from app.core.tracing.collector import TracingCollector, TracingLevel; \
	           tracer = TracingCollector(session_id='live-session', granularity=TracingLevel.DETAILED); \
	           with open('tracer_module.py', 'w') as f: f.write('from app.core.tracing.collector import TracingCollector\\ntracer = TracingCollector(session_id=\"live-session\", granularity=TracingLevel.DETAILED)\\n')" && \
	python -m app.cli.trace_viewer live --tracer-path tracer_module.py & \
	sleep 1 && \
	python scripts/test_agent_communication.py && \
	rm tracer_module.py

# Test LangGraph with real-time visualization
test-langgraph-live:
	cd $(BACKEND_DIR) && \
	python -c "from app.core.tracing.collector import TracingCollector, TracingLevel; \
	           tracer = TracingCollector(session_id='live-session', granularity=TracingLevel.DETAILED); \
	           with open('tracer_module.py', 'w') as f: f.write('from app.core.tracing.collector import TracingCollector\\ntracer = TracingCollector(session_id=\"live-session\", granularity=TracingLevel.DETAILED)\\n')" && \
	python -m app.cli.trace_viewer live --tracer-path tracer_module.py & \
	sleep 1 && \
	python scripts/test_langgraph_tracing.py && \
	rm tracer_module.py
```

#### Step 4: Create Enhanced Test Script

Create a more comprehensive test script in `backend/scripts/test_live_visualization.py`:

```python
"""Test script for real-time visualization."""

import asyncio
import os
import random
import time
from datetime import datetime
from typing import Dict, List, Optional

from app.agents.base import BaseAgent
from app.agents.co_ceo import CoCEOAgent
from app.core.tracing.collector import TracingCollector, TracingLevel
from app.core.tracing.utils import save_traces
from app.cli.live_visualization import visualize_live

class SlowResponseAgent(BaseAgent):
    """Agent that responds with a delay."""

    async def _process_message(self, message: str, context: Optional[Dict] = None) -> str:
        """Process a message and generate a response with a delay."""
        # Simulate thinking
        await asyncio.sleep(random.uniform(1.0, 3.0))

        # Generate response
        return f"Response from {self.agent_id}: After careful consideration, I've analyzed your query: '{message}'"

async def simulate_conversation(tracer: TracingCollector):
    """Simulate a conversation between agents."""
    # Create agents
    finance_agent = SlowResponseAgent(agent_id="finance", tracer=tracer)
    marketing_agent = SlowResponseAgent(agent_id="marketing", tracer=tracer)

    # Create co-ceo agent with department agents
    co_ceo_agent = CoCEOAgent(
        tracer=tracer,
        department_agents={
            "finance": finance_agent,
            "marketing": marketing_agent,
        }
    )

    # List of queries to process
    queries = [
        "What is our marketing budget?",
        "How is our financial performance this quarter?",
        "What are our marketing strategies for next year?",
        "What is the ROI on our marketing campaigns?",
        "How much should we allocate to the marketing budget next quarter?",
    ]

    # Process each query
    for query in queries:
        print(f"\nProcessing query: {query}")

        # Send query from user to co-ceo
        await co_ceo_agent.communication.receive_message(
            sender="user",
            content=query,
            context={"query_id": f"query-{random.randint(1000, 9999)}"}
        )

        # Process the query
        response = await co_ceo_agent.process_message(
            message=query,
            sender="user",
            context={"query_id": f"query-{random.randint(1000, 9999)}"}
        )

        # Send response from co-ceo to user
        await co_ceo_agent.communication.send_message(
            recipient="user",
            content=response,
            context={"query_id": f"query-{random.randint(1000, 9999)}"}
        )

        # Wait a bit before the next query
        await asyncio.sleep(random.uniform(1.0, 2.0))

async def main():
    """Run the test script."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="live-test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create tasks
    visualization_task = asyncio.create_task(
        visualize_live(tracer=tracer, update_interval=0.1, max_messages=100)
    )

    conversation_task = asyncio.create_task(
        simulate_conversation(tracer=tracer)
    )

    # Wait for the conversation to complete
    await conversation_task

    # Wait a bit to view the final state
    await asyncio.sleep(5)

    # Cancel the visualization task
    visualization_task.cancel()

    # Save traces to a file
    os.makedirs("traces", exist_ok=True)
    file_path = save_traces(tracer.traces, directory="traces", prefix="live-visualization")

    print(f"\nSaved {len(tracer.traces)} trace events to {file_path}")

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 5: Test Real-Time Visualization

Run the test script:

```bash
cd backend
python scripts/test_live_visualization.py
```

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing
make test-agents-live
make test-langgraph-live
```

#### Step 6: Integrate with CLI Testing

Update `backend/scripts/test_backend_cli.py` to support real-time visualization:

```python
# Add these imports
from app.core.tracing.collector import TracingCollector, TracingLevel
import threading
import asyncio

# Add this parameter to the main function
def main(
    # ... existing parameters ...
    trace: bool = False,
    live_trace: bool = False,
):
    """Run the CLI test."""
    # ... existing code ...

    # Initialize tracing if requested
    tracer = None
    if trace or live_trace:
        tracer = TracingCollector(
            session_id=f"cli-test-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            user_id="cli-user",
            thread_id=f"cli-thread-{random.randint(1000, 9999)}",
            granularity=TracingLevel.DETAILED
        )

    # Start live visualization if requested
    if live_trace:
        from app.cli.live_visualization import visualize_live

        # Create a thread for the live visualization
        def run_visualization():
            asyncio.run(visualize_live(tracer=tracer))

        visualization_thread = threading.Thread(target=run_visualization)
        visualization_thread.daemon = True
        visualization_thread.start()

    # ... rest of the function ...

    # Pass tracer to the agent
    agent = create_agent(
        # ... existing parameters ...
        tracer=tracer
    )

    # ... rest of the function ...

    # Save traces if requested
    if trace or live_trace:
        os.makedirs("traces", exist_ok=True)
        file_path = save_traces(tracer.traces, directory="traces", prefix="cli-test")
        print(f"\nSaved {len(tracer.traces)} trace events to {file_path}")

# Update the CLI arguments
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test the backend CLI")
    # ... existing arguments ...
    parser.add_argument("--trace", action="store_true", help="Enable tracing")
    parser.add_argument("--live-trace", action="store_true", help="Enable live tracing visualization")
    args = parser.parse_args()

    main(
        # ... existing arguments ...
        trace=args.trace,
        live_trace=args.live_trace
    )
```

Update the Makefile to support real-time visualization in CLI testing:

```makefile
# Add these commands to the Makefile

# Test RAG with real-time visualization
test-rag-live:
	cd $(BACKEND_DIR) && python scripts/test_backend_cli.py --query "$(QUERY)" --department co-ceo --live-trace

# Test agents with real-time visualization
test-agents-cli-live:
	cd $(BACKEND_DIR) && python scripts/test_backend_cli.py --query "$(QUERY)" --department co-ceo --live-trace
```

Test the CLI with real-time visualization:

```bash
cd makefiles/backend-cli-testing
make test-rag-live QUERY="What is our marketing budget?"
make test-agents-cli-live QUERY="What is our marketing strategy and financial performance?"
```

## 5. Phase 3: Vector Store Integration

### 5.1 Query Logging Implementation

#### Step 1: Integrate Query Logging with RAG Pipeline

Update `app/rag/retriever.py` to include query logging:

```python
"""RAG retriever implementation."""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from app.core.tracing.collector import TracingCollector
from app.db.session import async_session
from app.models.document import Document
from app.rag.query_logging import log_query
from app.rag.vector_store import VectorStore

class RAGRetriever:
    """RAG retriever for retrieving relevant documents."""

    def __init__(
        self,
        vector_store: VectorStore,
        tracer: Optional[TracingCollector] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
    ):
        """Initialize the RAG retriever.

        Args:
            vector_store: Vector store for document retrieval
            tracer: Tracing collector
            top_k: Number of documents to retrieve
            similarity_threshold: Minimum similarity score for retrieval
        """
        self.vector_store = vector_store
        self.tracer = tracer
        self.top_k = top_k
        self.similarity_threshold = similarity_threshold

    async def retrieve(
        self,
        query: str,
        department: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata_filter: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query.

        Args:
            query: Query text
            department: Department to filter by
            user_id: User ID for query logging
            thread_id: Thread ID for query logging
            session_id: Session ID for query logging
            metadata_filter: Additional metadata filters

        Returns:
            List of relevant documents with similarity scores
        """
        # Measure execution time
        start_time = time.time()

        # Apply department filter if provided
        if department:
            if metadata_filter is None:
                metadata_filter = {}
            metadata_filter["department"] = department

        # Retrieve documents from vector store
        results = await self.vector_store.search(
            query=query,
            top_k=self.top_k,
            metadata_filter=metadata_filter,
        )

        # Filter by similarity threshold
        filtered_results = [
            result for result in results
            if result["score"] >= self.similarity_threshold
        ]

        # Calculate execution time
        execution_time_ms = int((time.time() - start_time) * 1000)

        # Log the query
        await log_query(
            query_text=query,
            user_id=user_id,
            thread_id=thread_id,
            session_id=session_id,
            department=department,
            results=filtered_results,
            execution_time_ms=execution_time_ms,
            metadata={
                "top_k": self.top_k,
                "similarity_threshold": self.similarity_threshold,
                "metadata_filter": metadata_filter,
                "total_results": len(results),
                "filtered_results": len(filtered_results),
            }
        )

        # Add trace if tracer is provided
        if self.tracer:
            self.tracer.add_trace(
                node_id="rag_retriever",
                event_type="rag_retrieval",
                metadata={
                    "query": query,
                    "department": department,
                    "document_count": len(filtered_results),
                    "execution_time_ms": execution_time_ms,
                    "document_ids": [doc["id"] for doc in filtered_results],
                    "similarity_scores": [doc["score"] for doc in filtered_results],
                }
            )

        return filtered_results
```

#### Step 2: Create Query Analysis Module

Create `app/rag/query_analysis.py` for analyzing query logs:

```python
"""Query analysis utilities."""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sqlalchemy import func, select

from app.db.session import async_session
from app.models.query_log import QueryLog
from app.rag.query_logging import get_query_logs

async def get_query_statistics(
    department: Optional[str] = None,
    user_id: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> Dict[str, Any]:
    """Get statistics for queries.

    Args:
        department: Filter by department
        user_id: Filter by user ID
        start_time: Filter by start time
        end_time: Filter by end time

    Returns:
        Dictionary of query statistics
    """
    # Get query logs
    logs = await get_query_logs(
        department=department,
        user_id=user_id,
        start_time=start_time,
        end_time=end_time,
        limit=1000,  # Use a large limit to get comprehensive statistics
    )

    if not logs:
        return {
            "query_count": 0,
            "avg_execution_time_ms": 0,
            "avg_result_count": 0,
            "departments": {},
        }

    # Calculate statistics
    execution_times = [log.get("execution_time_ms", 0) for log in logs if log.get("execution_time_ms") is not None]
    result_counts = [len(log.get("top_results", [])) for log in logs]
    departments = {}

    for log in logs:
        dept = log.get("department")
        if dept:
            if dept not in departments:
                departments[dept] = 0
            departments[dept] += 1

    return {
        "query_count": len(logs),
        "avg_execution_time_ms": np.mean(execution_times) if execution_times else 0,
        "min_execution_time_ms": min(execution_times) if execution_times else 0,
        "max_execution_time_ms": max(execution_times) if execution_times else 0,
        "avg_result_count": np.mean(result_counts) if result_counts else 0,
        "departments": departments,
    }

async def get_query_performance_over_time(
    interval: str = "day",
    department: Optional[str] = None,
    user_id: Optional[str] = None,
    days: int = 30,
) -> List[Dict[str, Any]]:
    """Get query performance over time.

    Args:
        interval: Time interval (hour, day, week, month)
        department: Filter by department
        user_id: Filter by user ID
        days: Number of days to look back

    Returns:
        List of performance metrics over time
    """
    # Calculate start time
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)

    # Get query logs
    logs = await get_query_logs(
        department=department,
        user_id=user_id,
        start_time=start_time,
        end_time=end_time,
        limit=10000,  # Use a large limit to get comprehensive statistics
    )

    if not logs:
        return []

    # Group by interval
    interval_format = "%Y-%m-%d"
    if interval == "hour":
        interval_format = "%Y-%m-%d %H:00:00"
    elif interval == "week":
        interval_format = "%Y-%W"
    elif interval == "month":
        interval_format = "%Y-%m"

    # Group logs by interval
    grouped_logs = {}
    for log in logs:
        timestamp = datetime.fromisoformat(log.get("timestamp"))
        interval_key = timestamp.strftime(interval_format)

        if interval_key not in grouped_logs:
            grouped_logs[interval_key] = []

        grouped_logs[interval_key].append(log)

    # Calculate statistics for each interval
    result = []
    for interval_key, interval_logs in grouped_logs.items():
        execution_times = [log.get("execution_time_ms", 0) for log in interval_logs if log.get("execution_time_ms") is not None]
        result_counts = [len(log.get("top_results", [])) for log in interval_logs]

        result.append({
            "interval": interval_key,
            "query_count": len(interval_logs),
            "avg_execution_time_ms": np.mean(execution_times) if execution_times else 0,
            "avg_result_count": np.mean(result_counts) if result_counts else 0,
        })

    # Sort by interval
    result.sort(key=lambda x: x["interval"])

    return result

async def get_similar_queries(
    query: str,
    threshold: float = 0.8,
    limit: int = 10,
) -> List[Dict[str, Any]]:
    """Get similar queries from the query logs.

    Args:
        query: Query to find similar queries for
        threshold: Similarity threshold
        limit: Maximum number of similar queries to return

    Returns:
        List of similar queries with similarity scores
    """
    # This would typically use the vector store to find similar queries
    # For now, we'll use a simple keyword-based approach

    # Get all query logs
    logs = await get_query_logs(limit=1000)

    # Calculate similarity (simple word overlap for now)
    query_words = set(query.lower().split())
    similar_queries = []

    for log in logs:
        log_query = log.get("query_text", "")
        log_query_words = set(log_query.lower().split())

        if not log_query_words:
            continue

        # Calculate Jaccard similarity
        intersection = len(query_words.intersection(log_query_words))
        union = len(query_words.union(log_query_words))
        similarity = intersection / union if union > 0 else 0

        if similarity >= threshold:
            similar_queries.append({
                "query": log_query,
                "similarity": similarity,
                "timestamp": log.get("timestamp"),
                "department": log.get("department"),
                "execution_time_ms": log.get("execution_time_ms"),
                "result_count": len(log.get("top_results", [])),
            })

    # Sort by similarity (descending)
    similar_queries.sort(key=lambda x: x["similarity"], reverse=True)

    # Limit results
    return similar_queries[:limit]

async def get_top_queries(
    department: Optional[str] = None,
    user_id: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 10,
) -> List[Dict[str, Any]]:
    """Get top queries by frequency.

    Args:
        department: Filter by department
        user_id: Filter by user ID
        start_time: Filter by start time
        end_time: Filter by end time
        limit: Maximum number of top queries to return

    Returns:
        List of top queries with frequency
    """
    async with async_session() as session:
        # Build query
        query = (
            select(
                QueryLog.query_text,
                func.count(QueryLog.id).label("frequency"),
                func.avg(QueryLog.execution_time_ms).label("avg_execution_time_ms"),
            )
            .group_by(QueryLog.query_text)
            .order_by(func.count(QueryLog.id).desc())
            .limit(limit)
        )

        # Apply filters
        if department:
            query = query.filter(QueryLog.department == department)

        if user_id:
            query = query.filter(QueryLog.user_id == user_id)

        if start_time:
            query = query.filter(QueryLog.timestamp >= start_time)

        if end_time:
            query = query.filter(QueryLog.timestamp <= end_time)

        # Execute query
        result = await session.execute(query)
        rows = result.all()

        # Format results
        return [
            {
                "query": row.query_text,
                "frequency": row.frequency,
                "avg_execution_time_ms": row.avg_execution_time_ms,
            }
            for row in rows
        ]
```

#### Step 3: Create Query Analysis CLI

Create `app/cli/query_analysis.py` for CLI query analysis:

```python
"""Command-line interface for query analysis."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table

from app.cli.formatting import print_error, print_info, print_success, print_warning
from app.rag.query_analysis import (
    get_query_performance_over_time,
    get_query_statistics,
    get_similar_queries,
    get_top_queries,
)

# Create Typer app
app = typer.Typer(help="Query analysis CLI")
console = Console()

@app.command()
def statistics(
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    user_id: Optional[str] = typer.Option(None, help="Filter by user ID"),
    days: int = typer.Option(30, help="Number of days to look back"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Get query statistics."""
    # Calculate start time
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)

    # Get statistics
    stats = asyncio.run(get_query_statistics(
        department=department,
        user_id=user_id,
        start_time=start_time,
        end_time=end_time,
    ))

    # Print statistics
    console.print("\n[bold]Query Statistics[/bold]\n")

    console.print(f"Query Count: {stats['query_count']}")
    console.print(f"Average Execution Time: {stats['avg_execution_time_ms']:.2f} ms")
    console.print(f"Min Execution Time: {stats['min_execution_time_ms']:.2f} ms")
    console.print(f"Max Execution Time: {stats['max_execution_time_ms']:.2f} ms")
    console.print(f"Average Result Count: {stats['avg_result_count']:.2f}")

    # Print department breakdown
    if stats["departments"]:
        console.print("\n[bold]Department Breakdown[/bold]\n")

        table = Table(show_header=True)
        table.add_column("Department")
        table.add_column("Query Count")
        table.add_column("Percentage")

        for dept, count in stats["departments"].items():
            percentage = (count / stats["query_count"]) * 100
            table.add_row(dept, str(count), f"{percentage:.2f}%")

        console.print(table)

    # Save to file if requested
    if output_file:
        with open(output_file, "w") as f:
            json.dump(stats, f, indent=2)

        print_success(f"Statistics saved to {output_file}")

@app.command()
def performance(
    interval: str = typer.Option("day", help="Time interval (hour, day, week, month)"),
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    user_id: Optional[str] = typer.Option(None, help="Filter by user ID"),
    days: int = typer.Option(30, help="Number of days to look back"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Get query performance over time."""
    # Get performance data
    performance_data = asyncio.run(get_query_performance_over_time(
        interval=interval,
        department=department,
        user_id=user_id,
        days=days,
    ))

    if not performance_data:
        print_warning("No query data found for the specified filters")
        return

    # Print performance data
    console.print(f"\n[bold]Query Performance Over Time ({interval})[/bold]\n")

    table = Table(show_header=True)
    table.add_column("Interval")
    table.add_column("Query Count")
    table.add_column("Avg Execution Time (ms)")
    table.add_column("Avg Result Count")

    for data in performance_data:
        table.add_row(
            data["interval"],
            str(data["query_count"]),
            f"{data['avg_execution_time_ms']:.2f}",
            f"{data['avg_result_count']:.2f}",
        )

    console.print(table)

    # Save to file if requested
    if output_file:
        with open(output_file, "w") as f:
            json.dump(performance_data, f, indent=2)

        print_success(f"Performance data saved to {output_file}")

@app.command()
def similar(
    query: str = typer.Argument(..., help="Query to find similar queries for"),
    threshold: float = typer.Option(0.5, help="Similarity threshold"),
    limit: int = typer.Option(10, help="Maximum number of similar queries to return"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Get similar queries."""
    # Get similar queries
    similar_queries = asyncio.run(get_similar_queries(
        query=query,
        threshold=threshold,
        limit=limit,
    ))

    if not similar_queries:
        print_warning(f"No similar queries found for: {query}")
        return

    # Print similar queries
    console.print(f"\n[bold]Similar Queries for: {query}[/bold]\n")

    table = Table(show_header=True)
    table.add_column("Query")
    table.add_column("Similarity")
    table.add_column("Department")
    table.add_column("Execution Time (ms)")
    table.add_column("Result Count")

    for data in similar_queries:
        table.add_row(
            data["query"],
            f"{data['similarity']:.2f}",
            data.get("department", ""),
            f"{data.get('execution_time_ms', 0):.2f}",
            str(data.get("result_count", 0)),
        )

    console.print(table)

    # Save to file if requested
    if output_file:
        with open(output_file, "w") as f:
            json.dump(similar_queries, f, indent=2)

        print_success(f"Similar queries saved to {output_file}")

@app.command()
def top(
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    user_id: Optional[str] = typer.Option(None, help="Filter by user ID"),
    days: int = typer.Option(30, help="Number of days to look back"),
    limit: int = typer.Option(10, help="Maximum number of top queries to return"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Get top queries by frequency."""
    # Calculate start time
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)

    # Get top queries
    top_queries = asyncio.run(get_top_queries(
        department=department,
        user_id=user_id,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
    ))

    if not top_queries:
        print_warning("No queries found for the specified filters")
        return

    # Print top queries
    console.print(f"\n[bold]Top {limit} Queries[/bold]\n")

    table = Table(show_header=True)
    table.add_column("Query")
    table.add_column("Frequency")
    table.add_column("Avg Execution Time (ms)")

    for data in top_queries:
        table.add_row(
            data["query"],
            str(data["frequency"]),
            f"{data.get('avg_execution_time_ms', 0):.2f}",
        )

    console.print(table)

    # Save to file if requested
    if output_file:
        with open(output_file, "w") as f:
            json.dump(top_queries, f, indent=2)

        print_success(f"Top queries saved to {output_file}")

if __name__ == "__main__":
    app()
```

#### Step 4: Create Test Script for Query Logging

Create `backend/scripts/test_query_logging.py`:

```python
"""Test script for query logging and analysis."""

import asyncio
import json
import os
import random
from datetime import datetime, timedelta

from app.core.tracing.collector import TracingCollector, TracingLevel
from app.rag.query_analysis import (
    get_query_performance_over_time,
    get_query_statistics,
    get_similar_queries,
    get_top_queries,
)
from app.rag.query_logging import delete_query_logs, get_query_logs, log_query
from app.rag.retriever import RAGRetriever
from app.rag.vector_store import VectorStore

async def generate_test_queries():
    """Generate test queries and log them."""
    # Create a tracer
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create a vector store (mock implementation for testing)
    class MockVectorStore(VectorStore):
        async def search(self, query, top_k=5, metadata_filter=None):
            # Generate random results
            results = []
            for i in range(random.randint(1, top_k)):
                results.append({
                    "id": f"doc-{random.randint(1000, 9999)}",
                    "content": f"Content for {query}",
                    "metadata": {
                        "department": metadata_filter.get("department") if metadata_filter else None,
                        "title": f"Document {i+1}",
                    },
                    "score": random.uniform(0.7, 0.99),
                })
            return results

    # Create a RAG retriever
    retriever = RAGRetriever(
        vector_store=MockVectorStore(),
        tracer=tracer,
        top_k=5,
        similarity_threshold=0.7,
    )

    # Sample queries for different departments
    finance_queries = [
        "What is our financial performance this quarter?",
        "What is our budget for next year?",
        "How much revenue did we generate last month?",
        "What are our current expenses?",
        "What is our profit margin?",
    ]

    marketing_queries = [
        "What is our marketing budget?",
        "What are our marketing strategies?",
        "How effective was our last campaign?",
        "What is our social media engagement?",
        "Who are our target customers?",
    ]

    # Generate timestamps over the last 30 days
    end_time = datetime.now()
    start_time = end_time - timedelta(days=30)
    timestamps = []

    for i in range(100):
        # Random timestamp between start_time and end_time
        timestamp = start_time + timedelta(
            seconds=random.randint(0, int((end_time - start_time).total_seconds()))
        )
        timestamps.append(timestamp)

    # Sort timestamps
    timestamps.sort()

    # Log queries
    print("Generating test queries...")

    for i, timestamp in enumerate(timestamps):
        # Select department and query
        if random.random() < 0.6:
            department = "finance"
            query = random.choice(finance_queries)
        else:
            department = "marketing"
            query = random.choice(marketing_queries)

        # Add some randomness to the query
        if random.random() < 0.3:
            query = query.replace("?", f" for {random.choice(['Q1', 'Q2', 'Q3', 'Q4'])}?")

        # Generate a session ID
        session_id = f"session-{random.randint(1000, 9999)}"

        # Retrieve documents
        await retriever.retrieve(
            query=query,
            department=department,
            user_id="test-user",
            thread_id=f"thread-{random.randint(1000, 9999)}",
            session_id=session_id,
        )

        # Print progress
        if (i + 1) % 10 == 0:
            print(f"Generated {i + 1}/{len(timestamps)} queries")

    print(f"Generated {len(timestamps)} test queries")

async def test_query_analysis():
    """Test query analysis functions."""
    # Get query statistics
    print("\nQuery Statistics:")
    stats = await get_query_statistics()
    print(json.dumps(stats, indent=2))

    # Get query performance over time
    print("\nQuery Performance Over Time (day):")
    performance = await get_query_performance_over_time(interval="day")
    print(json.dumps(performance, indent=2))

    # Get similar queries
    print("\nSimilar Queries for 'What is our marketing budget?':")
    similar = await get_similar_queries(query="What is our marketing budget?", threshold=0.5)
    print(json.dumps(similar, indent=2))

    # Get top queries
    print("\nTop Queries:")
    top = await get_top_queries(limit=5)
    print(json.dumps(top, indent=2))

async def main():
    """Run the test script."""
    # Check if we should generate test queries
    generate = input("Generate test queries? (y/n): ").lower() == "y"

    if generate:
        # Delete existing query logs
        deleted = await delete_query_logs()
        print(f"Deleted {deleted} existing query logs")

        # Generate test queries
        await generate_test_queries()

    # Test query analysis
    await test_query_analysis()

    # Save query logs to a file
    logs = await get_query_logs(limit=1000)

    os.makedirs("data", exist_ok=True)
    with open("data/query_logs.json", "w") as f:
        json.dump(logs, f, indent=2)

    print(f"\nSaved {len(logs)} query logs to data/query_logs.json")

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 5: Update Makefile for Query Analysis

Update `makefiles/backend-cli-testing/Makefile` to include query analysis commands:

```makefile
# Add these commands to the Makefile

# Generate test queries
generate-test-queries:
	cd $(BACKEND_DIR) && python scripts/test_query_logging.py

# Query statistics
query-stats:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis statistics

# Query performance over time
query-performance:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis performance --interval $(INTERVAL)

# Similar queries
query-similar:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis similar "$(QUERY)"

# Top queries
query-top:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis top --limit $(LIMIT)
```

#### Step 6: Test Query Logging and Analysis

Run the test script:

```bash
cd backend
python scripts/test_query_logging.py
```

Test the CLI commands:

```bash
cd backend
python -m app.cli.query_analysis statistics
python -m app.cli.query_analysis performance --interval day
python -m app.cli.query_analysis similar "What is our marketing budget?"
python -m app.cli.query_analysis top --limit 10
```

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing
make generate-test-queries
make query-stats
make query-performance INTERVAL=day
make query-similar QUERY="What is our marketing budget?"
make query-top LIMIT=10
```

### 5.2 Embedding Statistics and Introspection

#### Step 1: Create Embedding Statistics Module Structure

First, let's create the basic structure for the embedding statistics module:

```bash
# Create the embedding statistics module
touch app/rag/embedding_stats.py
```

Add the basic imports and module structure to `app/rag/embedding_stats.py`:

```python
"""Embedding statistics and introspection utilities."""

import json
import logging
import math
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sqlalchemy import func, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import async_session
from app.models.document import Document

logger = logging.getLogger(__name__)

# Constants
DEFAULT_SAMPLE_SIZE = 100
DEFAULT_DIMENSIONS = 1536  # Default for OpenAI embeddings
```

#### Step 2: Implement Basic Embedding Statistics Functions

Add basic embedding statistics functions to `app/rag/embedding_stats.py`:

```python
async def get_embedding_count(
    session: Optional[AsyncSession] = None,
    department: Optional[str] = None,
) -> int:
    """Get the count of embeddings in the database.

    Args:
        session: Database session
        department: Filter by department

    Returns:
        Count of embeddings
    """
    close_session = False
    if session is None:
        session = async_session()
        close_session = True

    try:
        # Build query
        query = select(func.count(Document.id))

        # Apply department filter
        if department:
            query = query.filter(Document.metadata["department"].astext == department)

        # Execute query
        result = await session.execute(query)
        count = result.scalar_one()

        return count
    finally:
        if close_session:
            await session.close()

async def get_embedding_dimensions(
    session: Optional[AsyncSession] = None,
) -> int:
    """Get the dimensions of embeddings in the database.

    Args:
        session: Database session

    Returns:
        Dimensions of embeddings
    """
    close_session = False
    if session is None:
        session = async_session()
        close_session = True

    try:
        # Get a sample embedding
        query = select(Document.embedding).limit(1)
        result = await session.execute(query)
        embedding = result.scalar_one_or_none()

        if embedding is None:
            return DEFAULT_DIMENSIONS

        # Get dimensions
        return len(embedding)
    except Exception as e:
        logger.warning(f"Failed to get embedding dimensions: {e}")
        return DEFAULT_DIMENSIONS
    finally:
        if close_session:
            await session.close()

async def get_embedding_stats_by_department(
    session: Optional[AsyncSession] = None,
) -> Dict[str, int]:
    """Get embedding statistics by department.

    Args:
        session: Database session

    Returns:
        Dictionary of department counts
    """
    close_session = False
    if session is None:
        session = async_session()
        close_session = True

    try:
        # Execute raw SQL query to count by department
        query = text("""
            SELECT
                metadata->>'department' as department,
                COUNT(*) as count
            FROM
                documents
            GROUP BY
                metadata->>'department'
            ORDER BY
                count DESC
        """)

        result = await session.execute(query)
        rows = result.all()

        # Convert to dictionary
        stats = {}
        for row in rows:
            department = row[0] or "unknown"
            count = row[1]
            stats[department] = count

        return stats
    finally:
        if close_session:
            await session.close()
```

#### Step 3: Implement Embedding Distribution Analysis

Add functions to analyze the distribution of embeddings to `app/rag/embedding_stats.py`:

```python
async def get_embedding_sample(
    session: Optional[AsyncSession] = None,
    sample_size: int = DEFAULT_SAMPLE_SIZE,
    department: Optional[str] = None,
) -> List[List[float]]:
    """Get a sample of embeddings from the database.

    Args:
        session: Database session
        sample_size: Number of embeddings to sample
        department: Filter by department

    Returns:
        List of embedding vectors
    """
    close_session = False
    if session is None:
        session = async_session()
        close_session = True

    try:
        # Build query
        query = select(Document.embedding)

        # Apply department filter
        if department:
            query = query.filter(Document.metadata["department"].astext == department)

        # Apply limit
        query = query.order_by(func.random()).limit(sample_size)

        # Execute query
        result = await session.execute(query)
        embeddings = result.scalars().all()

        return [list(embedding) for embedding in embeddings]
    finally:
        if close_session:
            await session.close()

async def calculate_embedding_statistics(
    embeddings: List[List[float]],
) -> Dict[str, Any]:
    """Calculate statistics for a set of embeddings.

    Args:
        embeddings: List of embedding vectors

    Returns:
        Dictionary of statistics
    """
    if not embeddings:
        return {
            "count": 0,
            "dimensions": DEFAULT_DIMENSIONS,
            "mean": None,
            "std": None,
            "min": None,
            "max": None,
            "l2_norm_mean": None,
            "l2_norm_std": None,
        }

    # Convert to numpy array
    embeddings_array = np.array(embeddings)

    # Calculate statistics
    mean = np.mean(embeddings_array, axis=0)
    std = np.std(embeddings_array, axis=0)
    min_values = np.min(embeddings_array, axis=0)
    max_values = np.max(embeddings_array, axis=0)

    # Calculate L2 norms
    l2_norms = np.linalg.norm(embeddings_array, axis=1)
    l2_norm_mean = np.mean(l2_norms)
    l2_norm_std = np.std(l2_norms)

    return {
        "count": len(embeddings),
        "dimensions": embeddings_array.shape[1],
        "mean": mean.tolist(),
        "std": std.tolist(),
        "min": min_values.tolist(),
        "max": max_values.tolist(),
        "l2_norm_mean": float(l2_norm_mean),
        "l2_norm_std": float(l2_norm_std),
    }

async def get_embedding_distribution(
    session: Optional[AsyncSession] = None,
    sample_size: int = DEFAULT_SAMPLE_SIZE,
    department: Optional[str] = None,
) -> Dict[str, Any]:
    """Get the distribution of embeddings in the database.

    Args:
        session: Database session
        sample_size: Number of embeddings to sample
        department: Filter by department

    Returns:
        Dictionary of embedding distribution statistics
    """
    # Get a sample of embeddings
    embeddings = await get_embedding_sample(
        session=session,
        sample_size=sample_size,
        department=department,
    )

    # Calculate statistics
    stats = await calculate_embedding_statistics(embeddings)

    return stats
```

#### Step 4: Implement Embedding Similarity Analysis

Add functions to analyze embedding similarity to `app/rag/embedding_stats.py`:

```python
def cosine_similarity(v1: List[float], v2: List[float]) -> float:
    """Calculate cosine similarity between two vectors.

    Args:
        v1: First vector
        v2: Second vector

    Returns:
        Cosine similarity
    """
    dot_product = sum(a * b for a, b in zip(v1, v2))
    norm_v1 = math.sqrt(sum(a * a for a in v1))
    norm_v2 = math.sqrt(sum(b * b for b in v2))

    if norm_v1 == 0 or norm_v2 == 0:
        return 0

    return dot_product / (norm_v1 * norm_v2)

async def calculate_similarity_matrix(
    embeddings: List[List[float]],
    max_pairs: int = 1000,
) -> Dict[str, Any]:
    """Calculate similarity matrix for a set of embeddings.

    Args:
        embeddings: List of embedding vectors
        max_pairs: Maximum number of pairs to calculate

    Returns:
        Dictionary with similarity statistics
    """
    if not embeddings or len(embeddings) < 2:
        return {
            "count": 0,
            "mean_similarity": 0,
            "min_similarity": 0,
            "max_similarity": 0,
            "std_similarity": 0,
            "histogram": [],
        }

    # Calculate similarities for a sample of pairs
    n = len(embeddings)
    pairs = min(n * (n - 1) // 2, max_pairs)

    similarities = []
    indices = []

    # If we need to sample, generate random pairs
    if pairs < n * (n - 1) // 2:
        import random
        for _ in range(pairs):
            i, j = random.sample(range(n), 2)
            if (i, j) not in indices and (j, i) not in indices:
                indices.append((i, j))
    else:
        # Otherwise, use all pairs
        for i in range(n):
            for j in range(i + 1, n):
                indices.append((i, j))

    # Calculate similarities
    for i, j in indices:
        sim = cosine_similarity(embeddings[i], embeddings[j])
        similarities.append(sim)

    # Calculate statistics
    mean_similarity = np.mean(similarities)
    min_similarity = np.min(similarities)
    max_similarity = np.max(similarities)
    std_similarity = np.std(similarities)

    # Create histogram
    hist, bin_edges = np.histogram(similarities, bins=10, range=(0, 1))
    histogram = [
        {
            "bin": f"{bin_edges[i]:.1f}-{bin_edges[i+1]:.1f}",
            "count": int(hist[i]),
        }
        for i in range(len(hist))
    ]

    return {
        "count": len(similarities),
        "mean_similarity": float(mean_similarity),
        "min_similarity": float(min_similarity),
        "max_similarity": float(max_similarity),
        "std_similarity": float(std_similarity),
        "histogram": histogram,
    }

async def get_embedding_similarity_stats(
    session: Optional[AsyncSession] = None,
    sample_size: int = DEFAULT_SAMPLE_SIZE,
    department: Optional[str] = None,
) -> Dict[str, Any]:
    """Get similarity statistics for embeddings in the database.

    Args:
        session: Database session
        sample_size: Number of embeddings to sample
        department: Filter by department

    Returns:
        Dictionary of similarity statistics
    """
    # Get a sample of embeddings
    embeddings = await get_embedding_sample(
        session=session,
        sample_size=sample_size,
        department=department,
    )

    # Calculate similarity matrix
    similarity_stats = await calculate_similarity_matrix(embeddings)

    return similarity_stats
```

#### Step 5: Create Main Embedding Statistics Function

Add the main function to get comprehensive embedding statistics to `app/rag/embedding_stats.py`:

```python
async def get_embedding_stats(
    session: Optional[AsyncSession] = None,
    sample_size: int = DEFAULT_SAMPLE_SIZE,
    department: Optional[str] = None,
    include_similarity: bool = True,
) -> Dict[str, Any]:
    """Get comprehensive statistics for embeddings in the database.

    Args:
        session: Database session
        sample_size: Number of embeddings to sample
        department: Filter by department
        include_similarity: Whether to include similarity statistics

    Returns:
        Dictionary of embedding statistics
    """
    close_session = False
    if session is None:
        session = async_session()
        close_session = True

    try:
        # Get basic statistics
        count = await get_embedding_count(session=session, department=department)
        dimensions = await get_embedding_dimensions(session=session)
        department_stats = await get_embedding_stats_by_department(session=session)

        # Get distribution statistics
        distribution = await get_embedding_distribution(
            session=session,
            sample_size=sample_size,
            department=department,
        )

        # Get similarity statistics if requested
        similarity = None
        if include_similarity and count >= 2:
            similarity = await get_embedding_similarity_stats(
                session=session,
                sample_size=min(sample_size, count),
                department=department,
            )

        # Combine statistics
        stats = {
            "timestamp": datetime.now().isoformat(),
            "count": count,
            "dimensions": dimensions,
            "departments": department_stats,
            "distribution": distribution,
        }

        if similarity:
            stats["similarity"] = similarity

        return stats
    finally:
        if close_session:
            await session.close()
```

#### Step 6: Create Simple Test Script

Create a simple test script to verify the embedding statistics functions in `backend/scripts/test_embedding_stats.py`:

```python
"""Test script for embedding statistics."""

import asyncio
import json
import os
from typing import List

import numpy as np

from app.db.session import async_session
from app.models.document import Document
from app.rag.embedding_stats import get_embedding_stats

async def generate_test_embeddings(count: int = 100) -> List[Document]:
    """Generate test embeddings for testing.

    Args:
        count: Number of embeddings to generate

    Returns:
        List of generated documents
    """
    # Create random embeddings
    documents = []
    departments = ["finance", "marketing", "hr", "sales"]

    for i in range(count):
        # Generate random embedding
        embedding = np.random.normal(0, 1, 1536).astype(float)
        embedding = embedding / np.linalg.norm(embedding)  # Normalize

        # Create document
        document = Document(
            id=f"test-doc-{i}",
            content=f"Test document {i}",
            embedding=embedding.tolist(),
            metadata={
                "department": departments[i % len(departments)],
                "title": f"Test Document {i}",
                "source": "test",
            }
        )

        documents.append(document)

    return documents

async def save_test_embeddings(documents: List[Document]) -> None:
    """Save test embeddings to the database.

    Args:
        documents: List of documents to save
    """
    async with async_session() as session:
        # Add documents to session
        for document in documents:
            session.add(document)

        # Commit changes
        await session.commit()

        print(f"Saved {len(documents)} test embeddings to the database")

async def main():
    """Run the test script."""
    # Check if we should generate test embeddings
    generate = input("Generate test embeddings? (y/n): ").lower() == "y"

    if generate:
        # Generate and save test embeddings
        documents = await generate_test_embeddings(count=100)
        await save_test_embeddings(documents)

    # Get embedding statistics
    print("\nGetting embedding statistics...")
    stats = await get_embedding_stats(sample_size=50)

    # Print statistics
    print(f"\nFound {stats['count']} embeddings with {stats['dimensions']} dimensions")
    print(f"Departments: {stats['departments']}")

    if stats.get("similarity"):
        print(f"\nMean similarity: {stats['similarity']['mean_similarity']:.4f}")
        print(f"Min similarity: {stats['similarity']['min_similarity']:.4f}")
        print(f"Max similarity: {stats['similarity']['max_similarity']:.4f}")

    # Save statistics to file
    os.makedirs("data", exist_ok=True)
    with open("data/embedding_stats.json", "w") as f:
        json.dump(stats, f, indent=2)

    print(f"\nSaved embedding statistics to data/embedding_stats.json")

if __name__ == "__main__":
    asyncio.run(main())
```

### 5.3 Vector Store CLI Tools

#### Step 1: Create Vector Explorer Module Structure

First, let's create the basic structure for the vector explorer module:

```bash
# Create the vector explorer module
touch app/cli/vector_explorer.py
```

Add the basic imports and module structure to `app/cli/vector_explorer.py`:

```python
"""Command-line interface for vector store exploration."""

import asyncio
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text

from app.cli.formatting import print_error, print_info, print_success, print_warning
from app.db.session import async_session
from app.models.document import Document
from app.rag.embedding_stats import get_embedding_stats
from app.rag.vector_store import VectorStore

# Create Typer app
app = typer.Typer(help="Vector store explorer CLI")
console = Console()
```

#### Step 2: Implement Basic Vector Store Exploration Commands

Add basic vector store exploration commands to `app/cli/vector_explorer.py`:

```python
@app.command()
def stats(
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    sample_size: int = typer.Option(50, help="Number of embeddings to sample"),
    include_similarity: bool = typer.Option(True, help="Include similarity statistics"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Get statistics for embeddings in the vector store."""
    # Get embedding statistics
    stats = asyncio.run(get_embedding_stats(
        sample_size=sample_size,
        department=department,
        include_similarity=include_similarity,
    ))

    # Print basic statistics
    console.print("\n[bold]Embedding Statistics[/bold]\n")

    console.print(f"Count: {stats['count']}")
    console.print(f"Dimensions: {stats['dimensions']}")

    # Print department breakdown
    if stats["departments"]:
        console.print("\n[bold]Department Breakdown[/bold]\n")

        table = Table(show_header=True)
        table.add_column("Department")
        table.add_column("Count")
        table.add_column("Percentage")

        total = stats["count"]
        for dept, count in stats["departments"].items():
            percentage = (count / total) * 100 if total > 0 else 0
            table.add_row(dept, str(count), f"{percentage:.2f}%")

        console.print(table)

    # Print similarity statistics
    if stats.get("similarity"):
        console.print("\n[bold]Similarity Statistics[/bold]\n")

        sim_stats = stats["similarity"]
        console.print(f"Mean Similarity: {sim_stats['mean_similarity']:.4f}")
        console.print(f"Min Similarity: {sim_stats['min_similarity']:.4f}")
        console.print(f"Max Similarity: {sim_stats['max_similarity']:.4f}")
        console.print(f"Std Deviation: {sim_stats['std_similarity']:.4f}")

        # Print histogram
        if sim_stats.get("histogram"):
            console.print("\n[bold]Similarity Histogram[/bold]\n")

            table = Table(show_header=True)
            table.add_column("Range")
            table.add_column("Count")

            for bin_data in sim_stats["histogram"]:
                table.add_row(bin_data["bin"], str(bin_data["count"]))

            console.print(table)

    # Save to file if requested
    if output_file:
        os.makedirs(os.path.dirname(output_file) or ".", exist_ok=True)
        with open(output_file, "w") as f:
            json.dump(stats, f, indent=2)

        print_success(f"Statistics saved to {output_file}")

@app.command()
def list_documents(
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    limit: int = typer.Option(10, help="Maximum number of documents to list"),
    offset: int = typer.Option(0, help="Offset for pagination"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """List documents in the vector store."""
    async def _list_documents():
        async with async_session() as session:
            # Build query
            query = select(
                Document.id,
                Document.content,
                Document.metadata,
            )

            # Apply department filter
            if department:
                query = query.filter(Document.metadata["department"].astext == department)

            # Apply pagination
            query = query.offset(offset).limit(limit)

            # Execute query
            result = await session.execute(query)
            rows = result.all()

            # Format results
            documents = []
            for row in rows:
                doc = {
                    "id": row[0],
                    "content": row[1],
                    "metadata": row[2],
                }
                documents.append(doc)

            return documents

    # Get documents
    documents = asyncio.run(_list_documents())

    if not documents:
        print_warning("No documents found")
        return

    # Print documents
    console.print(f"\n[bold]Documents ({len(documents)})[/bold]\n")

    for i, doc in enumerate(documents):
        # Create panel for each document
        metadata_str = "\n".join([f"{k}: {v}" for k, v in doc["metadata"].items()])
        content = f"ID: {doc['id']}\n\nMetadata:\n{metadata_str}\n\nContent:\n{doc['content'][:200]}..."

        panel = Panel(
            content,
            title=f"Document {i+1}",
            border_style="blue"
        )

        console.print(panel)
        console.print()

    # Save to file if requested
    if output_file:
        os.makedirs(os.path.dirname(output_file) or ".", exist_ok=True)
        with open(output_file, "w") as f:
            json.dump(documents, f, indent=2)

        print_success(f"Documents saved to {output_file}")
```

#### Step 3: Implement Query Visualization Functions

Add functions to visualize query results to `app/cli/vector_explorer.py`:

```python
async def search_vector_store(
    query: str,
    department: Optional[str] = None,
    top_k: int = 5,
) -> List[Dict[str, Any]]:
    """Search the vector store for documents similar to the query.

    Args:
        query: Query text
        department: Filter by department
        top_k: Number of results to return

    Returns:
        List of search results
    """
    # Import here to avoid circular imports
    from app.rag.vector_store import get_vector_store

    # Get vector store
    vector_store = await get_vector_store()

    # Create metadata filter
    metadata_filter = None
    if department:
        metadata_filter = {"department": department}

    # Search vector store
    results = await vector_store.search(
        query=query,
        top_k=top_k,
        metadata_filter=metadata_filter,
    )

    return results

@app.command()
def search(
    query: str = typer.Argument(..., help="Query text"),
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    top_k: int = typer.Option(5, help="Number of results to return"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Search the vector store for documents similar to the query."""
    # Search vector store
    results = asyncio.run(search_vector_store(
        query=query,
        department=department,
        top_k=top_k,
    ))

    if not results:
        print_warning(f"No results found for query: {query}")
        return

    # Print results
    console.print(f"\n[bold]Search Results for: {query}[/bold]\n")

    table = Table(show_header=True)
    table.add_column("Rank")
    table.add_column("Document ID")
    table.add_column("Score")
    table.add_column("Department")
    table.add_column("Content")

    for i, result in enumerate(results):
        # Get department from metadata
        dept = result.get("metadata", {}).get("department", "")

        # Truncate content
        content = result.get("content", "")
        if len(content) > 50:
            content = content[:47] + "..."

        table.add_row(
            str(i + 1),
            result.get("id", ""),
            f"{result.get('score', 0):.4f}",
            dept,
            content,
        )

    console.print(table)

    # Print detailed results
    console.print("\n[bold]Detailed Results[/bold]\n")

    for i, result in enumerate(results):
        # Create panel for each result
        metadata_str = "\n".join([f"{k}: {v}" for k, v in result.get("metadata", {}).items()])
        content = f"ID: {result.get('id', '')}\nScore: {result.get('score', 0):.4f}\n\nMetadata:\n{metadata_str}\n\nContent:\n{result.get('content', '')[:200]}..."

        panel = Panel(
            content,
            title=f"Result {i+1}",
            border_style="blue"
        )

        console.print(panel)
        console.print()

    # Save to file if requested
    if output_file:
        os.makedirs(os.path.dirname(output_file) or ".", exist_ok=True)
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)

        print_success(f"Results saved to {output_file}")

@app.command()
def compare(
    query1: str = typer.Option(..., help="First query text"),
    query2: str = typer.Option(..., help="Second query text"),
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    top_k: int = typer.Option(5, help="Number of results to return"),
    output_file: Optional[str] = typer.Option(None, help="Output file for JSON results"),
):
    """Compare search results for two queries."""
    # Search vector store for both queries
    results1 = asyncio.run(search_vector_store(
        query=query1,
        department=department,
        top_k=top_k,
    ))

    results2 = asyncio.run(search_vector_store(
        query=query2,
        department=department,
        top_k=top_k,
    ))

    if not results1 and not results2:
        print_warning(f"No results found for either query")
        return

    # Print comparison
    console.print(f"\n[bold]Comparison of Search Results[/bold]\n")
    console.print(f"Query 1: {query1}")
    console.print(f"Query 2: {query2}")

    # Find common document IDs
    ids1 = {r.get("id") for r in results1}
    ids2 = {r.get("id") for r in results2}
    common_ids = ids1.intersection(ids2)

    console.print(f"\nQuery 1 Results: {len(results1)}")
    console.print(f"Query 2 Results: {len(results2)}")
    console.print(f"Common Results: {len(common_ids)}")

    # Create comparison table
    table = Table(show_header=True)
    table.add_column("Document ID")
    table.add_column("Query 1 Rank")
    table.add_column("Query 1 Score")
    table.add_column("Query 2 Rank")
    table.add_column("Query 2 Score")
    table.add_column("Department")

    # Map document IDs to ranks and scores
    id_to_rank1 = {r.get("id"): i+1 for i, r in enumerate(results1)}
    id_to_score1 = {r.get("id"): r.get("score", 0) for r in results1}
    id_to_rank2 = {r.get("id"): i+1 for i, r in enumerate(results2)}
    id_to_score2 = {r.get("id"): r.get("score", 0) for r in results2}
    id_to_dept = {}

    for r in results1 + results2:
        doc_id = r.get("id")
        dept = r.get("metadata", {}).get("department", "")
        id_to_dept[doc_id] = dept

    # Add all document IDs from both result sets
    all_ids = ids1.union(ids2)
    for doc_id in sorted(all_ids, key=lambda x: (id_to_rank1.get(x, float('inf')), id_to_rank2.get(x, float('inf')))):
        rank1 = id_to_rank1.get(doc_id, "-")
        score1 = f"{id_to_score1.get(doc_id, 0):.4f}" if doc_id in ids1 else "-"
        rank2 = id_to_rank2.get(doc_id, "-")
        score2 = f"{id_to_score2.get(doc_id, 0):.4f}" if doc_id in ids2 else "-"
        dept = id_to_dept.get(doc_id, "")

        table.add_row(
            doc_id,
            str(rank1),
            score1,
            str(rank2),
            score2,
            dept,
        )

    console.print(table)

    # Save to file if requested
    if output_file:
        os.makedirs(os.path.dirname(output_file) or ".", exist_ok=True)
        comparison = {
            "query1": query1,
            "query2": query2,
            "results1": results1,
            "results2": results2,
            "common_ids": list(common_ids),
        }
        with open(output_file, "w") as f:
            json.dump(comparison, f, indent=2)

        print_success(f"Comparison saved to {output_file}")
```

#### Step 4: Implement Text-Based Vector Visualization

Add functions for text-based vector visualization to `app/cli/vector_explorer.py`:

```python
def visualize_vector_2d(
    vectors: List[List[float]],
    labels: Optional[List[str]] = None,
    width: int = 60,
    height: int = 20,
) -> str:
    """Create a text-based 2D visualization of vectors.

    Args:
        vectors: List of vectors to visualize
        labels: Optional labels for vectors
        width: Width of the visualization
        height: Height of the visualization

    Returns:
        Text-based visualization
    """
    if not vectors:
        return "No vectors to visualize"

    # Use PCA to reduce to 2 dimensions
    from sklearn.decomposition import PCA

    # Convert to numpy array
    vectors_array = np.array(vectors)

    # Apply PCA
    pca = PCA(n_components=2)
    vectors_2d = pca.fit_transform(vectors_array)

    # Normalize to [0, 1] range
    min_x = np.min(vectors_2d[:, 0])
    max_x = np.max(vectors_2d[:, 0])
    min_y = np.min(vectors_2d[:, 1])
    max_y = np.max(vectors_2d[:, 1])

    x_range = max_x - min_x
    y_range = max_y - min_y

    if x_range == 0:
        x_range = 1
    if y_range == 0:
        y_range = 1

    vectors_2d[:, 0] = (vectors_2d[:, 0] - min_x) / x_range
    vectors_2d[:, 1] = (vectors_2d[:, 1] - min_y) / y_range

    # Create grid
    grid = [[" " for _ in range(width)] for _ in range(height)]

    # Plot vectors
    for i, (x, y) in enumerate(vectors_2d):
        # Convert to grid coordinates
        grid_x = int(x * (width - 1))
        grid_y = int((1 - y) * (height - 1))  # Invert y-axis

        # Ensure within bounds
        grid_x = max(0, min(grid_x, width - 1))
        grid_y = max(0, min(grid_y, height - 1))

        # Add point
        label = str(i + 1) if labels is None else labels[i]
        grid[grid_y][grid_x] = label[0]  # Use first character of label

    # Convert grid to string
    lines = []
    for row in grid:
        lines.append("".join(row))

    # Add border
    border = "+" + "-" * width + "+"
    lines = [border] + ["|" + line + "|" for line in lines] + [border]

    return "\n".join(lines)

@app.command()
def visualize(
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    sample_size: int = typer.Option(20, help="Number of embeddings to sample"),
    width: int = typer.Option(60, help="Width of the visualization"),
    height: int = typer.Option(20, help="Height of the visualization"),
):
    """Visualize embeddings in the vector store."""
    async def _get_embeddings():
        from app.rag.embedding_stats import get_embedding_sample

        # Get a sample of embeddings
        embeddings = await get_embedding_sample(
            sample_size=sample_size,
            department=department,
        )

        return embeddings

    # Get embeddings
    embeddings = asyncio.run(_get_embeddings())

    if not embeddings:
        print_warning("No embeddings found")
        return

    # Create visualization
    visualization = visualize_vector_2d(
        vectors=embeddings,
        width=width,
        height=height,
    )

    # Print visualization
    console.print(f"\n[bold]Vector Visualization (PCA 2D Projection)[/bold]\n")
    console.print(f"Sample Size: {len(embeddings)}")
    if department:
        console.print(f"Department: {department}")
    console.print()

    console.print(visualization)
    console.print("\nPoints are labeled with numbers 1-N")
```

#### Step 5: Create Main Entry Point

Add the main entry point to `app/cli/vector_explorer.py`:

```python
if __name__ == "__main__":
    app()
```

#### Step 6: Create Test Script for Vector Explorer

Create a test script in `backend/scripts/test_vector_explorer.py`:

```python
"""Test script for vector explorer."""

import asyncio
import os
import subprocess
import sys
from typing import List

import numpy as np

from app.db.session import async_session
from app.models.document import Document
from app.rag.embedding_stats import get_embedding_stats

async def ensure_test_embeddings(count: int = 100) -> None:
    """Ensure test embeddings exist in the database.

    Args:
        count: Minimum number of embeddings to ensure
    """
    # Check if we have enough embeddings
    async with async_session() as session:
        from sqlalchemy import func, select

        query = select(func.count(Document.id))
        result = await session.execute(query)
        existing_count = result.scalar_one()

        if existing_count >= count:
            print(f"Found {existing_count} existing embeddings")
            return

    # Generate test embeddings
    print(f"Generating {count - existing_count} test embeddings...")

    # Run the test_embedding_stats.py script
    subprocess.run([
        sys.executable,
        "scripts/test_embedding_stats.py"
    ], check=True)

def test_vector_explorer_commands():
    """Test vector explorer commands."""
    # Test stats command
    print("\nTesting stats command...")
    subprocess.run([
        sys.executable,
        "-m",
        "app.cli.vector_explorer",
        "stats",
        "--sample-size",
        "20"
    ], check=True)

    # Test list_documents command
    print("\nTesting list_documents command...")
    subprocess.run([
        sys.executable,
        "-m",
        "app.cli.vector_explorer",
        "list-documents",
        "--limit",
        "3"
    ], check=True)

    # Test search command
    print("\nTesting search command...")
    subprocess.run([
        sys.executable,
        "-m",
        "app.cli.vector_explorer",
        "search",
        "financial performance",
        "--top-k",
        "3"
    ], check=True)

    # Test compare command
    print("\nTesting compare command...")
    subprocess.run([
        sys.executable,
        "-m",
        "app.cli.vector_explorer",
        "compare",
        "--query1",
        "financial performance",
        "--query2",
        "marketing budget",
        "--top-k",
        "3"
    ], check=True)

    # Test visualize command
    print("\nTesting visualize command...")
    subprocess.run([
        sys.executable,
        "-m",
        "app.cli.vector_explorer",
        "visualize",
        "--sample-size",
        "10",
        "--width",
        "40",
        "--height",
        "15"
    ], check=True)

async def main():
    """Run the test script."""
    # Ensure test embeddings exist
    await ensure_test_embeddings(count=100)

    # Test vector explorer commands
    test_vector_explorer_commands()

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 7: Update Makefile for Vector Explorer

Update `makefiles/backend-cli-testing/Makefile` to include vector explorer commands:

```makefile
# Add these commands to the Makefile

# Vector store statistics
vector-stats:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer stats

# List documents in vector store
vector-list:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer list-documents --limit $(LIMIT)

# Search vector store
vector-search:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer search "$(QUERY)" --top-k $(TOP_K)

# Compare queries
vector-compare:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer compare --query1 "$(QUERY1)" --query2 "$(QUERY2)" --top-k $(TOP_K)

# Visualize embeddings
vector-visualize:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer visualize --sample-size $(SAMPLE_SIZE)
```

#### Step 8: Test Vector Explorer

Run the test script:

```bash
cd backend
python scripts/test_vector_explorer.py
```

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing
make vector-stats
make vector-list LIMIT=5
make vector-search QUERY="financial performance" TOP_K=3
make vector-compare QUERY1="financial performance" QUERY2="marketing budget" TOP_K=3
make vector-visualize SAMPLE_SIZE=10
```

## 6. Phase 4: Jupyter Notebook Integration

### 6.1 Trace Analysis Notebook

#### Step 1: Create Notebook Directory Structure

First, let's create the basic directory structure for Jupyter notebooks:

```bash
# Create notebook directories
mkdir -p app/notebooks
mkdir -p notebooks

# Create __init__.py files
touch app/notebooks/__init__.py
```

Add a basic description to `app/notebooks/__init__.py`:

```python
"""
Jupyter notebook integration modules.

This package contains modules for integrating with Jupyter notebooks,
providing functionality for trace analysis, vector visualization, and
interactive query exploration.
"""
```

#### Step 2: Create Notebook Utilities Module

Create a utilities module for common notebook functions in `app/notebooks/utils.py`:

```python
"""Utilities for Jupyter notebooks."""

import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from IPython.display import HTML, display

def setup_notebook_styling():
    """Set up styling for notebooks."""
    # Import here to avoid dependency issues outside notebooks
    from IPython.core.display import HTML

    # Set up pandas display options
    pd.set_option('display.max_rows', 100)
    pd.set_option('display.max_columns', 50)
    pd.set_option('display.width', 1000)

    # Custom CSS for better notebook styling
    custom_css = """
    <style>
        .dataframe {
            font-family: Arial, sans-serif;
            font-size: 12px;
            border-collapse: collapse;
            border: none;
            width: 100%;
        }
        .dataframe th {
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
            padding: 8px;
            text-align: left;
            border-bottom: 2px solid #ddd;
        }
        .dataframe td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        .dataframe tr:hover {
            background-color: #f5f5f5;
        }
        .output_area pre {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .jp-OutputArea-output {
            max-height: 500px;
            overflow-y: auto;
        }
        .jp-RenderedText {
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
    """

    return HTML(custom_css)

def load_json_file(file_path: str) -> Any:
    """Load JSON data from a file.

    Args:
        file_path: Path to the JSON file

    Returns:
        Loaded JSON data
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    with open(file_path, "r") as f:
        return json.load(f)

def save_json_file(data: Any, file_path: str, indent: int = 2) -> None:
    """Save data to a JSON file.

    Args:
        data: Data to save
        file_path: Path to save the JSON file
        indent: Indentation level for JSON formatting
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(file_path) or ".", exist_ok=True)

    with open(file_path, "w") as f:
        json.dump(data, f, indent=indent)

    print(f"Saved data to {file_path}")

def display_dict(data: Dict[str, Any], max_depth: int = 2, current_depth: int = 0) -> None:
    """Display a dictionary in a readable format.

    Args:
        data: Dictionary to display
        max_depth: Maximum depth to display
        current_depth: Current depth (for recursion)
    """
    if current_depth >= max_depth:
        print("...")
        return

    for key, value in data.items():
        indent = "  " * current_depth
        if isinstance(value, dict):
            print(f"{indent}{key}:")
            display_dict(value, max_depth, current_depth + 1)
        elif isinstance(value, list):
            if len(value) > 0 and isinstance(value[0], dict):
                print(f"{indent}{key}: [{len(value)} items]")
                if len(value) > 0 and current_depth < max_depth - 1:
                    display_dict(value[0], max_depth, current_depth + 1)
            else:
                if len(value) > 5:
                    print(f"{indent}{key}: [{len(value)} items]")
                else:
                    print(f"{indent}{key}: {value}")
        else:
            print(f"{indent}{key}: {value}")

def create_timestamp_formatter():
    """Create a function to format ISO timestamps.

    Returns:
        Function that formats ISO timestamps
    """
    def format_timestamp(timestamp: str) -> str:
        """Format ISO timestamp to a more readable format.

        Args:
            timestamp: ISO format timestamp

        Returns:
            Formatted timestamp string
        """
        try:
            dt = datetime.fromisoformat(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        except (ValueError, TypeError):
            return timestamp

    return format_timestamp

def plot_timeline(
    events: List[Dict[str, Any]],
    x_key: str = "timestamp",
    y_key: str = "node_id",
    color_key: Optional[str] = "event_type",
    title: str = "Event Timeline",
    figsize: tuple = (12, 6),
):
    """Plot a timeline of events.

    Args:
        events: List of event dictionaries
        x_key: Key for x-axis values (usually timestamp)
        y_key: Key for y-axis values (usually node_id)
        color_key: Key for color coding (usually event_type)
        title: Plot title
        figsize: Figure size
    """
    # Convert to DataFrame
    df = pd.DataFrame(events)

    # Convert timestamps to datetime
    if x_key == "timestamp":
        df[x_key] = pd.to_datetime(df[x_key])

    # Create figure
    plt.figure(figsize=figsize)

    # Get unique y values
    y_values = df[y_key].unique()
    y_dict = {y: i for i, y in enumerate(y_values)}

    # Get unique color values
    if color_key:
        colors = plt.cm.tab10(np.linspace(0, 1, len(df[color_key].unique())))
        color_dict = {c: colors[i] for i, c in enumerate(df[color_key].unique())}

    # Plot events
    for _, row in df.iterrows():
        y = y_dict[row[y_key]]
        color = color_dict[row[color_key]] if color_key else 'blue'
        plt.scatter(row[x_key], y, color=color, s=50)

    # Add legend
    if color_key:
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor=color, markersize=8, label=key)
            for key, color in color_dict.items()
        ]
        plt.legend(handles=legend_elements, title=color_key)

    # Set labels and title
    plt.yticks(list(y_dict.values()), list(y_dict.keys()))
    plt.title(title)
    plt.grid(True, alpha=0.3)

    # Format x-axis for timestamps
    if x_key == "timestamp":
        plt.gcf().autofmt_xdate()

    plt.tight_layout()
    plt.show()
```

#### Step 3: Create Trace Loading Module

Create a module for loading and processing trace data in `app/notebooks/trace_loader.py`:

```python
"""Trace loading and processing utilities for notebooks."""

import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from app.core.tracing.utils import filter_traces, load_traces

def load_trace_file(file_path: str) -> List[Dict[str, Any]]:
    """Load traces from a file.

    Args:
        file_path: Path to the trace file

    Returns:
        List of trace events
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Trace file not found: {file_path}")

    # Load traces
    traces = load_traces(file_path)

    # Sort by timestamp
    traces = sorted(traces, key=lambda t: t["timestamp"])

    return traces

def find_trace_files(directory: str = "traces", prefix: Optional[str] = None) -> List[str]:
    """Find trace files in a directory.

    Args:
        directory: Directory to search
        prefix: Optional prefix to filter by

    Returns:
        List of trace file paths
    """
    if not os.path.exists(directory):
        return []

    # Find trace files
    files = []
    for file in os.listdir(directory):
        if file.endswith(".json") or file.endswith(".ndjson"):
            if prefix is None or file.startswith(prefix):
                files.append(os.path.join(directory, file))

    # Sort by modification time (newest first)
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    return files

def traces_to_dataframe(traces: List[Dict[str, Any]]) -> pd.DataFrame:
    """Convert traces to a pandas DataFrame.

    Args:
        traces: List of trace events

    Returns:
        DataFrame of trace events
    """
    # Create DataFrame
    df = pd.DataFrame(traces)

    # Convert timestamp to datetime
    df["timestamp"] = pd.to_datetime(df["timestamp"])

    # Extract correlation IDs
    if "correlation" in df.columns:
        correlation_df = pd.json_normalize(df["correlation"])
        correlation_df.columns = [f"correlation_{c}" for c in correlation_df.columns]
        df = pd.concat([df.drop("correlation", axis=1), correlation_df], axis=1)

    # Extract metadata
    if "metadata" in df.columns:
        # Create separate columns for common metadata fields
        common_fields = set()
        for meta in df["metadata"]:
            if isinstance(meta, dict):
                common_fields.update(meta.keys())

        for field in common_fields:
            df[f"metadata_{field}"] = df["metadata"].apply(
                lambda x: x.get(field) if isinstance(x, dict) else None
            )

    return df

def get_agent_messages(traces: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract agent messages from traces.

    Args:
        traces: List of trace events

    Returns:
        List of agent message events
    """
    # Filter agent message events
    messages = filter_traces(traces, event_type="agent_message")

    # Sort by timestamp
    messages = sorted(messages, key=lambda t: t["timestamp"])

    # Extract message details
    result = []
    for message in messages:
        metadata = message.get("metadata", {})
        result.append({
            "timestamp": message["timestamp"],
            "node_id": message["node_id"],
            "sender": metadata.get("sender", "Unknown"),
            "recipient": metadata.get("recipient", "Unknown"),
            "content": metadata.get("content", ""),
            "message_type": metadata.get("message_type", "text"),
            "context": metadata.get("context", {}),
        })

    return result

def get_agent_message_flow(traces: List[Dict[str, Any]]) -> pd.DataFrame:
    """Get the flow of messages between agents.

    Args:
        traces: List of trace events

    Returns:
        DataFrame of agent message flow
    """
    # Get agent messages
    messages = get_agent_messages(traces)

    # Convert to DataFrame
    df = pd.DataFrame(messages)

    # Convert timestamp to datetime
    if "timestamp" in df.columns:
        df["timestamp"] = pd.to_datetime(df["timestamp"])

    return df
```

#### Step 4: Create Basic Trace Analysis Module

Create a module for analyzing trace data in `app/notebooks/trace_analysis.py`:

```python
"""Trace analysis utilities for notebooks."""

import json
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
import pandas as pd
from IPython.display import HTML, display

from app.notebooks.trace_loader import (
    get_agent_message_flow,
    get_agent_messages,
    load_trace_file,
    traces_to_dataframe,
)
from app.notebooks.utils import plot_timeline

def analyze_trace_events(traces: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze trace events.

    Args:
        traces: List of trace events

    Returns:
        Dictionary of analysis results
    """
    # Convert to DataFrame
    df = traces_to_dataframe(traces)

    # Get basic statistics
    stats = {
        "event_count": len(df),
        "start_time": df["timestamp"].min(),
        "end_time": df["timestamp"].max(),
        "duration_seconds": (df["timestamp"].max() - df["timestamp"].min()).total_seconds(),
    }

    # Get event type counts
    if "event_type" in df.columns:
        event_type_counts = df["event_type"].value_counts().to_dict()
        stats["event_type_counts"] = event_type_counts

    # Get node counts
    if "node_id" in df.columns:
        node_counts = df["node_id"].value_counts().to_dict()
        stats["node_counts"] = node_counts

    # Get agent message counts
    agent_messages = get_agent_messages(traces)
    if agent_messages:
        agent_df = pd.DataFrame(agent_messages)

        # Get sender counts
        if "sender" in agent_df.columns:
            sender_counts = agent_df["sender"].value_counts().to_dict()
            stats["sender_counts"] = sender_counts

        # Get recipient counts
        if "recipient" in agent_df.columns:
            recipient_counts = agent_df["recipient"].value_counts().to_dict()
            stats["recipient_counts"] = recipient_counts

        # Get sender-recipient pairs
        if "sender" in agent_df.columns and "recipient" in agent_df.columns:
            pair_counts = agent_df.groupby(["sender", "recipient"]).size().to_dict()
            stats["pair_counts"] = {f"{s} → {r}": c for (s, r), c in pair_counts.items()}

    return stats

def plot_event_timeline(traces: List[Dict[str, Any]], figsize: tuple = (12, 6)):
    """Plot a timeline of trace events.

    Args:
        traces: List of trace events
        figsize: Figure size
    """
    plot_timeline(
        events=traces,
        x_key="timestamp",
        y_key="node_id",
        color_key="event_type",
        title="Trace Event Timeline",
        figsize=figsize,
    )

def plot_agent_message_timeline(traces: List[Dict[str, Any]], figsize: tuple = (12, 6)):
    """Plot a timeline of agent messages.

    Args:
        traces: List of trace events
        figsize: Figure size
    """
    # Get agent messages
    messages = get_agent_messages(traces)

    plot_timeline(
        events=messages,
        x_key="timestamp",
        y_key="sender",
        color_key="recipient",
        title="Agent Message Timeline",
        figsize=figsize,
    )

def plot_agent_message_graph(traces: List[Dict[str, Any]], figsize: tuple = (10, 8)):
    """Plot a graph of agent message flow.

    Args:
        traces: List of trace events
        figsize: Figure size
    """
    # Get agent messages
    messages = get_agent_messages(traces)

    # Create graph
    G = nx.DiGraph()

    # Add nodes and edges
    for message in messages:
        sender = message["sender"]
        recipient = message["recipient"]

        # Add nodes
        if not G.has_node(sender):
            G.add_node(sender)
        if not G.has_node(recipient):
            G.add_node(recipient)

        # Add or update edge
        if G.has_edge(sender, recipient):
            G[sender][recipient]["weight"] += 1
        else:
            G.add_edge(sender, recipient, weight=1)

    # Create figure
    plt.figure(figsize=figsize)

    # Set node colors
    node_colors = []
    for node in G.nodes():
        if node.lower() == "user":
            node_colors.append("lightblue")
        elif "co-ceo" in node.lower():
            node_colors.append("lightgreen")
        elif "finance" in node.lower():
            node_colors.append("lightcoral")
        elif "marketing" in node.lower():
            node_colors.append("lightyellow")
        else:
            node_colors.append("lightgray")

    # Set edge weights for thickness
    edge_weights = [G[u][v]["weight"] * 2 for u, v in G.edges()]

    # Set edge labels
    edge_labels = {(u, v): f"{G[u][v]['weight']}" for u, v in G.edges()}

    # Draw graph
    pos = nx.spring_layout(G, seed=42)
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=1000, alpha=0.8)
    nx.draw_networkx_labels(G, pos, font_size=12)
    nx.draw_networkx_edges(G, pos, width=edge_weights, alpha=0.5, arrowsize=20)
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)

    plt.title("Agent Message Flow")
    plt.axis("off")
    plt.tight_layout()
    plt.show()

def display_agent_conversation(traces: List[Dict[str, Any]], max_messages: int = 50):
    """Display agent conversation in a readable format.

    Args:
        traces: List of trace events
        max_messages: Maximum number of messages to display
    """
    # Get agent messages
    messages = get_agent_messages(traces)

    # Limit number of messages
    if len(messages) > max_messages:
        print(f"Showing {max_messages} of {len(messages)} messages")
        messages = messages[:max_messages]

    # Create HTML for display
    html = """
    <style>
        .conversation {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .sender {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .timestamp {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 5px;
        }
        .content {
            white-space: pre-wrap;
        }
        .user {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
        }
        .co-ceo {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
        }
        .finance {
            background-color: #fff2e8;
            border-left: 4px solid #fa8c16;
        }
        .marketing {
            background-color: #f9f0ff;
            border-left: 4px solid #722ed1;
        }
        .other {
            background-color: #f5f5f5;
            border-left: 4px solid #d9d9d9;
        }
    </style>
    <div class="conversation">
    """

    for message in messages:
        # Format timestamp
        timestamp = datetime.fromisoformat(message["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")

        # Determine message class
        sender = message["sender"].lower()
        if "user" in sender:
            message_class = "user"
        elif "co-ceo" in sender:
            message_class = "co-ceo"
        elif "finance" in sender:
            message_class = "finance"
        elif "marketing" in sender:
            message_class = "marketing"
        else:
            message_class = "other"

        # Add message to HTML
        html += f"""
        <div class="message {message_class}">
            <div class="sender">{message["sender"]} → {message["recipient"]}</div>
            <div class="timestamp">{timestamp}</div>
            <div class="content">{message["content"]}</div>
        </div>
        """

    html += "</div>"

    # Display HTML
    display(HTML(html))
```

### 6.2 Vector Visualization Notebook

#### Step 1: Create Vector Visualization Module Structure

First, let's create the basic structure for the vector visualization module:

```bash
# Create the vector visualization module
touch app/notebooks/vector_visualization.py
```

Add the basic imports and module structure to `app/notebooks/vector_visualization.py`:

```python
"""Vector visualization utilities for notebooks."""

import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from IPython.display import HTML, display
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler

from app.db.session import async_session
from app.models.document import Document
from app.notebooks.utils import load_json_file, save_json_file
```

#### Step 2: Implement Vector Loading Functions

Add functions to load vector data from PostgreSQL to `app/notebooks/vector_visualization.py`:

```python
async def load_embeddings_from_db(
    limit: int = 1000,
    department: Optional[str] = None,
) -> Tuple[List[List[float]], List[Dict[str, Any]]]:
    """Load embeddings from the database.

    Args:
        limit: Maximum number of embeddings to load
        department: Filter by department

    Returns:
        Tuple of (embeddings, metadata)
    """
    from sqlalchemy import select

    async with async_session() as session:
        # Build query
        query = select(Document.id, Document.embedding, Document.metadata, Document.content)

        # Apply department filter
        if department:
            query = query.filter(Document.metadata["department"].astext == department)

        # Apply limit
        query = query.limit(limit)

        # Execute query
        result = await session.execute(query)
        rows = result.all()

        # Extract embeddings and metadata
        embeddings = []
        metadata = []

        for row in rows:
            doc_id, embedding, meta, content = row

            if embedding is not None:
                embeddings.append(embedding)

                # Add document info to metadata
                doc_meta = dict(meta) if meta else {}
                doc_meta["id"] = doc_id
                doc_meta["content"] = content[:100] + "..." if len(content) > 100 else content

                metadata.append(doc_meta)

        return embeddings, metadata

def load_embeddings_from_file(file_path: str) -> Tuple[List[List[float]], List[Dict[str, Any]]]:
    """Load embeddings from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        Tuple of (embeddings, metadata)
    """
    # Load data from file
    data = load_json_file(file_path)

    # Extract embeddings and metadata
    embeddings = []
    metadata = []

    for item in data:
        if "embedding" in item and item["embedding"] is not None:
            embeddings.append(item["embedding"])

            # Create metadata
            meta = {k: v for k, v in item.items() if k != "embedding"}
            metadata.append(meta)

    return embeddings, metadata

async def get_query_embedding(
    query: str,
) -> List[float]:
    """Get embedding for a query.

    Args:
        query: Query text

    Returns:
        Query embedding
    """
    # Import here to avoid circular imports
    from app.rag.embeddings import get_embedding_model

    # Get embedding model
    embedding_model = await get_embedding_model()

    # Get embedding
    embedding = await embedding_model.embed_query(query)

    return embedding

async def search_similar_embeddings(
    query_embedding: List[float],
    embeddings: List[List[float]],
    metadata: List[Dict[str, Any]],
    top_k: int = 5,
) -> List[Dict[str, Any]]:
    """Search for embeddings similar to a query embedding.

    Args:
        query_embedding: Query embedding
        embeddings: List of embeddings to search
        metadata: List of metadata for embeddings
        top_k: Number of results to return

    Returns:
        List of search results with similarity scores
    """
    import numpy as np

    # Convert to numpy arrays
    query_array = np.array(query_embedding)
    embeddings_array = np.array(embeddings)

    # Normalize embeddings
    query_norm = query_array / np.linalg.norm(query_array)
    embeddings_norm = embeddings_array / np.linalg.norm(embeddings_array, axis=1, keepdims=True)

    # Calculate cosine similarities
    similarities = np.dot(embeddings_norm, query_norm)

    # Get top k indices
    top_indices = np.argsort(similarities)[-top_k:][::-1]

    # Create results
    results = []
    for i in top_indices:
        result = dict(metadata[i])
        result["score"] = float(similarities[i])
        results.append(result)

    return results
```

#### Step 3: Implement Basic Dimensionality Reduction

Add functions for dimensionality reduction to `app/notebooks/vector_visualization.py`:

```python
def reduce_dimensions(
    embeddings: List[List[float]],
    method: str = "pca",
    n_components: int = 2,
    random_state: int = 42,
) -> np.ndarray:
    """Reduce dimensions of embeddings.

    Args:
        embeddings: List of embeddings
        method: Dimensionality reduction method (pca, tsne)
        n_components: Number of components
        random_state: Random state for reproducibility

    Returns:
        Reduced embeddings
    """
    # Convert to numpy array
    embeddings_array = np.array(embeddings)

    # Standardize data
    scaler = StandardScaler()
    embeddings_scaled = scaler.fit_transform(embeddings_array)

    # Apply dimensionality reduction
    if method == "pca":
        reducer = PCA(n_components=n_components, random_state=random_state)
    elif method == "tsne":
        reducer = TSNE(n_components=n_components, random_state=random_state)
    else:
        raise ValueError(f"Unknown method: {method}")

    # Fit and transform
    embeddings_reduced = reducer.fit_transform(embeddings_scaled)

    return embeddings_reduced

def create_embedding_dataframe(
    embeddings_reduced: np.ndarray,
    metadata: List[Dict[str, Any]],
    label_key: Optional[str] = None,
) -> pd.DataFrame:
    """Create a DataFrame from reduced embeddings and metadata.

    Args:
        embeddings_reduced: Reduced embeddings
        metadata: List of metadata for embeddings
        label_key: Key to use for labels

    Returns:
        DataFrame with reduced embeddings and metadata
    """
    # Create DataFrame
    df = pd.DataFrame(embeddings_reduced, columns=[f"dim_{i+1}" for i in range(embeddings_reduced.shape[1])])

    # Add metadata
    for i, meta in enumerate(metadata):
        for key, value in meta.items():
            if key not in df.columns:
                df[key] = None
            df.at[i, key] = value

    # Add label
    if label_key and label_key in df.columns:
        df["label"] = df[label_key]
    else:
        df["label"] = range(len(df))

    return df
```

#### Step 4: Implement Basic Scatter Plot Visualization

Add functions for basic scatter plot visualization to `app/notebooks/vector_visualization.py`:

```python
def plot_embeddings_2d(
    df: pd.DataFrame,
    x_col: str = "dim_1",
    y_col: str = "dim_2",
    label_col: str = "label",
    title: str = "Embedding Visualization",
    figsize: tuple = (10, 8),
    alpha: float = 0.7,
    show_legend: bool = True,
):
    """Plot embeddings in 2D.

    Args:
        df: DataFrame with reduced embeddings
        x_col: Column for x-axis
        y_col: Column for y-axis
        label_col: Column for labels
        title: Plot title
        figsize: Figure size
        alpha: Alpha for points
        show_legend: Whether to show legend
    """
    # Create figure
    plt.figure(figsize=figsize)

    # Get unique labels
    labels = df[label_col].unique()

    # Create colormap
    cmap = plt.cm.get_cmap("tab10", len(labels))

    # Plot each label
    for i, label in enumerate(labels):
        mask = df[label_col] == label
        plt.scatter(
            df.loc[mask, x_col],
            df.loc[mask, y_col],
            c=[cmap(i)],
            label=label,
            alpha=alpha,
        )

    # Add labels and title
    plt.xlabel(x_col)
    plt.ylabel(y_col)
    plt.title(title)

    # Add legend
    if show_legend and len(labels) <= 20:  # Only show legend if not too many labels
        plt.legend()

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

def plot_embeddings_3d(
    df: pd.DataFrame,
    x_col: str = "dim_1",
    y_col: str = "dim_2",
    z_col: str = "dim_3",
    label_col: str = "label",
    title: str = "Embedding Visualization (3D)",
    figsize: tuple = (10, 8),
    alpha: float = 0.7,
    show_legend: bool = True,
):
    """Plot embeddings in 3D.

    Args:
        df: DataFrame with reduced embeddings
        x_col: Column for x-axis
        y_col: Column for y-axis
        z_col: Column for z-axis
        label_col: Column for labels
        title: Plot title
        figsize: Figure size
        alpha: Alpha for points
        show_legend: Whether to show legend
    """
    # Create figure
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection="3d")

    # Get unique labels
    labels = df[label_col].unique()

    # Create colormap
    cmap = plt.cm.get_cmap("tab10", len(labels))

    # Plot each label
    for i, label in enumerate(labels):
        mask = df[label_col] == label
        ax.scatter(
            df.loc[mask, x_col],
            df.loc[mask, y_col],
            df.loc[mask, z_col],
            c=[cmap(i)],
            label=label,
            alpha=alpha,
        )

    # Add labels and title
    ax.set_xlabel(x_col)
    ax.set_ylabel(y_col)
    ax.set_zlabel(z_col)
    ax.set_title(title)

    # Add legend
    if show_legend and len(labels) <= 20:  # Only show legend if not too many labels
        ax.legend()

    plt.tight_layout()
    plt.show()
```

#### Step 5: Implement Interactive Visualization with Hover

Add functions for interactive visualization with hover to `app/notebooks/vector_visualization.py`:

```python
def plot_embeddings_interactive(
    df: pd.DataFrame,
    x_col: str = "dim_1",
    y_col: str = "dim_2",
    label_col: str = "label",
    hover_cols: Optional[List[str]] = None,
    title: str = "Interactive Embedding Visualization",
    figsize: tuple = (800, 600),
):
    """Plot embeddings in an interactive plot with hover.

    Args:
        df: DataFrame with reduced embeddings
        x_col: Column for x-axis
        y_col: Column for y-axis
        label_col: Column for labels
        hover_cols: Columns to show on hover
        title: Plot title
        figsize: Figure size (width, height)
    """
    # Import plotly
    import plotly.express as px

    # Set default hover columns
    if hover_cols is None:
        hover_cols = [col for col in df.columns if col not in [x_col, y_col] and df[col].dtype == "object"]
        if len(hover_cols) > 5:
            hover_cols = hover_cols[:5]

    # Create figure
    fig = px.scatter(
        df,
        x=x_col,
        y=y_col,
        color=label_col,
        hover_data=hover_cols,
        title=title,
        width=figsize[0],
        height=figsize[1],
    )

    # Update layout
    fig.update_layout(
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1,
        ),
    )

    # Show figure
    fig.show()
```

### 6.3 Query Playground

#### Step 1: Implement Query Playground Functions

Add query playground functions to `app/notebooks/vector_visualization.py`:

```python
async def create_query_embedding_visualization(
    query: str,
    embeddings: List[List[float]],
    metadata: List[Dict[str, Any]],
    method: str = "pca",
    n_components: int = 2,
    label_key: str = "department",
    top_k: int = 5,
):
    """Create a visualization of query embedding and similar documents.

    Args:
        query: Query text
        embeddings: List of embeddings
        metadata: List of metadata for embeddings
        method: Dimensionality reduction method
        n_components: Number of components
        label_key: Key to use for labels
        top_k: Number of similar documents to highlight
    """
    # Get query embedding
    query_embedding = await get_query_embedding(query)

    # Search for similar embeddings
    results = await search_similar_embeddings(
        query_embedding=query_embedding,
        embeddings=embeddings,
        metadata=metadata,
        top_k=top_k,
    )

    # Add query embedding to the list
    all_embeddings = embeddings + [query_embedding]

    # Add query metadata
    query_meta = {
        "id": "query",
        "content": query,
        label_key: "query",
    }
    all_metadata = metadata + [query_meta]

    # Reduce dimensions
    embeddings_reduced = reduce_dimensions(
        embeddings=all_embeddings,
        method=method,
        n_components=n_components,
    )

    # Create DataFrame
    df = create_embedding_dataframe(
        embeddings_reduced=embeddings_reduced,
        metadata=all_metadata,
        label_key=label_key,
    )

    # Mark similar documents
    df["is_similar"] = False
    for result in results:
        doc_id = result["id"]
        df.loc[df["id"] == doc_id, "is_similar"] = True
        df.loc[df["id"] == doc_id, "similarity_score"] = result["score"]

    # Mark query
    df.loc[df["id"] == "query", "is_similar"] = True

    # Create custom label column for visualization
    df["viz_label"] = df[label_key]
    df.loc[df["is_similar"] & (df["id"] != "query"), "viz_label"] = "similar"

    # Plot embeddings
    if n_components == 2:
        # Create figure
        plt.figure(figsize=(12, 10))

        # Plot background points
        mask_background = ~df["is_similar"]
        plt.scatter(
            df.loc[mask_background, "dim_1"],
            df.loc[mask_background, "dim_2"],
            c="lightgray",
            alpha=0.5,
            label="other",
        )

        # Plot similar documents
        mask_similar = df["is_similar"] & (df["id"] != "query")
        plt.scatter(
            df.loc[mask_similar, "dim_1"],
            df.loc[mask_similar, "dim_2"],
            c="orange",
            alpha=0.8,
            s=100,
            label="similar",
        )

        # Plot query
        mask_query = df["id"] == "query"
        plt.scatter(
            df.loc[mask_query, "dim_1"],
            df.loc[mask_query, "dim_2"],
            c="red",
            marker="*",
            s=200,
            label="query",
        )

        # Add labels and title
        plt.xlabel("Dimension 1")
        plt.ylabel("Dimension 2")
        plt.title(f"Query: {query}")
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()

    elif n_components == 3:
        # Create figure
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection="3d")

        # Plot background points
        mask_background = ~df["is_similar"]
        ax.scatter(
            df.loc[mask_background, "dim_1"],
            df.loc[mask_background, "dim_2"],
            df.loc[mask_background, "dim_3"],
            c="lightgray",
            alpha=0.5,
            label="other",
        )

        # Plot similar documents
        mask_similar = df["is_similar"] & (df["id"] != "query")
        ax.scatter(
            df.loc[mask_similar, "dim_1"],
            df.loc[mask_similar, "dim_2"],
            df.loc[mask_similar, "dim_3"],
            c="orange",
            alpha=0.8,
            s=100,
            label="similar",
        )

        # Plot query
        mask_query = df["id"] == "query"
        ax.scatter(
            df.loc[mask_query, "dim_1"],
            df.loc[mask_query, "dim_2"],
            df.loc[mask_query, "dim_3"],
            c="red",
            marker="*",
            s=200,
            label="query",
        )

        # Add labels and title
        ax.set_xlabel("Dimension 1")
        ax.set_ylabel("Dimension 2")
        ax.set_zlabel("Dimension 3")
        ax.set_title(f"Query: {query}")
        ax.legend()
        plt.tight_layout()
        plt.show()

    # Display similar documents
    print(f"\nTop {len(results)} similar documents:")
    for i, result in enumerate(results):
        print(f"\n{i+1}. {result.get('id')} (Score: {result.get('score'):.4f})")
        print(f"   Department: {result.get('department', 'N/A')}")
        print(f"   Content: {result.get('content', 'N/A')}")

    return df, results

def create_interactive_query_playground(
    embeddings: List[List[float]],
    metadata: List[Dict[str, Any]],
    default_query: str = "financial performance",
    method: str = "pca",
    n_components: int = 2,
    label_key: str = "department",
):
    """Create an interactive query playground.

    Args:
        embeddings: List of embeddings
        metadata: List of metadata for embeddings
        default_query: Default query text
        method: Dimensionality reduction method
        n_components: Number of components
        label_key: Key to use for labels
    """
    # Import ipywidgets
    import ipywidgets as widgets
    from IPython.display import display, clear_output

    # Create widgets
    query_input = widgets.Text(
        value=default_query,
        description="Query:",
        style={"description_width": "initial"},
        layout=widgets.Layout(width="500px"),
    )

    method_dropdown = widgets.Dropdown(
        options=["pca", "tsne"],
        value=method,
        description="Method:",
        style={"description_width": "initial"},
    )

    components_dropdown = widgets.Dropdown(
        options=[2, 3],
        value=n_components,
        description="Dimensions:",
        style={"description_width": "initial"},
    )

    label_dropdown = widgets.Dropdown(
        options=[key for key in metadata[0].keys() if key != "embedding"],
        value=label_key,
        description="Label by:",
        style={"description_width": "initial"},
    )

    top_k_slider = widgets.IntSlider(
        value=5,
        min=1,
        max=20,
        step=1,
        description="Top K:",
        style={"description_width": "initial"},
    )

    search_button = widgets.Button(
        description="Search",
        button_style="primary",
        icon="search",
    )

    output = widgets.Output()

    # Create layout
    controls = widgets.HBox([
        widgets.VBox([query_input, search_button]),
        widgets.VBox([method_dropdown, components_dropdown]),
        widgets.VBox([label_dropdown, top_k_slider]),
    ])

    # Define search function
    async def search(button):
        with output:
            clear_output(wait=True)

            # Get values from widgets
            query = query_input.value
            method = method_dropdown.value
            n_components = components_dropdown.value
            label_key = label_dropdown.value
            top_k = top_k_slider.value

            # Create visualization
            await create_query_embedding_visualization(
                query=query,
                embeddings=embeddings,
                metadata=metadata,
                method=method,
                n_components=n_components,
                label_key=label_key,
                top_k=top_k,
            )

    # Connect button to function
    search_button.on_click(lambda b: asyncio.ensure_future(search(b)))

    # Display widgets
    display(controls)
    display(output)

    # Initial search
    asyncio.ensure_future(search(None))
```

#### Step 2: Implement Example Creation Functions

Add functions to create example notebooks to `app/notebooks/vector_visualization.py`:

```python
def create_vector_visualization_notebook():
    """Create a vector visualization notebook."""
    # Create notebook content
    content = """
{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Vector Embedding Visualization\n",
    "\n",
    "This notebook demonstrates how to visualize vector embeddings from the database."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import asyncio\n",
    "import os\n",
    "import sys\n",
    "\n",
    "# Add the project root to the path\n",
    "sys.path.append(os.path.abspath('../'))\n",
    "\n",
    "# Import visualization utilities\n",
    "from app.notebooks.utils import setup_notebook_styling\n",
    "from app.notebooks.vector_visualization import (\n",
    "    load_embeddings_from_db,\n",
    "    reduce_dimensions,\n",
    "    create_embedding_dataframe,\n",
    "    plot_embeddings_2d,\n",
    "    plot_embeddings_3d,\n",
    "    plot_embeddings_interactive\n",
    ")\n",
    "\n",
    "# Set up styling\n",
    "setup_notebook_styling()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Load Embeddings from Database\n",
    "\n",
    "First, let's load embeddings from the database."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load embeddings\n",
    "embeddings, metadata = await load_embeddings_from_db(limit=500)\n",
    "\n",
    "print(f\"Loaded {len(embeddings)} embeddings\")\n",
    "print(f\"Embedding dimensions: {len(embeddings[0])}\")\n",
    "print(f\"Sample metadata: {metadata[0]}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Reduce Dimensions with PCA\n",
    "\n",
    "Let's reduce the dimensions of the embeddings using PCA."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Reduce dimensions with PCA\n",
    "embeddings_2d = reduce_dimensions(embeddings, method=\"pca\", n_components=2)\n",
    "embeddings_3d = reduce_dimensions(embeddings, method=\"pca\", n_components=3)\n",
    "\n",
    "print(f\"2D shape: {embeddings_2d.shape}\")\n",
    "print(f\"3D shape: {embeddings_3d.shape}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Create DataFrames\n",
    "\n",
    "Now, let's create DataFrames with the reduced embeddings and metadata."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create DataFrames\n",
    "df_2d = create_embedding_dataframe(embeddings_2d, metadata, label_key=\"department\")\n",
    "df_3d = create_embedding_dataframe(embeddings_3d, metadata, label_key=\"department\")\n",
    "\n",
    "# Display DataFrame\n",
    "df_2d.head()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Visualize Embeddings in 2D\n",
    "\n",
    "Let's visualize the embeddings in 2D."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Plot embeddings in 2D\n",
    "plot_embeddings_2d(\n",
    "    df=df_2d,\n",
    "    label_col=\"department\",\n",
    "    title=\"Document Embeddings by Department (PCA)\"\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Visualize Embeddings in 3D\n",
    "\n",
    "Let's visualize the embeddings in 3D."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Plot embeddings in 3D\n",
    "plot_embeddings_3d(\n",
    "    df=df_3d,\n",
    "    label_col=\"department\",\n",
    "    title=\"Document Embeddings by Department (PCA 3D)\"\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Interactive Visualization\n",
    "\n",
    "Let's create an interactive visualization with hover."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Plot interactive embeddings\n",
    "plot_embeddings_interactive(\n",
    "    df=df_2d,\n",
    "    label_col=\"department\",\n",
    "    hover_cols=[\"id\", \"content\"],\n",
    "    title=\"Interactive Document Embeddings\"\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Try Different Dimensionality Reduction Methods\n",
    "\n",
    "Let's try t-SNE for dimensionality reduction."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Reduce dimensions with t-SNE\n",
    "embeddings_tsne = reduce_dimensions(embeddings, method=\"tsne\", n_components=2)\n",
    "\n",
    "# Create DataFrame\n",
    "df_tsne = create_embedding_dataframe(embeddings_tsne, metadata, label_key=\"department\")\n",
    "\n",
    "# Plot embeddings\n",
    "plot_embeddings_2d(\n",
    "    df=df_tsne,\n",
    "    label_col=\"department\",\n",
    "    title=\"Document Embeddings by Department (t-SNE)\"\n",
    ")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
"""

    # Create notebook file
    os.makedirs("notebooks", exist_ok=True)
    with open("notebooks/vector_visualization.ipynb", "w") as f:
        f.write(content)

    print("Created vector visualization notebook: notebooks/vector_visualization.ipynb")

def create_query_playground_notebook():
    """Create a query playground notebook."""
    # Create notebook content
    content = """
{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Query Playground\n",
    "\n",
    "This notebook provides an interactive playground for querying the vector database and visualizing the results."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import asyncio\n",
    "import os\n",
    "import sys\n",
    "\n",
    "# Add the project root to the path\n",
    "sys.path.append(os.path.abspath('../'))\n",
    "\n",
    "# Import visualization utilities\n",
    "from app.notebooks.utils import setup_notebook_styling\n",
    "from app.notebooks.vector_visualization import (\n",
    "    load_embeddings_from_db,\n",
    "    get_query_embedding,\n",
    "    search_similar_embeddings,\n",
    "    create_query_embedding_visualization,\n",
    "    create_interactive_query_playground\n",
    ")\n",
    "\n",
    "# Set up styling\n",
    "setup_notebook_styling()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Load Embeddings from Database\n",
    "\n",
    "First, let's load embeddings from the database."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load embeddings\n",
    "embeddings, metadata = await load_embeddings_from_db(limit=500)\n",
    "\n",
    "print(f\"Loaded {len(embeddings)} embeddings\")\n",
    "print(f\"Embedding dimensions: {len(embeddings[0])}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Simple Query Example\n",
    "\n",
    "Let's try a simple query and visualize the results."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define a query\n",
    "query = \"financial performance\"\n",
    "\n",
    "# Get query embedding\n",
    "query_embedding = await get_query_embedding(query)\n",
    "\n",
    "# Search for similar embeddings\n",
    "results = await search_similar_embeddings(\n",
    "    query_embedding=query_embedding,\n",
    "    embeddings=embeddings,\n",
    "    metadata=metadata,\n",
    "    top_k=5\n",
    ")\n",
    "\n",
    "# Display results\n",
    "for i, result in enumerate(results):\n",
    "    print(f\"\\n{i+1}. {result.get('id')} (Score: {result.get('score'):.4f})\")\n",
    "    print(f\"   Department: {result.get('department', 'N/A')}\")\n",
    "    print(f\"   Content: {result.get('content', 'N/A')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Visualize Query Results\n",
    "\n",
    "Let's visualize the query and its results in the embedding space."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualize query results\n",
    "df, results = await create_query_embedding_visualization(\n",
    "    query=\"financial performance\",\n",
    "    embeddings=embeddings,\n",
    "    metadata=metadata,\n",
    "    method=\"pca\",\n",
    "    n_components=2,\n",
    "    label_key=\"department\",\n",
    "    top_k=5\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Interactive Query Playground\n",
    "\n",
    "Now, let's create an interactive query playground."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create interactive query playground\n",
    "create_interactive_query_playground(\n",
    "    embeddings=embeddings,\n",
    "    metadata=metadata,\n",
    "    default_query=\"marketing budget\",\n",
    "    method=\"pca\",\n",
    "    n_components=2,\n",
    "    label_key=\"department\"\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Try Different Queries\n",
    "\n",
    "You can try different queries in the interactive playground above. Here are some example queries to try:\n",
    "\n",
    "- marketing budget\n",
    "- financial performance\n",
    "- quarterly results\n",
    "- customer acquisition\n",
    "- revenue growth\n",
    "- cost reduction\n",
    "- market analysis\n",
    "- competitive landscape"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
"""

    # Create notebook file
    os.makedirs("notebooks", exist_ok=True)
    with open("notebooks/query_playground.ipynb", "w") as f:
        f.write(content)

    print("Created query playground notebook: notebooks/query_playground.ipynb")
```

#### Step 3: Create Test Script for Notebooks

Create a test script in `backend/scripts/test_notebooks.py`:

```python
"""Test script for Jupyter notebooks."""

import asyncio
import os
import subprocess
import sys

from app.notebooks.vector_visualization import (
    create_query_playground_notebook,
    create_vector_visualization_notebook,
)

def create_notebooks():
    """Create example notebooks."""
    # Create vector visualization notebook
    create_vector_visualization_notebook()

    # Create query playground notebook
    create_query_playground_notebook()

def test_notebooks():
    """Test notebooks with nbconvert."""
    # Check if nbconvert is available
    try:
        subprocess.run(
            ["jupyter", "nbconvert", "--version"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("jupyter nbconvert not available, skipping notebook tests")
        return

    # List notebooks
    notebooks = [
        "notebooks/vector_visualization.ipynb",
        "notebooks/query_playground.ipynb",
    ]

    # Test each notebook
    for notebook in notebooks:
        if not os.path.exists(notebook):
            print(f"Notebook not found: {notebook}")
            continue

        print(f"Testing notebook: {notebook}")

        # Convert notebook to HTML
        result = subprocess.run(
            [
                "jupyter",
                "nbconvert",
                "--to",
                "html",
                "--execute",
                "--ExecutePreprocessor.timeout=60",
                notebook,
            ],
            check=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        if result.returncode == 0:
            print(f"Successfully executed notebook: {notebook}")
        else:
            print(f"Failed to execute notebook: {notebook}")
            print(f"Error: {result.stderr.decode()}")

def main():
    """Run the test script."""
    # Create notebooks
    create_notebooks()

    # Test notebooks
    test_notebooks()

if __name__ == "__main__":
    main()
```

#### Step 4: Update Makefile for Notebooks

Update `makefiles/backend-cli-testing/Makefile` to include notebook commands:

```makefile
# Add these commands to the Makefile

# Create notebooks
create-notebooks:
	cd $(BACKEND_DIR) && python scripts/test_notebooks.py

# Open Jupyter Lab
jupyter-lab:
	cd $(BACKEND_DIR) && jupyter lab

# Run vector visualization notebook
run-vector-viz:
	cd $(BACKEND_DIR) && jupyter nbconvert --to html --execute notebooks/vector_visualization.ipynb

# Run query playground notebook
run-query-playground:
	cd $(BACKEND_DIR) && jupyter nbconvert --to html --execute notebooks/query_playground.ipynb
```

#### Step 5: Test Notebooks

Run the test script:

```bash
cd backend
python scripts/test_notebooks.py
```

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing
make create-notebooks
make jupyter-lab
make run-vector-viz
make run-query-playground
```

## 7. Phase 5: Integration and Testing

### 7.1 Makefile Integration

#### Step 1: Create Comprehensive Makefile for Tracing

Create a comprehensive Makefile for tracing in `makefiles/backend-cli-testing/Makefile`:

```makefile
# Tracing and Visualization Commands

# Enable tracing for CLI testing
test-with-trace: export ENABLE_TRACING=1
test-with-trace:
	cd $(BACKEND_DIR) && python scripts/test_backend_cli.py --query "$(QUERY)" --department $(DEPARTMENT) --trace

# Enable live tracing for CLI testing
test-with-live-trace: export ENABLE_TRACING=1
test-with-live-trace:
	cd $(BACKEND_DIR) && python scripts/test_backend_cli.py --query "$(QUERY)" --department $(DEPARTMENT) --live-trace

# View the latest trace file
view-trace:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest

# View the latest trace file in tree format
view-trace-tree:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest --format tree

# View the latest trace file in table format
view-trace-table:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest --format table

# View agent communication from the latest trace file
view-agents:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer agents --latest

# Filter trace by node ID
filter-trace-node:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest --node-id $(NODE_ID)

# Filter trace by event type
filter-trace-event:
	cd $(BACKEND_DIR) && python -m app.cli.trace_viewer view --latest --event-type $(EVENT_TYPE)

# Generate a test trace
test-trace:
	cd $(BACKEND_DIR) && python scripts/test_trace_visualization.py

# Test agents with real-time visualization
test-agents-live:
	cd $(BACKEND_DIR) && \
	python -c "from app.core.tracing.collector import TracingCollector, TracingLevel; \
	           tracer = TracingCollector(session_id='live-session', granularity=TracingLevel.DETAILED); \
	           with open('tracer_module.py', 'w') as f: f.write('from app.core.tracing.collector import TracingCollector\\ntracer = TracingCollector(session_id=\"live-session\", granularity=TracingLevel.DETAILED)\\n')" && \
	python -m app.cli.trace_viewer live --tracer-path tracer_module.py & \
	sleep 1 && \
	python scripts/test_agent_communication.py && \
	rm tracer_module.py

# Test LangGraph with real-time visualization
test-langgraph-live:
	cd $(BACKEND_DIR) && \
	python -c "from app.core.tracing.collector import TracingCollector, TracingLevel; \
	           tracer = TracingCollector(session_id='live-session', granularity=TracingLevel.DETAILED); \
	           with open('tracer_module.py', 'w') as f: f.write('from app.core.tracing.collector import TracingCollector\\ntracer = TracingCollector(session_id=\"live-session\", granularity=TracingLevel.DETAILED)\\n')" && \
	python -m app.cli.trace_viewer live --tracer-path tracer_module.py & \
	sleep 1 && \
	python scripts/test_langgraph_tracing.py && \
	rm tracer_module.py
```

#### Step 2: Create Comprehensive Makefile for Vector Exploration

Add vector exploration commands to `makefiles/backend-cli-testing/Makefile`:

```makefile
# Vector Store Commands

# Vector store statistics
vector-stats:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer stats

# Vector store statistics with department filter
vector-stats-dept:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer stats --department $(DEPARTMENT)

# Vector store statistics with output file
vector-stats-save:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer stats --output-file data/vector_stats.json

# List documents in vector store
vector-list:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer list-documents --limit $(LIMIT)

# List documents in vector store with department filter
vector-list-dept:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer list-documents --department $(DEPARTMENT) --limit $(LIMIT)

# Search vector store
vector-search:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer search "$(QUERY)" --top-k $(TOP_K)

# Search vector store with department filter
vector-search-dept:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer search "$(QUERY)" --department $(DEPARTMENT) --top-k $(TOP_K)

# Compare queries
vector-compare:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer compare --query1 "$(QUERY1)" --query2 "$(QUERY2)" --top-k $(TOP_K)

# Visualize embeddings
vector-visualize:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer visualize --sample-size $(SAMPLE_SIZE)

# Visualize embeddings with department filter
vector-visualize-dept:
	cd $(BACKEND_DIR) && python -m app.cli.vector_explorer visualize --department $(DEPARTMENT) --sample-size $(SAMPLE_SIZE)

# Generate test embeddings
generate-test-embeddings:
	cd $(BACKEND_DIR) && python scripts/test_embedding_stats.py

# Test vector explorer
test-vector-explorer:
	cd $(BACKEND_DIR) && python scripts/test_vector_explorer.py
```

#### Step 3: Create Comprehensive Makefile for Query Analysis

Add query analysis commands to `makefiles/backend-cli-testing/Makefile`:

```makefile
# Query Analysis Commands

# Generate test queries
generate-test-queries:
	cd $(BACKEND_DIR) && python scripts/test_query_logging.py

# Query statistics
query-stats:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis statistics

# Query statistics with department filter
query-stats-dept:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis statistics --department $(DEPARTMENT)

# Query statistics with output file
query-stats-save:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis statistics --output-file data/query_stats.json

# Query performance over time
query-performance:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis performance --interval $(INTERVAL)

# Query performance over time with department filter
query-performance-dept:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis performance --interval $(INTERVAL) --department $(DEPARTMENT)

# Similar queries
query-similar:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis similar "$(QUERY)"

# Top queries
query-top:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis top --limit $(LIMIT)

# Top queries with department filter
query-top-dept:
	cd $(BACKEND_DIR) && python -m app.cli.query_analysis top --department $(DEPARTMENT) --limit $(LIMIT)
```

#### Step 4: Create Comprehensive Makefile for Notebooks

Add notebook commands to `makefiles/backend-cli-testing/Makefile`:

```makefile
# Notebook Commands

# Create notebooks
create-notebooks:
	cd $(BACKEND_DIR) && python scripts/test_notebooks.py

# Open Jupyter Lab
jupyter-lab:
	cd $(BACKEND_DIR) && jupyter lab

# Run vector visualization notebook
run-vector-viz:
	cd $(BACKEND_DIR) && jupyter nbconvert --to html --execute notebooks/vector_visualization.ipynb

# Run query playground notebook
run-query-playground:
	cd $(BACKEND_DIR) && jupyter nbconvert --to html --execute notebooks/query_playground.ipynb

# Create trace analysis notebook
create-trace-notebook:
	cd $(BACKEND_DIR) && python -c "from app.notebooks.trace_analysis import create_trace_analysis_notebook; create_trace_analysis_notebook()"

# Run trace analysis notebook
run-trace-notebook:
	cd $(BACKEND_DIR) && jupyter nbconvert --to html --execute notebooks/trace_analysis.ipynb
```

#### Step 5: Add Help Documentation to Makefile

Add help documentation to `makefiles/backend-cli-testing/Makefile`:

```makefile
# Help Command

# Show help
help:
	@echo "BusinessLM Backend CLI Testing Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make <command> [OPTION=value]"
	@echo ""
	@echo "Tracing Commands:"
	@echo "  test-with-trace QUERY=\"...\" DEPARTMENT=co-ceo    Run CLI test with tracing"
	@echo "  test-with-live-trace QUERY=\"...\" DEPARTMENT=co-ceo Run CLI test with live tracing"
	@echo "  view-trace                                      View the latest trace file"
	@echo "  view-trace-tree                                 View the latest trace file in tree format"
	@echo "  view-trace-table                                View the latest trace file in table format"
	@echo "  view-agents                                     View agent communication from the latest trace file"
	@echo "  filter-trace-node NODE_ID=...                   Filter trace by node ID"
	@echo "  filter-trace-event EVENT_TYPE=...               Filter trace by event type"
	@echo "  test-trace                                      Generate a test trace"
	@echo "  test-agents-live                                Test agents with real-time visualization"
	@echo "  test-langgraph-live                             Test LangGraph with real-time visualization"
	@echo ""
	@echo "Vector Store Commands:"
	@echo "  vector-stats                                    Vector store statistics"
	@echo "  vector-stats-dept DEPARTMENT=...                Vector store statistics with department filter"
	@echo "  vector-stats-save                               Vector store statistics with output file"
	@echo "  vector-list LIMIT=10                            List documents in vector store"
	@echo "  vector-list-dept DEPARTMENT=... LIMIT=10        List documents with department filter"
	@echo "  vector-search QUERY=\"...\" TOP_K=5               Search vector store"
	@echo "  vector-search-dept QUERY=\"...\" DEPARTMENT=... TOP_K=5 Search with department filter"
	@echo "  vector-compare QUERY1=\"...\" QUERY2=\"...\" TOP_K=5  Compare queries"
	@echo "  vector-visualize SAMPLE_SIZE=20                 Visualize embeddings"
	@echo "  vector-visualize-dept DEPARTMENT=... SAMPLE_SIZE=20 Visualize with department filter"
	@echo "  generate-test-embeddings                        Generate test embeddings"
	@echo "  test-vector-explorer                            Test vector explorer"
	@echo ""
	@echo "Query Analysis Commands:"
	@echo "  generate-test-queries                           Generate test queries"
	@echo "  query-stats                                     Query statistics"
	@echo "  query-stats-dept DEPARTMENT=...                 Query statistics with department filter"
	@echo "  query-stats-save                                Query statistics with output file"
	@echo "  query-performance INTERVAL=day                  Query performance over time"
	@echo "  query-performance-dept INTERVAL=day DEPARTMENT=... Query performance with department filter"
	@echo "  query-similar QUERY=\"...\"                       Find similar queries"
	@echo "  query-top LIMIT=10                              Top queries"
	@echo "  query-top-dept DEPARTMENT=... LIMIT=10          Top queries with department filter"
	@echo ""
	@echo "Notebook Commands:"
	@echo "  create-notebooks                                Create notebooks"
	@echo "  jupyter-lab                                     Open Jupyter Lab"
	@echo "  run-vector-viz                                  Run vector visualization notebook"
	@echo "  run-query-playground                            Run query playground notebook"
	@echo "  create-trace-notebook                           Create trace analysis notebook"
	@echo "  run-trace-notebook                              Run trace analysis notebook"
	@echo ""
	@echo "Examples:"
	@echo "  make test-with-trace QUERY=\"What is our marketing budget?\" DEPARTMENT=co-ceo"
	@echo "  make vector-search QUERY=\"financial performance\" TOP_K=5"
	@echo "  make query-similar QUERY=\"marketing strategy\""
```

#### Step 6: Create Documentation for Makefile Commands

Create a documentation file for Makefile commands in `backend/docs/cli/makefile-commands.md`:

```markdown
# Makefile Commands for CLI Testing

This document provides detailed information about the Makefile commands available for CLI testing, tracing, and visualization.

## Table of Contents

1. [Tracing Commands](#tracing-commands)
2. [Vector Store Commands](#vector-store-commands)
3. [Query Analysis Commands](#query-analysis-commands)
4. [Notebook Commands](#notebook-commands)
5. [Examples](#examples)

## Tracing Commands

### Basic Tracing

| Command | Description | Options |
|---------|-------------|---------|
| `test-with-trace` | Run CLI test with tracing | `QUERY`, `DEPARTMENT` |
| `test-with-live-trace` | Run CLI test with live tracing | `QUERY`, `DEPARTMENT` |
| `view-trace` | View the latest trace file | None |
| `view-trace-tree` | View the latest trace file in tree format | None |
| `view-trace-table` | View the latest trace file in table format | None |
| `view-agents` | View agent communication from the latest trace file | None |
| `filter-trace-node` | Filter trace by node ID | `NODE_ID` |
| `filter-trace-event` | Filter trace by event type | `EVENT_TYPE` |
| `test-trace` | Generate a test trace | None |

### Real-Time Visualization

| Command | Description | Options |
|---------|-------------|---------|
| `test-agents-live` | Test agents with real-time visualization | None |
| `test-langgraph-live` | Test LangGraph with real-time visualization | None |

## Vector Store Commands

### Statistics and Exploration

| Command | Description | Options |
|---------|-------------|---------|
| `vector-stats` | Vector store statistics | None |
| `vector-stats-dept` | Vector store statistics with department filter | `DEPARTMENT` |
| `vector-stats-save` | Vector store statistics with output file | None |
| `vector-list` | List documents in vector store | `LIMIT` |
| `vector-list-dept` | List documents with department filter | `DEPARTMENT`, `LIMIT` |

### Search and Comparison

| Command | Description | Options |
|---------|-------------|---------|
| `vector-search` | Search vector store | `QUERY`, `TOP_K` |
| `vector-search-dept` | Search with department filter | `QUERY`, `DEPARTMENT`, `TOP_K` |
| `vector-compare` | Compare queries | `QUERY1`, `QUERY2`, `TOP_K` |

### Visualization and Testing

| Command | Description | Options |
|---------|-------------|---------|
| `vector-visualize` | Visualize embeddings | `SAMPLE_SIZE` |
| `vector-visualize-dept` | Visualize with department filter | `DEPARTMENT`, `SAMPLE_SIZE` |
| `generate-test-embeddings` | Generate test embeddings | None |
| `test-vector-explorer` | Test vector explorer | None |

## Query Analysis Commands

### Statistics and Performance

| Command | Description | Options |
|---------|-------------|---------|
| `generate-test-queries` | Generate test queries | None |
| `query-stats` | Query statistics | None |
| `query-stats-dept` | Query statistics with department filter | `DEPARTMENT` |
| `query-stats-save` | Query statistics with output file | None |
| `query-performance` | Query performance over time | `INTERVAL` |
| `query-performance-dept` | Query performance with department filter | `INTERVAL`, `DEPARTMENT` |

### Query Analysis

| Command | Description | Options |
|---------|-------------|---------|
| `query-similar` | Find similar queries | `QUERY` |
| `query-top` | Top queries | `LIMIT` |
| `query-top-dept` | Top queries with department filter | `DEPARTMENT`, `LIMIT` |

## Notebook Commands

| Command | Description | Options |
|---------|-------------|---------|
| `create-notebooks` | Create notebooks | None |
| `jupyter-lab` | Open Jupyter Lab | None |
| `run-vector-viz` | Run vector visualization notebook | None |
| `run-query-playground` | Run query playground notebook | None |
| `create-trace-notebook` | Create trace analysis notebook | None |
| `run-trace-notebook` | Run trace analysis notebook | None |

## Examples

### Tracing Examples

```bash
# Run CLI test with tracing
make test-with-trace QUERY="What is our marketing budget?" DEPARTMENT=co-ceo

# View the latest trace file
make view-trace

# View agent communication
make view-agents

# Filter trace by node ID
make filter-trace-node NODE_ID=co_ceo_agent

# Test agents with real-time visualization
make test-agents-live
```

### Vector Store Examples

```bash
# Get vector store statistics
make vector-stats

# List documents in vector store
make vector-list LIMIT=5

# Search vector store
make vector-search QUERY="financial performance" TOP_K=5

# Compare queries
make vector-compare QUERY1="financial performance" QUERY2="marketing budget" TOP_K=3

# Visualize embeddings
make vector-visualize SAMPLE_SIZE=20
```

### Query Analysis Examples

```bash
# Generate test queries
make generate-test-queries

# Get query statistics
make query-stats

# Get query performance over time
make query-performance INTERVAL=day

# Find similar queries
make query-similar QUERY="marketing strategy"

# Get top queries
make query-top LIMIT=10
```

### Notebook Examples

```bash
# Create notebooks
make create-notebooks

# Open Jupyter Lab
make jupyter-lab

# Run vector visualization notebook
make run-vector-viz

# Run query playground notebook
make run-query-playground
```
```

#### Step 7: Test Makefile Integration

Test the Makefile commands:

```bash
cd makefiles/backend-cli-testing

# Test help command
make help

# Test tracing commands
make test-trace
make view-trace
make view-agents

# Test vector store commands
make generate-test-embeddings
make vector-stats
make vector-list LIMIT=5
make vector-search QUERY="financial performance" TOP_K=3

# Test query analysis commands
make generate-test-queries
make query-stats
make query-similar QUERY="marketing strategy"

# Test notebook commands
make create-notebooks
```

### 7.2 End-to-End Testing

#### Step 1: Create Test Suite Structure

Create the basic structure for end-to-end tests:

```bash
# Create test directories
mkdir -p tests/integration
touch tests/integration/__init__.py
touch tests/integration/test_visualization.py
```

Add basic imports and setup to `tests/integration/test_visualization.py`:

```python
"""End-to-end tests for visualization features."""

import asyncio
import json
import os
import tempfile
import unittest
from datetime import datetime
from typing import Any, Dict, List, Optional

import pytest

from app.core.tracing.collector import TracingCollector, TracingLevel
from app.core.tracing.utils import save_traces
from app.db.session import async_session
from app.models.document import Document
from app.rag.embedding_stats import get_embedding_stats
from app.rag.query_logging import get_query_logs, log_query
```

#### Step 2: Implement Multi-Agent Orchestration Tests

Add multi-agent orchestration test cases to `tests/integration/test_visualization.py`:

```python
@pytest.mark.asyncio
async def test_agent_orchestration_tracing():
    """Test tracing with multi-agent orchestration."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create mock agents
    from app.agents.base import BaseAgent

    class MockAgent(BaseAgent):
        async def _process_message(self, message: str, context: Optional[Dict] = None) -> str:
            return f"Response from {self.agent_id}: {message}"

    # Create agents
    co_ceo_agent = MockAgent(agent_id="co-ceo", tracer=tracer)
    finance_agent = MockAgent(agent_id="finance", tracer=tracer)
    marketing_agent = MockAgent(agent_id="marketing", tracer=tracer)

    # Simulate agent communication
    await co_ceo_agent.send_message(
        recipient="finance",
        content="What is our financial performance?",
        context={"query_id": "test-query"}
    )

    await finance_agent.process_message(
        message="What is our financial performance?",
        sender="co-ceo",
        context={"query_id": "test-query"}
    )

    await finance_agent.send_message(
        recipient="co-ceo",
        content="Our financial performance is strong with 15% growth.",
        context={"query_id": "test-query"}
    )

    await co_ceo_agent.send_message(
        recipient="marketing",
        content="What is our marketing strategy?",
        context={"query_id": "test-query"}
    )

    await marketing_agent.process_message(
        message="What is our marketing strategy?",
        sender="co-ceo",
        context={"query_id": "test-query"}
    )

    await marketing_agent.send_message(
        recipient="co-ceo",
        content="Our marketing strategy focuses on digital channels.",
        context={"query_id": "test-query"}
    )

    # Verify traces were collected
    agent_messages = [t for t in tracer.traces if t["event_type"] == "agent_message"]
    assert len(agent_messages) >= 6

    # Verify sender and recipient information
    senders = [m["metadata"]["sender"] for m in agent_messages]
    recipients = [m["metadata"]["recipient"] for m in agent_messages]

    assert "CO-CEO" in senders
    assert "FINANCE" in senders
    assert "MARKETING" in senders
    assert "CO-CEO" in recipients
    assert "FINANCE" in recipients
    assert "MARKETING" in recipients

    # Save traces to a temporary file
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as temp_file:
        file_path = temp_file.name
        tracer.export_json(file_path)

    try:
        # Import visualization module
        from app.cli.tracing import visualize_agent_communication

        # Visualize agent communication (this should not raise exceptions)
        visualize_agent_communication(file_path, show_content=False)

        # Clean up
        os.unlink(file_path)
    except Exception as e:
        # Clean up even if test fails
        if os.path.exists(file_path):
            os.unlink(file_path)
        raise

@pytest.mark.asyncio
async def test_langgraph_tracing():
    """Test tracing with LangGraph."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create a simple state class
    from pydantic import BaseModel

    class TestState(BaseModel):
        query: str
        department: Optional[str] = None
        response: Optional[str] = None

    # Create simple node functions
    async def analyze_query(state: TestState, tracer: Optional[TracingCollector] = None):
        if tracer:
            tracer.add_trace(
                node_id="analyze_query",
                event_type="node_start",
                metadata={"query": state.query}
            )

        # Determine department
        if "finance" in state.query.lower():
            state.department = "finance"
        elif "marketing" in state.query.lower():
            state.department = "marketing"
        else:
            state.department = "general"

        if tracer:
            tracer.add_trace(
                node_id="analyze_query",
                event_type="node_end",
                metadata={"department": state.department}
            )

        return state

    async def generate_response(state: TestState, tracer: Optional[TracingCollector] = None):
        if tracer:
            tracer.add_trace(
                node_id="generate_response",
                event_type="node_start",
                metadata={"department": state.department}
            )

        # Generate response
        state.response = f"Response for {state.query} from {state.department} department"

        if tracer:
            tracer.add_trace(
                node_id="generate_response",
                event_type="node_end",
                metadata={"response": state.response}
            )

        return state

    # Create a simple graph
    from langgraph.graph import StateGraph

    # Create a graph
    builder = StateGraph(TestState)

    # Add nodes
    builder.add_node("analyze_query", analyze_query)
    builder.add_node("generate_response", generate_response)

    # Add edges
    builder.add_edge("analyze_query", "generate_response")
    builder.add_edge("generate_response", "END")

    # Set entry point
    builder.set_entry_point("analyze_query")

    # Compile the graph
    graph = builder.compile()

    # Create initial state
    state = TestState(query="What is our financial performance?")

    # Execute the graph
    result = await graph.ainvoke(state, {"tracer": tracer})

    # Verify traces were collected
    assert len(tracer.traces) >= 4

    # Verify node execution was traced
    node_starts = [t for t in tracer.traces if t["event_type"] == "node_start"]
    node_ends = [t for t in tracer.traces if t["event_type"] == "node_end"]

    assert len(node_starts) >= 2
    assert len(node_ends) >= 2

    # Verify result
    assert result.department == "finance"
    assert "financial performance" in result.response
```

#### Step 3: Implement RAG Pipeline Tests

Add RAG pipeline test cases to `tests/integration/test_visualization.py`:

```python
@pytest.mark.asyncio
async def test_rag_pipeline_query_logging():
    """Test query logging with RAG pipeline."""
    # Generate a unique user ID for testing
    import uuid
    test_user_id = str(uuid.uuid4())

    # Create a mock RAG retriever
    class MockRAGRetriever:
        async def retrieve(
            self,
            query: str,
            department: Optional[str] = None,
            user_id: Optional[str] = None,
            thread_id: Optional[str] = None,
            session_id: Optional[str] = None,
            metadata_filter: Optional[Dict[str, Any]] = None,
        ) -> List[Dict[str, Any]]:
            # Import query logging
            from app.rag.query_logging import log_query

            # Generate mock results
            results = [
                {
                    "id": f"doc-{i}",
                    "content": f"Content for {query}",
                    "metadata": {
                        "department": department,
                        "title": f"Document {i+1}",
                    },
                    "score": 0.9 - (i * 0.1),
                }
                for i in range(3)
            ]

            # Log the query
            await log_query(
                query_text=query,
                user_id=user_id,
                thread_id=thread_id,
                session_id=session_id,
                department=department,
                results=results,
                execution_time_ms=100,
                metadata={
                    "metadata_filter": metadata_filter,
                }
            )

            return results

    # Create retriever
    retriever = MockRAGRetriever()

    # Execute queries
    queries = [
        "What is our financial performance?",
        "What is our marketing budget?",
        "What are our sales targets?",
    ]

    departments = ["finance", "marketing", "sales"]

    for i, (query, department) in enumerate(zip(queries, departments)):
        results = await retriever.retrieve(
            query=query,
            department=department,
            user_id=test_user_id,
            thread_id=f"thread-{i}",
            session_id=f"session-{i}",
        )

        # Verify results
        assert len(results) == 3
        assert results[0]["score"] > results[1]["score"]
        assert results[1]["score"] > results[2]["score"]

    try:
        # Get query logs
        logs = await get_query_logs(user_id=test_user_id)

        # Verify logs
        assert len(logs) == 3

        # Verify query texts
        query_texts = [log["query_text"] for log in logs]
        for query in queries:
            assert query in query_texts

        # Verify departments
        log_departments = [log["department"] for log in logs]
        for department in departments:
            assert department in log_departments

        # Import query analysis
        from app.rag.query_analysis import get_query_statistics

        # Get query statistics
        stats = await get_query_statistics(user_id=test_user_id)

        # Verify statistics
        assert stats["query_count"] == 3
        assert "departments" in stats
        for department in departments:
            assert department in stats["departments"]
    finally:
        # Clean up test logs
        from app.rag.query_logging import delete_query_logs
        await delete_query_logs(user_id=test_user_id)

@pytest.mark.asyncio
async def test_rag_pipeline_tracing():
    """Test tracing with RAG pipeline."""
    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Create a mock RAG retriever
    class MockRAGRetriever:
        def __init__(self, tracer: Optional[TracingCollector] = None):
            self.tracer = tracer

        async def retrieve(
            self,
            query: str,
            department: Optional[str] = None,
        ) -> List[Dict[str, Any]]:
            # Add trace for retrieval start
            if self.tracer:
                self.tracer.add_trace(
                    node_id="rag_retriever",
                    event_type="rag_retrieval_start",
                    metadata={
                        "query": query,
                        "department": department,
                    }
                )

            # Generate mock results
            results = [
                {
                    "id": f"doc-{i}",
                    "content": f"Content for {query}",
                    "metadata": {
                        "department": department,
                        "title": f"Document {i+1}",
                    },
                    "score": 0.9 - (i * 0.1),
                }
                for i in range(3)
            ]

            # Add trace for retrieval end
            if self.tracer:
                self.tracer.add_trace(
                    node_id="rag_retriever",
                    event_type="rag_retrieval_end",
                    metadata={
                        "query": query,
                        "department": department,
                        "document_count": len(results),
                        "document_ids": [doc["id"] for doc in results],
                        "similarity_scores": [doc["score"] for doc in results],
                    }
                )

            return results

    # Create retriever
    retriever = MockRAGRetriever(tracer=tracer)

    # Execute queries
    queries = [
        "What is our financial performance?",
        "What is our marketing budget?",
        "What are our sales targets?",
    ]

    departments = ["finance", "marketing", "sales"]

    for query, department in zip(queries, departments):
        results = await retriever.retrieve(
            query=query,
            department=department,
        )

        # Verify results
        assert len(results) == 3

    # Verify traces were collected
    rag_traces = [t for t in tracer.traces if "rag_retrieval" in t["event_type"]]
    assert len(rag_traces) == 6  # 2 traces per query (start and end)

    # Verify trace metadata
    for trace in rag_traces:
        assert "query" in trace["metadata"]
        assert trace["metadata"]["department"] in departments

        if trace["event_type"] == "rag_retrieval_end":
            assert "document_count" in trace["metadata"]
            assert "document_ids" in trace["metadata"]
            assert "similarity_scores" in trace["metadata"]
```

#### Step 4: Implement Vector Visualization Tests

Add vector visualization test cases to `tests/integration/test_visualization.py`:

```python
@pytest.mark.asyncio
async def test_vector_visualization():
    """Test vector visualization with sample data."""
    # Create sample embeddings
    import numpy as np

    # Create 20 random embeddings
    embeddings = [np.random.normal(0, 1, 10).tolist() for _ in range(20)]

    # Create metadata
    metadata = [
        {
            "id": f"doc-{i}",
            "department": "finance" if i < 10 else "marketing",
            "title": f"Document {i}",
            "content": f"This is document {i}",
        }
        for i in range(20)
    ]

    # Import visualization functions
    from app.notebooks.vector_visualization import (
        reduce_dimensions,
        create_embedding_dataframe,
        plot_embeddings_2d,
    )

    # Reduce dimensions
    embeddings_2d = reduce_dimensions(embeddings, method="pca", n_components=2)

    # Create DataFrame
    df = create_embedding_dataframe(embeddings_2d, metadata, label_key="department")

    # Verify DataFrame
    assert len(df) == 20
    assert "dim_1" in df.columns
    assert "dim_2" in df.columns
    assert "department" in df.columns
    assert "id" in df.columns

    # Test visualization (this should not raise exceptions)
    try:
        import matplotlib
        matplotlib.use("Agg")  # Use non-interactive backend for testing

        # Create visualization
        plot_embeddings_2d(df, label_col="department")
    except Exception as e:
        pytest.skip(f"Visualization test failed: {e}")

@pytest.mark.asyncio
async def test_query_embedding_visualization():
    """Test query embedding visualization."""
    # Create sample embeddings
    import numpy as np

    # Create 20 random embeddings
    embeddings = [np.random.normal(0, 1, 10).tolist() for _ in range(20)]

    # Create metadata
    metadata = [
        {
            "id": f"doc-{i}",
            "department": "finance" if i < 10 else "marketing",
            "title": f"Document {i}",
            "content": f"This is document {i}",
        }
        for i in range(20)
    ]

    # Create a mock query embedding function
    async def mock_get_query_embedding(query: str):
        return np.random.normal(0, 1, 10).tolist()

    # Create a mock search function
    async def mock_search_similar_embeddings(
        query_embedding,
        embeddings,
        metadata,
        top_k=5,
    ):
        # Return random results
        import random
        indices = random.sample(range(len(embeddings)), min(top_k, len(embeddings)))

        results = []
        for i in indices:
            result = dict(metadata[i])
            result["score"] = random.random()
            results.append(result)

        return results

    # Import visualization functions
    from app.notebooks.vector_visualization import (
        reduce_dimensions,
        create_embedding_dataframe,
    )

    # Mock the necessary functions
    import unittest.mock as mock
    with mock.patch("app.notebooks.vector_visualization.get_query_embedding", mock_get_query_embedding), \
         mock.patch("app.notebooks.vector_visualization.search_similar_embeddings", mock_search_similar_embeddings):

        # Import the visualization function
        from app.notebooks.vector_visualization import create_query_embedding_visualization

        # Test visualization (this should not raise exceptions)
        try:
            import matplotlib
            matplotlib.use("Agg")  # Use non-interactive backend for testing

            # Create visualization
            df, results = await create_query_embedding_visualization(
                query="test query",
                embeddings=embeddings,
                metadata=metadata,
                method="pca",
                n_components=2,
                label_key="department",
                top_k=5
            )

            # Verify results
            assert len(results) <= 5
            assert "is_similar" in df.columns
            assert df["is_similar"].sum() <= 6  # 5 similar docs + 1 query
        except Exception as e:
            pytest.skip(f"Visualization test failed: {e}")
```

#### Step 5: Create Test Data Generation Utilities

Add test data generation utilities to `tests/integration/test_visualization.py`:

```python
def generate_test_traces(count: int = 10) -> List[Dict[str, Any]]:
    """Generate test traces.

    Args:
        count: Number of traces to generate

    Returns:
        List of trace events
    """
    import random

    # Create a tracing collector
    tracer = TracingCollector(
        session_id="test-session",
        user_id="test-user",
        thread_id="test-thread",
        granularity=TracingLevel.DETAILED
    )

    # Node IDs
    node_ids = ["analyze_query", "retrieve_knowledge", "generate_response", "co_ceo_agent", "finance_agent", "marketing_agent"]

    # Event types
    event_types = ["node_start", "node_end", "state_update", "decision", "llm_call", "rag_retrieval", "agent_message"]

    # Generate traces
    for i in range(count):
        # Select node ID and event type
        node_id = random.choice(node_ids)
        event_type = random.choice(event_types)

        # Create metadata
        metadata = {"timestamp": datetime.now().isoformat()}

        if event_type == "node_start":
            metadata["function"] = f"{node_id}_function"
        elif event_type == "node_end":
            metadata["duration_ms"] = random.randint(50, 500)
        elif event_type == "llm_call":
            metadata["model"] = random.choice(["gpt-4", "claude-3-opus", "gemini-pro"])
            metadata["tokens"] = random.randint(100, 1000)
        elif event_type == "rag_retrieval":
            metadata["document_count"] = random.randint(1, 10)
            metadata["similarity_scores"] = [random.random() for _ in range(metadata["document_count"])]
        elif event_type == "agent_message":
            metadata["sender"] = node_id.upper().replace("_AGENT", "")
            metadata["recipient"] = random.choice(["USER", "CO-CEO", "FINANCE", "MARKETING"])
            metadata["content"] = f"Message from {metadata['sender']} to {metadata['recipient']}"

        # Add trace
        tracer.add_trace(
            node_id=node_id,
            event_type=event_type,
            metadata=metadata
        )

    return tracer.traces

async def generate_test_embeddings(count: int = 10, dimensions: int = 10) -> List[Document]:
    """Generate test embeddings.

    Args:
        count: Number of embeddings to generate
        dimensions: Dimensions of embeddings

    Returns:
        List of Document objects
    """
    import numpy as np

    # Departments
    departments = ["finance", "marketing", "hr", "sales"]

    # Generate documents
    documents = []
    for i in range(count):
        # Create a random embedding
        embedding = np.random.normal(0, 1, dimensions).tolist()

        # Create metadata
        metadata = {
            "department": departments[i % len(departments)],
            "title": f"Test Document {i}",
            "source": "test"
        }

        # Create document
        document = Document(
            id=f"test-doc-{i}",
            content=f"This is test document {i} for the {metadata['department']} department.",
            embedding=embedding,
            metadata=metadata
        )

        documents.append(document)

    return documents

async def generate_test_queries(count: int = 10) -> List[str]:
    """Generate test queries.

    Args:
        count: Number of queries to generate

    Returns:
        List of query IDs
    """
    import random
    import uuid

    # Generate a unique user ID for testing
    test_user_id = str(uuid.uuid4())

    # Query templates
    query_templates = [
        "What is our {department} budget?",
        "How is our {department} performance?",
        "What are the {department} goals for next quarter?",
        "Who is responsible for {department}?",
        "What is the status of the {department} project?"
    ]

    # Departments
    departments = ["finance", "marketing", "hr", "sales"]

    # Generate queries
    query_ids = []
    for i in range(count):
        # Select template and department
        template = random.choice(query_templates)
        department = random.choice(departments)

        # Create query
        query_text = template.format(department=department)

        # Log query
        query_id = await log_query(
            query_text=query_text,
            user_id=test_user_id,
            thread_id=f"thread-{i}",
            session_id=f"session-{i}",
            department=department,
            results=[{"id": f"doc-{j}", "score": 0.9 - (j * 0.1)} for j in range(3)],
            execution_time_ms=100 + (i * 10),
            metadata={"test": "data"}
        )

        query_ids.append(str(query_id))

    return query_ids
```

#### Step 6: Create Test Script

Create a test script in `backend/scripts/run_integration_tests.py`:

```python
"""Run integration tests for visualization features."""

import os
import subprocess
import sys

def run_tests():
    """Run integration tests."""
    # Change to backend directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Run tests
    result = subprocess.run(
        [sys.executable, "-m", "pytest", "tests/integration/test_visualization.py", "-v"],
        check=False
    )

    return result.returncode

if __name__ == "__main__":
    sys.exit(run_tests())
```

#### Step 7: Update Makefile for End-to-End Testing

Add end-to-end testing commands to `makefiles/backend-cli-testing/Makefile`:

```makefile
# End-to-End Testing Commands

# Run all integration tests
test-integration:
	cd $(BACKEND_DIR) && python scripts/run_integration_tests.py

# Run multi-agent orchestration tests
test-agent-orchestration:
	cd $(BACKEND_DIR) && python -m pytest tests/integration/test_visualization.py::test_agent_orchestration_tracing tests/integration/test_visualization.py::test_langgraph_tracing -v

# Run RAG pipeline tests
test-rag-pipeline:
	cd $(BACKEND_DIR) && python -m pytest tests/integration/test_visualization.py::test_rag_pipeline_query_logging tests/integration/test_visualization.py::test_rag_pipeline_tracing -v

# Run vector visualization tests
test-vector-visualization:
	cd $(BACKEND_DIR) && python -m pytest tests/integration/test_visualization.py::test_vector_visualization tests/integration/test_visualization.py::test_query_embedding_visualization -v
```

#### Step 8: Run End-to-End Tests

Run the end-to-end tests:

```bash
cd makefiles/backend-cli-testing

# Run all integration tests
make test-integration

# Run specific test groups
make test-agent-orchestration
make test-rag-pipeline
make test-vector-visualization
```

### 7.3 Documentation Finalization

#### Step 1: Add Visual Elements to Documentation

Create diagrams and screenshots to enhance documentation:

```bash
# Create directory for documentation images
mkdir -p backend/docs/images
```

Create a system architecture diagram for `backend/docs/images/tracing-architecture.png`:

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  User Interface     │     │  Agent Orchestration│     │  RAG Pipeline       │
│                     │     │                     │     │                     │
└─────────┬───────────┘     └─────────┬───────────┘     └─────────┬───────────┘
          │                           │                           │
          │                           │                           │
          ▼                           ▼                           ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                          Tracing Collector                                  │
│                                                                             │
└─────────┬─────────────────────────────┬─────────────────────────────┬───────┘
          │                             │                             │
          │                             │                             │
          ▼                             ▼                             ▼
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  JSON Trace Files   │     │  PostgreSQL DB      │     │  Real-time Stream   │
│                     │     │                     │     │                     │
└─────────────────────┘     └─────────────────────┘     └─────────────────────┘
          │                             │                             │
          │                             │                             │
          ▼                             ▼                             ▼
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  CLI Visualization  │     │  Jupyter Notebooks  │     │  Live Dashboard     │
│                     │     │                     │     │                     │
└─────────────────────┘     └─────────────────────┘     └─────────────────────┘
```

Create a flow diagram for `backend/docs/images/tracing-flow.png`:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Initialize │     │  Configure  │     │  Collect    │     │  Export     │
│  Tracer     │────▶│  Granularity│────▶│  Traces     │────▶│  Traces     │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                   │
                                                                   │
                                                                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Analyze    │◀────│  Load       │◀────│  Filter     │◀────│  Store      │
│  Traces     │     │  Traces     │     │  Traces     │     │  Traces     │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
       │
       │
       ▼
┌─────────────┐     ┌─────────────┐
│             │     │             │
│  Visualize  │────▶│  Debug &    │
│  Traces     │     │  Optimize   │
│             │     │             │
└─────────────┘     └─────────────┘
```

Update `backend/docs/tracing/tracing-overview.md` with visual elements:

```markdown
# Tracing System Overview

## Architecture

The tracing system provides comprehensive observability across the entire application:

![Tracing Architecture](../images/tracing-architecture.png)

## Tracing Flow

The typical flow for using the tracing system:

![Tracing Flow](../images/tracing-flow.png)

## Key Components

1. **TracingCollector**: Central component for collecting trace events
2. **Trace Storage**: JSON files and PostgreSQL database
3. **Visualization Tools**: CLI, Jupyter notebooks, and real-time dashboard
```

#### Step 2: Add Section Numbering and Key Takeaways

Update `backend/docs/tracing/tracing-overview.md` with section numbering and key takeaways:

```markdown
# 1. Tracing System Overview

## 1.1 Architecture

The tracing system provides comprehensive observability across the entire application:

![Tracing Architecture](../images/tracing-architecture.png)

> **Key Takeaway**: The tracing system integrates with all major components of the application, providing a unified view of system behavior.

## 1.2 Tracing Flow

The typical flow for using the tracing system:

![Tracing Flow](../images/tracing-flow.png)

> **Key Takeaway**: Tracing follows a consistent lifecycle from initialization to visualization, making it easy to integrate into different parts of the application.

## 1.3 Key Components

1. **TracingCollector**: Central component for collecting trace events
2. **Trace Storage**: JSON files and PostgreSQL database
3. **Visualization Tools**: CLI, Jupyter notebooks, and real-time dashboard

> **Key Takeaway**: The modular design allows for flexible integration and multiple visualization options.

## 1.4 Benefits

1. **Debugging**: Quickly identify issues in complex workflows
2. **Performance Optimization**: Identify bottlenecks and optimization opportunities
3. **Observability**: Gain insights into system behavior and interactions
4. **Documentation**: Generate documentation from actual system behavior

> **Key Takeaway**: Tracing provides value across the entire development lifecycle, from debugging to documentation.
```

Update `backend/docs/vector-store/vector-visualization.md` with section numbering and key takeaways:

```markdown
# 1. Vector Store Visualization

## 1.1 Overview

Vector visualization provides insights into the embedding space and helps understand document relationships.

> **Key Takeaway**: Visualizing embeddings helps identify clusters, outliers, and relationships between documents.

## 1.2 Visualization Methods

1. **2D Projection**: Using PCA or t-SNE to visualize in 2D
2. **3D Projection**: Using PCA or t-SNE to visualize in 3D
3. **Interactive Visualization**: Using Plotly for interactive exploration
4. **Query Visualization**: Visualizing query results in the embedding space

> **Key Takeaway**: Different visualization methods provide complementary views of the embedding space.

## 1.3 Tools

1. **CLI Tools**: Command-line tools for quick visualization
2. **Jupyter Notebooks**: Interactive notebooks for in-depth analysis
3. **Vector Explorer**: Web-based tool for exploring the vector space

> **Key Takeaway**: Multiple tools are available for different use cases and user preferences.
```

#### Step 3: Expand Troubleshooting Sections

Create a new troubleshooting document in `backend/docs/tracing/troubleshooting.md`:

```markdown
# Tracing System Troubleshooting

This document provides solutions for common issues with the tracing system.

## Common Issues

### 1. Traces Not Being Collected

**Symptoms**:
- No trace files are being generated
- Trace files are empty
- Visualization shows no data

**Possible Causes**:
1. Tracing is not enabled
2. TracingCollector is not properly initialized
3. Tracing granularity is set too high

**Solutions**:
1. Check that `ENABLE_TRACING=1` is set in the environment
2. Verify that TracingCollector is initialized with the correct parameters
3. Set tracing granularity to `TracingLevel.DETAILED` for maximum visibility

```bash
# Enable tracing
export ENABLE_TRACING=1

# Check if tracing is enabled
echo $ENABLE_TRACING

# Run with explicit tracing flag
python scripts/test_backend_cli.py --query "test query" --trace
```

### 2. Performance Impact

**Symptoms**:
- System is slower when tracing is enabled
- High CPU or memory usage

**Possible Causes**:
1. Too many trace events being collected
2. Trace files growing too large
3. Real-time visualization overhead

**Solutions**:
1. Adjust tracing granularity to reduce the number of events
2. Implement trace sampling for high-volume components
3. Use async trace collection to minimize performance impact

```python
# Use a more selective tracing level
tracer = TracingCollector(
    session_id="test-session",
    granularity=TracingLevel.BASIC  # Less detailed tracing
)

# Implement sampling
if random.random() < 0.1:  # Only trace 10% of events
    tracer.add_trace(...)
```

### 3. Visualization Issues

**Symptoms**:
- CLI visualization is garbled
- Jupyter notebooks fail to render visualizations
- Real-time dashboard not updating

**Possible Causes**:
1. Terminal doesn't support rich formatting
2. Missing visualization dependencies
3. Trace format issues

**Solutions**:
1. Use `--format plain` for terminals with limited formatting support
2. Install required dependencies: `pip install rich plotly matplotlib networkx`
3. Validate trace format with the trace validation tool

```bash
# Use plain format for terminals with limited support
python -m app.cli.trace_viewer view --latest --format plain

# Check trace format
python -m app.cli.trace_viewer validate --latest
```

## Advanced Troubleshooting

### Debugging the Tracer

For advanced debugging of the tracing system itself:

```python
import logging

# Enable debug logging for the tracing module
logging.getLogger("app.core.tracing").setLevel(logging.DEBUG)

# Create a tracer with debug mode
tracer = TracingCollector(
    session_id="debug-session",
    debug=True  # Enables additional debug output
)
```

### Performance Profiling

To profile the performance impact of tracing:

```python
import cProfile

# Profile the tracing overhead
def profile_tracing():
    tracer = TracingCollector(session_id="profile-session")

    cProfile.runctx(
        "for i in range(1000): tracer.add_trace(node_id='test', event_type='test')",
        globals(),
        locals(),
        "tracing_profile.prof"
    )

# Run the profiling
profile_tracing()

# Analyze the results
# python -m pstats tracing_profile.prof
```

## Getting Help

If you continue to experience issues:

1. Check the logs for error messages
2. Run with `--debug` flag for additional information
3. Contact the development team with the trace files and error logs
```

#### Step 4: Add Cross-References

Update `backend/docs/tracing/tracing-overview.md` with cross-references:

```markdown
# 1. Tracing System Overview

## 1.1 Architecture

The tracing system provides comprehensive observability across the entire application:

![Tracing Architecture](../images/tracing-architecture.png)

> **Key Takeaway**: The tracing system integrates with all major components of the application, providing a unified view of system behavior.

For details on integration with LangGraph, see [LangGraph Integration](langgraph-integration.md).

## 1.2 Tracing Flow

The typical flow for using the tracing system:

![Tracing Flow](../images/tracing-flow.png)

> **Key Takeaway**: Tracing follows a consistent lifecycle from initialization to visualization, making it easy to integrate into different parts of the application.

For implementation details, see [Tracing Implementation](tracing-implementation.md).

## 1.3 Key Components

1. **TracingCollector**: Central component for collecting trace events
2. **Trace Storage**: JSON files and PostgreSQL database
3. **Visualization Tools**: CLI, Jupyter notebooks, and real-time dashboard

> **Key Takeaway**: The modular design allows for flexible integration and multiple visualization options.

For CLI usage, see [CLI Visualization](cli-visualization.md).
For Jupyter notebook usage, see [Jupyter Integration](../jupyter/trace-analysis.md).

## 1.4 Benefits

1. **Debugging**: Quickly identify issues in complex workflows
2. **Performance Optimization**: Identify bottlenecks and optimization opportunities
3. **Observability**: Gain insights into system behavior and interactions
4. **Documentation**: Generate documentation from actual system behavior

> **Key Takeaway**: Tracing provides value across the entire development lifecycle, from debugging to documentation.

For troubleshooting, see [Troubleshooting Guide](troubleshooting.md).
```

Update `backend/docs/vector-store/vector-visualization.md` with cross-references:

```markdown
# 1. Vector Store Visualization

## 1.1 Overview

Vector visualization provides insights into the embedding space and helps understand document relationships.

> **Key Takeaway**: Visualizing embeddings helps identify clusters, outliers, and relationships between documents.

For background on vector embeddings, see [Vector Store Overview](vector-store-overview.md).

## 1.2 Visualization Methods

1. **2D Projection**: Using PCA or t-SNE to visualize in 2D
2. **3D Projection**: Using PCA or t-SNE to visualize in 3D
3. **Interactive Visualization**: Using Plotly for interactive exploration
4. **Query Visualization**: Visualizing query results in the embedding space

> **Key Takeaway**: Different visualization methods provide complementary views of the embedding space.

For implementation details, see [Dimensionality Reduction](dimensionality-reduction.md).

## 1.3 Tools

1. **CLI Tools**: Command-line tools for quick visualization
2. **Jupyter Notebooks**: Interactive notebooks for in-depth analysis
3. **Vector Explorer**: Web-based tool for exploring the vector space

> **Key Takeaway**: Multiple tools are available for different use cases and user preferences.

For CLI usage, see [Vector CLI Tools](vector-cli-tools.md).
For Jupyter notebook usage, see [Vector Notebooks](../jupyter/vector-visualization.md).
```

## 8. Phase 6: Optimization and Refinement

### 8.1 Performance Optimization

#### Step 1: Profile Tracing System

Create a profiling script in `backend/scripts/profile_tracing.py`:

```python
"""Profile the tracing system performance."""

import cProfile
import pstats
import io
import time
import random
from datetime import datetime
import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.tracing.collector import TracingCollector, TracingLevel


def profile_trace_collection(num_traces=1000, granularity=TracingLevel.DETAILED):
    """Profile trace collection performance.

    Args:
        num_traces: Number of traces to collect
        granularity: Tracing granularity level
    """
    # Create a tracing collector
    tracer = TracingCollector(
        session_id=f"profile-{datetime.now().isoformat()}",
        user_id="profile-user",
        thread_id="profile-thread",
        granularity=granularity
    )

    # Node IDs and event types for profiling
    node_ids = ["node1", "node2", "node3", "node4", "node5"]
    event_types = ["start", "end", "state_update", "decision", "llm_call"]

    # Profile trace collection
    pr = cProfile.Profile()
    pr.enable()

    start_time = time.time()

    for i in range(num_traces):
        # Create random trace
        node_id = random.choice(node_ids)
        event_type = random.choice(event_types)
        metadata = {
            "index": i,
            "timestamp": datetime.now().isoformat(),
            "random_value": random.random(),
            "data": "x" * random.randint(10, 100)  # Random string of varying length
        }

        # Add trace
        tracer.add_trace(
            node_id=node_id,
            event_type=event_type,
            metadata=metadata
        )

    end_time = time.time()

    pr.disable()

    # Print basic stats
    duration = end_time - start_time
    traces_per_second = num_traces / duration

    print(f"Collected {num_traces} traces in {duration:.2f} seconds")
    print(f"Traces per second: {traces_per_second:.2f}")
    print(f"Average time per trace: {(duration / num_traces) * 1000:.2f} ms")

    # Print detailed profile stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats(20)  # Print top 20 functions by cumulative time
    print(s.getvalue())

    return tracer, duration


def profile_trace_export(tracer, format="json"):
    """Profile trace export performance.

    Args:
        tracer: TracingCollector with traces
        format: Export format (json or ndjson)
    """
    # Create a temporary file
    import tempfile
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    file_path = temp_file.name
    temp_file.close()

    # Profile export
    pr = cProfile.Profile()
    pr.enable()

    start_time = time.time()

    if format == "json":
        tracer.export_json(file_path)
    elif format == "ndjson":
        tracer.export_ndjson(file_path)

    end_time = time.time()

    pr.disable()

    # Get file size
    file_size = os.path.getsize(file_path)

    # Print basic stats
    duration = end_time - start_time
    traces_per_second = len(tracer.traces) / duration

    print(f"\nExported {len(tracer.traces)} traces to {format} in {duration:.2f} seconds")
    print(f"Traces per second: {traces_per_second:.2f}")
    print(f"File size: {file_size / 1024:.2f} KB")
    print(f"Bytes per trace: {file_size / len(tracer.traces):.2f}")

    # Print detailed profile stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats(20)  # Print top 20 functions by cumulative time
    print(s.getvalue())

    # Clean up
    os.unlink(file_path)


def profile_different_granularities():
    """Profile different tracing granularity levels."""
    print("\n=== Profiling Different Granularity Levels ===\n")

    granularities = [
        ("NONE", TracingLevel.NONE),
        ("MINIMAL", TracingLevel.MINIMAL),
        ("BASIC", TracingLevel.BASIC),
        ("DETAILED", TracingLevel.DETAILED),
    ]

    results = []

    for name, level in granularities:
        print(f"\n--- Granularity: {name} ---\n")
        tracer, duration = profile_trace_collection(num_traces=1000, granularity=level)
        results.append((name, len(tracer.traces), duration))

    # Print comparison
    print("\n=== Granularity Comparison ===\n")
    print("Granularity | Traces Collected | Duration (s) | Traces/s")
    print("------------|------------------|--------------|----------")

    for name, traces, duration in results:
        traces_per_second = traces / duration if duration > 0 else 0
        print(f"{name:11} | {traces:16} | {duration:12.2f} | {traces_per_second:9.2f}")


def profile_memory_usage():
    """Profile memory usage of the tracing system."""
    print("\n=== Profiling Memory Usage ===\n")

    try:
        import psutil
        import tracemalloc

        # Start memory tracking
        tracemalloc.start()
        process = psutil.Process(os.getpid())

        # Get initial memory usage
        initial_memory = process.memory_info().rss / 1024 / 1024
        initial_snapshot = tracemalloc.take_snapshot()

        # Create a tracing collector
        tracer = TracingCollector(
            session_id=f"memory-profile-{datetime.now().isoformat()}",
            user_id="profile-user",
            thread_id="profile-thread",
            granularity=TracingLevel.DETAILED
        )

        # Add traces
        num_traces = 10000
        for i in range(num_traces):
            tracer.add_trace(
                node_id=f"node{i % 5}",
                event_type=f"event{i % 3}",
                metadata={
                    "index": i,
                    "data": "x" * (i % 100)
                }
            )

        # Get memory usage after adding traces
        traces_memory = process.memory_info().rss / 1024 / 1024
        traces_snapshot = tracemalloc.take_snapshot()

        # Export traces
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file_path = temp_file.name

        tracer.export_json(file_path)

        # Get memory usage after export
        export_memory = process.memory_info().rss / 1024 / 1024
        export_snapshot = tracemalloc.take_snapshot()

        # Clean up
        os.unlink(file_path)

        # Print memory usage
        print(f"Initial memory usage: {initial_memory:.2f} MB")
        print(f"Memory after adding {num_traces} traces: {traces_memory:.2f} MB")
        print(f"Memory after export: {export_memory:.2f} MB")
        print(f"Memory per trace: {(traces_memory - initial_memory) / num_traces * 1024:.2f} KB")

        # Print memory diff
        print("\nTop 10 memory differences after adding traces:")
        traces_diff = traces_snapshot.compare_to(initial_snapshot, 'lineno')
        for stat in traces_diff[:10]:
            print(f"{stat.size_diff / 1024:.1f} KB: {stat.traceback.format()[0]}")

        # Stop memory tracking
        tracemalloc.stop()

    except ImportError:
        print("psutil and/or tracemalloc not available. Install with: pip install psutil")


def main():
    """Run profiling tests."""
    print("=== Tracing System Performance Profiling ===\n")

    # Profile trace collection
    print("\n=== Profiling Trace Collection ===\n")
    tracer, _ = profile_trace_collection(num_traces=5000)

    # Profile trace export
    print("\n=== Profiling Trace Export (JSON) ===\n")
    profile_trace_export(tracer, format="json")

    print("\n=== Profiling Trace Export (NDJSON) ===\n")
    profile_trace_export(tracer, format="ndjson")

    # Profile different granularities
    profile_different_granularities()

    # Profile memory usage
    profile_memory_usage()


if __name__ == "__main__":
    main()
```

Create a profiling script for visualization in `backend/scripts/profile_visualization.py`:

```python
"""Profile visualization performance."""

import cProfile
import pstats
import io
import time
import random
from datetime import datetime
import os
import sys
import json
import tempfile

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.tracing.collector import TracingCollector, TracingLevel
from app.cli.trace_viewer import load_traces, filter_traces, format_traces_as_tree


def generate_test_traces(num_traces=1000):
    """Generate test traces for profiling.

    Args:
        num_traces: Number of traces to generate

    Returns:
        List of trace events
    """
    # Create a tracing collector
    tracer = TracingCollector(
        session_id=f"profile-{datetime.now().isoformat()}",
        user_id="profile-user",
        thread_id="profile-thread",
        granularity=TracingLevel.DETAILED
    )

    # Node IDs and event types for profiling
    node_ids = ["node1", "node2", "node3", "node4", "node5"]
    event_types = ["start", "end", "state_update", "decision", "llm_call"]

    # Generate traces
    for i in range(num_traces):
        # Create random trace
        node_id = random.choice(node_ids)
        event_type = random.choice(event_types)
        metadata = {
            "index": i,
            "timestamp": datetime.now().isoformat(),
            "random_value": random.random(),
            "data": "x" * random.randint(10, 100)  # Random string of varying length
        }

        # Add trace
        tracer.add_trace(
            node_id=node_id,
            event_type=event_type,
            metadata=metadata
        )

    return tracer.traces


def profile_trace_loading(file_path, num_iterations=10):
    """Profile trace loading performance.

    Args:
        file_path: Path to trace file
        num_iterations: Number of iterations for profiling
    """
    print(f"\n=== Profiling Trace Loading ({num_iterations} iterations) ===\n")

    # Profile loading
    pr = cProfile.Profile()
    pr.enable()

    start_time = time.time()

    for _ in range(num_iterations):
        traces = load_traces(file_path)

    end_time = time.time()

    pr.disable()

    # Print basic stats
    duration = end_time - start_time
    avg_duration = duration / num_iterations

    print(f"Loaded traces {num_iterations} times in {duration:.2f} seconds")
    print(f"Average loading time: {avg_duration:.4f} seconds")

    # Print detailed profile stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats(20)  # Print top 20 functions by cumulative time
    print(s.getvalue())

    return traces


def profile_trace_filtering(traces, num_iterations=100):
    """Profile trace filtering performance.

    Args:
        traces: List of trace events
        num_iterations: Number of iterations for profiling
    """
    print(f"\n=== Profiling Trace Filtering ({num_iterations} iterations) ===\n")

    # Node IDs and event types for filtering
    node_ids = ["node1", "node2", "node3", "node4", "node5"]
    event_types = ["start", "end", "state_update", "decision", "llm_call"]

    # Profile filtering
    pr = cProfile.Profile()
    pr.enable()

    start_time = time.time()

    for _ in range(num_iterations):
        # Random filter criteria
        node_id = random.choice(node_ids)
        event_type = random.choice(event_types)

        # Apply filters
        filtered_traces = filter_traces(
            traces,
            node_id=node_id if random.random() > 0.5 else None,
            event_type=event_type if random.random() > 0.5 else None
        )

    end_time = time.time()

    pr.disable()

    # Print basic stats
    duration = end_time - start_time
    avg_duration = duration / num_iterations

    print(f"Filtered traces {num_iterations} times in {duration:.2f} seconds")
    print(f"Average filtering time: {avg_duration:.4f} seconds")

    # Print detailed profile stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats(20)  # Print top 20 functions by cumulative time
    print(s.getvalue())


def profile_trace_formatting(traces, format_type="tree", num_iterations=10):
    """Profile trace formatting performance.

    Args:
        traces: List of trace events
        format_type: Format type (tree, table, etc.)
        num_iterations: Number of iterations for profiling
    """
    print(f"\n=== Profiling Trace Formatting ({format_type}, {num_iterations} iterations) ===\n")

    # Profile formatting
    pr = cProfile.Profile()
    pr.enable()

    start_time = time.time()

    for _ in range(num_iterations):
        if format_type == "tree":
            formatted_traces = format_traces_as_tree(traces)
        else:
            # Add other format types as needed
            formatted_traces = str(traces)

    end_time = time.time()

    pr.disable()

    # Print basic stats
    duration = end_time - start_time
    avg_duration = duration / num_iterations

    print(f"Formatted traces {num_iterations} times in {duration:.2f} seconds")
    print(f"Average formatting time: {avg_duration:.4f} seconds")

    # Print detailed profile stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats("cumulative")
    ps.print_stats(20)  # Print top 20 functions by cumulative time
    print(s.getvalue())


def profile_visualization_scaling():
    """Profile visualization performance with different trace counts."""
    print("\n=== Profiling Visualization Scaling ===\n")

    trace_counts = [100, 500, 1000, 5000, 10000]
    results = []

    for count in trace_counts:
        print(f"\n--- Trace Count: {count} ---\n")

        # Generate traces
        traces = generate_test_traces(num_traces=count)

        # Measure formatting time
        start_time = time.time()
        format_traces_as_tree(traces)
        end_time = time.time()

        formatting_time = end_time - start_time
        results.append((count, formatting_time))

        print(f"Formatted {count} traces in {formatting_time:.4f} seconds")

    # Print comparison
    print("\n=== Scaling Comparison ===\n")
    print("Trace Count | Formatting Time (s) | Time per Trace (ms)")
    print("-----------|---------------------|------------------")

    for count, formatting_time in results:
        time_per_trace = formatting_time / count * 1000
        print(f"{count:10} | {formatting_time:19.4f} | {time_per_trace:18.4f}")


def main():
    """Run profiling tests."""
    print("=== Visualization Performance Profiling ===\n")

    # Generate test traces
    print("\n=== Generating Test Traces ===\n")
    traces = generate_test_traces(num_traces=5000)

    # Save traces to a temporary file
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as temp_file:
        file_path = temp_file.name
        json.dump(traces, temp_file)

    try:
        # Profile trace loading
        loaded_traces = profile_trace_loading(file_path)

        # Profile trace filtering
        profile_trace_filtering(loaded_traces)

        # Profile trace formatting
        profile_trace_formatting(loaded_traces, format_type="tree")

        # Profile visualization scaling
        profile_visualization_scaling()

    finally:
        # Clean up
        os.unlink(file_path)


if __name__ == "__main__":
    main()
```

Add a profiling script for the Makefile in `makefiles/backend-cli-testing/Makefile`:

```makefile
# Performance Profiling Commands

# Profile tracing system
profile-tracing:
	cd $(BACKEND_DIR) && python scripts/profile_tracing.py

# Profile visualization
profile-visualization:
	cd $(BACKEND_DIR) && python scripts/profile_visualization.py

# Profile with specific trace count
profile-with-count:
	cd $(BACKEND_DIR) && python scripts/profile_tracing.py --num-traces $(COUNT)

# Profile with memory profiler
profile-memory:
	cd $(BACKEND_DIR) && python -m memory_profiler scripts/profile_tracing.py

# Profile with line profiler
profile-line:
	cd $(BACKEND_DIR) && python -m line_profiler scripts/profile_tracing.py
```

#### Step 2: Identify Optimization Opportunities

Create a document to track optimization opportunities in `backend/docs/optimization/tracing-optimization.md`:

```markdown
# Tracing System Optimization

This document tracks optimization opportunities for the tracing system based on profiling results.

## Performance Bottlenecks

### 1. Trace Collection

Based on profiling results, the following bottlenecks have been identified in trace collection:

1. **JSON Serialization**: Converting Python objects to JSON during trace collection is expensive
2. **Timestamp Generation**: Creating ISO format timestamps for each trace adds overhead
3. **Metadata Copying**: Deep copying metadata for each trace is expensive

### 2. Trace Export

Bottlenecks in trace export:

1. **File I/O**: Writing traces to disk is a bottleneck for large trace files
2. **JSON Formatting**: Pretty-printing JSON adds significant overhead
3. **Memory Usage**: Large trace collections can cause memory pressure during export

### 3. Trace Visualization

Bottlenecks in trace visualization:

1. **Tree Formation**: Converting flat traces to a tree structure is expensive for large trace sets
2. **Rendering**: Rendering large trace trees in the terminal is slow
3. **Filtering**: Filtering large trace sets can be slow without proper indexing

## Optimization Strategies

### 1. Trace Collection Optimizations

1. **Lazy Serialization**: Delay JSON serialization until export time
2. **Timestamp Caching**: Cache timestamps for events that occur close together
3. **Metadata References**: Use references instead of deep copies for metadata
4. **Batch Processing**: Collect traces in memory and process in batches
5. **Async Collection**: Make trace collection asynchronous to minimize impact on main thread

### 2. Trace Export Optimizations

1. **Streaming Export**: Stream traces to disk instead of writing all at once
2. **NDJSON Format**: Use NDJSON (newline-delimited JSON) for more efficient streaming
3. **Compression**: Compress trace files to reduce disk I/O
4. **Binary Format**: Consider a binary format for more efficient storage and loading

### 3. Trace Visualization Optimizations

1. **Pagination**: Implement pagination for large trace sets
2. **Lazy Loading**: Load and render traces on demand
3. **Indexing**: Create indexes for common filter criteria
4. **Caching**: Cache formatted traces for reuse
5. **Incremental Rendering**: Render traces incrementally for better responsiveness

## Implementation Plan

### Phase 1: Quick Wins

1. **Switch to NDJSON**: Use NDJSON format for more efficient streaming
2. **Implement Pagination**: Add pagination to CLI visualization
3. **Optimize Metadata Handling**: Reduce deep copying of metadata

### Phase 2: Structural Improvements

1. **Implement Async Collection**: Make trace collection asynchronous
2. **Add Indexing**: Create indexes for common filter criteria
3. **Optimize Tree Formation**: Improve algorithm for converting flat traces to trees

### Phase 3: Advanced Optimizations

1. **Implement Binary Format**: Create a binary format for more efficient storage
2. **Add Compression**: Implement trace file compression
3. **Create Caching Layer**: Add caching for formatted traces
```

### 8.2 User Experience Refinement

#### Step 1: Create Feedback Collection Form

Create a feedback collection form in `backend/docs/feedback/visualization-feedback-form.md`:

```markdown
# Visualization System Feedback Form

Thank you for using the visualization system! Your feedback is valuable for improving the system.

## Basic Information

**Name (optional):** _________________________

**Role:** [ ] Developer [ ] Data Scientist [ ] Manager [ ] Other: _____________

**Experience Level:** [ ] Beginner [ ] Intermediate [ ] Advanced

## Feature Usage

Please indicate which features you have used:

- [ ] CLI Trace Visualization
- [ ] Jupyter Notebook Trace Analysis
- [ ] Vector Visualization
- [ ] Query Playground
- [ ] Real-time Tracing
- [ ] Other: _________________________

## Usability Rating

Please rate the following aspects from 1 (Poor) to 5 (Excellent):

| Aspect | 1 | 2 | 3 | 4 | 5 |
|--------|---|---|---|---|---|
| Ease of installation | [ ] | [ ] | [ ] | [ ] | [ ] |
| Documentation clarity | [ ] | [ ] | [ ] | [ ] | [ ] |
| Command-line interface | [ ] | [ ] | [ ] | [ ] | [ ] |
| Jupyter notebook interface | [ ] | [ ] | [ ] | [ ] | [ ] |
| Visualization quality | [ ] | [ ] | [ ] | [ ] | [ ] |
| Performance | [ ] | [ ] | [ ] | [ ] | [ ] |
| Overall experience | [ ] | [ ] | [ ] | [ ] | [ ] |

## Detailed Feedback

### What aspects of the visualization system do you find most useful?

_________________________________________________________________

_________________________________________________________________

### What aspects of the visualization system do you find most challenging or frustrating?

_________________________________________________________________

_________________________________________________________________

### What features would you like to see added or improved?

_________________________________________________________________

_________________________________________________________________

### Have you encountered any bugs or unexpected behavior?

_________________________________________________________________

_________________________________________________________________

### Any other comments or suggestions?

_________________________________________________________________

_________________________________________________________________

## Contact Information (optional)

If you'd like to discuss your feedback further:

**Email:** _________________________

**Preferred contact method:** [ ] Email [ ] Slack [ ] Meeting

Thank you for your feedback!
```

#### Step 2: Improve Terminal UI

Create an improvement plan for the terminal UI in `backend/docs/improvement/terminal-ui-improvement-plan.md`:

```markdown
# Terminal UI Improvement Plan

Based on user feedback and usability testing, this document outlines the plan for improving the terminal UI.

## Current Pain Points

1. **Navigation Challenges**
   - Difficult to navigate large trace outputs
   - Limited search capabilities
   - No bookmarking or highlighting

2. **Visual Clarity**
   - Information density can be overwhelming
   - Important information doesn't stand out
   - Color scheme issues in some terminals

3. **Interactivity Limitations**
   - Limited interactive features
   - No real-time updates
   - Difficult to filter or sort on the fly

## Improvement Goals

1. **Enhance Navigation**
   - Implement better pagination
   - Add search functionality
   - Add bookmarking for important sections

2. **Improve Visual Clarity**
   - Optimize information density
   - Highlight important information
   - Create terminal-friendly color schemes

3. **Add Interactive Features**
   - Implement interactive filtering
   - Add real-time updates
   - Create collapsible sections

## Implementation Plan

### Phase 1: Navigation Enhancements

1. **Implement Smart Pagination**
   - Add page-by-page navigation
   - Show progress indicators
   - Remember position between commands

2. **Add Search Functionality**
   - Implement text search within traces
   - Highlight search results
   - Add next/previous result navigation

3. **Add Bookmarking**
   - Allow marking important traces
   - Navigate between bookmarks
   - Save bookmarks between sessions

### Phase 2: Visual Clarity Improvements

1. **Optimize Information Display**
   - Create compact and expanded views
   - Implement smart truncation
   - Add detail levels (summary, normal, verbose)

2. **Enhance Highlighting**
   - Use color coding for different event types
   - Highlight errors and warnings
   - Add syntax highlighting for structured data

3. **Improve Color Schemes**
   - Create high-contrast theme
   - Add light and dark themes
   - Implement terminal capability detection

### Phase 3: Interactive Features

1. **Add Interactive Filtering**
   - Filter by node, event type, time range
   - Save and reuse filters
   - Combine multiple filters

2. **Implement Real-time Updates**
   - Add live mode for watching traces
   - Show updates in real-time
   - Allow pausing and resuming

3. **Create Collapsible Sections**
   - Collapse and expand trace sections
   - Auto-collapse based on importance
   - Remember collapsed state

## Code Examples

### Pagination Implementation

```python
def paginate_output(traces, page_size=20, current_page=1):
    """Paginate trace output.

    Args:
        traces: List of traces
        page_size: Number of traces per page
        current_page: Current page number

    Returns:
        Tuple of (paginated_traces, total_pages)
    """
    total_traces = len(traces)
    total_pages = (total_traces + page_size - 1) // page_size

    start_idx = (current_page - 1) * page_size
    end_idx = min(start_idx + page_size, total_traces)

    paginated_traces = traces[start_idx:end_idx]

    return paginated_traces, total_pages
```

### Interactive Filtering

```python
def create_interactive_filter(traces):
    """Create an interactive filter for traces.

    Args:
        traces: List of traces

    Returns:
        Filtered traces based on user input
    """
    from prompt_toolkit import prompt
    from prompt_toolkit.completion import WordCompleter

    # Get unique values for completion
    node_ids = sorted(set(t["node_id"] for t in traces))
    event_types = sorted(set(t["event_type"] for t in traces))

    # Create completers
    node_completer = WordCompleter(node_ids)
    event_completer = WordCompleter(event_types)

    # Get filter criteria
    print("Enter filter criteria (leave blank to skip):")
    node_id = prompt("Node ID: ", completer=node_completer)
    event_type = prompt("Event Type: ", completer=event_completer)

    # Apply filters
    filtered_traces = traces
    if node_id:
        filtered_traces = [t for t in filtered_traces if t["node_id"] == node_id]
    if event_type:
        filtered_traces = [t for t in filtered_traces if t["event_type"] == event_type]

    return filtered_traces
```

## Testing Plan

1. **Usability Testing**
   - Test with different user types
   - Gather feedback on specific improvements
   - Iterate based on feedback

2. **Terminal Compatibility Testing**
   - Test on different terminal emulators
   - Verify compatibility with different operating systems
   - Test with different terminal sizes and capabilities

3. **Performance Testing**
   - Test with large trace files
   - Measure rendering time
   - Verify memory usage

## Timeline

- **Phase 1**: 1 week
- **Phase 2**: 1 week
- **Phase 3**: 2 weeks

Total: 4 weeks
```

#### Step 3: Enhance Jupyter Notebooks

Create an improvement plan for Jupyter notebooks in `backend/docs/improvement/jupyter-notebook-improvement-plan.md`:

```markdown
# Jupyter Notebook Improvement Plan

This document outlines the plan for enhancing the Jupyter notebook experience for visualization.

## Current Limitations

1. **Documentation Gaps**
   - Limited inline documentation
   - Few examples
   - Unclear workflow guidance

2. **Visualization Limitations**
   - Limited interactivity
   - Performance issues with large datasets
   - Limited customization options

3. **Integration Challenges**
   - Difficult to connect to live systems
   - Limited export options
   - No sharing capabilities

## Improvement Goals

1. **Enhance Documentation**
   - Add comprehensive inline documentation
   - Create example notebooks
   - Provide clear workflow guidance

2. **Improve Visualizations**
   - Add more interactive features
   - Optimize performance
   - Expand customization options

3. **Strengthen Integration**
   - Simplify connection to live systems
   - Add export options
   - Enable sharing capabilities

## Implementation Plan

### Phase 1: Documentation Enhancements

1. **Improve Inline Documentation**
   - Add detailed docstrings
   - Include parameter explanations
   - Add usage examples

2. **Create Example Notebooks**
   - Basic trace analysis
   - Advanced trace analysis
   - Vector visualization
   - Query playground

3. **Develop Workflow Guides**
   - Step-by-step tutorials
   - Common use cases
   - Troubleshooting guides

### Phase 2: Visualization Improvements

1. **Enhance Interactivity**
   - Add interactive filters
   - Implement drill-down capabilities
   - Create linked visualizations

2. **Optimize Performance**
   - Implement data sampling
   - Add lazy loading
   - Optimize rendering

3. **Expand Customization**
   - Add theme options
   - Create visualization presets
   - Allow saving custom configurations

### Phase 3: Integration Enhancements

1. **Simplify Live Connections**
   - Add connection wizards
   - Create connection presets
   - Implement auto-reconnect

2. **Improve Export Options**
   - Add PDF export
   - Enable HTML export with interactivity
   - Create report templates

3. **Enable Sharing**
   - Add notebook sharing
   - Create collaborative features
   - Implement version control

## Example Notebook Improvements

### Before:

```python
# Load traces
traces = load_traces("trace_file.json")

# Plot timeline
plot_timeline(traces)
```

### After:

```python
# Load traces
# This function loads trace events from a JSON file and returns them as a list
traces = load_traces("trace_file.json")
print(f"Loaded {len(traces)} trace events")

# Display basic statistics
display_trace_statistics(traces)

# Plot interactive timeline
# This creates an interactive timeline visualization with hover details and filtering
plot_timeline(
    traces,
    title="Trace Event Timeline",
    color_by="event_type",
    show_details=True,
    height=600,
    width=900
)

# Add interactive filters
create_interactive_filters(traces, target_plot="timeline")
```

## Testing Plan

1. **User Testing**
   - Test with data scientists
   - Test with developers
   - Test with non-technical users

2. **Performance Testing**
   - Test with different dataset sizes
   - Measure rendering time
   - Verify memory usage

3. **Compatibility Testing**
   - Test with different Jupyter environments
   - Verify browser compatibility
   - Test with different operating systems

## Timeline

- **Phase 1**: 1 week
- **Phase 2**: 2 weeks
- **Phase 3**: 1 week

Total: 4 weeks
```

#### Step 4: Test User Experience Improvements

Create a test plan for user experience improvements in `backend/docs/testing/ux-testing-plan.md`:

```markdown
# User Experience Testing Plan

This document outlines the plan for testing user experience improvements to the visualization system.

## Testing Goals

1. **Validate Improvements**
   - Verify that identified pain points have been addressed
   - Confirm that new features meet user needs
   - Ensure that usability has improved

2. **Identify Remaining Issues**
   - Discover any new usability issues
   - Identify edge cases or unexpected behaviors
   - Gather feedback for future improvements

3. **Measure Success**
   - Quantify usability improvements
   - Measure user satisfaction
   - Assess efficiency gains

## Testing Methodology

### 1. Usability Testing Sessions

**Participants:**
- 3-5 developers
- 2-3 data scientists
- 1-2 non-technical users

**Session Structure:**
1. Introduction (5 minutes)
2. Pre-test questionnaire (5 minutes)
3. Task completion (30 minutes)
4. Post-test questionnaire (10 minutes)
5. Open feedback (10 minutes)

**Tasks to Test:**
1. Analyze a trace file using the CLI
2. Explore vector embeddings in Jupyter
3. Debug an issue using real-time tracing
4. Create a custom visualization
5. Export and share findings

### 2. A/B Testing

Compare the old and new interfaces for specific tasks:

1. **Time to Complete**
   - Measure time to complete common tasks
   - Compare between old and new interfaces

2. **Error Rate**
   - Count errors made during task completion
   - Compare between old and new interfaces

3. **User Preference**
   - Ask users which interface they prefer
   - Gather specific feedback on differences

### 3. Metrics Collection

Collect quantitative metrics:

1. **Task Success Rate**
   - Percentage of tasks completed successfully
   - Comparison to baseline

2. **Time on Task**
   - Average time to complete tasks
   - Comparison to baseline

3. **Error Rate**
   - Number of errors per task
   - Comparison to baseline

4. **System Usability Scale (SUS)**
   - Standard 10-item questionnaire
   - Score out of 100

## Test Scenarios

### CLI Visualization Testing

1. **Basic Trace Analysis**
   - Load a trace file
   - Filter by node ID
   - Search for specific events
   - Export filtered results

2. **Advanced Navigation**
   - Use pagination
   - Use search functionality
   - Set and navigate bookmarks
   - Use collapsible sections

3. **Real-time Tracing**
   - Start real-time tracing
   - Filter live events
   - Pause and resume
   - Save interesting events

### Jupyter Notebook Testing

1. **Trace Analysis**
   - Load and analyze traces
   - Create visualizations
   - Filter and explore data
   - Save findings

2. **Vector Visualization**
   - Load embeddings
   - Visualize in 2D and 3D
   - Explore clusters
   - Compare queries

3. **Query Playground**
   - Create and run queries
   - Visualize results
   - Compare different queries
   - Save interesting findings

## Data Collection

1. **Quantitative Data**
   - Task completion times
   - Error rates
   - SUS scores
   - Feature usage statistics

2. **Qualitative Data**
   - Think-aloud observations
   - Post-test interviews
   - Open-ended feedback
   - Satisfaction ratings

## Analysis Plan

1. **Comparative Analysis**
   - Compare metrics before and after improvements
   - Analyze differences between user groups
   - Identify trends and patterns

2. **Issue Categorization**
   - Categorize identified issues by severity
   - Group issues by feature area
   - Prioritize based on impact and frequency

3. **Success Evaluation**
   - Determine if improvement goals were met
   - Identify most successful improvements
   - Highlight areas needing further work

## Reporting

Create a comprehensive report including:

1. **Executive Summary**
   - Key findings
   - Overall success assessment
   - Major recommendations

2. **Detailed Results**
   - Task-by-task analysis
   - Metrics comparison
   - User feedback summary

3. **Recommendations**
   - Immediate fixes
   - Short-term improvements
   - Long-term considerations

4. **Next Steps**
   - Implementation priorities
   - Further testing needs
   - Timeline for addressing issues

## Timeline

- **Preparation**: 3 days
- **Testing Sessions**: 1 week
- **Data Analysis**: 2 days
- **Report Creation**: 2 days

Total: 2 weeks
```

### 8.3 Final Integration and Documentation

#### Step 1: Create Integration Test Plan

Create an integration test plan in `backend/docs/testing/integration-test-plan.md`:

```markdown
# Integration Test Plan

This document outlines the plan for testing the integration of all visualization components.

## Integration Test Goals

1. **Verify Component Interoperability**
   - Ensure all components work together seamlessly
   - Verify data flows correctly between components
   - Confirm that interfaces are consistent

2. **Validate End-to-End Workflows**
   - Test complete user workflows
   - Verify that all use cases are supported
   - Ensure a consistent user experience

3. **Identify Integration Issues**
   - Discover any compatibility issues
   - Identify performance bottlenecks
   - Find edge cases and error conditions

## Test Environment

1. **Development Environment**
   - Local development setup
   - PostgreSQL database
   - Mock LLM providers

2. **Testing Environment**
   - Clean installation
   - Test database
   - Test LLM providers

3. **Production-like Environment**
   - Realistic data volumes
   - Production-like configuration
   - Real LLM providers

## Integration Test Scenarios

### 1. Tracing System Integration

**Test Case 1.1: Trace Collection and Storage**
- **Description**: Verify that traces are collected and stored correctly
- **Steps**:
  1. Enable tracing in the application
  2. Perform various operations that generate traces
  3. Verify traces are stored in the database
  4. Verify traces are stored in JSON files
- **Expected Result**: Traces are correctly collected and stored in both formats

**Test Case 1.2: Trace Visualization Integration**
- **Description**: Verify that traces can be visualized through different interfaces
- **Steps**:
  1. Generate traces using the application
  2. View traces using the CLI
  3. View traces using Jupyter notebooks
  4. View traces using real-time dashboard
- **Expected Result**: Traces are correctly visualized in all interfaces

**Test Case 1.3: Trace Analysis Integration**
- **Description**: Verify that traces can be analyzed and filtered
- **Steps**:
  1. Generate complex traces with multiple components
  2. Filter traces by different criteria
  3. Analyze trace patterns and performance
  4. Export analysis results
- **Expected Result**: Traces can be effectively analyzed and insights extracted

### 2. Vector Store Integration

**Test Case 2.1: Vector Storage and Retrieval**
- **Description**: Verify that vectors are stored and retrieved correctly
- **Steps**:
  1. Store embeddings in the vector store
  2. Retrieve embeddings using different queries
  3. Verify similarity search works correctly
  4. Verify metadata filtering works correctly
- **Expected Result**: Vectors are correctly stored and retrieved

**Test Case 2.2: Vector Visualization Integration**
- **Description**: Verify that vectors can be visualized through different interfaces
- **Steps**:
  1. Store embeddings in the vector store
  2. Visualize embeddings using the CLI
  3. Visualize embeddings using Jupyter notebooks
  4. Compare different visualization methods
- **Expected Result**: Vectors are correctly visualized in all interfaces

**Test Case 2.3: Query Playground Integration**
- **Description**: Verify that the query playground works correctly
- **Steps**:
  1. Create and run queries in the playground
  2. Visualize query results
  3. Compare different queries
  4. Save and load queries
- **Expected Result**: Query playground functions correctly and integrates with other components

### 3. End-to-End Workflows

**Test Case 3.1: RAG Pipeline Workflow**
- **Description**: Verify the complete RAG pipeline workflow
- **Steps**:
  1. Load documents into the vector store
  2. Query the RAG pipeline
  3. Trace the query execution
  4. Analyze the trace and query performance
- **Expected Result**: Complete RAG pipeline works correctly with tracing and analysis

**Test Case 3.2: Multi-Agent Orchestration Workflow**
- **Description**: Verify the multi-agent orchestration workflow
- **Steps**:
  1. Set up multiple agents
  2. Execute a multi-agent workflow
  3. Trace the agent communications
  4. Visualize and analyze the agent interactions
- **Expected Result**: Multi-agent orchestration works correctly with tracing and visualization

**Test Case 3.3: Debugging Workflow**
- **Description**: Verify the debugging workflow
- **Steps**:
  1. Introduce a deliberate issue in the system
  2. Use tracing to identify the issue
  3. Use visualization to analyze the problem
  4. Fix the issue and verify the solution
- **Expected Result**: Debugging workflow effectively helps identify and fix issues

## Test Data

1. **Sample Traces**
   - Simple traces with few events
   - Complex traces with many events
   - Traces with different event types
   - Traces with different correlation IDs

2. **Sample Vectors**
   - Vectors from different departments
   - Vectors with different dimensions
   - Vectors with different metadata
   - Vectors with known clusters

3. **Sample Queries**
   - Simple queries
   - Complex queries
   - Queries with different parameters
   - Queries with different expected results

## Test Execution

1. **Manual Testing**
   - Execute test cases manually
   - Document results
   - Investigate issues

2. **Automated Testing**
   - Implement automated tests for critical paths
   - Run automated tests regularly
   - Report results

3. **Continuous Integration**
   - Integrate tests into CI pipeline
   - Run tests on every significant change
   - Block merges if tests fail

## Issue Tracking

1. **Issue Documentation**
   - Document all issues found
   - Include steps to reproduce
   - Include expected vs. actual results

2. **Issue Prioritization**
   - Prioritize issues based on severity
   - Focus on integration blockers
   - Address critical issues first

3. **Issue Resolution**
   - Assign issues to appropriate team members
   - Track resolution progress
   - Verify fixes with regression tests

## Reporting

1. **Test Results Report**
   - Summary of test execution
   - Pass/fail status for each test case
   - Details of any issues found

2. **Integration Status Report**
   - Overall integration status
   - Component compatibility matrix
   - Recommendations for improvements

3. **Final Acceptance Report**
   - Final integration status
   - Remaining known issues
   - Recommendations for production readiness

## Timeline

- **Test Planning**: 2 days
- **Test Environment Setup**: 1 day
- **Test Execution**: 3 days
- **Issue Resolution**: 2 days
- **Final Reporting**: 1 day

Total: 9 days
```

#### Step 2: Update Documentation with Final Changes

Create a documentation update plan in `backend/docs/documentation-update-plan.md`:

```markdown
# Documentation Update Plan

This document outlines the plan for updating all documentation with final changes and examples.

## Documentation Audit

### 1. Core Documentation

| Document | Status | Updates Needed |
|----------|--------|----------------|
| `README.md` | Needs update | Add visualization features, update installation instructions |
| `CONTRIBUTING.md` | Needs update | Add guidelines for visualization components |
| `ARCHITECTURE.md` | Needs update | Add visualization architecture |

### 2. Tracing Documentation

| Document | Status | Updates Needed |
|----------|--------|----------------|
| `tracing-overview.md` | Needs update | Add final architecture, examples |
| `tracing-implementation.md` | Needs update | Update with final implementation details |
| `cli-visualization.md` | Needs update | Add new commands, examples |
| `langgraph-integration.md` | Needs update | Add final integration details |

### 3. Vector Store Documentation

| Document | Status | Updates Needed |
|----------|--------|----------------|
| `vector-store-overview.md` | Needs update | Add visualization features |
| `vector-visualization.md` | Needs update | Add final examples, screenshots |
| `query-playground.md` | Needs update | Add usage examples |
| `embedding-analysis.md` | Needs update | Add final analysis techniques |

### 4. Jupyter Notebook Documentation

| Document | Status | Updates Needed |
|----------|--------|----------------|
| `jupyter-overview.md` | Needs update | Add final notebook list, examples |
| `trace-analysis.md` | Needs update | Add final analysis techniques |
| `vector-visualization.md` | Needs update | Add final visualization techniques |
| `query-playground.md` | Needs update | Add final query examples |

### 5. CLI Documentation

| Document | Status | Updates Needed |
|----------|--------|----------------|
| `cli-overview.md` | Needs update | Add all visualization commands |
| `trace-viewer.md` | Needs update | Add final commands, examples |
| `vector-explorer.md` | Needs update | Add final commands, examples |
| `query-analysis.md` | Needs update | Add final commands, examples |

## Update Priorities

1. **High Priority**
   - Core README and installation instructions
   - Main overview documents
   - CLI command reference

2. **Medium Priority**
   - Implementation details
   - Integration documentation
   - Example workflows

3. **Low Priority**
   - Advanced usage examples
   - Troubleshooting guides
   - Performance optimization guides

## Documentation Tasks

### 1. Update Core Documentation

1. **Update README.md**
   - Add visualization features overview
   - Update installation instructions
   - Add quick start examples

2. **Update CONTRIBUTING.md**
   - Add guidelines for visualization components
   - Update testing instructions
   - Add documentation standards

3. **Update ARCHITECTURE.md**
   - Add visualization architecture
   - Update component diagrams
   - Add data flow descriptions

### 2. Update Tracing Documentation

1. **Update tracing-overview.md**
   - Add final architecture diagram
   - Update component descriptions
   - Add example workflows

2. **Update tracing-implementation.md**
   - Update with final implementation details
   - Add code examples
   - Add configuration options

3. **Update cli-visualization.md**
   - Add all new commands
   - Add example usage
   - Add screenshots

4. **Update langgraph-integration.md**
   - Add final integration details
   - Add example workflows
   - Add troubleshooting tips

### 3. Update Vector Store Documentation

1. **Update vector-store-overview.md**
   - Add visualization features
   - Update architecture diagram
   - Add example workflows

2. **Update vector-visualization.md**
   - Add final examples
   - Add screenshots
   - Add usage instructions

3. **Update query-playground.md**
   - Add usage examples
   - Add screenshots
   - Add advanced techniques

4. **Update embedding-analysis.md**
   - Add final analysis techniques
   - Add code examples
   - Add visualization examples

### 4. Update Jupyter Notebook Documentation

1. **Update jupyter-overview.md**
   - Add final notebook list
   - Add installation instructions
   - Add usage examples

2. **Update trace-analysis.md**
   - Add final analysis techniques
   - Add code examples
   - Add visualization examples

3. **Update vector-visualization.md**
   - Add final visualization techniques
   - Add code examples
   - Add customization options

4. **Update query-playground.md**
   - Add final query examples
   - Add code examples
   - Add advanced techniques

### 5. Update CLI Documentation

1. **Update cli-overview.md**
   - Add all visualization commands
   - Add command categories
   - Add usage examples

2. **Update trace-viewer.md**
   - Add final commands
   - Add example usage
   - Add screenshots

3. **Update vector-explorer.md**
   - Add final commands
   - Add example usage
   - Add screenshots

4. **Update query-analysis.md**
   - Add final commands
   - Add example usage
   - Add screenshots

## Documentation Standards

1. **Format**
   - Use Markdown for all documentation
   - Follow consistent formatting
   - Include table of contents for long documents

2. **Code Examples**
   - Include working code examples
   - Use syntax highlighting
   - Add comments to explain complex code

3. **Screenshots**
   - Use high-quality screenshots
   - Annotate screenshots where helpful
   - Include captions

4. **Cross-References**
   - Add links between related documents
   - Use consistent terminology
   - Avoid duplicate information

## Timeline

- **Documentation Audit**: 1 day
- **Core Documentation Updates**: 1 day
- **Tracing Documentation Updates**: 2 days
- **Vector Store Documentation Updates**: 2 days
- **Jupyter Notebook Documentation Updates**: 2 days
- **CLI Documentation Updates**: 2 days
- **Review and Finalization**: 1 day

Total: 11 days
```

#### Step 3: Create Demonstrations

Create a demonstration plan in `backend/docs/demonstration-plan.md`:

```markdown
# Visualization System Demonstration Plan

This document outlines the plan for creating demonstrations of the visualization system.

## Demonstration Goals

1. **Showcase Key Features**
   - Highlight the most important features
   - Demonstrate unique capabilities
   - Show integration between components

2. **Illustrate Workflows**
   - Demonstrate end-to-end workflows
   - Show how to accomplish common tasks
   - Provide realistic usage examples

3. **Facilitate Adoption**
   - Make it easy to understand the system
   - Reduce learning curve
   - Encourage usage

## Demonstration Types

### 1. Screenshots

**Purpose**: Provide quick visual reference for documentation

**Screenshots to Create**:
1. CLI Trace Viewer
   - Basic trace view
   - Tree view
   - Table view
   - Agent communication view
2. Vector Visualization
   - 2D embedding visualization
   - 3D embedding visualization
   - Query visualization
   - Cluster visualization
3. Jupyter Notebooks
   - Trace analysis notebook
   - Vector visualization notebook
   - Query playground notebook
4. Real-time Tracing
   - Live trace view
   - Agent communication view
   - Performance metrics view

**Tools**:
- Screen capture software
- Image editing software for annotations
- Documentation integration

### 2. GIFs

**Purpose**: Demonstrate simple interactions and workflows

**GIFs to Create**:
1. CLI Navigation
   - Navigating through traces
   - Filtering traces
   - Searching traces
2. Interactive Visualizations
   - Interacting with vector visualizations
   - Exploring query results
   - Manipulating 3D visualizations
3. Real-time Updates
   - Watching live traces
   - Seeing agent communications in real-time
   - Observing performance metrics

**Tools**:
- Screen recording software with GIF export
- GIF optimization tools
- Documentation integration

### 3. Video Tutorials

**Purpose**: Provide comprehensive guidance for complex workflows

**Videos to Create**:
1. Getting Started (5 minutes)
   - Installation and setup
   - Basic usage
   - Key concepts
2. Tracing System (10 minutes)
   - Enabling tracing
   - Viewing and analyzing traces
   - Debugging with traces
3. Vector Visualization (10 minutes)
   - Understanding embeddings
   - Visualizing vector spaces
   - Analyzing query results
4. Jupyter Integration (10 minutes)
   - Setting up notebooks
   - Analyzing traces
   - Visualizing vectors
5. End-to-End Workflows (15 minutes)
   - RAG pipeline with tracing
   - Multi-agent orchestration with visualization
   - Debugging and optimization

**Tools**:
- Screen recording software
- Video editing software
- Narration recording
- Video hosting platform

## Demonstration Content

### 1. CLI Demonstration

**Key Features to Showcase**:
- Command discovery and help
- Trace viewing and navigation
- Filtering and searching
- Real-time updates
- Export and sharing

**Sample Commands**:
```bash
# Show help
python -m app.cli.trace_viewer --help

# View a trace file
python -m app.cli.trace_viewer view --latest

# Filter traces
python -m app.cli.trace_viewer view --latest --node-id analyze_query

# View agent communication
python -m app.cli.trace_viewer agents --latest

# Watch real-time traces
python -m app.cli.trace_viewer live
```

### 2. Vector Visualization Demonstration

**Key Features to Showcase**:
- Embedding visualization
- Dimensionality reduction
- Query visualization
- Cluster analysis
- Interactive exploration

**Sample Commands**:
```bash
# Show vector store statistics
python -m app.cli.vector_explorer stats

# List documents
python -m app.cli.vector_explorer list-documents

# Search vector store
python -m app.cli.vector_explorer search "financial performance"

# Visualize embeddings
python -m app.cli.vector_explorer visualize
```

### 3. Jupyter Notebook Demonstration

**Key Features to Showcase**:
- Trace analysis
- Vector visualization
- Query playground
- Interactive filtering
- Custom visualizations

**Sample Notebooks**:
- `trace_analysis.ipynb`
- `vector_visualization.ipynb`
- `query_playground.ipynb`

### 4. End-to-End Workflow Demonstration

**Key Workflows to Showcase**:
- RAG pipeline with tracing
- Multi-agent orchestration with visualization
- Debugging and optimization
- Performance analysis

**Sample Scenario**:
1. User submits a query
2. System traces the query through the RAG pipeline
3. User analyzes the trace to understand the system behavior
4. User visualizes the vector space to see how the query relates to documents
5. User optimizes the system based on insights from visualization

## Production Plan

### 1. Preparation

- **Script Writing**: Create detailed scripts for each demonstration
- **Environment Setup**: Prepare clean environment with sample data
- **Tool Selection**: Choose appropriate recording and editing tools
- **Rehearsal**: Practice demonstrations to ensure smooth execution

### 2. Production

- **Recording**: Record raw footage for each demonstration
- **Editing**: Edit footage to create polished demonstrations
- **Narration**: Add clear narration and explanations
- **Annotations**: Add annotations, highlights, and callouts

### 3. Distribution

- **Documentation Integration**: Embed demonstrations in documentation
- **Repository Hosting**: Add demonstrations to the repository
- **Website Integration**: Add demonstrations to project website
- **Social Media**: Share demonstrations on relevant platforms

## Timeline

- **Planning and Scripting**: 2 days
- **Environment Setup**: 1 day
- **Screenshot Creation**: 1 day
- **GIF Creation**: 2 days
- **Video Recording**: 2 days
- **Editing and Finalization**: 2 days
- **Distribution**: 1 day

Total: 11 days
```

#### Step 4: Conduct Final Review

Create a final review checklist in `backend/docs/final-review-checklist.md`:

```markdown
# Final Review Checklist

This document provides a comprehensive checklist for the final review of the visualization system.

## Functionality Review

### Tracing System

- [ ] **Trace Collection**
  - [ ] Traces are collected correctly
  - [ ] All event types are supported
  - [ ] Metadata is captured correctly
  - [ ] Correlation IDs are maintained

- [ ] **Trace Storage**
  - [ ] Traces are stored in JSON files
  - [ ] Traces are stored in the database
  - [ ] Storage is efficient
  - [ ] Retrieval is fast

- [ ] **Trace Visualization**
  - [ ] CLI visualization works correctly
  - [ ] Jupyter notebook visualization works correctly
  - [ ] Real-time visualization works correctly
  - [ ] All visualization formats are supported

### Vector Store

- [ ] **Vector Storage**
  - [ ] Vectors are stored correctly
  - [ ] Metadata is stored correctly
  - [ ] Storage is efficient
  - [ ] Retrieval is fast

- [ ] **Vector Search**
  - [ ] Similarity search works correctly
  - [ ] Metadata filtering works correctly
  - [ ] Search is efficient
  - [ ] Results are accurate

- [ ] **Vector Visualization**
  - [ ] 2D visualization works correctly
  - [ ] 3D visualization works correctly
  - [ ] Interactive visualization works correctly
  - [ ] Query visualization works correctly

### Jupyter Integration

- [ ] **Notebook Setup**
  - [ ] Notebooks are created correctly
  - [ ] Dependencies are managed correctly
  - [ ] Installation is straightforward
  - [ ] Documentation is clear

- [ ] **Trace Analysis**
  - [ ] Trace loading works correctly
  - [ ] Analysis functions work correctly
  - [ ] Visualizations work correctly
  - [ ] Examples are helpful

- [ ] **Vector Visualization**
  - [ ] Vector loading works correctly
  - [ ] Dimensionality reduction works correctly
  - [ ] Visualizations work correctly
  - [ ] Examples are helpful

- [ ] **Query Playground**
  - [ ] Query execution works correctly
  - [ ] Result visualization works correctly
  - [ ] Interactive features work correctly
  - [ ] Examples are helpful

### CLI Tools

- [ ] **Command Structure**
  - [ ] Commands are intuitive
  - [ ] Help text is clear
  - [ ] Options are consistent
  - [ ] Error messages are helpful

- [ ] **Trace Viewer**
  - [ ] Viewing works correctly
  - [ ] Filtering works correctly
  - [ ] Formatting works correctly
  - [ ] Navigation works correctly

- [ ] **Vector Explorer**
  - [ ] Statistics work correctly
  - [ ] Search works correctly
  - [ ] Visualization works correctly
  - [ ] Export works correctly

- [ ] **Query Analysis**
  - [ ] Statistics work correctly
  - [ ] Performance analysis works correctly
  - [ ] Visualization works correctly
  - [ ] Export works correctly

## Performance Review

- [ ] **Trace Collection Performance**
  - [ ] Minimal impact on application performance
  - [ ] Scales with high event volume
  - [ ] Memory usage is reasonable
  - [ ] CPU usage is reasonable

- [ ] **Visualization Performance**
  - [ ] Handles large trace files
  - [ ] Renders quickly
  - [ ] Memory usage is reasonable
  - [ ] CPU usage is reasonable

- [ ] **Vector Operations Performance**
  - [ ] Handles large vector collections
  - [ ] Search is fast
  - [ ] Visualization is responsive
  - [ ] Memory usage is reasonable

- [ ] **Database Performance**
  - [ ] Queries are efficient
  - [ ] Indexes are used correctly
  - [ ] Connection pooling works correctly
  - [ ] Scales with data volume

## Usability Review

- [ ] **Installation and Setup**
  - [ ] Installation is straightforward
  - [ ] Dependencies are managed correctly
  - [ ] Configuration is clear
  - [ ] Documentation is helpful

- [ ] **CLI Usability**
  - [ ] Commands are discoverable
  - [ ] Output is readable
  - [ ] Navigation is intuitive
  - [ ] Error handling is helpful

- [ ] **Jupyter Usability**
  - [ ] Notebooks are well-documented
  - [ ] Functions are well-documented
  - [ ] Examples are helpful
  - [ ] Visualizations are clear

- [ ] **Integration Usability**
  - [ ] Components work together seamlessly
  - [ ] Workflows are intuitive
  - [ ] Data flows correctly
  - [ ] User experience is consistent

## Documentation Review

- [ ] **Core Documentation**
  - [ ] README is clear and comprehensive
  - [ ] Installation instructions are accurate
  - [ ] Architecture documentation is up-to-date
  - [ ] Contributing guidelines are clear

- [ ] **Feature Documentation**
  - [ ] All features are documented
  - [ ] Documentation is accurate
  - [ ] Examples are helpful
  - [ ] Screenshots and diagrams are included

- [ ] **API Documentation**
  - [ ] All public APIs are documented
  - [ ] Parameters are explained
  - [ ] Return values are explained
  - [ ] Examples are included

- [ ] **Tutorial Documentation**
  - [ ] Getting started tutorial is clear
  - [ ] Common workflows are documented
  - [ ] Advanced usage is explained
  - [ ] Troubleshooting is covered

## Code Quality Review

- [ ] **Code Structure**
  - [ ] Code is well-organized
  - [ ] Modules have clear responsibilities
  - [ ] Dependencies are managed correctly
  - [ ] Interfaces are clean

- [ ] **Code Style**
  - [ ] Code follows style guidelines
  - [ ] Naming is consistent and clear
  - [ ] Comments are helpful
  - [ ] Docstrings are comprehensive

- [ ] **Error Handling**
  - [ ] Errors are handled gracefully
  - [ ] Error messages are helpful
  - [ ] Edge cases are handled
  - [ ] Recovery is possible

- [ ] **Testing**
  - [ ] Unit tests cover core functionality
  - [ ] Integration tests verify component interaction
  - [ ] End-to-end tests validate workflows
  - [ ] Edge cases are tested

## Security Review

- [ ] **Data Security**
  - [ ] Sensitive data is protected
  - [ ] Authentication is implemented correctly
  - [ ] Authorization is implemented correctly
  - [ ] Data validation is thorough

- [ ] **Dependency Security**
  - [ ] Dependencies are up-to-date
  - [ ] No known vulnerabilities
  - [ ] Minimal attack surface
  - [ ] Security best practices are followed

- [ ] **Error Exposure**
  - [ ] Error messages don't expose sensitive information
  - [ ] Stack traces are not exposed to users
  - [ ] Debug information is controlled
  - [ ] Logging is secure

## Final Checks

- [ ] **Version Control**
  - [ ] All changes are committed
  - [ ] Commit messages are clear
  - [ ] Branch strategy is followed
  - [ ] Tags are applied correctly

- [ ] **Release Preparation**
  - [ ] Version numbers are updated
  - [ ] Changelog is updated
  - [ ] Release notes are prepared
  - [ ] Distribution packages are created

- [ ] **Deployment Readiness**
  - [ ] Installation from scratch works
  - [ ] Upgrade from previous version works
  - [ ] Dependencies are resolved correctly
  - [ ] Configuration is documented

- [ ] **User Acceptance**
  - [ ] User feedback is addressed
  - [ ] Key stakeholders have reviewed
  - [ ] Acceptance criteria are met
  - [ ] Sign-off is obtained

## Issue Resolution

| Issue ID | Description | Severity | Status | Resolution |
|----------|-------------|----------|--------|------------|
| | | | | |
| | | | | |
| | | | | |

## Final Decision

- [ ] **Ready for Release**
- [ ] **Needs Minor Fixes**
- [ ] **Needs Major Fixes**
- [ ] **Not Ready for Release**

## Notes

_Add any additional notes, observations, or recommendations here._
```

## 9. Implementation Timeline

| Phase | Week | Focus | Key Deliverables |
|-------|------|-------|------------------|
| 1 | Week 1 | Foundation Setup | TracingCollector, Database Schema, Basic CLI |
| 2 | Week 2 | LangGraph Integration | Tracing Hooks, Agent Communication, Real-Time CLI |
| 3 | Week 3 | Vector Store Integration | Query Logging, Embedding Stats, Vector CLI Tools |
| 4 | Week 4 | Jupyter Integration | Trace Analysis, Vector Visualization, Query Playground |
| 5 | Week 5 | Integration and Testing | Makefile Integration, End-to-End Testing, Documentation |
| 6 | Week 6 | Optimization and Refinement | Performance, User Experience, Final Integration |

## 10. Key Dependencies

- **Rich**: For advanced terminal formatting
- **Plotly**: For interactive visualizations in Jupyter
- **scikit-learn**: For dimensionality reduction and clustering
- **pgvector**: For vector operations in PostgreSQL
- **Jupyter**: For notebook integration
- **LangGraph**: For agent orchestration

## 11. Risk Mitigation

1. **Performance Impact**: Monitor and optimize tracing overhead
   - Implement configurable granularity levels
   - Use sampling for high-volume production environments
   - Profile and optimize critical paths

2. **Database Load**: Manage database load effectively
   - Implement connection pooling
   - Use async database access
   - Optimize queries for performance

3. **Terminal Compatibility**: Ensure compatibility across environments
   - Test on different terminal emulators
   - Implement graceful degradation for limited terminals
   - Provide alternative visualization formats

4. **Large Data Handling**: Handle large datasets efficiently
   - Implement pagination and sampling
   - Use efficient data structures
   - Optimize memory usage

5. **Integration Complexity**: Manage integration complexity
   - Use modular design with clear interfaces
   - Implement comprehensive testing
   - Document integration points clearly
