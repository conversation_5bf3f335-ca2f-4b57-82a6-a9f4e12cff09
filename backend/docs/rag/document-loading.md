# Document Loading and Initialization

This document outlines the document loading and initialization process for the RAG system, including current implementations and recommendations for production use.

## 1. 🔍 Overview

The RAG system requires documents to be loaded into the vector store for retrieval. This document describes:

1. Current document loading implementations
2. Temporary solutions for testing
3. Recommended approaches for production

## 2. 🧩 Current Implementations

### System Initialization Loading

The primary method for loading documents is integrated into the system initialization process in `backend/app/main.py`. This approach automatically loads documents when the system starts up based on environment variables:

```python
# In backend/app/main.py
if os.getenv("USE_MOCK_DOCUMENTS", "false").lower() == "true":
    try:
        # Get the mock document path from environment variable or use default
        mock_document_path = os.environ.get("MOCK_DOCUMENT_PATH", "~/.businesslm/mock_documents.json")
        mock_document_path = os.path.expanduser(mock_document_path)
        
        # Check if file exists
        if os.path.exists(mock_document_path):
            logger.info(f"Loading mock documents from {mock_document_path}")
            with open(mock_document_path, "r") as f:
                mock_documents = json.load(f)
            
            # Add documents to knowledge base
            for doc_id, doc in mock_documents.items():
                # Format the document for the knowledge base
                formatted_doc = {
                    "id": doc_id,
                    "title": doc.get("title", "Untitled"),
                    "content": doc.get("content", ""),
                    "department": doc.get("department", ""),
                    "tags": doc.get("tags", []),
                    "created_at": doc.get("created_at", ""),
                    "metadata": doc.get("metadata", {})
                }
                
                # Add the document to the knowledge base
                await knowledge_base_service.add_document(formatted_doc)
                logger.info(f"Added mock document {doc_id} to knowledge base")
```

### Mock Document Adapter

The `MockDocumentAdapter` in `backend/app/rag/mock_document_adapter.py` provides a simplified implementation for testing:

```python
def load_documents(self):
    """Load mock documents from disk."""
    try:
        # Get the mock document path from environment variable or use default
        file_path = os.environ.get("MOCK_DOCUMENT_PATH", "~/.businesslm/mock_documents.json")
        file_path = os.path.expanduser(file_path)

        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                self.documents = json.load(f)

            logger.info(f"Loaded {len(self.documents)} mock documents from {file_path}")
        else:
            logger.warning(f"No mock documents file found at {file_path}")
    except Exception as e:
        logger.error(f"Error loading mock documents: {e}")
```

### Standalone Script (Temporary Solution)

The `backend/scripts/load_mock_documents.py` script is a **temporary solution for testing purposes**. It loads documents from a JSON file and adds them to the knowledge base:

```python
async def load_documents(file_path):
    """
    Load documents from a JSON file and add them to the knowledge base.
    
    Args:
        file_path: Path to the JSON file containing the documents
    """
    try:
        # Load documents from file
        with open(file_path, 'r') as f:
            documents = json.load(f)
        
        # Initialize embedding model and vector store
        embedding_model = get_embedding_model()
        vector_store = get_vector_store()
        
        # Initialize knowledge base service
        knowledge_base = PgVectorKnowledgeBaseService(
            vector_store=vector_store,
            embedding_model=embedding_model,
            vector_weight=0.7,
            keyword_weight=0.3,
            use_reranking=False
        )
        
        # Add documents to knowledge base
        for doc_id, doc in documents.items():
            # Format the document for the knowledge base
            formatted_doc = {
                "id": doc_id,
                "title": doc.get("title", "Untitled"),
                "content": doc.get("content", ""),
                "department": doc.get("department", ""),
                "tags": doc.get("tags", []),
                "created_at": doc.get("created_at", ""),
                "metadata": doc.get("metadata", {})
            }
            
            # Add the document to the knowledge base
            await knowledge_base.add_document(formatted_doc)
    except Exception as e:
        logger.error(f"Error loading documents: {e}")
        raise
```

## 3. ⚠️ Limitations of Current Approaches

The current document loading implementations have several limitations:

1. **Manual Execution**: The standalone script requires manual execution
2. **Limited Error Handling**: Basic error handling without retry mechanisms
3. **No Progress Tracking**: No progress tracking for large document sets
4. **No Validation**: Limited validation of document structure and content
5. **No Incremental Loading**: No support for incremental loading of new documents
6. **No Document Processing Pipeline**: No preprocessing, chunking, or metadata extraction

## 4. 🚀 Recommended Production Approach

For production use, we recommend implementing a more robust document loading system:

### Document Upload API

Implement a proper document upload API endpoint that handles:

1. Document validation
2. Preprocessing (text extraction, cleaning)
3. Chunking for large documents
4. Metadata extraction
5. Embedding generation
6. Storage in the vector database

```python
# Example API endpoint
@router.post("/documents")
async def upload_document(
    document: DocumentUpload,
    background_tasks: BackgroundTasks,
    knowledge_base_service: KnowledgeBaseService = Depends(get_knowledge_base_service)
):
    """Upload a document to the knowledge base."""
    # Validate document
    validate_document(document)
    
    # Process document in background
    background_tasks.add_task(
        process_and_store_document,
        document,
        knowledge_base_service
    )
    
    return {"status": "processing", "document_id": document.id}
```

### Database Migration Scripts

For initializing the system with baseline documents, implement database migration scripts:

```python
# Example migration script
async def migrate_documents():
    """Migrate documents from JSON to vector database."""
    # Get knowledge base service
    knowledge_base_service = get_knowledge_base_service()
    
    # Load documents from JSON
    documents = load_documents_from_json()
    
    # Process and store documents
    for document in documents:
        await knowledge_base_service.add_document(document)
```

### Scheduled Document Processing

For regularly updating documents, implement a scheduled job:

```python
# Example scheduled job
@app.on_event("startup")
async def schedule_document_processing():
    """Schedule document processing job."""
    scheduler = BackgroundScheduler()
    scheduler.add_job(
        process_new_documents,
        "interval",
        hours=24
    )
    scheduler.start()
```

## 5. 🛠️ Implementation Plan

To move from the current testing implementation to a production-ready solution:

1. **Document Upload API**:
   - Implement document upload endpoint
   - Add document validation
   - Implement background processing

2. **Document Processing Pipeline**:
   - Implement text extraction for different file types
   - Add document chunking for large documents
   - Implement metadata extraction

3. **Database Migration**:
   - Create migration scripts for initial data
   - Add versioning for migrations

4. **Monitoring and Logging**:
   - Add detailed logging for document processing
   - Implement monitoring for document processing status

5. **Error Handling and Recovery**:
   - Add retry mechanisms for failed processing
   - Implement error reporting

## 6. 📝 Conclusion

The current document loading implementation using `load_mock_documents.py` is a temporary solution for testing purposes. For production use, we recommend implementing a more robust document loading system with proper API endpoints, background processing, and monitoring.

The integrated approach in `main.py` is a step in the right direction, but it should be enhanced with proper document processing, validation, and error handling for production use.
