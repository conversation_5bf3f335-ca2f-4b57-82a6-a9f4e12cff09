# RAG Embeddings Implementation

This document provides detailed implementation guidance for the embeddings component of the RAG system. For an overview of the complete RAG system, refer to the [RAG Implementation Guide](rag-implementation.md).

## Overview

The embeddings component is responsible for converting text to vector representations. These vectors capture the semantic meaning of the text and enable similarity-based retrieval.

## Embedding Models Overview

This section provides a comprehensive overview of available embedding models, their characteristics, and guidance on selecting the right model for different use cases.

We divide models into two main categories:

- **Text-Only Embedding Models**: Best for documents like Markdown, PDFs, chat logs, and general business knowledge bases.
- **Multimodal Embedding Models**: Necessary when dealing with image-heavy documents (e.g., scanned receipts, screenshots, layouts).

Depending on the project phase and needs (PoC vs. full multimodal), the appropriate model can be selected.

---

## Text-Only Embedding Models

**Text-only models are the foundation for RAG systems focused on pure textual knowledge**, such as:

- Structured business documents (contracts, reports, internal wikis)
- User queries and search tasks
- Semantic clustering of documents

| Model | Dimensions | Provider | Best Use Cases | Notes |
|-------|------------|----------|----------------|-------|
| **text-embedding-3-small** | 1024 | OpenAI | Production RAG, high-quality semantic search | Default in our implementation; excellent quality-to-cost ratio |
| **text-embedding-3-large** | 1536 | OpenAI | Maximum accuracy retrieval, complex semantic tasks | Higher cost but state-of-the-art quality for critical applications |
| **text-embedding-ada-002** | 1536 | OpenAI | Legacy support | Older model, maintained for backward compatibility |
| **intfloat/e5-base-v2** | 768 | Hugging Face (intfloat) | General retrieval tasks, semantic search, RAG | 🔥 SOTA dense retrieval; trained on BEIR; very strong for queries and documents |
| **intfloat/e5-large-v2** | 1024 | Hugging Face (intfloat) | High-accuracy retrieval, slight speed tradeoff | Larger, slower, even better retrieval quality |
| **BAAI/bge-base-en** | 768 | Hugging Face (BAAI) | Knowledge base retrieval, domain-specific search | Very strong, comparable to e5 models |
| **BAAI/bge-large-en** | 1024 | Hugging Face (BAAI) | Top recall performance, max precision | Larger, higher-quality embeddings |
| **sentence-transformers/all-mpnet-base-v2** | 768 | Hugging Face (SBERT) | General-purpose semantic retrieval | Older but reliable model |
| **sentence-transformers/all-MiniLM-L6-v2** | 384 | Hugging Face (SBERT) | Lightweight, mobile-friendly retrieval | Excellent tradeoff between size and performance |
| **Alibaba-NLP/gte-base** | 768 | Hugging Face (Alibaba) | Lightweight semantic search | Good alternative to e5 when resources are tight |
| **Alibaba-NLP/gte-large** | 1024 | Hugging Face (Alibaba) | Higher-quality variant for deeper retrieval | |
| **distilbert-base-nli-stsb-mean-tokens** | 768 | Hugging Face (SBERT) | Basic similarity search | Older generation; not ideal for production RAG today |

---

## Multimodal Embedding Models

**When documents contain both text and images**, multimodal embeddings allow unified retrieval across:

- Scanned receipts
- Invoices
- Screenshots
- PDF layouts
- Visual charts + textual content

| Model | Modalities Supported | Provider | Best Use Cases | Notes |
|-------|----------------------|----------|----------------|-------|
| **openai/clip-vit-base-patch16** | Image + Text | Hugging Face (OpenAI CLIP release) | OCR, receipts, basic visual retrieval | Runs locally, ~300MB, fast |
| **Salesforce/blip2-opt-2.7b** | Image + Text (via captioning) | Hugging Face (Salesforce) | Caption extraction before semantic search | Heavy (~15GB), for advanced visual understanding |
| **laion/CLIP-faithful** | Image + Text | Hugging Face (LAION) | Long document image search with better factual grounding | Fine-tuned CLIP |
| **microsoft/LayoutLMv3-base** | OCR layout + Text | Hugging Face (Microsoft) | Structured PDFs, invoices, form understanding | Specialized for OCR-rich, form-based documents |
| **Alibaba-pai/mplug-base** | Image + Text | Hugging Face (Alibaba) | General-purpose multimodal tasks (VQA, retrieval) | |

---

## How to Select an Embedding Model

Choosing the right model depends on:

- Document type (plain text vs. scanned image)
- Content complexity (plain sentences vs. structured layouts)
- Speed vs. accuracy needs
- Hardware constraints

### Quick Decision Flow

| Need | Suggested Model | Why |
|------|----------------|-----|
| General text RAG now | **e5-base-v2** | Best retrieval model, easy to deploy |
| High-accuracy text retrieval | **bge-large-en** | Slightly slower, extremely accurate |
| Lightweight RAG | **MiniLM-L6-v2** | Fast, low-latency |
| OCR receipts, screenshots | **CLIP-vit-base** | Simple multimodal retrieval |
| Complex document layout (e.g., invoices) | **LayoutLMv3** | Specialized in structured OCR documents |
| Future-proof full multimodal RAG | **CLIP-faithful** or **mplug-base** | End-to-end vision-language retrieval |

### Dynamic Model Selection Example

```python
def select_embedding_model(file_type: str, has_images: bool = False) -> str:
    if file_type in {"txt", "md", "docx", "html", "json"}:
        return "intfloat/e5-base-v2"
    if file_type == "pdf" and not has_images:
        return "intfloat/e5-base-v2"
    if file_type in {"pdf", "jpg", "png", "jpeg"} and has_images:
        return "openai/clip-vit-base-patch16"
    if file_type in {"pdf", "jpg", "png"} and has_images:
        return "microsoft/LayoutLMv3-base"
    return "intfloat/e5-base-v2"
```

---

## Architectural Recommendations

For a scalable system:

- Start **text-first** (PoC) → [e5-base-v2]
- Abstract an `EmbeddingBackend` interface
- Add multimodal support (CLIP, BLIP2) once OCR/receipts/image search becomes critical
- Always allow easy model swapping via factory methods or config-driven model loading

```python
class EmbeddingBackend(ABC):
    @abstractmethod
    async def embed_text(self, text: str) -> List[float]: ...

    @abstractmethod
    async def embed_image(self, image_bytes: bytes) -> List[float]: ...
```

---

## Key Takeaways

- Focus on **e5-base-v2** for text for now — it's best in class.
- Architect cleanly for future multimodal needs (OCR receipts, scanned docs).
- Keep an eye on computational costs: multimodal models like BLIP2 and LayoutLMv3 require heavier infra.
- All models listed here are open-source, freely usable, and available on Hugging Face.


## Advanced Embedding Techniques

### Late Interaction: Beyond Single-Vector Embeddings

In standard embedding retrieval, both documents and queries are compressed into a single dense vector. While this is computationally efficient, it often loses fine-grained token-level relationships — especially problematic in nuanced or multi-concept queries.

#### Single-Vector Retrieval (Traditional Approach)

| Step | Action | Limitation |
|------|--------|------------|
| 1 | Encode the entire document into one vector | Compression loses token-specific meaning |
| 2 | Encode the entire query into one vector | Same |
| 3 | Compare using cosine similarity | Surface-level semantic match only |
| 4 | Retrieve based on similarity | Misses finer-grained alignments |

⚡ Single-vector retrieval works well for simple queries and large datasets where speed and low memory footprint are priorities.

#### Late Interaction Retrieval (Advanced Approach)

Instead of compressing early, Late Interaction retains token-level embeddings for both documents and queries.

| Step | Action | Benefit |
|------|--------|---------|
| 1 | Embed each token in the document separately | Preserves token-level semantic meaning |
| 2 | Embed each query token separately | |
| 3 | Match each query token to its best matching document token | Fine-grained relevance scoring |
| 4 | Aggregate maximum similarities | More nuanced and robust retrieval scores |

The overall document relevance score is calculated as:

```
Similarity = Sum(max(dot(query_token, document_tokens)))
```

✅ Result:
- Captures subtle, specific matches between query and document content
- Reduces hallucinations and increases answer precision
- Ideal for complex business, legal, financial, or technical queries

#### When to Prefer Late Interaction

| Scenario | Recommendation |
|----------|---------------|
| Complex queries | Strongly consider Late Interaction |
| Small-to-medium knowledge base (up to a few million docs) | Suitable |
| Large web-scale corpus (>10M docs) | Single-vector may still be preferred for speed |
| Regulatory, legal, financial RAG | Highly recommended |
| Simple FAQ retrieval | Single-vector sufficient |

#### Available Late Interaction Models (Open Source)

| Model | Provider | Notes |
|-------|----------|-------|
| ColBERT | Facebook AI | Pioneering late-interaction retrieval architecture |
| ColPal | Research community | Passage-level retrieval optimization |
| ColQwen | Alibaba (Qwen team) | Modern ColBERT variant; open license |

📚 Libraries like BeIR and ColBERTv2 provide ready-to-use late interaction modules.

#### Trade-offs: Single-Vector vs Late Interaction

| Attribute | Single-Vector Retrieval | Late Interaction Retrieval |
|-----------|-------------------------|----------------------------|
| Speed | ✅ Very fast | ❗ Slower (but still manageable) |
| Storage | ✅ Compact (one vector per doc) | ❗ Heavier (token-level embeddings) |
| Retrieval Quality | 🟡 Good | ✅ Excellent |
| Suitable for Simple Queries | ✅ | 🟡 |
| Suitable for Complex Queries | 🟡 | ✅ |
| Scalability (to billions of docs) | ✅ | ❗ Needs optimization |

#### Quick Visual Summary

```mermaid
flowchart TD
    A[User Query] --> B[Embed Query Tokens Separately]
    B --> C[Embed Document Tokens Separately]
    C --> D[Compare each Query Token to all Document Tokens]
    D --> E[Max Similarity for Each Query Token]
    E --> F[Sum of Maximum Similarities]
    F --> G[Document Score]
    G --> H[Select Top Documents]
```

#### Key Takeaways

- Single-vector retrieval is great for lightweight, large-scale retrieval pipelines.
- Late interaction retrieval provides far better fine-grained relevance for RAG systems that prioritize precision over speed.
- For your evolving multi-agent RAG system, consider adopting late interaction retrieval once the base system stabilizes, especially if you aim for expert-level query answering.

#### Next Steps

If planning for future enhancements:

- Build your retriever module to optionally support Late Interaction models.
- Keep embedding logic modular to allow easy switching between single-vector and late-interaction backends.
- Benchmark both approaches once your dataset grows — start with single-vector (e5-base-v2), then add ColBERT-based retrieval later.


## Implementation Details

This section explains the implementation of the embedding model component, which is the foundation of our RAG system. The implementation follows a modular, extensible design with clear separation of concerns.

### 1. Base Class

The `EmbeddingModel` abstract base class defines the interface that all embedding implementations must follow. This ensures consistency across different embedding providers and allows for easy swapping of implementations.

**Key Design Decisions:**
- **Async Interface**: All methods are async to support non-blocking operations, crucial for both API calls and potentially slow local computations.
- **Separate Query/Document Methods**: Different methods for queries vs. documents allow for specialized processing (some models use different prompts for each).
- **Dimension Property**: Exposes the embedding dimension as a property, essential for vector store compatibility.

```python
class EmbeddingModel:
    """
    Base class for embedding models that convert text to vector representations.

    This abstract class defines the interface for all embedding models.
    Concrete implementations should override the embed_query and embed_documents methods.
    """

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        raise NotImplementedError("Subclasses must implement embed_query")

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        raise NotImplementedError("Subclasses must implement embed_documents")

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors produced by this model.

        Returns:
            The dimension (number of elements) in each embedding vector
        """
        raise NotImplementedError("Subclasses must implement dimension")
```

### 2. HuggingFace Implementation

The `HuggingFaceEmbedding` class provides a local embedding solution using HuggingFace's sentence-transformers library. This implementation runs entirely on the local machine without requiring API calls, making it suitable for development, offline usage, or cost-sensitive production environments.

**Key Features:**
- **Local Computation**: Runs entirely on the local machine
- **Model Flexibility**: Supports various HuggingFace models like e5-base-v2 and instructor models
- **Special Handling for Instructor Models**: Automatically adds appropriate prompts for instructor models
- **Non-blocking Operation**: Uses asyncio.to_thread to prevent blocking the event loop during computation
- **Configurable Device**: Can run on CPU, CUDA (NVIDIA GPU), or MPS (Apple Silicon)

```python
class HuggingFaceEmbedding(EmbeddingModel):
    """
    Embedding model using HuggingFace's sentence-transformers.

    This class uses models like intfloat/e5-base-v2 or InstructorXL to generate
    embeddings locally without requiring API calls.
    """

    def __init__(
        self,
        model_name: str = "intfloat/e5-base-v2",
        device: str = "cpu",
        normalize_embeddings: bool = True,
        **kwargs
    ):
        """
        Initialize the HuggingFace embedding model.

        Args:
            model_name: The name of the model to use (default: intfloat/e5-base-v2)
            device: The device to run the model on (default: cpu)
            normalize_embeddings: Whether to normalize embeddings to unit length (default: True)
            **kwargs: Additional arguments to pass to the model
        """
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(model_name, device=device)
            self.normalize = normalize_embeddings
            self._dimension = self.model.get_sentence_embedding_dimension()
            self.model_name = model_name
            self.is_instructor = "instructor" in model_name.lower()
        except ImportError:
            raise ImportError(
                "sentence-transformers package is required. "
                "Install with 'pip install sentence-transformers'"
            )

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        # Format text for instructor models
        if self.is_instructor:
            text = f"Represent this sentence for retrieval: {text}"

        # Use asyncio to run the embedding in a thread pool
        import asyncio
        embedding = await asyncio.to_thread(
            self.model.encode,
            text,
            normalize_embeddings=self.normalize
        )

        return embedding.tolist()

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        # Format texts for instructor models
        if self.is_instructor:
            texts = [f"Represent this document for retrieval: {text}" for text in texts]

        # Use asyncio to run the embedding in a thread pool
        import asyncio
        embeddings = await asyncio.to_thread(
            self.model.encode,
            texts,
            normalize_embeddings=self.normalize
        )

        return embeddings.tolist()

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors produced by this model.

        Returns:
            The dimension (number of elements) in each embedding vector
        """
        return self._dimension
```

### 3. OpenAI Implementation

The `OpenAIEmbedding` class provides a cloud-based embedding solution using OpenAI's API. This implementation offers high-quality embeddings without local computational overhead, making it suitable for production environments where embedding quality is paramount or where local computational resources are limited.

**Key Features:**
- **Cloud-Based Computation**: Offloads computation to OpenAI's servers
- **High-Quality Embeddings**: Leverages OpenAI's state-of-the-art embedding models
- **Custom Dimensions**: Supports requesting specific embedding dimensions
- **Async API Calls**: Uses OpenAI's async client for non-blocking operation
- **Result Ordering**: Ensures returned embeddings match the order of input texts

```python
class OpenAIEmbedding(EmbeddingModel):
    """
    Embedding model using OpenAI's embedding API.

    This class uses models like text-embedding-3-small to generate
    embeddings via API calls to OpenAI.
    """

    def __init__(
        self,
        model_name: str = "text-embedding-3-small",
        api_key: Optional[str] = None,
        dimensions: Optional[int] = None,
        **kwargs
    ):
        """
        Initialize the OpenAI embedding model.

        Args:
            model_name: The name of the model to use (default: text-embedding-3-small)
            api_key: OpenAI API key (default: from environment)
            dimensions: Output dimensions for the embeddings (default: model's default)
            **kwargs: Additional arguments to pass to the API
        """
        try:
            from openai import AsyncOpenAI
            self.api_key = api_key or os.getenv("OPENAI_API_KEY")
            if not self.api_key:
                raise ValueError("OpenAI API key is required")

            self.client = AsyncOpenAI(api_key=self.api_key)
            self.model_name = model_name
            self.dimensions = dimensions

            # Default dimensions based on model
            self._dimension = dimensions or (
                1536 if "text-embedding-ada-002" in model_name else
                1536 if "text-embedding-3-large" in model_name else
                1024 if "text-embedding-3-small" in model_name else
                1536  # Default fallback
            )
        except ImportError:
            raise ImportError(
                "openai package is required. "
                "Install with 'pip install openai'"
            )

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        params = {"model": self.model_name, "input": text}
        if self.dimensions:
            params["dimensions"] = self.dimensions

        response = await self.client.embeddings.create(**params)

        return response.data[0].embedding

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        params = {"model": self.model_name, "input": texts}
        if self.dimensions:
            params["dimensions"] = self.dimensions

        response = await self.client.embeddings.create(**params)

        # Sort by index to ensure order matches input
        sorted_data = sorted(response.data, key=lambda x: x.index)
        return [item.embedding for item in sorted_data]

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors produced by this model.

        Returns:
            The dimension (number of elements) in each embedding vector
        """
        return self._dimension
```

### 4. Factory Pattern Implementation

The `EmbeddingFactory` class implements the factory pattern to create embedding models with appropriate fallback mechanisms. This provides a clean, unified interface for creating embedding models while handling potential errors and dependencies gracefully.

**Key Features:**
- **Provider Selection**: Allows choosing between HuggingFace and OpenAI
- **Automatic Fallback**: Can automatically try alternative providers if the primary one fails
- **Configuration Forwarding**: Passes configuration options to the specific implementation
- **Robust Error Handling**: Provides clear error messages and graceful degradation

```python
class EmbeddingFactory:
    """
    Factory class for creating embedding models.

    This class provides methods for creating embedding models with
    appropriate fallback mechanisms.
    """

    @staticmethod
    def create(
        provider: Literal["huggingface", "openai"] = "huggingface",
        fallback: bool = True,
        **kwargs
    ) -> EmbeddingModel:
        """
        Create an embedding model.

        Args:
            provider: The embedding provider to use (default: huggingface)
            fallback: Whether to fall back to other providers if the requested one fails
            **kwargs: Additional arguments to pass to the embedding model

        Returns:
            An instance of EmbeddingModel

        Raises:
            ValueError: If the provider is unknown and fallback is False
            ImportError: If the required dependencies are not installed and fallback is False
        """
        try:
            if provider == "huggingface":
                return HuggingFaceEmbedding(**kwargs)
            elif provider == "openai":
                return OpenAIEmbedding(**kwargs)
            else:
                if fallback:
                    logger.warning(
                        f"Unknown provider '{provider}', falling back to huggingface"
                    )
                    return HuggingFaceEmbedding(**kwargs)
                else:
                    raise ValueError(f"Unknown embedding provider: {provider}")
        except ImportError as e:
            if not fallback:
                raise

            logger.warning(
                f"Provider '{provider}' unavailable: {str(e)}. "
                "Trying fallback providers."
            )

            # Try OpenAI if HuggingFace fails
            if provider == "huggingface":
                try:
                    return OpenAIEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )

            # Try HuggingFace if OpenAI fails
            elif provider == "openai":
                try:
                    return HuggingFaceEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )
```

## Usage Examples

The following examples demonstrate how to use the embedding models in practice. These patterns can be incorporated into your RAG pipeline for generating embeddings for queries and documents.

### Basic Usage

This example shows the standard pattern for creating an embedding model and using it to embed a query and multiple documents.

```python
# Create an embedding model
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")

# Embed a query
query = "What is our marketing strategy for Q2?"
query_embedding = await embedding_model.embed_query(query)

# Embed documents
documents = [
    "Our Q2 marketing strategy focuses on digital channels.",
    "We plan to increase social media advertising in Q2.",
    "The budget for Q2 marketing initiatives is $500,000."
]
document_embeddings = await embedding_model.embed_documents(documents)
```

### With Fallback

This example demonstrates how to use the fallback mechanism to ensure robustness in different environments. This is particularly useful in production systems where you want to ensure the embedding functionality continues to work even if the primary provider is unavailable.

```python
# Try to use HuggingFace with fallback to OpenAI
try:
    embedding_model = EmbeddingFactory.create("huggingface", fallback=True)
    logger.info(f"Using embedding model: {embedding_model.__class__.__name__}")
except ImportError:
    logger.error("No embedding providers available")
    raise
```

## Testing Strategy

### Unit Tests

1. **Test Base Class**:
   - Verify that abstract methods raise NotImplementedError

2. **Test HuggingFace Implementation**:
   - Test initialization with different models
   - Test embedding of queries and documents
   - Verify dimension property returns correct value
   - Test error handling for missing dependencies

3. **Test OpenAI Implementation**:
   - Test initialization with different models
   - Test embedding of queries and documents
   - Verify dimension property returns correct value
   - Test error handling for missing API key
   - Test error handling for missing dependencies

4. **Test Factory Function**:
   - Test creation of different providers
   - Test fallback behavior
   - Test error handling for unknown providers

### Integration Tests

1. **Test with Vector Store**:
   - Verify that embeddings can be stored and retrieved
   - Test similarity search with embedded queries

2. **Test with Retriever**:
   - Verify that embeddings are used correctly for retrieval
   - Test end-to-end retrieval pipeline

## Dependencies

- **sentence-transformers**: For HuggingFace embeddings
- **openai**: For OpenAI embeddings
- **numpy**: For vector operations
- **asyncio**: For asynchronous processing

## Implementation Plan

1. **Create Base Class**:
   - Define the EmbeddingModel abstract base class
   - Implement required methods and properties

2. **Implement HuggingFace Embedding**:
   - Create HuggingFaceEmbedding class
   - Implement embed_query and embed_documents methods
   - Add support for different models and configurations

3. **Implement OpenAI Embedding**:
   - Create OpenAIEmbedding class
   - Implement embed_query and embed_documents methods
   - Add support for different models and dimensions

4. **Create Factory Function**:
   - Implement EmbeddingFactory class
   - Add create method with fallback mechanism
   - Test with different providers and configurations

5. **Write Tests**:
   - Create unit tests for each component
   - Create integration tests for the complete pipeline
   - Verify correct behavior with different configurations


## Conclusion

The embeddings component is the foundation of the RAG system, enabling semantic search and retrieval. By implementing both HuggingFace and OpenAI embedding models with a fallback mechanism, we ensure robustness and flexibility in different deployment scenarios.

The modular design allows for easy extension with additional embedding providers in the future, while the asynchronous API ensures efficient processing of queries and documents.

For the initial implementation, we recommend starting with single-vector retrieval using the e5-base-v2 model, which provides an excellent balance of quality and performance. As the system matures and more complex query handling is required, consider implementing late interaction retrieval for improved precision.
