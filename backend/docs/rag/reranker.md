# 🔄 Cross-Encoder Reranker

This document outlines the architecture and implementation details for the cross-encoder reranker component in BusinessLM's RAG system. The reranker significantly improves search result relevance by applying a more sophisticated model to rerank initial retrieval results.

> **Note**: This documentation serves as a comprehensive reference for the cross-encoder reranker implementation in BusinessLM. It covers both the architectural design and implementation details to provide a complete understanding of the system.

<div align="right"><a href="#-cross-encoder-reranker">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
   - [Purpose and Benefits](#purpose-and-benefits)
   - [Key Features](#key-features)
   - [Core Components](#core-components)
   - [Architecture](#architecture)
2. [�� Quickstart (Local Development)](#-quickstart-local-development)
3. [🧩 Core Functionality](#-core-functionality)
   - [Reranking Process](#reranking-process)
   - [Cross-Encoder Models](#cross-encoder-models)
   - [Error Handling and Resilience](#error-handling-and-resilience)
4. [📝 API Design](#-api-design)
   - [Class Structure](#class-structure)
   - [Interface Design Decisions](#interface-design-decisions)
   - [Usage Examples](#usage-examples)
5. [⚙️ Implementation Details](#️-implementation-details)
   - [Model Selection](#model-selection)
   - [Batch Processing](#batch-processing)
   - [Score Normalization](#score-normalization)
   - [Integration with Knowledge Base](#integration-with-knowledge-base)
6. [🛡️ Error Handling and Resilience](#️-error-handling-and-resilience)
   - [Timeout Mechanism](#timeout-mechanism)
   - [Retry Logic](#retry-logic)
   - [Graceful Degradation](#graceful-degradation)
7. [🚀 Performance Considerations](#-performance-considerations)
   - [Optimization Strategies](#optimization-strategies)
   - [Scalability](#scalability)
   - [Memory Management](#memory-management)
8. [🧪 Testing Strategy](#-testing-strategy)
   - [Unit Tests](#unit-tests)
   - [Integration Tests](#integration-tests)
   - [Evaluation Metrics](#evaluation-metrics)
   - [Golden Tests](#golden-tests)
9. [🔮 Future Enhancements](#-future-enhancements)
10. [📦 Deployment Considerations](#-deployment-considerations)
11. [📝 Conclusion](#-conclusion)

<div align="right"><a href="#-cross-encoder-reranker">⬆️ Back to top</a></div>

## 🔍 Overview

### Purpose and Benefits

The Cross-Encoder Reranker is a specialized component that significantly improves the relevance of search results by reranking them using a more powerful model. While the initial retrieval uses efficient but less accurate methods like vector search or keyword search, the reranker applies a more sophisticated model to a smaller set of candidate results to improve their ordering.

This two-stage approach combines the efficiency of first-stage retrieval with the accuracy of cross-encoder models, providing an optimal balance between performance and relevance. The reranker addresses the "semantic gap" problem in information retrieval, where initial retrieval methods may miss semantically relevant documents that don't share exact keywords with the query.

Key benefits include:

- **🎯 Improved Relevance**: More accurate ranking of search results (typically 15-30% improvement in NDCG metrics)
- **🧠 Deeper Semantic Understanding**: Better matching of query intent to document content through cross-attention mechanisms
- **⚖️ Balanced Approach**: Combines efficient retrieval with accurate reranking for optimal performance-quality tradeoff
- **�� Flexible Integration**: Works with any retrieval method (vector, keyword, hybrid) without requiring changes to the retrieval pipeline
- **🛡️ Graceful Degradation**: Falls back to original ranking if reranking fails, ensuring system resilience
- **📊 Improved User Experience**: Higher quality results lead to better user satisfaction and reduced need for query reformulation
- **🔍 Enhanced Precision**: Particularly effective for complex queries where keyword matching is insufficient

### Key Features

The Cross-Encoder Reranker provides the following key features:

- **Bi-directional Attention**: Uses transformer cross-attention to model complex query-document interactions
- **Configurable Model Selection**: Supports multiple pre-trained cross-encoder models with different size/performance tradeoffs
- **Batch Processing**: Efficiently processes multiple query-document pairs in parallel
- **Score Normalization**: Combines reranking scores with original scores using configurable weights
- **Timeout Handling**: Implements timeouts to prevent performance degradation from slow inference
- **Retry Logic**: Includes retry mechanisms for transient failures
- **Async Interface**: Fully asynchronous API for non-blocking operation
- **Graceful Fallbacks**: Falls back to original ranking when reranking fails
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

### Core Components

The system consists of the following key components:

- **Reranker Interface**: Abstract base class defining the reranking contract
- **Cross-Encoder Implementation**: Concrete implementation using transformer-based cross-encoder models
- **Model Initialization**: Lazy loading of models to minimize startup time
- **Batch Processing Logic**: Efficient batching of query-document pairs
- **Score Combination**: Algorithms for combining original and reranked scores
- **Error Handling**: Comprehensive error handling and fallback mechanisms
- **Factory Function**: Convenient factory method for creating reranker instances

### Architecture

The Cross-Encoder Reranker fits into the RAG system as follows:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Query          │────►│  Initial        │────►│  Candidate      │
│                 │     │  Retrieval      │     │  Results        │
│                 │     │  (Vector/       │     │  (Top-K         │
│                 │     │   Keyword)      │     │   Documents)    │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                   Cross-Encoder Reranker                        │
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐       │
│  │             │     │             │     │             │       │
│  │  Pair       │────►│  Score      │────►│  Reorder    │       │
│  │  Creation   │     │  Calculation│     │  Results    │       │
│  │             │     │             │     │             │       │
│  └─────────────┘     └─────────────┘     └─────────────┘       │
│                                                                 │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                │
                                ▼
                      ┌─────────────────┐
                      │                 │
                      │  Reranked       │
                      │  Results        │
                      │  (Improved      │
                      │   Relevance)    │
                      └─────────────────┘
```

The reranker integrates seamlessly with the Knowledge Base Service, providing a significant relevance boost with minimal changes to the existing retrieval pipeline. It can be enabled or disabled on a per-query basis, allowing for flexible deployment strategies based on performance requirements.

<div align="right"><a href="#-cross-encoder-reranker">⬆️ Back to top</a></div>

## 🚀 Quickstart (Local Development)

To use the Cross-Encoder Reranker in your local development environment:

```python
# Install required dependencies
# pip install -r requirements.txt  # or uv pip install -r requirements.txt

from app.rag.reranker import get_reranker
from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService

# Initialize the reranker
reranker = get_reranker(
    reranker_type="cross-encoder",
    model_name="cross-encoder/ms-marco-MiniLM-L-6-v2",
    device="cpu",  # Use "cuda" if GPU is available
    batch_size=16
)

# Create knowledge base service with reranker
knowledge_base = PgVectorKnowledgeBaseService(
    embedding_model=embedding_model,
    vector_store=vector_store,
    reranker=reranker,
    use_reranking=True,
    reranking_candidates=20
)

# Search with automatic reranking
results = await knowledge_base.search(
    query="What is our marketing strategy for Q2?",
    search_type="hybrid",
    limit=5
)

# Explicitly control reranking for specific queries
results = await knowledge_base.search(
    query="What is our marketing strategy for Q2?",
    search_type="hybrid",
    limit=5,
    use_reranking=True  # Can be set to False to disable reranking for this query
)
```
