# RAG Implementation Guide

This document provides a comprehensive guide for implementing the Retrieval-Augmented Generation (RAG) system in the Python migration.

---

## 1. Overview

### System Purpose and Benefits

The RAG system enhances LLM responses with relevant information retrieved from a knowledge base. This approach offers several key benefits:

- **Improved Accuracy**: Responses are grounded in factual information from trusted sources
- **Reduced Hallucination**: LLMs are less likely to generate incorrect information when provided with relevant context
- **Domain Adaptation**: General-purpose LLMs can provide domain-specific responses without fine-tuning
- **Knowledge Recency**: Access to up-to-date information beyond the LLM's training cutoff
- **Source Attribution**: Responses can include citations to source documents for verification

### High-Level Architecture

The RAG system consists of several components that work together to provide contextually informed responses:

```
backend/app/rag/
├── __init__.py           # Package exports
├── embeddings.py         # Text-to-vector conversion
├── vector_store.py       # Vector storage and retrieval
├── retriever.py          # Query processing and document retrieval
├── generator.py          # Context-enhanced response generation
├── pgvector_knowledge_base.py # PostgreSQL knowledge base service
├── knowledge_base_factory.py # Factory for creating knowledge base services
└── query_analyzer.py     # Query intent analysis (existing)
```

### Key Components and Interactions

The RAG system operates through the following flow:

1. **Query Processing**:
   - User query is received
   - `QueryAnalyzer` determines intent and relevant departments
   - Query is optionally rewritten or decomposed

2. **Document Retrieval**:
   - Query is embedded using `EmbeddingModel`
   - `Retriever` fetches relevant documents from `VectorStore`
   - Retrieved documents are processed and formatted

3. **Response Generation**:
   - `Generator` combines query and retrieved context
   - LLM generates a response with citations
   - Response is returned to the user

### 🔧 Core RAG Strategies

To ensure high-quality, trustworthy responses in a complex, multi-source environment, the system adopts **layered RAG strategies** that prioritize:

- **High recall** — retrieving all relevant context
- **High precision** — surfacing the most useful content
- **Scoped reasoning** — enabling domain- or department-specific intelligence

| Strategy                        | Description                                                                                                     | Architectural Role                                                             |
|---------------------------------|-----------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------|
| **🔁 Hybrid Search + Reranking** | Combines semantic (vector) retrieval and keyword (BM25) search using **Reciprocal Rank Fusion (RRF)**.         | Core to the `Retriever` — boosts recall and precision for diverse query types  |
| **🧠 Multi-Agent RAG**           | Task- and domain-specialized agents handle retrieval, generation, and orchestration based on scoped memory.    | Powered by LangGraph — enables intent routing, department-specific context     |

This dual-layer strategy ensures queries are handled by the most capable agents, using both **semantic similarity** and **exact keyword matches**, then reranked to return the most reliable context chunks.

---

### 🧭 Future Strategic Enhancements

As the system matures, these advanced RAG patterns can be layered in to handle more complex use cases — from regulatory compliance to multimodal reasoning.

| Strategy                      | Use Case Example                                      | Status       | Integration Path                                                   |
|-------------------------------|--------------------------------------------------------|--------------|--------------------------------------------------------------------|
| **🧬 Fine-Tuned Embeddings**   | Legal, medical, or financial documents                 | 🔜 Planned    | Drop-in replacement in `EmbeddingFactory`                          |
| **🧩 Sub-Query Decomposition** | “Compare Q1 and Q2 trends across revenue & expenses”   | 🚧 In Progress | `QueryAnalyzer` + Planner Agent (LangGraph node decomposition)     |
| **🧾 Semantic Tree Navigation**| PageIndex-style reasoning over long PDFs               | ⏳ Future     | Replace chunking logic with section-aware tree walker              |
| **👻 Hypothetical Reasoning**  | Filling in knowledge gaps with GPT-based “ghost docs” | ⏳ Future     | Generator enhancements via assumption injection or scaffolding     |
| **📉 Confidence Scoring**      | Regulated domains (e.g., finance, healthcare)          | ⏳ Future     | `SelfCritiqueGenerator` + Co-CEO self-evaluation layer             |
| **🧠 Multimodal + Graph RAG**  | Tables, charts, diagrams, structured data graphs       | ⏳ Future     | Future plugins for Gemini/Claude + LangGraph-based graph traversal|

> ⚙️ These RAG patterns are **modular** — they can be layered, combined, or routed dynamically based on the **query complexity**, **agent scope**, and **retrieval confidence**.

---

## 2. Component Details

### Embeddings

The `embeddings.py` module provides functionality for converting text to vector representations using various embedding models.

#### `EmbeddingModel` Class

```python
class EmbeddingModel:
    """Base class for embedding models that convert text to vector representations."""

    async def embed_query(self, text: str) -> List[float]:
        """Convert a query text to a vector representation."""
        pass

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Convert a list of document texts to vector representations."""
        pass
```

#### Concrete Implementations

1. **HuggingFaceEmbedding**
   - Primary embedding model using `intfloat/e5-base-v2` or `InstructorXL`
   - 768-dimensional embeddings
   - Local computation for efficiency

2. **OpenAIEmbedding**
   - Fallback embedding model using `text-embedding-3-small`
   - Used when local computation is not feasible
   - Requires API key

3. **EmbeddingFactory**
   - Factory function to create appropriate embedding model
   - Supports fallback mechanism similar to LLM adapter

### Vector Store

The `vector_store.py` module provides functionality for storing and retrieving vector embeddings efficiently.

#### `VectorStore` Class

```python
class VectorStore:
    """Base class for vector storage and retrieval."""

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: List[Dict[str, Any]]
    ) -> List[str]:
        """Add embeddings to the vector store."""
        pass

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors in the store."""
        pass
```

#### Concrete Implementation

**FirestoreVectorStore**
- Stores embeddings in Firestore collection
- Implements in-memory similarity search
- Supports metadata filtering
- Uses existing Firebase client

### Retriever

The `retriever.py` module provides functionality for processing queries and retrieving relevant documents.

#### `Retriever` Class

```python
class Retriever:
    """Base class for document retrieval."""

    async def retrieve(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query."""
        pass
```

#### Concrete Implementations

1. **HybridRetriever**
   - Combines vector similarity and keyword search
   - Uses Reciprocal Rank Fusion (RRF) to merge results
   - Supports metadata filtering

2. **ContextWindowManager**
   - Manages context window size for LLM
   - Truncates or summarizes retrieved documents as needed
   - Ensures context fits within token limits

### Generator

The `generator.py` module provides functionality for combining retrieved context with LLM generation.

#### `Generator` Class

```python
class Generator:
    """Base class for context-enhanced response generation."""

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        **kwargs
    ) -> str:
        """Generate a response based on the query and retrieved context."""
        pass
```

#### Concrete Implementation

**RAGGenerator**
- Formats retrieved context with citations
- Constructs prompts for the LLM
- Uses the LLM adapter for generation
- Supports streaming responses

### Knowledge Base Service

The `knowledge_base.py` module provides a service for interacting with the knowledge base.

#### `KnowledgeBaseService` Class (Enhanced)

```python
class KnowledgeBaseService:
    """Service for interacting with the knowledge base."""

    async def search(
        self,
        query: str,
        collection: str = "knowledge",
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Search for documents in the knowledge base."""
        pass

    async def add_document(
        self,
        document: Dict[str, Any],
        collection: str = "knowledge"
    ) -> str:
        """Add a document to the knowledge base."""
        pass
```

#### Enhancements

- Add vector search capabilities using embeddings
- Support for document chunking and processing
- Integration with the vector store

### Query Analyzer

The `query_analyzer.py` module provides functionality for analyzing query intent and relevant departments.

#### `QueryAnalyzer` Class (Enhanced)

```python
class QueryAnalyzer:
    """Analyzer for user queries."""

    async def analyze(self, query: str) -> Dict[str, Any]:
        """Analyze a query to determine its intent and relevant departments."""
        pass
```

#### Enhancements

- Query rewriting for better retrieval
- Query decomposition for complex queries
- Integration with the retriever

## 3. Implementation Roadmap

### Must-Have Components for the PoC

The following components are essential for the proof-of-concept implementation and should be prioritized:

1. **Embedding-Based Similarity in Query Analyzer** ⭐
   - Integrate the embedding model with the query analyzer
   - Implement embedding-based department detection
   - Add confidence scoring for detected departments

2. **Hybrid Detection Approach in Query Analyzer** ⭐
   - Combine embedding-based, keyword-based, and LLM-based detection
   - Implement weighted scoring for different methods
   - Add threshold filtering for reliable results

3. **Error Handling and Fallbacks** ⭐
   - Implement graceful degradation when methods fail
   - Add retry logic for external service calls
   - Include detailed logging for troubleshooting

4. **Integration Between Components** ⭐
   - Connect the query analyzer with the knowledge base service
   - Ensure the query analyzer results guide the knowledge base retrieval
   - Create a cohesive RAG pipeline

> **⚠️ PoC Implementation Note**: These components are the minimum required for a functional proof-of-concept. They should be implemented first before moving on to other components.

### Phase 1: Core Components

#### Embedding Model Implementation

**Estimated Time: 2-3 days**

1. **Create Base Class**:
   - Implement `EmbeddingModel` abstract base class
   - Define interface methods: `embed_query`, `embed_documents`, `dimension`

2. **Implement HuggingFace Embedding**:
   - Create `HuggingFaceEmbedding` class
   - Support for `intfloat/e5-base-v2` model
   - Implement async embedding with `asyncio.to_thread`

3. **Implement OpenAI Embedding**:
   - Create `OpenAIEmbedding` class
   - Support for `text-embedding-3-small` model
   - Implement async API calls

4. **Create Factory Function**:
   - Implement `EmbeddingFactory` class
   - Add fallback mechanism
   - Support for different configurations

5. **Write Tests**:
   - Unit tests for each implementation
   - Test fallback behavior
   - Test with different models

#### Vector Store Implementation

**Estimated Time: 2-3 days**

1. **Create Base Class**:
   - Implement `VectorStore` abstract base class
   - Define interface methods: `add_embeddings`, `search`, `delete`, `get`

2. **Implement Firestore Vector Store**:
   - Create `FirestoreVectorStore` class
   - Implement document storage in Firestore
   - Implement in-memory similarity search

3. **Add Caching Enhancement**:
   - Create `CachedFirestoreVectorStore` class
   - Implement LRU cache for frequently accessed embeddings
   - Add cache invalidation logic

4. **Write Tests**:
   - Unit tests for each implementation
   - Test with different distance metrics
   - Test caching behavior

#### Enhance Knowledge Base Service

**Estimated Time: 1-2 days**

1. **Update Existing Service**:
   - Enhance `KnowledgeBaseService` with vector search capabilities
   - Add methods for document processing and chunking
   - Integrate with embedding model and vector store

2. **Add Document Ingestion**:
   - Implement document chunking and processing
   - Add methods for adding documents to the knowledge base
   - Support for metadata extraction

3. **Write Tests**:
   - Unit tests for enhanced functionality
   - Test with different document types
   - Test integration with other components

### Phase 2: Advanced Components

#### Retriever Implementation

**Estimated Time: 2-3 days**

1. **Create Base Class**:
   - Implement `Retriever` abstract base class
   - Define interface methods: `retrieve`

2. **Implement Hybrid Retriever**:
   - Create `HybridRetriever` class
   - Implement vector and keyword search
   - Add Reciprocal Rank Fusion for result combination

3. **Add Context Window Management**:
   - Create `ContextWindowManager` class
   - Implement token counting and document truncation
   - Add context window fitting logic

4. **Implement Query Rewriting**:
   - Create `QueryRewriter` class
   - Implement LLM-based query rewriting
   - Add error handling

5. **Write Tests**:
   - Unit tests for each implementation
   - Test with different configurations
   - Test integration with other components

#### Generator Implementation

**Estimated Time: 2-3 days**

1. **Create Base Class**:
   - Implement `Generator` abstract base class
   - Define interface methods: `generate`

2. **Implement RAG Generator**:
   - Create `RAGGenerator` class
   - Implement context formatting and prompt construction
   - Add citation mechanism

3. **Add Self-Critique Enhancement**:
   - Create `SelfCritiqueGenerator` class
   - Implement critique and refinement logic
   - Test with different critique strategies

4. **Implement Streaming Enhancement**:
   - Create `StreamingRAGGenerator` class
   - Add progress indicators
   - Implement enhanced streaming support

5. **Write Tests**:
   - Unit tests for each implementation
   - Test with different configurations
   - Test integration with other components

### Phase 3: Integration and Optimization

#### End-to-End Integration

**Estimated Time: 1-2 days**

1. **Create RAG Pipeline**:
   - Implement end-to-end RAG pipeline
   - Add configuration options
   - Create usage examples

2. **Integrate with Agents**:
   - Create `RAGTool` for agent use
   - Implement department-specific RAG
   - Add to agent toolkits

3. **Write Integration Tests**:
   - Test complete RAG pipeline
   - Test with different configurations
   - Test with agent integration

#### Performance Optimization

**Estimated Time: 1-2 days**

1. **Optimize Vector Search**:
   - Implement batching for large collections
   - Add indexing for faster retrieval
   - Optimize similarity calculations

2. **Enhance Caching**:
   - Implement more sophisticated caching strategies
   - Add cache warming for frequent queries
   - Optimize cache invalidation

3. **Add Monitoring**:
   - Implement performance metrics
   - Add logging for debugging
   - Create dashboard for monitoring

### Phase 4: Future Enhancements

#### PostgreSQL + pgvector Implementation

**Estimated Time: Completed**

1. **Set Up PostgreSQL**:
   - Install PostgreSQL with pgvector extension
   - Create schema for documents and embeddings
   - Set up indexing for vector search

2. **Implement PostgreSQL Vector Store**:
   - Create `PgVectorStore` class
   - Implement vector search using pgvector
   - Add advanced filtering capabilities

3. **Implement Knowledge Base Service**:
   - Create `PgVectorKnowledgeBaseService` class
   - Implement vector, keyword, and hybrid search
   - Add factory for creating knowledge base services

#### Advanced RAG Enhancements

**Estimated Time: 3-5 days (future work)**

1. **Implement Semantic Tree Navigation**:
   - Create hierarchical document representation
   - Implement tree-based navigation
   - Add reasoning-first retrieval

2. **Add Confidence Scoring**:
   - Implement response confidence estimation
   - Add uncertainty indicators
   - Create confidence-based fallback mechanisms

3. **Implement Hypothetical Document Reasoning**:
   - Create "ghost document" generation
   - Implement reasoning about missing information
   - Add knowledge gap identification

## 4. Implementation Timeline

### Estimated Time for Each Component

| Phase | Component | Estimated Time | Dependencies |
|-------|-----------|----------------|--------------|
| 1 | Embedding Model | 2-3 days | None |
| 1 | Vector Store | 2-3 days | Embedding Model |
| 1 | Knowledge Base Enhancement | 1-2 days | Embedding Model, Vector Store |
| 2 | Retriever | 2-3 days | Knowledge Base Enhancement |
| 2 | Generator | 2-3 days | None |
| 3 | End-to-End Integration | 1-2 days | All Phase 1 & 2 Components |
| 3 | Performance Optimization | 1-2 days | End-to-End Integration |
| 4 | PostgreSQL Migration | 3-5 days | All Previous Phases |
| 4 | Advanced RAG Enhancements | 3-5 days | All Previous Phases |

**Total Estimated Time for Phases 1-3 (PoC): 10-16 days**

### Dependencies Between Components

- **Embedding Model**: Foundation for vector search, required by Vector Store
- **Vector Store**: Depends on Embedding Model, required by Knowledge Base Enhancement
- **Knowledge Base Enhancement**: Depends on Embedding Model and Vector Store, required by Retriever
- **Retriever**: Depends on Knowledge Base Enhancement, used by End-to-End Integration
- **Generator**: Independent component, used by End-to-End Integration
- **End-to-End Integration**: Depends on all Phase 1 & 2 components
- **Performance Optimization**: Depends on End-to-End Integration
- **PostgreSQL Migration**: Depends on all previous phases
- **Advanced RAG Enhancements**: Depends on all previous phases

### Priority Order

For the proof of concept, we will focus on implementing the must-have components first, followed by the remaining components in Phases 1-3. Phase 4 enhancements can be implemented later as the system matures.

#### Must-Have Components (Highest Priority)

1. **Embedding-Based Similarity in Query Analyzer** ⭐: Integrate embedding model with query analyzer
2. **Hybrid Detection Approach in Query Analyzer** ⭐: Combine multiple detection methods
3. **Error Handling and Fallbacks** ⭐: Ensure robust operation
4. **Integration Between Components** ⭐: Create a cohesive RAG pipeline

#### Remaining Components

5. **Embedding Model**: Foundation for vector search
6. **Vector Store**: Storage and retrieval of embeddings
7. **Knowledge Base Enhancement**: Integration with existing service
8. **Retriever**: Intelligent document retrieval
9. **Generator**: Context-enhanced response generation
10. **End-to-End Integration**: Complete RAG pipeline
11. **Performance Optimization**: Improved efficiency and scalability

## 5. Next Steps

### Starting with Must-Have Components

To begin implementing the RAG system for the proof-of-concept, focus on the must-have components first:

1. **Enhance Query Analyzer with Embedding-Based Similarity** ⭐:
   - Update the `QueryAnalyzer` constructor to accept an embedding model
   - Add department descriptions for embedding-based comparison
   - Implement embedding-based department detection
   - Add confidence scoring for detected departments

2. **Implement Hybrid Detection Approach** ⭐:
   - Combine embedding-based, keyword-based, and LLM-based detection
   - Add method-specific weights for different detection methods
   - Implement weighted scoring for final confidence calculation
   - Add threshold filtering for reliable results

3. **Add Error Handling and Fallbacks** ⭐:
   - Implement graceful degradation when methods fail
   - Add retry logic for embedding model and LLM calls
   - Include detailed logging for troubleshooting
   - Ensure at least one department is returned even in failure cases

4. **Create Integration Between Components** ⭐:
   - Create a higher-level RAG service that combines the query analyzer and knowledge base
   - Ensure the query analyzer results guide the knowledge base retrieval
   - Implement a factory function for creating the complete RAG pipeline
   - Add integration tests for the end-to-end flow

### Implementation Approach

For each component:

1. **Create Base Class**:
   - Define the interface with abstract methods
   - Document the expected behavior
   - Add type hints for better IDE support

2. **Implement Concrete Classes**:
   - Follow the detailed implementation guides
   - Add error handling and logging
   - Support configuration options
   - Implement fallback mechanisms

3. **Write Tests**:
   - Create unit tests for each class
   - Test with different configurations
   - Test error handling and edge cases
   - Create integration tests for component interactions

4. **Document Usage**:
   - Add docstrings to all classes and methods
   - Create usage examples
   - Update architecture documentation as needed

### Testing Strategy

1. **Unit Tests**:
   - Test each class in isolation
   - Mock dependencies for predictable behavior
   - Test error handling and edge cases
   - Test with different configurations

2. **Integration Tests**:
   - Test component interactions
   - Test end-to-end RAG pipeline
   - Test with different document types and queries
   - Test performance with varying load

3. **Evaluation Metrics**:
   - Retrieval precision and recall
   - Response relevance and accuracy
   - Performance benchmarks
   - User satisfaction metrics

## 6. Detailed Implementation Guides

For detailed implementation guidance on each component, refer to:

- [Embeddings Implementation](embeddings.md) - Detailed guide for implementing the embedding models
- [Vector Store Implementation](vector-store.md) - Detailed guide for implementing the vector storage
- [Retriever Implementation](retriever.md) - Detailed guide for implementing the document retrieval
- [Generator Implementation](generator.md) - Detailed guide for implementing the response generation
- [Document Loading](document-loading.md) - Detailed guide for document loading and initialization

## 7. Appendix

### Usage Examples

#### Basic RAG Pipeline

```python
# Initialize components
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
vector_store = FirestoreVectorStore(firebase_client)
retriever = HybridRetriever(embedding_model, vector_store, knowledge_base_service)
generator = RAGGenerator(llm_adapter)

# Process a query
query = "What is our marketing strategy for Q2?"
context = await retriever.retrieve(query, limit=5)
response = await generator.generate(query, context)
```

### Document Ingestion

```python
# Initialize components
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
vector_store = FirestoreVectorStore(firebase_client)
knowledge_base = KnowledgeBaseService(firebase_client, embedding_model, vector_store)

# Process and store a document
document = {
    "title": "Q2 Marketing Strategy",
    "content": "Our Q2 marketing strategy focuses on...",
    "metadata": {
        "department": "marketing",
        "date": "2023-04-01"
    }
}

doc_id = await knowledge_base.add_document(document)
```

### With Self-Critique

```python
# Initialize components
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create generator with self-critique
generator = SelfCritiqueGenerator(
    llm_adapter=llm_adapter,
    include_citations=True,
    citation_format="inline"
)

# Generate response with self-critique
response = await generator.generate(query, context)
```

### API Reference

#### Embedding Model

```python
class EmbeddingModel:
    async def embed_query(self, text: str) -> List[float]: ...
    async def embed_documents(self, texts: List[str]) -> List[List[float]]: ...
    @property
    def dimension(self) -> int: ...

class HuggingFaceEmbedding(EmbeddingModel):
    def __init__(
        self,
        model_name: str = "intfloat/e5-base-v2",
        device: str = "cpu",
        normalize_embeddings: bool = True,
        **kwargs
    ): ...

class OpenAIEmbedding(EmbeddingModel):
    def __init__(
        self,
        model_name: str = "text-embedding-3-small",
        api_key: Optional[str] = None,
        dimensions: Optional[int] = None,
        **kwargs
    ): ...

class EmbeddingFactory:
    @staticmethod
    def create(
        provider: Literal["huggingface", "openai"] = "huggingface",
        fallback: bool = True,
        **kwargs
    ) -> EmbeddingModel: ...
```

#### Vector Store

```python
class VectorStore:
    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]: ...

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]: ...

    async def delete(self, ids: List[str]) -> None: ...

    async def get(self, ids: List[str]) -> List[Dict[str, Any]]: ...

class FirestoreVectorStore(VectorStore):
    def __init__(
        self,
        firebase_client,
        collection_name: str = "embeddings",
        distance_metric: str = "cosine",
        **kwargs
    ): ...

class CachedFirestoreVectorStore(FirestoreVectorStore):
    def __init__(
        self,
        firebase_client,
        collection_name: str = "embeddings",
        distance_metric: str = "cosine",
        cache_size: int = 1000,
        **kwargs
    ): ...
```

#### Retriever

```python
class Retriever:
    async def retrieve(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]: ...

class HybridRetriever(Retriever):
    def __init__(
        self,
        embedding_model: EmbeddingModel,
        vector_store: VectorStore,
        knowledge_base_service: KnowledgeBaseService,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        **kwargs
    ): ...

class ContextWindowManager:
    def __init__(
        self,
        llm_adapter,
        max_tokens: int = 4000,
        token_buffer: int = 1000,
        **kwargs
    ): ...

    async def fit_to_context_window(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]: ...
```

#### Generator

```python
class Generator:
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        **kwargs
    ) -> str: ...

class RAGGenerator(Generator):
    def __init__(
        self,
        llm_adapter,
        include_citations: bool = True,
        citation_format: str = "inline",
        **kwargs
    ): ...

class SelfCritiqueGenerator(RAGGenerator):
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]: ...

class StreamingRAGGenerator(RAGGenerator):
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = True,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]: ...
```

### Troubleshooting

#### Common Issues

1. **Missing Dependencies**:
   - Error: `ImportError: No module named 'sentence_transformers'`
   - Solution: Install required packages with `pip install sentence-transformers`

2. **API Key Issues**:
   - Error: `ValueError: OpenAI API key is required`
   - Solution: Set the `OPENAI_API_KEY` environment variable or pass the key explicitly

3. **Memory Issues with Large Embeddings**:
   - Error: `MemoryError` when processing large documents
   - Solution: Implement batching for document processing or use a smaller embedding model

4. **Slow Vector Search**:
   - Issue: Vector search is too slow with large collections
   - Solution: Implement caching, use indexing, or consider migrating to pgvector

5. **Context Window Overflow**:
   - Error: `InvalidRequestError: This model's maximum context length is 4097 tokens`
   - Solution: Use the `ContextWindowManager` to fit documents to the context window

#### Debugging Tips

1. **Enable Verbose Logging**:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Test Components in Isolation**:
   - Test embedding model separately
   - Test vector store with known embeddings
   - Test retriever with mock vector store

3. **Use Smaller Test Sets**:
   - Start with a small number of documents
   - Use shorter documents for testing
   - Gradually increase size as components are validated

4. **Monitor Performance**:
   - Track embedding time
   - Measure vector search latency
   - Monitor memory usage

5. **Check Intermediate Results**:
   - Print embeddings to verify dimensions
   - Inspect retrieved documents before generation
   - Examine similarity scores for relevance

## Conclusion

This RAG implementation provides a solid foundation for enhancing LLM responses with relevant context. By leveraging existing Firebase infrastructure for the PoC and planning for a future migration to PostgreSQL + pgvector, we balance immediate needs with long-term scalability.

The modular design allows for incremental implementation and testing, while the integration with existing components ensures compatibility with the broader system. The focus on testing and documentation throughout the process ensures a robust and maintainable implementation.
