# 📚 RAG Vector Store Implementation

This document provides detailed implementation guidance for the vector store component of the RAG system. For an overview of the complete RAG system, refer to the [RAG Implementation Guide](rag-implementation.md).

> **Note**: This documentation serves as a comprehensive reference for the vector store implementation in `backend/app/rag/vector_store.py`. It covers both architectural design and implementation details to provide a complete understanding of the system.
>
> For a comprehensive guide to the entire PostgreSQL + pgvector implementation, including vector storage and retrieval, please refer to the main documentation at `backend/docs/postgresql-pgvector-implementation.md`.

<div align="right"><a href="#-rag-vector-store-implementation">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🏗️ Architecture](#️-architecture)
3. [🧩 Component Details](#-component-details)
4. [🔄 Backend Implementations](#-backend-implementations)
5. [📝 Usage Examples](#-usage-examples)
6. [🔌 Adding New Backends](#-adding-new-backends)
7. [🧪 Testing Strategy](#-testing-strategy)
8. [📊 Performance Considerations](#-performance-considerations)
9. [⚠️ Troubleshooting](#️-troubleshooting)
10. [🔒 Security & Persistence](#-security--persistence)
11. [🚀 Future Enhancements](#-future-enhancements)

## 🔍 Overview

The vector store component is responsible for storing and retrieving vector embeddings efficiently. It serves as the foundation for semantic search capabilities in the RAG system, enabling similarity-based retrieval of relevant documents.

### Key Features

- **Unified Interface**: Consistent API across different vector store backends
- **Multiple Backends**: Support for FAISS, Chroma, and future PostgreSQL+pgvector
- **Metadata Filtering**: Rich filtering capabilities based on document metadata
- **Async API**: Full async support for non-blocking operations
- **Persistence**: Save and load capabilities for vector stores
- **Extensibility**: Easy to add new vector store backends

### Architecture Overview

We implement a modular vector store architecture with multiple backend options:

1. **FAISS**: In-memory vector store for fast similarity search
2. **Chroma**: Alternative vector store with persistence capabilities
3. **PostgreSQL+pgvector**: Primary vector store for production use

This modular approach allows us to:
- Use the most appropriate backend for different use cases
- Easily switch between backends without changing client code
- Scale from development to production with minimal changes
- Leverage PostgreSQL+pgvector for efficient vector similarity search

<div align="right"><a href="#-rag-vector-store-implementation">⬆️ Back to top</a></div>

## 🏗️ Architecture

### Design Principles

The vector store implementation follows these key design principles:

1. **Separation of Concerns**: Clear separation between interface and implementation
2. **Dependency Inversion**: High-level modules depend on abstractions, not concrete implementations
3. **Open/Closed Principle**: Open for extension (new backends) but closed for modification
4. **Interface Segregation**: Clean, focused interfaces that are easy to implement
5. **Composition Over Inheritance**: Favor composition for flexibility and testability

### Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     VectorStore (ABC)                       │
│                     (Abstract Base Class)                   │
└───────┬───────────┬───────────┬────────────────────────────┘
        │           │           │
        ▼           ▼           ▼
┌───────────┐ ┌──────────┐ ┌──────────────┐
│FAISSVector│ │ChromaVect│ │PostgresVector│
│   Store   │ │  orStore │ │    Store     │
│(In-Memory)│ │(Persisted)│ │ (Production) │
└─────┬─────┘ └────┬─────┘ └──────┬────────┘
      │            │              │
      │            │              │
      └────────────┴──────────────┐
                   │              │
                   ▼              ▼
┌─────────────────────────┐ ┌─────────────────────────┐
│    RAG Retriever        │ │        Test Suite       │
└─────────────────────────┘ └─────────────────────────┘
```

### File Structure

```
backend/app/rag/
├── vector_store.py       # Base class + FAISS implementation
├── chroma_store.py       # Optional Chroma implementation
└── pgvector_store.py     # PostgreSQL + pgvector implementation
```

<div align="right"><a href="#-rag-vector-store-implementation">⬆️ Back to top</a></div>

## 🧩 Component Details

### VectorStore Base Class

The `VectorStore` abstract base class defines the interface that all vector store implementations must follow. It provides a consistent API for storing, retrieving, and searching vector embeddings.

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any, TypedDict, Union
import uuid

# Version identifier for serialization compatibility
VECTOR_STORE_VERSION = "1.0.0"

class DocumentChunk(TypedDict):
    """Type definition for a document chunk with its embedding and metadata."""
    id: str
    text: str
    embedding: List[float]
    metadata: Dict[str, Any]

class SearchResult(TypedDict):
    """Type definition for a search result."""
    id: str
    text: str
    metadata: Dict[str, Any]
    score: float

class VectorStore(ABC):
    """
    Abstract base class for vector storage and retrieval.

    This class defines the interface that all vector store implementations must follow.
    """

    # Version identifier for serialization compatibility
    VERSION = VECTOR_STORE_VERSION

    @abstractmethod
    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of original texts
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        pass

    @abstractmethod
    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for similar vectors in the store.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of SearchResult dictionaries containing:
            - id: The embedding ID
            - text: The original text
            - metadata: The metadata dictionary
            - score: The similarity score
        """
        pass

    @abstractmethod
    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings from the store.

        Args:
            ids: List of embedding IDs to delete
        """
        pass

    @abstractmethod
    async def get(self, ids: List[str]) -> List[DocumentChunk]:
        """
        Get embeddings by ID.

        Args:
            ids: List of embedding IDs to retrieve

        Returns:
            List of DocumentChunk dictionaries containing:
            - id: The embedding ID
            - embedding: The embedding vector
            - text: The original text
            - metadata: The metadata dictionary
        """
        pass

    @abstractmethod
    async def clear(self) -> None:
        """
        Clear all embeddings from the store.
        """
        pass

    @abstractmethod
    async def save(self, file_path: str) -> None:
        """
        Save the vector store to disk.

        Args:
            file_path: Path to save the vector store
        """
        pass

    @abstractmethod
    async def load(self, file_path: str) -> None:
        """
        Load the vector store from disk.

        Args:
            file_path: Path to load the vector store from
        """
        pass

    @property
    @abstractmethod
    def count(self) -> int:
        """
        Get the number of embeddings in the store.

        Returns:
            Number of embeddings
        """
        pass

    def estimate_memory_usage(self) -> int:
        """
        Estimate the memory usage of the vector store in bytes.

        This provides a rough estimate for monitoring and resource planning.
        Implementations should override this with more accurate estimates.

        Returns:
            Estimated memory usage in bytes
        """
        # Default implementation provides a very rough estimate
        # based on number of vectors and dimension
        if hasattr(self, "dimension"):
            dimension = getattr(self, "dimension")
            # Rough estimate: 4 bytes per float * dimension * number of vectors
            # Plus overhead for metadata and other data structures
            return self.count * dimension * 4 * 1.5  # 50% overhead
        return 0
```

### FAISS Vector Store Implementation

FAISS (Facebook AI Similarity Search) is a library for efficient similarity search and clustering of dense vectors. It's particularly well-suited for our RAG implementation due to its speed and efficiency.

```python
class FAISSVectorStore(VectorStore):
    """
    Vector store implementation using FAISS for efficient similarity search.

    This class provides an in-memory vector store with FAISS as the backend.
    It supports various index types and distance metrics for different use cases.
    """

    def __init__(
        self,
        dimension: int = 384,  # Default for e5-base-v2
        index_type: str = "flat",  # "flat", "ivf", "hnsw"
        distance_metric: str = "cosine",  # "cosine", "l2", "ip" (inner product)
        **kwargs
    ):
        """
        Initialize the FAISS vector store.

        Args:
            dimension: Dimension of the embedding vectors
            index_type: Type of FAISS index to use
            distance_metric: Distance metric for similarity search
            **kwargs: Additional arguments for specific index types
        """
        self.dimension = dimension
        self.index_type = index_type
        self.distance_metric = distance_metric
        self.metadata = {}  # Store metadata by ID
        self.texts = {}  # Store original texts by ID
        self.id_to_index = {}  # Map IDs to FAISS indices
        self.index_to_id = []  # Map FAISS indices to IDs

        # Import required libraries
        try:
            import numpy as np
            import faiss
            self.np = np
            self.faiss = faiss
        except ImportError:
            raise ImportError(
                "faiss-cpu and numpy packages are required. "
                "Install with 'pip install faiss-cpu numpy'"
            )

        # Initialize FAISS index based on configuration
        self._create_index(**kwargs)

    def _create_index(self, **kwargs):
        """
        Create the FAISS index based on configuration.

        Args:
            **kwargs: Additional arguments for specific index types
        """
        # Determine the base index based on distance metric
        if self.distance_metric == "cosine":
            # For cosine similarity, we need to normalize vectors
            self.normalize = True
            base_index = self.faiss.IndexFlatIP(self.dimension)
        elif self.distance_metric == "l2":
            self.normalize = False
            base_index = self.faiss.IndexFlatL2(self.dimension)
        elif self.distance_metric == "ip":
            self.normalize = False
            base_index = self.faiss.IndexFlatIP(self.dimension)
        else:
            raise ValueError(f"Unsupported distance metric: {self.distance_metric}")

        # Create the final index based on index type
        if self.index_type == "flat":
            # Flat index is the base index
            self.index = base_index
        elif self.index_type == "ivf":
            # IVF index for faster search with slight accuracy trade-off
            nlist = kwargs.get("nlist", 100)  # Number of clusters
            self.index = self.faiss.IndexIVFFlat(base_index, self.dimension, nlist)
            # Need to train with some data before using
            self.needs_training = True
        elif self.index_type == "hnsw":
            # HNSW index for very fast search
            m = kwargs.get("m", 16)  # Number of connections per layer
            self.index = self.faiss.IndexHNSWFlat(self.dimension, m)
            self.needs_training = False
        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of original texts
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        # Validate inputs
        if len(embeddings) != len(texts):
            raise ValueError("Number of embeddings must match number of texts")

        if metadatas is not None and len(metadatas) != len(embeddings):
            raise ValueError("Number of metadata items must match number of embeddings")

        # Generate IDs if not provided
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(embeddings))]
        elif len(ids) != len(embeddings):
            raise ValueError("Number of IDs must match number of embeddings")

        # Create metadata if not provided
        if metadatas is None:
            metadatas = [{} for _ in range(len(embeddings))]

        # Convert embeddings to numpy array
        embeddings_np = self.np.array(embeddings, dtype=self.np.float32)

        # Normalize vectors if using cosine similarity
        if self.normalize:
            faiss.normalize_L2(embeddings_np)

        # Train the index if needed and this is the first batch
        if getattr(self, "needs_training", False) and self.index.ntotal == 0:
            self.index.train(embeddings_np)

        # Get the starting index for this batch
        start_index = len(self.index_to_id)

        # Add embeddings to the index
        self.index.add(embeddings_np)

        # Store metadata, texts, and ID mappings
        for i, (doc_id, text, metadata) in enumerate(zip(ids, texts, metadatas)):
            idx = start_index + i
            self.metadata[doc_id] = metadata
            self.texts[doc_id] = text
            self.id_to_index[doc_id] = idx
            self.index_to_id.append(doc_id)

        return ids

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for similar vectors in the store.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of SearchResult dictionaries
        """
        if self.index.ntotal == 0:
            return []

        # Convert query to numpy array
        query_np = self.np.array([query_embedding], dtype=self.np.float32)

        # Normalize if using cosine similarity
        if self.normalize:
            self.faiss.normalize_L2(query_np)

        # Adjust limit if using filters (we'll need to fetch more and filter)
        search_limit = limit * 10 if filters else limit
        search_limit = min(search_limit, self.index.ntotal)

        # Search the index
        scores, indices = self.index.search(query_np, search_limit)

        # Process results
        results = []
        for score, idx in zip(scores[0], indices[0]):
            # Skip invalid indices
            if idx == -1 or idx >= len(self.index_to_id):
                continue

            doc_id = self.index_to_id[idx]
            metadata = self.metadata.get(doc_id, {})

            # Apply filters if provided
            if filters and not self._matches_filters(metadata, filters):
                continue

            # Add to results
            results.append({
                "id": doc_id,
                "text": self.texts.get(doc_id, ""),
                "metadata": metadata,
                "score": float(score)
            })

            # Stop once we have enough results
            if len(results) >= limit:
                break

        return results

    def _matches_filters(self, metadata: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """
        Check if metadata matches the provided filters.

        Args:
            metadata: The metadata dictionary
            filters: The filter dictionary

        Returns:
            True if metadata matches all filters, False otherwise
        """
        for key, value in filters.items():
            if key.startswith("metadata."):
                # Handle nested metadata filters
                metadata_key = key.replace("metadata.", "")
                if metadata.get(metadata_key) != value:
                    return False
            elif metadata.get(key) != value:
                return False

        return True

    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings from the store.

        Note: FAISS doesn't support direct deletion, so we mark entries as removed
        and rebuild the index when necessary.

        Args:
            ids: List of embedding IDs to delete
        """
        # FAISS doesn't support direct deletion, so we need to rebuild the index
        # For now, we'll just mark the entries as removed
        for doc_id in ids:
            if doc_id in self.id_to_index:
                idx = self.id_to_index[doc_id]
                # Mark as removed by setting index_to_id[idx] to None
                if 0 <= idx < len(self.index_to_id):
                    self.index_to_id[idx] = None
                # Remove from mappings
                del self.id_to_index[doc_id]
                if doc_id in self.metadata:
                    del self.metadata[doc_id]
                if doc_id in self.texts:
                    del self.texts[doc_id]

    async def get(self, ids: List[str]) -> List[DocumentChunk]:
        """
        Get embeddings by ID.

        Args:
            ids: List of embedding IDs to retrieve

        Returns:
            List of DocumentChunk dictionaries
        """
        results = []
        for doc_id in ids:
            if doc_id in self.id_to_index:
                idx = self.id_to_index[doc_id]
                # Get the embedding vector
                if 0 <= idx < self.index.ntotal:
                    # Reconstruct the embedding from the index
                    embedding = self.index.reconstruct(idx).tolist()
                    results.append({
                        "id": doc_id,
                        "embedding": embedding,
                        "text": self.texts.get(doc_id, ""),
                        "metadata": self.metadata.get(doc_id, {})
                    })

        return results

    async def clear(self) -> None:
        """
        Clear all embeddings from the store.
        """
        # Reset the index
        self._create_index()

        # Clear all mappings
        self.metadata = {}
        self.texts = {}
        self.id_to_index = {}
        self.index_to_id = []

    async def save(self, file_path: str) -> None:
        """
        Save the vector store to disk.

        Args:
            file_path: Path to save the vector store
        """
        import os
        import pickle
        import time

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Save the FAISS index
        self.faiss.write_index(self.index, f"{file_path}.index")

        # Save the metadata and mappings with version information
        with open(f"{file_path}.meta", "wb") as f:
            pickle.dump({
                "version": self.VERSION,
                "metadata": self.metadata,
                "texts": self.texts,
                "id_to_index": self.id_to_index,
                "index_to_id": self.index_to_id,
                "dimension": self.dimension,
                "index_type": self.index_type,
                "distance_metric": self.distance_metric,
                "normalize": getattr(self, "normalize", False),
                "created_at": time.time(),
                "vector_count": self.count
            }, f)

    async def load(self, file_path: str) -> None:
        """
        Load the vector store from disk.

        Args:
            file_path: Path to load the vector store from

        Raises:
            ValueError: If the version of the saved file is incompatible
        """
        import pickle
        import logging

        # Load the metadata and mappings
        with open(f"{file_path}.meta", "rb") as f:
            data = pickle.load(f)

            # Check version compatibility
            file_version = data.get("version", "0.0.0")
            if file_version != self.VERSION:
                logging.warning(
                    f"Vector store version mismatch: file version {file_version}, "
                    f"current version {self.VERSION}. This may cause issues."
                )

            # Load data
            self.metadata = data["metadata"]
            self.texts = data["texts"]
            self.id_to_index = data["id_to_index"]
            self.index_to_id = data["index_to_id"]
            self.dimension = data["dimension"]
            self.index_type = data["index_type"]
            self.distance_metric = data["distance_metric"]
            self.normalize = data["normalize"]

        # Load the FAISS index
        self.index = self.faiss.read_index(f"{file_path}.index")

        # Validate index size matches metadata
        if self.index.ntotal != len(self.index_to_id):
            logging.warning(
                f"Index size mismatch: FAISS index has {self.index.ntotal} vectors, "
                f"but metadata has {len(self.index_to_id)} entries. "
                "This may indicate data corruption."
            )

    @property
    def count(self) -> int:
        """
        Get the number of embeddings in the store.

        Returns:
            Number of embeddings
        """
        return self.index.ntotal

    def estimate_memory_usage(self) -> int:
        """
        Estimate the memory usage of the FAISS vector store in bytes.

        This provides a more accurate estimate for FAISS indexes based on
        the index type, dimension, and number of vectors.

        Returns:
            Estimated memory usage in bytes
        """
        # Base memory for vectors: 4 bytes per float * dimension * number of vectors
        vector_memory = self.index.ntotal * self.dimension * 4

        # Memory for metadata and texts (rough estimate)
        metadata_memory = sum(len(str(m)) * 2 for m in self.metadata.values())
        text_memory = sum(len(t) * 2 for t in self.texts.values())

        # Memory for mappings
        mapping_memory = (len(self.id_to_index) * 50) + (len(self.index_to_id) * 30)

        # Additional memory based on index type
        index_overhead = 0
        if self.index_type == "flat":
            # Flat index has minimal overhead
            index_overhead = vector_memory * 0.1  # 10% overhead
        elif self.index_type == "ivf":
            # IVF has centroids and lists
            nlist = getattr(self.index, "nlist", 100)
            index_overhead = nlist * self.dimension * 4 * 2  # Centroids + overhead
        elif self.index_type == "hnsw":
            # HNSW has graph connections
            m = getattr(self.index, "hnsw", {}).get("M", 16)
            index_overhead = self.index.ntotal * m * 8  # Graph connections

        total_memory = vector_memory + metadata_memory + text_memory + mapping_memory + index_overhead
        return int(total_memory)
```

### Chroma Vector Store Implementation

Chroma is a modern vector database that provides persistent storage and efficient similarity search. It's a good alternative to FAISS when persistence is a priority.

```python
class ChromaVectorStore(VectorStore):
    """
    Vector store implementation using Chroma for persistent storage and similarity search.

    This class provides a persistent vector store with Chroma as the backend.
    It supports metadata filtering and various distance metrics.
    """

    def __init__(
        self,
        collection_name: str = "embeddings",
        persist_directory: Optional[str] = None,
        distance_metric: str = "cosine",  # "cosine", "l2", "ip"
        **kwargs
    ):
        """
        Initialize the Chroma vector store.

        Args:
            collection_name: Name of the collection to store embeddings
            persist_directory: Directory to persist the database (None for in-memory)
            distance_metric: Distance metric for similarity search
            **kwargs: Additional arguments for Chroma
        """
        self.collection_name = collection_name
        self.persist_directory = persist_directory
        self.distance_metric = distance_metric

        # Store version information in collection metadata
        self._version_metadata = {
            "version": self.VERSION,
            "created_at": kwargs.get("created_at", None)
        }

        # Import required libraries
        try:
            import chromadb
            self.chromadb = chromadb
        except ImportError:
            raise ImportError(
                "chromadb package is required. "
                "Install with 'pip install chromadb'"
            )

        # Initialize Chroma client and collection
        self._initialize_chroma(**kwargs)

    def _initialize_chroma(self, **kwargs):
        """
        Initialize the Chroma client and collection.

        Args:
            **kwargs: Additional arguments for Chroma
        """
        import time
        import logging

        # Create client
        if self.persist_directory:
            self.client = self.chromadb.PersistentClient(
                path=self.persist_directory
            )
        else:
            self.client = self.chromadb.Client()

        # Map our distance metrics to Chroma's
        metric_mapping = {
            "cosine": "cosine",
            "l2": "l2",
            "ip": "dot"
        }
        chroma_metric = metric_mapping.get(self.distance_metric, "cosine")

        # Get or create collection
        collection_metadata = {
            "hnsw:space": chroma_metric,
            "vector_store_version": self.VERSION,
            "created_at": self._version_metadata["created_at"] or time.time()
        }

        self.collection = self.client.get_or_create_collection(
            name=self.collection_name,
            metadata=collection_metadata
        )

        # Check version compatibility if collection already existed
        existing_metadata = self.collection.metadata
        if existing_metadata and "vector_store_version" in existing_metadata:
            existing_version = existing_metadata["vector_store_version"]
            if existing_version != self.VERSION:
                logging.warning(
                    f"Chroma collection version mismatch: collection version {existing_version}, "
                    f"current version {self.VERSION}. This may cause issues."
                )

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of original texts
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        # Validate inputs
        if len(embeddings) != len(texts):
            raise ValueError("Number of embeddings must match number of texts")

        if metadatas is not None and len(metadatas) != len(embeddings):
            raise ValueError("Number of metadata items must match number of embeddings")

        # Generate IDs if not provided
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(embeddings))]
        elif len(ids) != len(embeddings):
            raise ValueError("Number of IDs must match number of embeddings")

        # Create metadata if not provided
        if metadatas is None:
            metadatas = [{} for _ in range(len(embeddings))]

        # Add embeddings to Chroma
        # Note: Chroma's add is synchronous, so we're wrapping it
        # in an async function for API consistency
        self.collection.add(
            embeddings=embeddings,
            documents=texts,
            metadatas=metadatas,
            ids=ids
        )

        return ids

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for similar vectors in the store.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of SearchResult dictionaries
        """
        # Convert filters to Chroma's where clause format
        where_clause = None
        if filters:
            where_clause = {}
            for key, value in filters.items():
                if key.startswith("metadata."):
                    # Handle nested metadata filters
                    metadata_key = key.replace("metadata.", "")
                    where_clause[metadata_key] = value
                else:
                    where_clause[key] = value

        # Query Chroma
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=limit,
            where=where_clause
        )

        # Process results
        processed_results = []
        if results and len(results["ids"]) > 0:
            for i, doc_id in enumerate(results["ids"][0]):
                processed_results.append({
                    "id": doc_id,
                    "text": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "score": float(results["distances"][0][i]) if "distances" in results else 1.0
                })

        return processed_results

    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings from the store.

        Args:
            ids: List of embedding IDs to delete
        """
        self.collection.delete(ids=ids)

    async def get(self, ids: List[str]) -> List[DocumentChunk]:
        """
        Get embeddings by ID.

        Args:
            ids: List of embedding IDs to retrieve

        Returns:
            List of DocumentChunk dictionaries
        """
        # Get embeddings from Chroma
        results = self.collection.get(
            ids=ids,
            include=["embeddings", "documents", "metadatas"]
        )

        # Process results
        processed_results = []
        if results and len(results["ids"]) > 0:
            for i, doc_id in enumerate(results["ids"]):
                processed_results.append({
                    "id": doc_id,
                    "embedding": results["embeddings"][i],
                    "text": results["documents"][i],
                    "metadata": results["metadatas"][i]
                })

        return processed_results

    async def clear(self) -> None:
        """
        Clear all embeddings from the store.
        """
        # Delete the collection and recreate it
        self.client.delete_collection(self.collection_name)
        self._initialize_chroma()

    async def save(self, file_path: str) -> None:
        """
        Save the vector store to disk.

        For Chroma, this is only needed for in-memory collections.
        Persistent collections are saved automatically.

        Args:
            file_path: Path to save the vector store
        """
        if not self.persist_directory:
            # For in-memory collections, we need to create a persistent client
            # and copy the data
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Create a persistent client
            persistent_client = self.chromadb.PersistentClient(path=file_path)

            # Create a new collection
            persistent_collection = persistent_client.create_collection(
                name=self.collection_name,
                metadata={"hnsw:space": self.distance_metric}
            )

            # Get all data from the in-memory collection
            data = self.collection.get()

            # Add to the persistent collection if there's data
            if data and len(data["ids"]) > 0:
                persistent_collection.add(
                    embeddings=data["embeddings"],
                    documents=data["documents"],
                    metadatas=data["metadatas"],
                    ids=data["ids"]
                )
        else:
            # For persistent collections, we don't need to do anything
            # as Chroma handles persistence automatically
            pass

    async def load(self, file_path: str) -> None:
        """
        Load the vector store from disk.

        Args:
            file_path: Path to load the vector store from
        """
        # Update the persist directory
        self.persist_directory = file_path

        # Reinitialize with the new directory
        self._initialize_chroma()

    @property
    def count(self) -> int:
        """
        Get the number of embeddings in the store.

        Returns:
            Number of embeddings
        """
        return self.collection.count()

    def estimate_memory_usage(self) -> int:
        """
        Estimate the memory usage of the Chroma vector store in bytes.

        This provides a rough estimate for Chroma collections based on
        the number of vectors and their dimension.

        Returns:
            Estimated memory usage in bytes
        """
        # Get collection info
        count = self.collection.count()
        if count == 0:
            return 0

        # Try to get dimension from a sample embedding
        dimension = 0
        try:
            sample = self.collection.get(limit=1, include=["embeddings"])
            if sample and "embeddings" in sample and sample["embeddings"]:
                dimension = len(sample["embeddings"][0])
        except:
            # Default to a reasonable dimension if we can't determine it
            dimension = 384  # Common embedding dimension

        # Base memory for vectors: 4 bytes per float * dimension * number of vectors
        vector_memory = count * dimension * 4

        # Memory for documents and metadata (rough estimate)
        # Assume average document size of 200 bytes and metadata size of 100 bytes
        doc_metadata_memory = count * 300

        # Chroma overhead (indexes, etc.)
        overhead = vector_memory * 0.5  # 50% overhead

        # If using persistent storage, memory usage is lower
        if self.persist_directory:
            # Only a portion is kept in memory
            return int((vector_memory + doc_metadata_memory + overhead) * 0.3)
        else:
            # Everything is in memory
            return int(vector_memory + doc_metadata_memory + overhead)
```

## 🔄 Backend Implementations

The vector store component supports multiple backend implementations, each with its own strengths:

| Backend | Strengths | Use Cases | Persistence |
|---------|-----------|-----------|-------------|
| **FAISS** | Fast in-memory search, efficient indexing | Development, small-to-medium datasets | Manual (save/load) |
| **Chroma** | Built-in persistence, metadata filtering | Production, medium datasets | Automatic |
| **Firestore** | Integration with existing data | Legacy compatibility | Cloud-based |
| **PostgreSQL+pgvector** (Future) | Scalable, transactional | Large-scale production | Database |

### Choosing the Right Backend

- **FAISS**: Best for speed and efficiency when persistence isn't critical
- **Chroma**: Best for ease of use and built-in persistence
- **Firestore**: Best for compatibility with existing Firebase infrastructure
- **PostgreSQL+pgvector**: Best for production-scale deployments (future)

## 📝 Usage Examples

### Basic Usage with FAISS

```python
import asyncio
from backend.app.rag.vector_store import FAISSVectorStore
from backend.app.rag.embeddings import get_embedding_model

async def main():
    # Create a vector store
    vector_store = FAISSVectorStore(dimension=384)  # e5-base-v2 dimension

    # Create an embedding model
    embedding_model = get_embedding_model("e5-base-v2")

    # Add documents
    texts = [
        "The quick brown fox jumps over the lazy dog",
        "The five boxing wizards jump quickly",
        "How vexingly quick daft zebras jump"
    ]

    # Generate embeddings
    embeddings = await embedding_model.embed_documents(texts)

    # Add to vector store
    ids = await vector_store.add_embeddings(
        embeddings=embeddings,
        texts=texts,
        metadatas=[{"source": "example"} for _ in texts]
    )

    # Search
    query = "jumping animals"
    query_embedding = await embedding_model.embed_query(query)

    results = await vector_store.search(
        query_embedding=query_embedding,
        limit=2
    )

    # Print results
    for result in results:
        print(f"Score: {result['score']:.4f} | Text: {result['text']}")

    # Save the vector store
    await vector_store.save("./data/vector_store")

asyncio.run(main())
```

### Using Chroma with Persistence

```python
import asyncio
from backend.app.rag.chroma_store import ChromaVectorStore
from backend.app.rag.embeddings import get_embedding_model

async def main():
    # Create a persistent vector store
    vector_store = ChromaVectorStore(
        collection_name="documents",
        persist_directory="./data/chroma_db"
    )

    # Create an embedding model
    embedding_model = get_embedding_model("e5-base-v2")

    # Add documents
    texts = [
        "Python is a programming language",
        "TypeScript is a typed superset of JavaScript",
        "RAG stands for Retrieval Augmented Generation"
    ]

    # Generate embeddings
    embeddings = await embedding_model.embed_documents(texts)

    # Add to vector store with metadata
    ids = await vector_store.add_embeddings(
        embeddings=embeddings,
        texts=texts,
        metadatas=[
            {"category": "programming", "language": "python"},
            {"category": "programming", "language": "typescript"},
            {"category": "ai", "technique": "rag"}
        ]
    )

    # Search with metadata filter
    query = "typed programming languages"
    query_embedding = await embedding_model.embed_query(query)

    results = await vector_store.search(
        query_embedding=query_embedding,
        limit=2,
        filters={"category": "programming"}
    )

    # Print results
    for result in results:
        print(f"Score: {result['score']:.4f} | Text: {result['text']}")

asyncio.run(main())
```

### Memory Monitoring Example

```python
import asyncio
from backend.app.rag.vector_store import FAISSVectorStore
import random

async def monitor_memory_usage():
    # Create a vector store with different index types
    flat_store = FAISSVectorStore(dimension=384, index_type="flat")
    ivf_store = FAISSVectorStore(dimension=384, index_type="ivf")
    hnsw_store = FAISSVectorStore(dimension=384, index_type="hnsw")

    # Generate random embeddings for testing
    def generate_random_embeddings(count):
        return [
            [random.random() for _ in range(384)]
            for _ in range(count)
        ]

    # Add embeddings in batches and monitor memory usage
    batch_sizes = [100, 1000, 10000]
    stores = [flat_store, ivf_store, hnsw_store]
    store_names = ["FAISS Flat", "FAISS IVF", "FAISS HNSW"]

    print("Memory Usage Comparison (MB)")
    print("----------------------------")
    print("Vectors | " + " | ".join(store_names))

    total_vectors = 0
    for batch_size in batch_sizes:
        # Generate embeddings
        embeddings = generate_random_embeddings(batch_size)
        texts = [f"Document {i}" for i in range(batch_size)]

        # Add to each store
        for store in stores:
            await store.add_embeddings(embeddings, texts)

        # Update total
        total_vectors += batch_size

        # Print memory usage
        memory_usage = [
            f"{store.estimate_memory_usage() / (1024 * 1024):.2f}"
            for store in stores
        ]
        print(f"{total_vectors:7d} | " + " | ".join(memory_usage))

asyncio.run(monitor_memory_usage())
```

## 🛠️ Implementation Details

The vector store component is implemented in the following files:

- `backend/app/rag/vector_store.py`: Contains the base class and implementations

### Installation

To use the vector store component, you need to install the required dependencies:

```bash
pip install faiss-cpu numpy chromadb
```

Or add them to your requirements file:

```
# Vector store dependencies
faiss-cpu>=1.7.4  # For FAISS vector store
numpy>=1.24.0     # Required for vector operations
chromadb>=0.4.18  # For Chroma vector store
```

### Factory Function

The vector store module provides a factory function to create vector stores of different types:

```python
from backend.app.rag.vector_store import get_vector_store

# Create a FAISS vector store
faiss_store = get_vector_store("faiss", dimension=384)

# Create a Chroma vector store
chroma_store = get_vector_store("chroma", collection_name="documents")
```

### Error Handling

The implementation includes robust error handling for common issues:

1. **Missing Dependencies**: Clear error messages when required packages are not installed
2. **Version Mismatches**: Warnings when loading vector stores with different versions
3. **Input Validation**: Comprehensive validation of inputs to prevent errors
4. **Graceful Fallbacks**: Appropriate fallback behavior when operations fail

### Testing

A test script is provided to verify the implementation:

```python
# Run the test script
python -m backend.app.rag.test_vector_store
```

The test script demonstrates:
- Creating vector stores with different configurations
- Adding embeddings and searching
- Filtering search results
- Saving and loading vector stores
- Estimating memory usage

## 🔌 Adding New Backends

To add a new vector store backend, follow these steps:

1. Create a new class that inherits from `VectorStore`
2. Implement all abstract methods
3. Add appropriate error handling and validation
4. Ensure all methods are async for consistency
5. Add comprehensive tests

Example skeleton for a new backend:

```python
class NewVectorStore(VectorStore):
    """
    Vector store implementation using a new backend.
    """

    def __init__(self, **kwargs):
        """Initialize the vector store."""
        # Implementation details...

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Add embeddings to the vector store."""
        # Implementation details...

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar vectors in the store."""
        # Implementation details...

    async def delete(self, ids: List[str]) -> None:
        """Delete embeddings from the store."""
        # Implementation details...

    async def get(self, ids: List[str]) -> List[DocumentChunk]:
        """Get embeddings by ID."""
        # Implementation details...

    async def clear(self) -> None:
        """Clear all embeddings from the store."""
        # Implementation details...

    async def save(self, file_path: str) -> None:
        """Save the vector store to disk."""
        # Implementation details...

    async def load(self, file_path: str) -> None:
        """Load the vector store from disk."""
        # Implementation details...

    @property
    def count(self) -> int:
        """Get the number of embeddings in the store."""
        # Implementation details...
```

## 🧪 Testing Strategy

Testing the vector store implementations requires a comprehensive approach:

1. **Unit Tests**: Test each method in isolation
2. **Integration Tests**: Test the vector store with the embedding model
3. **Performance Tests**: Benchmark search performance with different configurations
4. **Edge Cases**: Test with empty stores, large datasets, and invalid inputs

Example test structure:

```python
import pytest
import numpy as np
from backend.app.rag.vector_store import FAISSVectorStore

@pytest.fixture
def vector_store():
    """Create a vector store for testing."""
    return FAISSVectorStore(dimension=4)

@pytest.fixture
def sample_data():
    """Create sample data for testing."""
    return {
        "embeddings": [
            [1.0, 0.0, 0.0, 0.0],
            [0.0, 1.0, 0.0, 0.0],
            [0.0, 0.0, 1.0, 0.0]
        ],
        "texts": ["doc1", "doc2", "doc3"],
        "metadatas": [{"id": 1}, {"id": 2}, {"id": 3}]
    }

@pytest.mark.asyncio
async def test_add_embeddings(vector_store, sample_data):
    """Test adding embeddings to the vector store."""
    ids = await vector_store.add_embeddings(
        embeddings=sample_data["embeddings"],
        texts=sample_data["texts"],
        metadatas=sample_data["metadatas"]
    )
    assert len(ids) == 3
    assert vector_store.count == 3

@pytest.mark.asyncio
async def test_search(vector_store, sample_data):
    """Test searching the vector store."""
    await vector_store.add_embeddings(
        embeddings=sample_data["embeddings"],
        texts=sample_data["texts"],
        metadatas=sample_data["metadatas"]
    )

    results = await vector_store.search(
        query_embedding=[1.0, 0.1, 0.1, 0.1],
        limit=2
    )

    assert len(results) == 2
    assert results[0]["text"] == "doc1"  # Closest to [1,0,0,0]
```

## 📊 Performance Considerations

Vector search performance is critical for RAG applications. Consider these factors:

1. **Index Type**:
   - `flat`: Most accurate but slowest for large datasets
   - `ivf`: Good balance of speed and accuracy
   - `hnsw`: Fastest but may sacrifice some accuracy

2. **Batch Operations**:
   - Add embeddings in batches rather than one at a time
   - Typical batch size: 100-1000 documents

3. **Dimension Reduction**:
   - Consider PCA or other dimension reduction techniques for very high-dimensional embeddings
   - Trade-off between accuracy and performance

4. **Memory vs. Disk**:
   - FAISS: Fastest but requires all data in memory
   - Chroma: Good balance with memory-mapped storage
   - PostgreSQL+pgvector: Best for very large datasets that don't fit in memory

## ⚠️ Troubleshooting

Common issues and solutions:

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| **Low search quality** | Incorrect distance metric | Ensure you're using the appropriate metric (cosine for normalized embeddings) |
| **Slow search performance** | Using flat index with large dataset | Switch to IVF or HNSW index type |
| **Out of memory errors** | Too many embeddings in FAISS | Switch to Chroma or implement pagination |
| **Metadata filtering not working** | Incorrect filter format | Check the specific format required by each backend |
| **Dimension mismatch errors** | Inconsistent embedding dimensions | Ensure all embeddings have the same dimension |

## 🔒 Security, Persistence & Monitoring

### Data Security

When implementing vector stores, consider these security aspects:

1. **Sensitive Data**: Be careful about storing sensitive information in metadata
2. **Access Control**: Implement appropriate access controls for the vector store
3. **Encryption**: Consider encrypting the vector store files at rest

### Persistence Strategies

Different backends have different persistence characteristics:

1. **FAISS**:
   - Manual save/load operations
   - Recommended save frequency: After significant updates or on a schedule
   - Consider implementing a background save task

2. **Chroma**:
   - Automatic persistence with `persist_directory`
   - Transactions ensure data integrity
   - Regular backups still recommended

3. **Future PostgreSQL+pgvector**:
   - Database-level persistence and transactions
   - Regular database backup procedures apply
   - Consider connection pooling for performance

### Versioning

The vector store implementation includes versioning to ensure compatibility across updates:

1. **Version Tracking**:
   - Each vector store implementation includes a `VERSION` constant
   - Version information is stored in saved files and collection metadata
   - Version mismatches trigger warnings to alert developers

2. **Version Compatibility**:
   - Minor version updates should maintain backward compatibility
   - Major version updates may require migration scripts
   - Version checks during loading help prevent data corruption

3. **Migration Strategy**:
   - When upgrading to a new version, check for version compatibility
   - Provide migration utilities for major version changes
   - Document breaking changes between versions

### Memory Monitoring

The vector store implementation includes memory usage estimation to help with resource planning:

1. **Memory Estimation**:
   - The `estimate_memory_usage()` method provides memory usage estimates in bytes
   - Each backend implementation provides specialized estimates based on its characteristics
   - Use these estimates for capacity planning and monitoring

2. **Monitoring Integration**:
   - Integrate memory estimates with monitoring systems
   - Set alerts for when memory usage approaches system limits
   - Track memory usage over time to identify growth patterns

3. **Optimization Opportunities**:
   - Use memory estimates to identify when to switch to more efficient index types
   - Consider dimension reduction techniques when memory usage is high
   - Implement sharding or distributed search for very large collections

## 🚀 Future Enhancements

Planned enhancements for the vector store component:

1. **PostgreSQL+pgvector Implementation**:
   - Scale to millions of documents
   - SQL-based filtering and aggregation
   - Transaction support

2. **Hybrid Search**:
   - Combine vector similarity with keyword search
   - Improve relevance for specific queries
   - Support for BM25 or other keyword ranking algorithms

3. **Distributed Vector Search**:
   - Shard large indices across multiple machines
   - Implement distributed search protocols
   - Support for cloud-native vector databases

4. **Advanced Filtering**:
   - Structured filter system with typed query DSL
   - Semantic filtering based on embedding regions
   - Hierarchical filtering with nested metadata
   - Range and fuzzy matching on metadata fields

5. **Optimized Indexing**:
   - Incremental index updates
   - Background reindexing for optimal performance
   - Automatic index type selection based on dataset size

6. **Reliability & Observability**:
   - Background save scheduler for automatic persistence
   - Comprehensive memory usage monitoring
   - Performance metrics collection and reporting
   - Streaming search API for large result sets

7. **Versioning & Compatibility**:
   - Schema versioning for vector stores
   - Migration utilities for format changes
   - Backward compatibility layers

<div align="right"><a href="#-rag-vector-store-implementation">⬆️ Back to top</a></div>
