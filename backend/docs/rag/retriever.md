# 📚 RAG Retriever Implementation

This document provides detailed implementation guidance for the retriever component of the RAG system. For an overview of the complete RAG system, refer to the [RAG Implementation Guide](rag-implementation.md).

<div align="right"><a href="#-rag-retriever-implementation">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🧩 Core Components](#-core-components)
   - [Base Retriever Class](#base-retriever-class)
   - [Hybrid Retriever](#hybrid-retriever)
   - [Context Window Manager](#context-window-manager)
   - [Query Rewriter](#query-rewriter)
3. [🛠️ Implementation Details](#️-implementation-details)
   - [Vector Search](#vector-search)
   - [Keyword Search](#keyword-search)
   - [Result Combination](#result-combination)
   - [Token Counting](#token-counting)
   - [Document Truncation](#document-truncation)
4. [📊 Advanced Techniques](#-advanced-techniques)
   - [Reciprocal Rank Fusion](#reciprocal-rank-fusion)
   - [Query Expansion](#query-expansion)
   - [Metadata Filtering](#metadata-filtering)
5. [📝 Usage Examples](#-usage-examples)
   - [Basic Retrieval](#basic-retrieval)
   - [With Context Window Management](#with-context-window-management)
   - [With Query Rewriting](#with-query-rewriting)
6. [🧪 Testing Strategy](#-testing-strategy)
7. [📦 Dependencies](#-dependencies)
8. [🔄 Implementation Plan](#-implementation-plan)
9. [📚 Conclusion](#-conclusion)

<div align="right"><a href="#-rag-retriever-implementation">⬆️ Back to top</a></div>

## 🔍 Overview

The retriever component is the heart of the RAG system, responsible for finding the most relevant documents to answer a user's query. It bridges the gap between user questions and the knowledge base by:

1. Processing and potentially rewriting the user's query
2. Finding relevant documents using multiple search strategies
3. Combining results from different search methods
4. Ensuring the retrieved context fits within the LLM's context window

A well-implemented retriever dramatically improves the quality of RAG responses by providing the LLM with the most relevant context, reducing hallucinations, and enabling accurate, knowledge-grounded answers.

### Integration with Existing Components

The retriever is designed to work seamlessly with the existing components in the RAG system:

- **Query Analyzer**: Uses the retrieval strategy determined by the query analyzer to guide its search approach
- **Knowledge Base Service**: Leverages the search methods provided by the knowledge base service rather than implementing duplicate functionality
- **Vector Store**: Accesses the vector store through the knowledge base service rather than directly
- **LLM Adapter**: Uses the LLM adapter for query rewriting and token counting

This integration approach ensures that the retriever builds upon the existing functionality while adding new capabilities for enhanced retrieval.

## 🧩 Core Components

### Base Retriever Class

```python
class Retriever:
    """
    Base class for document retrieval.

    This abstract class defines the interface for all retrievers.
    Concrete implementations should override the retrieve method.
    """

    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query.

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        raise NotImplementedError("Subclasses must implement retrieve")
```

The base `Retriever` class defines the interface that all retriever implementations must follow. It includes:

- An abstract `retrieve` method that must be implemented by subclasses
- Support for retrieval strategies determined by the query analyzer
- Flexible parameters for customizing the retrieval process
- A standardized return format for consistent processing

### Hybrid Retriever

```python
class HybridRetriever(Retriever):
    """
    Retriever that combines vector similarity and keyword search.

    This class coordinates between the query analyzer and knowledge base service
    to provide enhanced retrieval results using multiple search methods.
    """

    def __init__(
        self,
        query_analyzer,
        knowledge_base_service: PgVectorKnowledgeBaseService,
        default_vector_weight: float = 0.7,
        default_keyword_weight: float = 0.3,
        **kwargs
    ):
        """
        Initialize the hybrid retriever.

        Args:
            query_analyzer: The query analyzer for determining retrieval strategies
            knowledge_base_service: The knowledge base service for search operations
            default_vector_weight: Default weight for vector similarity results (0.0 to 1.0)
            default_keyword_weight: Default weight for keyword search results (0.0 to 1.0)
            **kwargs: Additional arguments
        """
        self.query_analyzer = query_analyzer
        self.knowledge_base_service = knowledge_base_service

        # Ensure weights sum to 1.0
        total_weight = default_vector_weight + default_keyword_weight
        self.default_vector_weight = default_vector_weight / total_weight
        self.default_keyword_weight = default_keyword_weight / total_weight

    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents using hybrid search.

        This method:
        1. Analyzes the query if no retrieval strategy is provided
        2. Applies the retrieval strategy to guide the search
        3. Performs search using the knowledge base service
        4. Enhances results with Reciprocal Rank Fusion

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        # Get retrieval strategy if not provided
        if retrieval_strategy is None:
            analysis = await self.query_analyzer.analyze(query)
            retrieval_strategy = analysis["retrieval_strategy"]

        # Extract search parameters from strategy
        search_type = retrieval_strategy.get("search_type", "hybrid")
        strategy_limit = retrieval_strategy.get("limit", limit)
        strategy_filters = retrieval_strategy.get("filters", {})
        vector_weight = retrieval_strategy.get("weights", {}).get("vector", self.default_vector_weight)
        keyword_weight = retrieval_strategy.get("weights", {}).get("keyword", self.default_keyword_weight)

        # Merge filters
        merged_filters = {**strategy_filters, **(filters or {})}

        # Perform search using knowledge base service
        results = await self.knowledge_base_service.search(
            query=query,

            limit=strategy_limit,
            search_type=search_type,
            filters=merged_filters,
            vector_weight=vector_weight,
            keyword_weight=keyword_weight
        )

        # Apply additional processing if needed
        # (e.g., reranking, result enhancement)

        return results

    async def _vector_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform vector similarity search.

        Args:
            query: The query text
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of search results
        """
        # Embed the query
        query_embedding = await self.embedding_model.embed_query(query)

        # Search the vector store
        results = await self.vector_store.search(
            query_embedding,
            limit=limit,
            filters=filters
        )

        return results

    async def _keyword_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword-based search.

        Args:
            query: The query text
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of search results
        """
        # Use the knowledge base service for keyword search
        # No collection parameter needed for PgVectorKnowledgeBaseService
        # Remove collection from filters if present
        if filters and "collection" in filters:
            filters.pop("collection")

        results = await self.knowledge_base_service.search(
            query,

            limit=limit
        )

        # Convert to standard format
        formatted_results = []
        for doc in results:
            # Extract document ID
            doc_id = doc.get("id", "")

            # Extract text content
            content = doc.get("content", "")

            # Extract metadata
            metadata = {k: v for k, v in doc.items() if k not in ["id", "content"]}

            # Add to results
            formatted_results.append({
                "id": doc_id,
                "text": content,
                "metadata": metadata,
                "score": doc.get("score", 0.0)
            })

        return formatted_results

    def _combine_results(
        self,
        vector_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        limit: int
    ) -> List[Dict[str, Any]]:
        """
        Combine vector and keyword search results using Reciprocal Rank Fusion.

        Args:
            vector_results: Results from vector search
            keyword_results: Results from keyword search
            limit: Maximum number of results to return

        Returns:
            Combined and ranked results
        """
        # Create a dictionary to store combined scores
        combined_scores = {}

        # Process vector results
        for i, result in enumerate(vector_results):
            doc_id = result["id"]
            # RRF formula: 1 / (rank + k), where k is a constant (typically 60)
            rrf_score = 1.0 / (i + 60)

            if doc_id not in combined_scores:
                combined_scores[doc_id] = {
                    "id": doc_id,
                    "text": result["text"],
                    "metadata": result["metadata"],
                    "vector_score": result["score"],
                    "keyword_score": 0.0,
                    "rrf_score": self.vector_weight * rrf_score
                }
            else:
                combined_scores[doc_id]["vector_score"] = result["score"]
                combined_scores[doc_id]["rrf_score"] += self.vector_weight * rrf_score

        # Process keyword results
        for i, result in enumerate(keyword_results):
            doc_id = result["id"]
            # RRF formula: 1 / (rank + k), where k is a constant (typically 60)
            rrf_score = 1.0 / (i + 60)

            if doc_id not in combined_scores:
                combined_scores[doc_id] = {
                    "id": doc_id,
                    "text": result["text"],
                    "metadata": result["metadata"],
                    "vector_score": 0.0,
                    "keyword_score": result["score"],
                    "rrf_score": self.keyword_weight * rrf_score
                }
            else:
                combined_scores[doc_id]["keyword_score"] = result["score"]
                combined_scores[doc_id]["rrf_score"] += self.keyword_weight * rrf_score

        # Convert to list and sort by RRF score
        combined_results = list(combined_scores.values())
        combined_results.sort(key=lambda x: x["rrf_score"], reverse=True)

        # Format final results
        final_results = []
        for result in combined_results[:limit]:
            final_results.append({
                "id": result["id"],
                "text": result["text"],
                "metadata": result["metadata"],
                "score": result["rrf_score"],
                "vector_score": result["vector_score"],
                "keyword_score": result["keyword_score"]
            })

        return final_results
```

### Context Window Manager

```python
class ContextWindowManager:
    """
    Manages context window size for LLM input.

    This class ensures that retrieved documents fit within the LLM's
    context window by truncating or summarizing as needed.
    """

    def __init__(
        self,
        llm_adapter,
        max_tokens: int = 4000,
        token_buffer: int = 1000,
        **kwargs
    ):
        """
        Initialize the context window manager.

        Args:
            llm_adapter: The LLM adapter to use for token counting and summarization
            max_tokens: Maximum tokens allowed in the context window
            token_buffer: Buffer tokens to reserve for the query and response
            **kwargs: Additional arguments
        """
        self.llm_adapter = llm_adapter
        self.max_tokens = max_tokens
        self.token_buffer = token_buffer

        # Import tiktoken for token counting if available
        try:
            import tiktoken
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
            self.has_tiktoken = True
        except ImportError:
            self.has_tiktoken = False

    async def fit_to_context_window(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Ensure documents fit within the context window.

        Args:
            query: The query text
            documents: The retrieved documents

        Returns:
            Documents that fit within the context window
        """
        # Estimate query tokens
        query_tokens = await self._count_tokens(query)

        # Calculate available tokens for documents
        available_tokens = self.max_tokens - query_tokens - self.token_buffer

        # Estimate tokens for each document
        documents_with_tokens = []
        for doc in documents:
            doc_tokens = await self._count_tokens(doc["text"])
            documents_with_tokens.append({
                **doc,
                "tokens": doc_tokens
            })

        # Sort by score (descending)
        documents_with_tokens.sort(key=lambda x: x["score"], reverse=True)

        # Fit documents to context window
        fitted_documents = []
        total_tokens = 0

        for doc in documents_with_tokens:
            doc_tokens = doc["tokens"]

            if total_tokens + doc_tokens <= available_tokens:
                # Document fits entirely
                fitted_documents.append({
                    "id": doc["id"],
                    "text": doc["text"],
                    "metadata": doc["metadata"],
                    "score": doc["score"]
                })
                total_tokens += doc_tokens
            elif total_tokens < available_tokens:
                # Document needs truncation
                truncated_text = await self._truncate_text(
                    doc["text"],
                    available_tokens - total_tokens
                )

                fitted_documents.append({
                    "id": doc["id"],
                    "text": truncated_text,
                    "metadata": doc["metadata"],
                    "score": doc["score"],
                    "truncated": True
                })

                # Update total tokens
                truncated_tokens = await self._count_tokens(truncated_text)
                total_tokens += truncated_tokens
            else:
                # No more space available
                break

        return fitted_documents

    async def _count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.

        Args:
            text: The text to count tokens for

        Returns:
            Number of tokens
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate token counting
            return len(self.tokenizer.encode(text))
        else:
            # Fall back to LLM adapter for token counting
            try:
                tokens, _ = await self.llm_adapter.get_token_count([{"role": "user", "content": text}])
                return tokens
            except:
                # Last resort: estimate based on characters
                return len(text) // 4

    async def _truncate_text(self, text: str, max_tokens: int) -> str:
        """
        Truncate text to fit within max_tokens.

        Args:
            text: The text to truncate
            max_tokens: Maximum tokens allowed

        Returns:
            Truncated text
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate truncation
            tokens = self.tokenizer.encode(text)
            if len(tokens) <= max_tokens:
                return text

            truncated_tokens = tokens[:max_tokens]
            return self.tokenizer.decode(truncated_tokens)
        else:
            # Estimate based on characters
            chars_per_token = 4
            max_chars = max_tokens * chars_per_token

            if len(text) <= max_chars:
                return text

            return text[:max_chars] + "..."
```

### Query Rewriter

```python
class QueryRewriter:
    """
    Rewrites queries to improve retrieval performance.

    This class uses multiple strategies to enhance queries for better retrieval results:
    1. Rule-based enhancements (acronym expansion, term extraction)
    2. LLM-based rewriting for sophisticated query reformulation
    3. Context-aware rewriting using knowledge base search results
    4. Fallback mechanisms for when rewriting fails
    """

    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        knowledge_base_service=None,
        **kwargs
    ):
        """
        Initialize the query rewriter.

        Args:
            llm_adapter: The LLM adapter to use for query rewriting
            embedding_model: Optional embedding model for semantic analysis
            knowledge_base_service: Optional knowledge base service for context-aware rewriting
            **kwargs: Additional arguments
        """
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.knowledge_base_service = knowledge_base_service

        # Common terms to avoid expanding (stopwords, common business terms)
        self.common_terms = {
            "the", "and", "or", "a", "an", "in", "on", "at", "to", "for", "with", "by",
            "business", "company", "team", "project", "report", "meeting", "email",
            "plan", "strategy", "goal", "objective", "target", "result", "outcome"
        }

        # Common acronyms and their expansions
        self.acronyms = {
            "roi": "return on investment",
            "kpi": "key performance indicator",
            "ctr": "click-through rate",
            "cac": "customer acquisition cost",
            "ltv": "lifetime value",
            "arpu": "average revenue per user",
            "mrr": "monthly recurring revenue",
            "arr": "annual recurring revenue",
            # ... more business acronyms
        }

    async def rewrite_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """
        Rewrite a query to improve retrieval performance.

        This method uses a multi-strategy approach:
        1. First tries simple rule-based enhancements (acronym expansion, etc.)
        2. Then attempts LLM-based rewriting if available
        3. Finally tries context-aware rewriting if knowledge base service is available
        4. Falls back to the original query with minimal enhancements if needed

        Args:
            query: The original query
            context: Optional context to guide rewriting (e.g., user info, department)

        Returns:
            Rewritten query
        """
        # Start with the original query
        enhanced_query = query.strip()

        try:
            # Step 1: Apply rule-based enhancements
            rule_based_query = await self._apply_rule_based_enhancements(enhanced_query)

            # If rule-based enhancements made significant changes, use that
            if self._is_significantly_different(enhanced_query, rule_based_query):
                enhanced_query = rule_based_query
                logger.debug(f"Rule-based query enhancement: '{query}' -> '{enhanced_query}'")

            # Step 2: Try LLM-based rewriting
            llm_rewritten_query = await self._llm_rewrite_query(enhanced_query, context)

            # If LLM rewriting was successful and made significant changes, use that
            if llm_rewritten_query and self._is_significantly_different(enhanced_query, llm_rewritten_query):
                enhanced_query = llm_rewritten_query
                logger.debug(f"LLM-based query rewriting: '{query}' -> '{enhanced_query}'")

            # Step 3: Try context-aware rewriting if knowledge base service is available
            if self.knowledge_base_service and context:
                context_aware_query = await self._context_aware_rewrite(enhanced_query, context)

                # If context-aware rewriting made significant changes, use that
                if context_aware_query and self._is_significantly_different(enhanced_query, context_aware_query):
                    enhanced_query = context_aware_query
                    logger.debug(f"Context-aware query rewriting: '{query}' -> '{enhanced_query}'")

            return enhanced_query

        except Exception as e:
            logger.error(f"Error rewriting query: {e}", exc_info=True)
            # Fall back to original query with basic enhancements
            return await self._fallback_rewrite(query)

    async def _apply_rule_based_enhancements(self, query: str) -> str:
        """
        Apply rule-based enhancements to the query.

        Args:
            query: The original query

        Returns:
            Enhanced query
        """
        # Check for acronyms directly in the query
        words = query.lower().split()
        has_expanded_acronyms = False
        expanded_acronyms = []

        for word in words:
            # Clean the word from punctuation
            clean_word = word.strip(".,?!:;()")
            if clean_word in self.acronyms:
                expanded_acronyms.append(f"{clean_word} ({self.acronyms[clean_word]})")
                has_expanded_acronyms = True
            else:
                expanded_acronyms.append(word)

        # If we expanded any acronyms, use the expanded version
        if has_expanded_acronyms:
            enhanced_query = f"{query} ({' '.join(self.acronyms[w.strip('.,?!:;()')] for w in words if w.strip('.,?!:;()') in self.acronyms)})"
        else:
            enhanced_query = query

        return enhanced_query

    async def _llm_rewrite_query(self, query: str, context: Dict[str, Any] = None) -> Optional[str]:
        """
        Use the LLM to rewrite the query.

        Args:
            query: The query to rewrite
            context: Optional context to guide rewriting

        Returns:
            Rewritten query or None if rewriting failed
        """
        # Construct context-aware prompt if context is provided
        context_str = ""
        if context:
            if "user_info" in context:
                context_str += f"\nUser Information: {context['user_info']}"
            if "conversation_history" in context:
                context_str += f"\nConversation History: {context['conversation_history']}"
            if "department" in context:
                context_str += f"\nDepartment Focus: {context['department']}"

        prompt = [
            {
                "role": "system",
                "content": (
                    "You are a query rewriting assistant. Your task is to rewrite search queries "
                    "to improve retrieval performance. Focus on:"
                    "\n1. Expanding acronyms"
                    "\n2. Adding synonyms for important terms"
                    "\n3. Making implicit information explicit"
                    "\n4. Removing unnecessary words"
                    "\n5. Focusing on key concepts"
                    "\n6. Extracting specific entities, metrics, or time periods"
                    "\n7. Adding domain-specific terminology"
                    f"{context_str}"
                    "\nProvide ONLY the rewritten query without explanation."
                )
            },
            {
                "role": "user",
                "content": f"Original query: {query}\n\nRewritten query:"
            }
        ]

        try:
            rewritten_query = await self.llm_adapter.chat(prompt)
            return rewritten_query.strip()
        except Exception as e:
            logger.warning(f"LLM-based query rewriting failed: {e}")
            return None

    async def _context_aware_rewrite(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Rewrite the query based on available context.

        Args:
            query: The query to rewrite
            context: Context to guide rewriting

        Returns:
            Context-aware rewritten query or None if rewriting failed
        """
        if not self.knowledge_base_service:
            return None

        try:
            # Extract department if available
            department = context.get("department")

            # Perform a preliminary search to get relevant terms
            filters = {"department": department} if department else {}
            preliminary_results = await self.knowledge_base_service.search(
                query=query,

                limit=3,
                search_type="keyword",
                filters=filters
            )

            if not preliminary_results:
                return None

            # Extract key terms from the preliminary results
            key_terms = set()
            for result in preliminary_results:
                # Extract terms from the document title and content
                if "metadata" in result and "title" in result["metadata"]:
                    title_terms = self._extract_terms(result["metadata"]["title"])
                    key_terms.update([t for t in title_terms if t.lower() not in self.common_terms])

                if "text" in result:
                    # Only use the first 200 characters to extract key terms
                    content_sample = result["text"][:200]
                    content_terms = self._extract_terms(content_sample)
                    key_terms.update([t for t in content_terms if t.lower() not in self.common_terms])

            # Filter out terms already in the query
            query_terms = set(self._extract_terms(query.lower()))
            new_terms = [term for term in key_terms if term.lower() not in query_terms]

            # If we found new terms, enhance the query
            if new_terms:
                # Take at most 3 new terms to avoid query explosion
                selected_terms = new_terms[:3]
                enhanced_query = f"{query} {' '.join(selected_terms)}"
                return enhanced_query

            return None

        except Exception as e:
            logger.warning(f"Context-aware query rewriting failed: {e}")
            return None

    async def _fallback_rewrite(self, query: str) -> str:
        """
        Apply minimal enhancements when other methods fail.

        Args:
            query: The original query

        Returns:
            Minimally enhanced query
        """
        # Just expand acronyms as a last resort
        query_lower = query.lower()
        words = query_lower.split()

        # Check for acronyms
        for i, word in enumerate(words):
            if word in self.acronyms:
                words[i] = self.acronyms[word]

        # If changes were made, return the enhanced query
        enhanced_query = " ".join(words)
        if enhanced_query != query_lower:
            return f"{query} ({enhanced_query})"

        # Otherwise, return the original query
        return query

    def _extract_terms(self, text: str) -> List[str]:
        """
        Extract meaningful terms from text.

        Args:
            text: The text to extract terms from

        Returns:
            List of extracted terms
        """
        # Clean words from punctuation
        cleaned_words = [word.strip(".,?!:;()") for word in text.split()]

        # Filter out common terms and very short terms
        filtered_words = [word for word in cleaned_words if word.lower() not in self.common_terms and len(word) > 2]

        # Extract multi-word phrases
        phrases = []
        for i in range(len(cleaned_words) - 1):
            word1 = cleaned_words[i].lower()
            word2 = cleaned_words[i+1].lower()

            if word1 not in self.common_terms and word2 not in self.common_terms and len(word1) > 2 and len(word2) > 2:
                phrases.append(f"{cleaned_words[i]} {cleaned_words[i+1]}")

        # Combine single words and phrases
        all_terms = filtered_words + phrases

        # Remove duplicates and limit the number of terms
        unique_terms = list(dict.fromkeys(all_terms))
        return unique_terms[:15]  # Limit to 15 terms

    def _is_significantly_different(self, original: str, rewritten: str) -> bool:
        """
        Check if the rewritten query is significantly different from the original.

        Args:
            original: The original query
            rewritten: The rewritten query

        Returns:
            True if significantly different, False otherwise
        """
        if not rewritten or rewritten == original:
            return False

        # Check if length difference is significant
        if len(rewritten) < len(original) * 0.8 or len(rewritten) > len(original) * 2.0:
            return True

        # Check if term difference is significant
        original_terms = set(self._extract_terms(original.lower()))
        rewritten_terms = set(self._extract_terms(rewritten.lower()))

        # Calculate Jaccard similarity
        intersection = len(original_terms.intersection(rewritten_terms))
        union = len(original_terms.union(rewritten_terms))

        if union == 0:
            return False

        similarity = intersection / union

        # If similarity is low, queries are significantly different
        return similarity < 0.7
```

## 🛠️ Implementation Details

### Vector Search

The vector search component uses embeddings to find semantically similar documents. This approach captures the meaning of the query rather than just matching keywords.

```python
async def _vector_search(
    self,
    query: str,
    limit: int = 5,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Perform vector similarity search.

    This method:
    1. Generates an embedding for the query
    2. Searches the vector store for similar documents
    3. Formats the results for consistent processing

    Args:
        query: The query text
        limit: Maximum number of results to return
        filters: Optional metadata filters

    Returns:
        List of search results with standardized format
    """
    # Generate query embedding
    query_embedding = await self.embedding_model.embed_query(query)

    # Search vector store
    results = await self.vector_store.search(
        query_embedding,
        limit=limit,
        filters=filters
    )

    return results
```

### Keyword Search

The keyword search component uses traditional text matching to find documents containing the query terms. This approach is particularly effective for queries with specific terms, names, or numbers.

```python
async def _keyword_search(
    self,
    query: str,
    limit: int = 5,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Perform keyword-based search.

    This method:
    1. Preprocesses the query to extract important terms
    2. Searches the knowledge base for documents matching these terms
    3. Formats the results for consistent processing

    Args:
        query: The query text
        limit: Maximum number of results to return
        filters: Optional metadata filters

    Returns:
        List of search results with standardized format
    """
    # Search knowledge base
    results = await self.knowledge_base_service.search(
        query,
        limit=limit,
        filters=filters
    )

    return results
```

### Result Combination

The result combination component merges results from different search methods using Reciprocal Rank Fusion (RRF). This approach gives higher weight to documents that appear high in multiple result sets.

```python
def _combine_results(
    self,
    vector_results: List[Dict[str, Any]],
    keyword_results: List[Dict[str, Any]],
    limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Combine results from vector and keyword search using Reciprocal Rank Fusion.

    This method:
    1. Assigns ranks to documents from each search method
    2. Calculates RRF scores based on these ranks
    3. Combines and sorts the results by RRF score

    Args:
        vector_results: Results from vector search
        keyword_results: Results from keyword search
        limit: Maximum number of results to return

    Returns:
        Combined and ranked results
    """
    # Constant for RRF calculation
    k = 60  # Standard value from RRF literature

    # Track combined scores by document ID
    combined_scores = {}

    # Process vector results
    for rank, doc in enumerate(vector_results):
        doc_id = doc["id"]
        if doc_id not in combined_scores:
            combined_scores[doc_id] = {
                "id": doc_id,
                "text": doc["text"],
                "metadata": doc["metadata"],
                "vector_score": doc["score"],
                "keyword_score": 0.0,
                "vector_rank": rank + 1,
                "keyword_rank": float('inf'),
                "rrf_score": 0.0
            }
        else:
            combined_scores[doc_id]["vector_score"] = doc["score"]
            combined_scores[doc_id]["vector_rank"] = rank + 1

    # Process keyword results
    for rank, doc in enumerate(keyword_results):
        doc_id = doc["id"]
        if doc_id not in combined_scores:
            combined_scores[doc_id] = {
                "id": doc_id,
                "text": doc["text"],
                "metadata": doc["metadata"],
                "vector_score": 0.0,
                "keyword_score": doc["score"],
                "vector_rank": float('inf'),
                "keyword_rank": rank + 1,
                "rrf_score": 0.0
            }
        else:
            combined_scores[doc_id]["keyword_score"] = doc["score"]
            combined_scores[doc_id]["keyword_rank"] = rank + 1

    # Calculate RRF scores
    for doc_id, doc in combined_scores.items():
        vector_rrf = self.vector_weight * (1 / (k + doc["vector_rank"]))
        keyword_rrf = self.keyword_weight * (1 / (k + doc["keyword_rank"]))
        doc["rrf_score"] = vector_rrf + keyword_rrf

    # Sort by RRF score and return top results
    results = sorted(
        combined_scores.values(),
        key=lambda x: x["rrf_score"],
        reverse=True
    )[:limit]

    return results
```

### Token Counting

The token counting component estimates the number of tokens in a text. This is essential for ensuring that retrieved documents fit within the LLM's context window.

```python
async def _count_tokens(self, text: str) -> int:
    """
    Count the number of tokens in a text.

    This method uses multiple approaches with fallbacks:
    1. tiktoken (if available) for accurate counting
    2. LLM adapter's token counting (if available)
    3. Character-based estimation as a last resort

    Args:
        text: The text to count tokens for

    Returns:
        Estimated number of tokens
    """
    # Try tiktoken first (most accurate)
    if self.has_tiktoken:
        return len(self.tokenizer.encode(text))

    # Fall back to LLM adapter
    try:
        tokens, _ = await self.llm_adapter.get_token_count([
            {"role": "user", "content": text}
        ])
        return tokens
    except Exception:
        # Last resort: estimate based on characters
        # Rough approximation: 1 token ≈ 4 characters for English text
        return len(text) // 4
```

### Document Truncation

The document truncation component ensures that documents fit within token limits by truncating them intelligently.

```python
async def _truncate_text(self, text: str, max_tokens: int) -> str:
    """
    Truncate text to fit within max_tokens.

    This method:
    1. Checks if truncation is necessary
    2. Uses tiktoken for accurate truncation if available
    3. Falls back to character-based estimation if needed

    Args:
        text: The text to truncate
        max_tokens: Maximum tokens allowed

    Returns:
        Truncated text that fits within the token limit
    """
    # Check if truncation is needed
    current_tokens = await self._count_tokens(text)
    if current_tokens <= max_tokens:
        return text

    # Use tiktoken for accurate truncation if available
    if self.has_tiktoken:
        tokens = self.tokenizer.encode(text)
        truncated_tokens = tokens[:max_tokens]
        return self.tokenizer.decode(truncated_tokens)

    # Fall back to character-based estimation
    # Estimate characters per token (typically around 4 for English)
    chars_per_token = len(text) / current_tokens
    max_chars = int(max_tokens * chars_per_token)

    # Truncate and add ellipsis
    return text[:max_chars] + "..."
```

## 📊 Advanced Techniques

### Reciprocal Rank Fusion

Reciprocal Rank Fusion (RRF) is a technique for combining results from multiple search methods. It works by:

1. Assigning a rank to each document in each result set
2. Calculating a score for each document based on its ranks
3. Combining and sorting the results by this score

The RRF score for a document is calculated as:

```
RRF_score(d) = Σ w_i * (1 / (k + rank_i(d)))
```

Where:
- `d` is the document
- `w_i` is the weight for search method i
- `rank_i(d)` is the rank of document d in search method i
- `k` is a constant (typically 60) that prevents very high scores for documents that appear first in only one result set

RRF is particularly effective because:
- It naturally handles different score scales across search methods
- It gives higher weight to documents that appear high in multiple result sets
- It's simple to implement and computationally efficient

### Query Expansion

Query expansion is a technique for improving retrieval by adding related terms to the original query. This can be done using:

1. **Synonym Expansion**: Adding synonyms for important terms
2. **Acronym Expansion**: Expanding acronyms to their full form
3. **LLM-Based Expansion**: Using an LLM to generate related terms

The `QueryRewriter` class implements LLM-based query expansion by prompting the LLM to rewrite the query with expanded terms.

### Metadata Filtering

Metadata filtering allows for more targeted retrieval by filtering documents based on their metadata. This can include:

1. **Department Filtering**: Limiting results to specific departments
2. **Date Filtering**: Limiting results to specific time periods
3. **Document Type Filtering**: Limiting results to specific document types

The retriever supports metadata filtering through the `filters` parameter, which is passed to both the vector search and keyword search components.

## 📝 Usage Examples

### Basic Retrieval

```python
# Initialize components
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
vector_store = FirestoreVectorStore(firebase_client)
knowledge_base = KnowledgeBaseService(firebase_client)

# Create retriever
retriever = HybridRetriever(
    embedding_model=embedding_model,
    vector_store=vector_store,
    knowledge_base_service=knowledge_base,
    vector_weight=0.7,
    keyword_weight=0.3
)

# Retrieve documents
query = "What is our marketing strategy for Q2?"
documents = await retriever.retrieve(
    query,
    limit=5,
    filters={"department": "marketing"}
)

# Process results
for doc in documents:
    print(f"ID: {doc['id']}")
    print(f"Text: {doc['text']}")
    print(f"Score: {doc['score']}")
    print(f"Metadata: {doc['metadata']}")
```

### With Context Window Management

```python
# Initialize components
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
vector_store = FirestoreVectorStore(firebase_client)
knowledge_base = KnowledgeBaseService(firebase_client)
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create retriever
retriever = HybridRetriever(
    embedding_model=embedding_model,
    vector_store=vector_store,
    knowledge_base_service=knowledge_base
)

# Create context window manager
context_manager = ContextWindowManager(
    llm_adapter=llm_adapter,
    max_tokens=4000,
    token_buffer=1000
)

# Retrieve documents
query = "What is our marketing strategy for Q2?"
documents = await retriever.retrieve(
    query,
    limit=10,  # Retrieve more documents than needed
    filters={"department": "marketing"}
)

# Fit documents to context window
fitted_documents = await context_manager.fit_to_context_window(query, documents)

# Process results
for doc in fitted_documents:
    print(f"ID: {doc['id']}")
    print(f"Text: {doc['text']}")
    print(f"Score: {doc['score']}")
    print(f"Truncated: {doc.get('truncated', False)}")
```

### With Query Rewriting

```python
# Initialize components
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
vector_store = FirestoreVectorStore(firebase_client)
knowledge_base = KnowledgeBaseService(firebase_client)
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create retriever
retriever = HybridRetriever(
    embedding_model=embedding_model,
    vector_store=vector_store,
    knowledge_base_service=knowledge_base
)

# Create query rewriter
query_rewriter = QueryRewriter(llm_adapter=llm_adapter)

# Rewrite query
original_query = "Q2 marketing plans?"
rewritten_query = await query_rewriter.rewrite_query(original_query)
print(f"Original query: {original_query}")
print(f"Rewritten query: {rewritten_query}")

# Retrieve documents with rewritten query
documents = await retriever.retrieve(
    rewritten_query,
    limit=5,
    filters={"department": "marketing"}
)

# Process results
for doc in documents:
    print(f"ID: {doc['id']}")
    print(f"Text: {doc['text']}")
    print(f"Score: {doc['score']}")
```

## 🧪 Testing Strategy

### Unit Tests

1. **Test Base Class**:
   - Verify that abstract methods raise NotImplementedError
   - Test interface contract compliance

2. **Test Hybrid Retriever**:
   - Test integration with query analyzer for retrieval strategy determination
   - Test proper parameter extraction from retrieval strategy
   - Test search method selection based on strategy
   - Test filtering by metadata (department, date, document type)
   - Test error handling and graceful degradation

3. **Test Context Window Manager**:
   - Test token counting with tiktoken available
   - Test token counting with LLM adapter fallback
   - Test token counting with character-based estimation
   - Test document truncation with different methods
   - Test fitting documents to context window with various document sizes
   - Test handling of edge cases (empty documents, very large documents)

4. **Test Query Rewriter**:
   - Test rule-based query enhancements (acronym expansion, term extraction)
   - Test LLM-based query rewriting with different input types
   - Test context-aware query rewriting with department-specific context
   - Test handling of acronyms and technical terms
   - Test error handling and fallback mechanisms
   - Test significant difference detection between original and rewritten queries
   - Test performance with and without query rewriting

### Integration Tests

1. **Test with Real Components**:
   - Test with actual query analyzer
   - Test with actual knowledge base service
   - Measure retrieval quality with standard test queries

2. **Test End-to-End Pipeline**:
   - Verify that retriever integrates properly with query analyzer
   - Verify that retriever outputs are properly formatted for the generator
   - Test complete RAG pipeline from query to response
   - Measure end-to-end latency and identify bottlenecks

3. **Test with Different Query Types**:
   - Simple factual queries
   - Complex multi-part queries
   - Queries with specific terms or jargon
   - Queries requiring numerical reasoning
   - Queries with ambiguous terms

## 📦 Dependencies

- **Core Dependencies**:
  - **Python 3.11+**: For modern async features and type hints
  - **asyncio**: For asynchronous processing
  - **typing**: For comprehensive type annotations

- **Integration Dependencies**:
  - **query_analyzer**: For determining retrieval strategies
  - **knowledge_base_service**: For accessing the knowledge base
  - **llm_adapter**: For token counting and query rewriting

- **Token Management Dependencies**:
  - **tiktoken**: For accurate token counting (optional)
  - **regex**: For text processing

- **Testing Dependencies**:
  - **pytest**: For unit and integration testing
  - **pytest-asyncio**: For testing async functions
  - **unittest.mock**: For mocking components

## 🔄 Implementation Plan

### Phase 1: Core Components (1-2 days)

1. **Create Base Class**:
   - Define the `Retriever` abstract base class
   - Implement required methods and type hints
   - Create comprehensive docstrings

2. **Implement Hybrid Retriever**:
   - Create `HybridRetriever` class
   - Implement integration with query analyzer
   - Implement knowledge base service integration
   - Add metadata filtering support
   - Add error handling and logging

3. **Write Basic Tests**:
   - Create unit tests for base class
   - Create unit tests for hybrid retriever
   - Test with mock components

### Phase 2: Advanced Components (2-3 days)

4. **Implement Context Window Manager**:
   - Create `ContextWindowManager` class
   - Implement token counting with multiple fallback methods
   - Implement document truncation
   - Implement context window fitting algorithm
   - Add support for handling edge cases

5. **Implement Query Rewriter**:
   - Create `QueryRewriter` class
   - Implement LLM-based query rewriting
   - Add error handling and fallback mechanisms
   - Optimize prompts for different query types

6. **Expand Test Coverage**:
   - Add tests for context window manager
   - Add tests for query rewriter
   - Create integration tests with real components

### Phase 3: Optimization and Integration (1 day)

7. **Performance Optimization**:
   - Profile and optimize critical paths
   - Add logging for performance monitoring
   - Implement graceful degradation for edge cases

8. **Integration with RAG Pipeline**:
   - Ensure proper integration with query analyzer
   - Ensure proper integration with generator
   - Test end-to-end RAG pipeline

9. **Documentation and Examples**:
   - Update documentation with implementation details
   - Create comprehensive usage examples
   - Add troubleshooting guide

## 📚 Conclusion

The retriever component is the critical bridge between user queries and the knowledge base in a RAG system. By leveraging the existing query analyzer and knowledge base service, we create a streamlined implementation that coordinates between these components to provide enhanced retrieval capabilities.

The modular design with clear separation of concerns allows for:
- Seamless integration with existing components
- Leveraging of established functionality rather than duplication
- Robust error handling and fallback mechanisms
- Efficient context window management

The implementation prioritizes:
- **Integration**: Working seamlessly with existing components
- **Coordination**: Applying retrieval strategies determined by the query analyzer
- **Enhancement**: Adding new capabilities like context window management
- **Robustness**: Handling errors and edge cases gracefully

With these capabilities, the retriever component provides a solid foundation for building high-quality RAG applications that can answer complex queries with accurate, knowledge-grounded responses while maintaining a clean architecture that leverages existing functionality.
