# RAG Generator Implementation

This document provides detailed implementation guidance for the generator component of the RAG system. For an overview of the complete RAG system, refer to the [RAG Implementation Guide](rag-implementation.md).

## 🔍 Overview

The generator component is the final piece of the RAG pipeline, responsible for combining retrieved context with LLM generation to produce contextually informed responses. It formats retrieved documents, constructs prompts, and manages the interaction with the LLM to generate accurate, knowledge-grounded answers.

### Integration with Existing Components

The generator is designed to work seamlessly with the existing components in the RAG system:

1. **Query Analyzer**: Uses the query analysis to tailor the response format and style
2. **Retriever**: Processes the documents returned by the retriever
3. **LLM Adapter**: Leverages the LLM adapter for generating responses
4. **Knowledge Base**: Incorporates metadata from the knowledge base for citations

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🧩 Core Components](#-core-components)
   - [Base Generator Class](#base-generator-class)
   - [RAG Generator](#rag-generator)
   - [Self-Critique Generator](#self-critique-generator)
   - [Streaming Generator](#streaming-generator)
3. [🛠️ Implementation Details](#️-implementation-details)
   - [Context Formatting](#context-formatting)
   - [Prompt Construction](#prompt-construction)
   - [Citation Handling](#citation-handling)
   - [Response Generation](#response-generation)
4. [📊 Advanced Techniques](#-advanced-techniques)
   - [Self-Critique Mechanism](#self-critique-mechanism)
   - [Enhanced Streaming](#enhanced-streaming)
   - [Response Formatting](#response-formatting)
5. [📝 Usage Examples](#-usage-examples)
   - [Basic Generation](#basic-generation)
   - [With Self-Critique](#with-self-critique)
   - [With Streaming](#with-streaming)
6. [🧪 Testing Strategy](#-testing-strategy)
7. [🔄 Implementation Plan](#-implementation-plan)

## 🧩 Core Components

The generator component consists of several key classes that work together to provide a flexible and powerful generation system. For the proof-of-concept, we will focus on implementing the core components (marked with ✅), while deferring the advanced components (marked with ❌) for future phases:

### Base Generator Class ✅

```python
class Generator:
    """
    Base class for context-enhanced response generation.

    This abstract class defines the interface for all generators.
    Concrete implementations should override the generate method.
    """

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        **kwargs
    ) -> str:
        """
        Generate a response based on the query and retrieved context.

        Args:
            query: The query text
            context: The retrieved documents
            **kwargs: Additional arguments

        Returns:
            Generated response
        """
        raise NotImplementedError("Subclasses must implement generate")
```

### RAG Generator ✅

```python
class RAGGenerator(Generator):
    """
    Generator that combines retrieved context with LLM generation.

    This class formats retrieved documents, constructs prompts, and
    manages the interaction with the LLM to produce contextually
    informed responses.
    """

    def __init__(
        self,
        llm_adapter,
        include_citations: bool = True,
        citation_format: str = "inline",
        query_rewriter = None,
        **kwargs
    ):
        """
        Initialize the RAG generator.

        Args:
            llm_adapter: The LLM adapter to use for generation
            include_citations: Whether to include citations in the response
            citation_format: Format for citations (inline or footnote)
            query_rewriter: Optional QueryRewriter instance for query enhancement
            **kwargs: Additional arguments
        """
        self.llm_adapter = llm_adapter
        self.include_citations = include_citations
        self.citation_format = citation_format
        self.query_rewriter = query_rewriter

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Generate a response based on the query and retrieved context.

        Args:
            query: The query text
            context: The retrieved documents
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the LLM

        Returns:
            Generated response or async iterator for streaming
        """
        # Enhance query with query rewriter if available
        enhanced_query = query
        if self.query_rewriter:
            try:
                # Extract metadata from context for query rewriting
                rewrite_context = {}

                # Extract department information if available
                departments = set()
                for doc in context:
                    if "metadata" in doc and "department" in doc["metadata"]:
                        departments.add(doc["metadata"]["department"])

                if departments:
                    # If multiple departments, use the first one
                    rewrite_context["department"] = list(departments)[0]

                # Add any additional context from kwargs
                if "user_info" in kwargs:
                    rewrite_context["user_info"] = kwargs["user_info"]
                if "conversation_history" in kwargs:
                    rewrite_context["conversation_history"] = kwargs["conversation_history"]

                # Rewrite the query
                enhanced_query = await self.query_rewriter.rewrite_query(query, rewrite_context)
                logger.debug(f"Enhanced query for generation: '{query}' -> '{enhanced_query}'")
            except Exception as e:
                logger.warning(f"Query enhancement for generation failed: {e}. Using original query.")
                enhanced_query = query

        # Format context for the prompt
        formatted_context = self._format_context(context)

        # Construct the prompt with the enhanced query
        prompt = self._construct_prompt(enhanced_query, formatted_context, original_query=query)

        # Generate response
        if stream:
            return await self.llm_adapter.chat(prompt, stream=True, **kwargs)
        else:
            return await self.llm_adapter.chat(prompt, **kwargs)

    def _format_context(self, context: List[Dict[str, Any]]) -> str:
        """
        Format retrieved documents for inclusion in the prompt.

        Args:
            context: The retrieved documents

        Returns:
            Formatted context string
        """
        if not context:
            return "No relevant information found."

        formatted_chunks = []

        for i, doc in enumerate(context):
            # Extract document information
            text = doc["text"]
            metadata = doc.get("metadata", {})

            # Extract citation information
            source = metadata.get("source", "Unknown")
            title = metadata.get("title", "Untitled")
            section = metadata.get("section", "")
            page = metadata.get("page", "")

            # Format citation
            citation = f"[{i+1}] {title}"
            if section:
                citation += f", {section}"
            if page:
                citation += f", p.{page}"
            if source:
                citation += f" (Source: {source})"

            # Format chunk with citation
            formatted_chunk = f"Document {i+1}:\n{text}\nCitation: {citation}\n"
            formatted_chunks.append(formatted_chunk)

        return "\n".join(formatted_chunks)

    def _construct_prompt(self, query: str, formatted_context: str, original_query: str = None) -> List[Dict[str, str]]:
        """
        Construct a prompt for the LLM.

        Args:
            query: The query text (possibly enhanced)
            formatted_context: The formatted context
            original_query: The original query before enhancement (if different)

        Returns:
            Prompt for the LLM
        """
        system_message = (
            "You are a helpful assistant that provides accurate, informative responses based on the "
            "provided context. Follow these guidelines:\n"
            "1. Base your response primarily on the information in the provided documents.\n"
            "2. If the documents don't contain relevant information, say so clearly.\n"
            "3. Don't make up information that isn't supported by the documents.\n"
            "4. Use a clear, concise, and helpful tone.\n"
        )

        if self.include_citations:
            system_message += (
                "5. Include citations to reference specific documents.\n"
            )

            if self.citation_format == "inline":
                system_message += (
                    "   - Use inline citations like [1], [2], etc.\n"
                    "   - You can cite multiple documents for a single statement if needed.\n"
                )
            elif self.citation_format == "footnote":
                system_message += (
                    "   - Use footnote citations like ¹, ², etc.\n"
                    "   - Include a References section at the end with the full citations.\n"
                )

        # Include both original and enhanced queries if they differ
        question_section = ""
        if original_query and original_query != query:
            question_section = (
                f"Original Question: {original_query}\n"
                f"Enhanced Question: {query}\n"
            )
        else:
            question_section = f"Question: {query}\n"

        user_message = (
            f"{question_section}\n"
            f"Context:\n{formatted_context}\n\n"
            f"Please provide a comprehensive answer based on the above context."
        )

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]
```

### Self-Critique Generator ❌

```python
class SelfCritiqueGenerator(RAGGenerator):
    """
    Generator with self-critique capabilities.

    This class extends RAGGenerator with the ability to critique and
    refine its own responses for improved accuracy and quality.
    """

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Generate a response with self-critique.

        Args:
            query: The query text
            context: The retrieved documents
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the LLM

        Returns:
            Generated response or async iterator for streaming
        """
        # If streaming is requested, fall back to base implementation
        if stream:
            return await super().generate(query, context, stream=True, **kwargs)

        # Generate initial response
        initial_response = await super().generate(query, context, stream=False, **kwargs)

        # Critique and refine the response
        refined_response = await self._critique_and_refine(
            query,
            context,
            initial_response
        )

        return refined_response

    async def _critique_and_refine(
        self,
        query: str,
        context: List[Dict[str, Any]],
        initial_response: str
    ) -> str:
        """
        Critique and refine a response.

        Args:
            query: The query text
            context: The retrieved documents
            initial_response: The initial response

        Returns:
            Refined response
        """
        # Format context for the prompt
        formatted_context = self._format_context(context)

        # Construct the critique prompt
        critique_prompt = [
            {
                "role": "system",
                "content": (
                    "You are a critical evaluator tasked with improving an AI assistant's response. "
                    "Analyze the response for factual accuracy, completeness, and citation quality. "
                    "Focus on:\n"
                    "1. Factual errors or inconsistencies with the provided context\n"
                    "2. Missing important information from the context\n"
                    "3. Unsupported claims that aren't in the context\n"
                    "4. Improper or missing citations\n"
                    "5. Unclear or confusing explanations\n\n"
                    "Provide specific, constructive feedback for improvement."
                )
            },
            {
                "role": "user",
                "content": (
                    f"Question: {query}\n\n"
                    f"Context:\n{formatted_context}\n\n"
                    f"Response to evaluate:\n{initial_response}\n\n"
                    f"Please provide your critique:"
                )
            }
        ]

        # Generate critique
        critique = await self.llm_adapter.chat(critique_prompt)

        # Construct the refinement prompt
        refinement_prompt = [
            {
                "role": "system",
                "content": (
                    "You are a helpful assistant that provides accurate, informative responses based on the "
                    "provided context. You've been given a critique of your previous response. "
                    "Use this feedback to create an improved response that addresses the issues raised."
                )
            },
            {
                "role": "user",
                "content": (
                    f"Question: {query}\n\n"
                    f"Context:\n{formatted_context}\n\n"
                    f"Your previous response:\n{initial_response}\n\n"
                    f"Critique:\n{critique}\n\n"
                    f"Please provide an improved response that addresses the critique:"
                )
            }
        ]

        # Generate refined response
        refined_response = await self.llm_adapter.chat(refinement_prompt)

        return refined_response
```

### Streaming Generator ❌

```python
class StreamingRAGGenerator(RAGGenerator):
    """
    Generator with enhanced streaming capabilities.

    This class extends RAGGenerator with improved streaming support,
    including progress indicators during context processing.
    """

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = True,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Generate a response with enhanced streaming.

        Args:
            query: The query text
            context: The retrieved documents
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the LLM

        Returns:
            Generated response or async iterator for streaming
        """
        # If streaming is not requested, fall back to base implementation
        if not stream:
            return await super().generate(query, context, stream=False, **kwargs)

        # Return a streaming generator
        return self._generate_streaming(query, context, **kwargs)

    async def _generate_streaming(
        self,
        query: str,
        context: List[Dict[str, Any]],
        **kwargs
    ) -> AsyncIterator[str]:
        """
        Generate a streaming response.

        Args:
            query: The query text
            context: The retrieved documents
            **kwargs: Additional arguments to pass to the LLM

        Yields:
            Chunks of the generated response
        """
        # Format context for the prompt
        formatted_context = self._format_context(context)

        # Construct the prompt
        prompt = self._construct_prompt(query, formatted_context)

        # Yield initial progress indicator
        yield "Analyzing context... "

        # Get streaming response from LLM
        response_stream = await self.llm_adapter.chat(prompt, stream=True, **kwargs)

        # Yield chunks from the response stream
        first_chunk = True
        async for chunk in response_stream:
            if first_chunk:
                # Replace progress indicator with first chunk
                yield "\r" + " " * 20 + "\r"  # Clear the progress indicator
                first_chunk = False

            yield chunk
```

## 🛠️ Implementation Details

### Context Formatting

The context formatting process transforms retrieved documents into a structured format suitable for inclusion in the LLM prompt. This involves:

1. **Document Organization**: Arranging documents in a clear, readable format
2. **Metadata Extraction**: Extracting relevant metadata for citations
3. **Citation Formatting**: Creating properly formatted citations for each document
4. **Truncation Handling**: Indicating when documents have been truncated to fit context windows
5. **Relevance Scoring**: Including relevance scores to help the LLM prioritize information

The `_format_context` method in the `RAGGenerator` class handles this process, ensuring that the context is presented to the LLM in a way that maximizes its ability to generate accurate, well-cited responses. The implementation supports multiple citation formats and includes rich metadata extraction.

```python
def _format_context(self, context: List[Dict[str, Any]]) -> str:
    """Format retrieved documents for inclusion in the prompt."""
    if not context:
        return "No relevant information found."

    formatted_chunks = []

    for i, doc in enumerate(context):
        # Extract document information
        text = doc.get("text", "")
        metadata = doc.get("metadata", {})

        # Extract citation information
        source = metadata.get("source", "Unknown")
        title = metadata.get("title", "Untitled")
        section = metadata.get("section", "")
        page = metadata.get("page", "")
        author = metadata.get("author", "")
        date = metadata.get("date", "")
        year = metadata.get("year", "")
        url = metadata.get("url", "")

        # Format citation based on format
        if self.citation_format == "academic":
            # Academic format: Author (Year). Title. Source.
            citation_author = author or "Unknown Author"
            citation_year = year or date.split("-")[0] if date else "n.d."
            citation = f"[{i+1}] {citation_author}"
            if citation_year:
                citation += f" ({citation_year})"
            citation += f". {title}"
            if source:
                citation += f". {source}"
        elif self.citation_format == "endnote":
            # Endnote format: [1] Title, Section, Source (Year)
            citation = f"[{i+1}] {title}"
            if section:
                citation += f", {section}"
            if source:
                citation += f", {source}"
            if year or date:
                year_str = year or date.split("-")[0] if date else ""
                if year_str:
                    citation += f" ({year_str})"
        elif self.citation_format == "footnote":
            # Footnote format: Title, Section, p.Page (Source)
            citation = f"[^{i+1}] {title}"
            if section:
                citation += f", {section}"
            if page:
                citation += f", p.{page}"
            if source:
                citation += f" (Source: {source})"
        else:
            # Default inline format: [1] Title, Section, p.Page (Source)
            citation = f"[{i+1}] {title}"
            if section:
                citation += f", {section}"
            if page:
                citation += f", p.{page}"
            if source:
                citation += f" (Source: {source})"

        # Add URL if available and not already in source
        if url and "http" not in citation:
            citation += f" URL: {url}"

        # Check if document was truncated
        truncated = doc.get("truncated", False)
        truncation_note = " [truncated]" if truncated else ""

        # Add relevance score if available
        score_note = ""
        if "score" in doc and isinstance(doc["score"], (int, float)):
            score = doc["score"]
            if score <= 1.0:  # Normalized score
                score_percentage = f"{score:.0%}"
            else:  # Raw score
                score_percentage = f"{score:.2f}"
            score_note = f" [relevance: {score_percentage}]"

        # Format chunk with citation
        formatted_chunk = f"Document {i+1}:{truncation_note}{score_note}\n{text}\nCitation: {citation}\n"
        formatted_chunks.append(formatted_chunk)

    return "\n".join(formatted_chunks)
```

### Context Window Management

A key enhancement in our implementation is sophisticated context window management, which ensures that the most relevant information is included in the prompt while staying within token limits:

1. **Token Estimation**: Fast estimation of token counts to avoid expensive tokenization
2. **Smart Document Selection**: Prioritizing documents based on relevance and information density
3. **Partial Document Inclusion**: Including partial content from high-priority documents when necessary
4. **Fallback Mechanisms**: Graceful degradation when context exceeds token limits

The `_manage_context_window` method handles this process, using a combination of strategies to maximize the value of the included context:

```python
def _manage_context_window(
    self,
    context: List[Dict[str, Any]],
    model_name: str = "gpt-3.5-turbo"
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Manage the context window to ensure it fits within token limits."""
    if not context:
        return [], {"original_docs": 0, "used_docs": 0, "truncated": False, "summarized": False}

    # Quick estimate of tokens using character count
    estimated_total_chars = sum(len(doc.get("text", "")) for doc in context)
    estimated_total_tokens = estimate_tokens_from_chars(estimated_total_chars)

    # If estimated tokens are well below the limit, do accurate counting
    if estimated_total_tokens <= self.max_context_tokens * 0.7:
        # Calculate tokens for each document
        for doc in context:
            doc["token_count"] = count_tokens(doc.get("text", ""), model_name)

        # Calculate total tokens in context
        total_tokens = sum(doc["token_count"] for doc in context)

        # Initialize context info
        context_info = {
            "original_docs": len(context),
            "original_tokens": total_tokens,
            "used_docs": len(context),
            "used_tokens": total_tokens,
            "truncated": False,
            "summarized": False
        }

        # If context is within limits, return as is
        if total_tokens <= self.max_context_tokens:
            return context, context_info
    else:
        # Initialize context info with estimates
        context_info = {
            "original_docs": len(context),
            "original_tokens": estimated_total_tokens,
            "used_docs": 0,
            "used_tokens": 0,
            "truncated": True,
            "summarized": False
        }

    # Context exceeds token limit, apply management strategies
    logger.info(
        f"Context likely exceeds token limit (est. {estimated_total_tokens} > {self.max_context_tokens}), "
        f"applying management strategies"
    )

    # Ensure all documents have token counts
    for doc in context:
        if "token_count" not in doc:
            doc["token_count"] = count_tokens(doc.get("text", ""), model_name)

    # Calculate information density scores
    for doc in context:
        relevance = doc.get("score", 0.5)
        if relevance <= 0:
            relevance = 0.01
        if relevance > 1.0:
            relevance = relevance / 10.0 if relevance < 10 else 0.9
        doc["density_score"] = relevance / (doc["token_count"] / 100)

    # Sort by density score (highest first)
    sorted_context = sorted(context, key=lambda x: x.get("density_score", 0), reverse=True)

    # Allocate tokens to documents based on priority
    managed_context = []
    current_tokens = 0
    remaining_tokens = self.max_context_tokens

    # First pass: include highest priority documents that fit completely
    for doc in sorted_context:
        if doc["token_count"] <= remaining_tokens:
            managed_context.append(doc)
            current_tokens += doc["token_count"]
            remaining_tokens -= doc["token_count"]

    # Second pass: include partial content from the next highest priority document if needed
    if (len(managed_context) < min(3, len(context)) or
            remaining_tokens > self.max_context_tokens * 0.2) and sorted_context:

        next_docs = [doc for doc in sorted_context if doc not in managed_context]
        if next_docs:
            next_doc = next_docs[0]

            # Truncate the document to fit the remaining tokens
            truncated_text, _ = truncate_to_token_limit(
                next_doc.get("text", ""),
                remaining_tokens,
                model_name
            )

            if len(truncated_text) > 100:
                truncated_doc = next_doc.copy()
                truncated_doc["text"] = truncated_text
                truncated_doc["truncated"] = True
                truncated_doc["token_count"] = count_tokens(truncated_text, model_name)
                managed_context.append(truncated_doc)
                current_tokens += truncated_doc["token_count"]

    # Update context info
    context_info["used_docs"] = len(managed_context)
    context_info["used_tokens"] = current_tokens
    context_info["truncated"] = len(managed_context) < len(context)

    # Consider summarization for future implementation
    if (self.fallback_to_summarization and
            len(managed_context) < min(3, len(context)) and
            len(context) > 3):
        context_info["summarized"] = True

    return managed_context, context_info
```

### Prompt Construction

Prompt construction is a critical aspect of the generator component, as it directly influences the quality and relevance of the generated responses. Our enhanced implementation includes:

1. **System Message**: Detailed instructions for the LLM on how to use the context
2. **Citation Guidelines**: Format-specific instructions for including citations
3. **Query Presentation**: Clear presentation of both original and enhanced queries
4. **Context Information**: Notes about context processing (truncation, summarization)
5. **Structured Output Instructions**: Guidelines for organizing the response

The `_construct_prompt` method creates a prompt that guides the LLM to generate accurate, informative responses:

```python
def _construct_prompt(
    self,
    query: str,
    formatted_context: str,
    original_query: str = None,
    context_info: Dict[str, Any] = None
) -> List[Dict[str, str]]:
    """Construct a prompt for the LLM."""
    system_message = (
        "You are a helpful assistant that provides accurate, informative responses based on the "
        "provided context. Follow these guidelines:\n"
        "1. Base your response primarily on the information in the provided documents.\n"
        "2. If the documents don't contain relevant information, say so clearly.\n"
        "3. Don't make up information that isn't supported by the documents.\n"
        "4. Use a clear, concise, and helpful tone.\n"
    )

    if self.include_citations:
        system_message += (
            "5. Include citations to reference specific documents.\n"
        )

        if self.citation_format == "inline":
            system_message += (
                "   - Use inline citations like [1], [2], etc.\n"
                "   - You can cite multiple documents for a single statement if needed.\n"
                "   - Include a References section at the end with the full citations.\n"
            )
        elif self.citation_format == "footnote":
            system_message += (
                "   - Use footnote citations like [^1], [^2], etc.\n"
                "   - Include a References section at the end with the full citations.\n"
            )
        elif self.citation_format == "endnote":
            system_message += (
                "   - Use endnote citations like [1], [2], etc.\n"
                "   - Include a numbered References section at the end.\n"
            )
        elif self.citation_format == "academic":
            system_message += (
                "   - Use academic citations like (Author, Year) or (Author et al., Year).\n"
                "   - Include a References section at the end in academic format.\n"
            )

    # Include both original and enhanced queries if they differ
    question_section = ""
    if original_query and original_query != query:
        question_section = (
            f"Original Question: {original_query}\n"
            f"Enhanced Question: {query}\n"
        )
    else:
        question_section = f"Question: {query}\n"

    # Add context information if available
    context_section = ""
    if context_info:
        # If context was truncated, add a note
        if context_info.get("truncated", False):
            original_docs = context_info.get("original_docs", 0)
            used_docs = context_info.get("used_docs", 0)
            if original_docs > used_docs:
                context_section += (
                    f"Note: Only {used_docs} of {original_docs} relevant documents "
                    f"could be included due to context limitations.\n"
                )

        # If context was summarized, add a note
        if context_info.get("summarized", False):
            context_section += (
                "Note: Some documents were summarized due to their length.\n"
            )

    # Construct the user message
    user_message = f"{question_section}\n"

    if context_section:
        user_message += f"{context_section}\n"

    user_message += (
        f"Context:\n{formatted_context}\n\n"
        f"Please provide a comprehensive answer based on the above context."
    )

    # Add specific instructions based on citation format
    if self.include_citations:
        if self.citation_format == "inline":
            user_message += (
                " Make sure to include inline citations [1], [2], etc. for each piece of information."
            )
        elif self.citation_format == "footnote":
            user_message += (
                " Make sure to include footnote citations [^1], [^2], etc. for each piece of information."
            )
        elif self.citation_format == "endnote":
            user_message += (
                " Make sure to include endnote citations [1], [2], etc. for each piece of information."
            )
        elif self.citation_format == "academic":
            user_message += (
                " Make sure to include academic citations (Author, Year) for each piece of information."
            )

    return [
        {"role": "system", "content": system_message},
        {"role": "user", "content": user_message}
    ]
```

### Citation Handling

Citations are essential for traceability and credibility in RAG systems. Our implementation supports multiple citation formats:

1. **Inline Citations**: References like [1], [2], etc. within the text
2. **Footnote Citations**: References like [^1], [^2], etc. with a references section
3. **Endnote Citations**: References like [1], [2], etc. with a numbered references section
4. **Academic Citations**: References like (Author, Year) with an academic references section

The citation format is configurable through the `citation_format` parameter, and the implementation includes post-processing to ensure proper citation formatting:

```python
def _ensure_citations(self, response: str, context: List[Dict[str, Any]]) -> str:
    """Ensure that the response includes proper citations."""
    # If no context or citations not required, return as is
    if not context or not self.include_citations or self.citation_format == "none":
        return response

    # Check if response already has citations
    citation_patterns = {
        "inline": r'\[\d+\]',
        "footnote": r'\[\^?\d+\]',
        "endnote": r'\[\d+\]',
        "academic": r'\([A-Za-z]+ et al\., \d{4}\)'
    }

    pattern = citation_patterns.get(self.citation_format, r'\[\d+\]')
    has_citations = bool(re.search(pattern, response))

    # If response already has citations, return as is
    if has_citations:
        return response

    # Add missing citations section
    if "References" not in response and "Sources" not in response:
        references = "\n\n## References\n"

        for i, doc in enumerate(context, 1):
            metadata = doc.get("metadata", {})
            source = metadata.get("source", "Unknown")
            title = metadata.get("title", "Untitled")
            section = metadata.get("section", "")

            if self.citation_format == "academic":
                author = metadata.get("author", "Unknown Author")
                year = metadata.get("year", "n.d.")
                references += f"{i}. {author} ({year}). {title}. {source}"
            else:
                references += f"{i}. {title}"
                if section:
                    references += f", {section}"
                references += f" (Source: {source})"

            references += "\n"

        response += references

    return response
```

### Response Generation

The response generation process has been enhanced with several key features:

1. **Context Window Management**: Smart handling of large context to fit within token limits
2. **Structured Output**: Guidelines for organizing responses with clear sections
3. **Citation Enforcement**: Post-processing to ensure proper citations
4. **Fallback Notices**: Transparent communication about any limitations

The `generate` method orchestrates this process:

```python
async def generate(
    self,
    query: str,
    context: List[Dict[str, Any]],
    stream: bool = False,
    model_name: str = None,
    **kwargs
) -> Union[str, AsyncIterator[str]]:
    """Generate a response based on the query and retrieved context."""
    try:
        # Get model name for token counting
        model_name = model_name or kwargs.get("model", "gpt-3.5-turbo")

        # Track if we had to apply any fallbacks
        applied_fallbacks = []

        # Enhance query with query rewriter if available
        enhanced_query = query
        if self.query_rewriter:
            try:
                # Extract metadata from context for query rewriting
                rewrite_context = {}

                # Extract department information if available
                departments = set()
                for doc in context:
                    if "metadata" in doc and "department" in doc["metadata"]:
                        departments.add(doc["metadata"]["department"])

                if departments:
                    # If multiple departments, use the first one
                    rewrite_context["department"] = list(departments)[0]

                # Add any additional context from kwargs
                if "user_info" in kwargs:
                    rewrite_context["user_info"] = kwargs["user_info"]
                if "conversation_history" in kwargs:
                    rewrite_context["conversation_history"] = kwargs["conversation_history"]

                # Rewrite the query
                from app.core.timeout import with_timeout_and_retry
                enhanced_query = await with_timeout_and_retry(
                    self.query_rewriter.rewrite_query,
                    query,
                    rewrite_context,
                    timeout_seconds=3,
                    operation_name="query_rewriter.rewrite_query_for_generator",
                    max_attempts=1
                )
                logger.debug(f"Enhanced query for generation: '{query}' -> '{enhanced_query}'")
            except Exception as e:
                logger.warning(f"Query enhancement for generation failed: {e}. Using original query.")
                enhanced_query = query
                applied_fallbacks.append("query_enhancement")

        # Sort context by relevance score if available
        if context:
            context = sorted(
                context,
                key=lambda x: x.get("score", 0) if isinstance(x.get("score"), (int, float)) else 0,
                reverse=True
            )

        # Apply context window management
        managed_context, context_info = self._manage_context_window(
            context,
            model_name=model_name
        )

        if context_info.get("truncated", False):
            applied_fallbacks.append("context_truncation")
            logger.info(
                f"Context was truncated from {context_info.get('original_docs', 0)} to "
                f"{context_info.get('used_docs', 0)} documents due to token limit"
            )

        if context_info.get("summarized", False):
            applied_fallbacks.append("context_summarization")
            logger.info(f"Context was summarized due to token limit")

        # Format context for the prompt
        formatted_context = self._format_context(managed_context)

        # Construct the prompt with the enhanced query
        prompt = self._construct_prompt(
            enhanced_query,
            formatted_context,
            original_query=query,
            context_info=context_info
        )

        # Add structured output instructions if enabled
        if self.structured_output:
            prompt = self._add_structured_output_instructions(prompt)

        # Generate response
        if stream:
            logger.debug(f"Generating streaming response for query: {enhanced_query}")
            return await self.llm_adapter.chat(prompt, stream=True, **kwargs)
        else:
            logger.debug(f"Generating response for query: {enhanced_query}")
            response = await self.llm_adapter.chat(prompt, **kwargs)

            # Post-process response to ensure proper citation formatting
            if self.include_citations:
                response = self._ensure_citations(response, managed_context)

            # Add fallback notices if any were applied
            if applied_fallbacks and not stream:
                response = self._add_fallback_notices(response, applied_fallbacks, context_info)

            return response
    except Exception as e:
        logger.error(f"Error generating response: {e}", exc_info=True)
        return f"I encountered an error while generating a response: {str(e)}"
```

## 📊 Advanced Techniques

### Self-Critique Mechanism

The self-critique mechanism enhances response quality through a two-step process:

1. **Initial Generation**: Generate a first-draft response based on the query and context
2. **Critique Generation**: Analyze the initial response for factual accuracy, completeness, and citation quality
3. **Response Refinement**: Generate an improved response that addresses the issues identified in the critique

This approach significantly improves response quality by catching and correcting issues that might be present in the initial response, resulting in more accurate, comprehensive, and well-cited answers.

### Enhanced Streaming

Enhanced streaming improves the user experience by:

1. **Progress Indicators**: Showing the user that the system is working on their query
2. **Chunk-by-Chunk Delivery**: Delivering response chunks as they are generated
3. **Smooth Transitions**: Ensuring smooth transitions between progress indicators and response chunks

This approach provides a more responsive and engaging user experience, especially for complex queries that require significant processing time.

### Response Formatting

Response formatting ensures that the generated responses are clear, well-structured, and easy to understand. This includes:

1. **Citation Integration**: Properly integrating citations into the response
2. **Structural Elements**: Adding headings, bullet points, and other structural elements
3. **Readability Enhancements**: Ensuring that the response is easy to read and understand

The generator component provides guidance to the LLM on how to format responses, resulting in consistent, high-quality outputs.

## 📝 Usage Examples

### Basic Generation

```python
# Initialize components
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create generator
generator = RAGGenerator(
    llm_adapter=llm_adapter,
    include_citations=True,
    citation_format="inline"
)

# Sample context (retrieved documents)
context = [
    {
        "id": "doc1",
        "text": "Our Q2 marketing strategy focuses on digital channels, with a 30% increase in social media advertising budget.",
        "metadata": {
            "source": "Marketing Plan",
            "title": "Q2 Marketing Strategy",
            "section": "Budget Allocation",
            "department": "marketing"
        },
        "score": 0.92
    },
    {
        "id": "doc2",
        "text": "The Q2 campaign will target millennials and Gen Z through Instagram and TikTok, with emphasis on video content.",
        "metadata": {
            "source": "Campaign Brief",
            "title": "Q2 Campaign Targeting",
            "section": "Audience Strategy",
            "department": "marketing"
        },
        "score": 0.85
    }
]

# Generate response
query = "What is our marketing strategy for Q2?"
response = await generator.generate(query, context)

print(response)
```

### With Query Rewriter

```python
# Initialize components
llm_adapter = get_llm_adapter("openai", model="gpt-4")
embedding_model = EmbeddingFactory.create("huggingface", model_name="intfloat/e5-base-v2")
knowledge_base_service = KnowledgeBaseService(firebase_client)

# Create query rewriter
query_rewriter = QueryRewriter(
    llm_adapter=llm_adapter,
    embedding_model=embedding_model,
    knowledge_base_service=knowledge_base_service
)

# Create generator with query rewriter
generator = RAGGenerator(
    llm_adapter=llm_adapter,
    include_citations=True,
    citation_format="inline",
    query_rewriter=query_rewriter
)

# Generate response with enhanced query
query = "Q2 marketing plans?"
response = await generator.generate(
    query,
    context,
    user_info="Marketing Director",
    conversation_history="Previous discussion about digital marketing trends"
)

print(response)
```

### With Self-Critique

```python
# Initialize components
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create generator with self-critique
generator = SelfCritiqueGenerator(
    llm_adapter=llm_adapter,
    include_citations=True,
    citation_format="inline"
)

# Generate response with self-critique
response = await generator.generate(query, context)

print(response)
```

### With Streaming

```python
# Initialize components
llm_adapter = get_llm_adapter("openai", model="gpt-4")

# Create generator with enhanced streaming
generator = StreamingRAGGenerator(
    llm_adapter=llm_adapter,
    include_citations=True,
    citation_format="inline"
)

# Generate streaming response
stream = await generator.generate(query, context, stream=True)

# Process streaming response
async for chunk in stream:
    print(chunk, end="", flush=True)
```

## 🧪 Testing Strategy

### Unit Tests

1. **Test Base Class**:
   - Verify that abstract methods raise NotImplementedError

2. **Test RAG Generator**:
   - Test context formatting with different inputs
   - Test prompt construction with original and enhanced queries
   - Test response generation with and without query rewriting
   - Test citation formatting
   - Test integration with QueryRewriter
   - Test extraction of context metadata for query rewriting

3. **Test Self-Critique Generator**:
   - Test critique generation
   - Test response refinement
   - Compare initial and refined responses

4. **Test Streaming Generator**:
   - Test streaming behavior
   - Verify progress indicators
   - Test with different chunk sizes

### Integration Tests

1. **Test with Retriever**:
   - Verify that generator works with retriever output
   - Test end-to-end RAG pipeline

2. **Test with Different LLM Adapters**:
   - Verify that generator works with different LLM adapters
   - Test with streaming and non-streaming adapters

### Testing Dependencies

- **pytest**: For unit and integration testing
- **pytest-asyncio**: For testing async functions
- **unittest.mock**: For mocking components

## 📦 Dependencies

- **openai**: For LLM-based generation (via LLM adapter)
- **anthropic**: For LLM-based generation (via LLM adapter)
- **asyncio**: For asynchronous processing

## 🔄 Implementation Plan

1. **Create Base Class** ✅:
   - Define the Generator abstract base class
   - Implement required methods

2. **Implement RAG Generator** ✅:
   - Create RAGGenerator class
   - Implement context formatting
   - Implement prompt construction
   - Implement response generation

3. **Implement Context Window Management** ✅:
   - Implement token counting utilities
   - Implement smart document selection
   - Implement partial document inclusion
   - Implement fallback mechanisms

4. **Implement Enhanced Citation Support** ✅:
   - Support multiple citation formats (inline, footnote, endnote, academic)
   - Implement citation post-processing
   - Add citation enforcement

5. **Implement Structured Output** ✅:
   - Add structured output instructions
   - Implement section formatting
   - Add fallback notices

6. **Implement Self-Critique Enhancement** ❌:
   - Create SelfCritiqueGenerator class
   - Implement critique and refinement logic
   - Test with different critique strategies

7. **Implement Streaming Enhancement** ❌:
   - Create StreamingRAGGenerator class
   - Implement enhanced streaming support
   - Add progress indicators

8. **Write Tests** ✅:
   - Create unit tests for each component
   - Create integration tests for the complete pipeline
   - Verify correct behavior with different configurations

### Implementation Status

| Phase | Tasks | Status | Notes |
|-------|-------|--------|-------|
| **Phase 1** | Create base class and RAG Generator | ✅ Completed | Implemented with enhanced features |
| **Phase 2** | Implement Context Window Management | ✅ Completed | Added sophisticated token management |
| **Phase 3** | Implement Enhanced Citation Support | ✅ Completed | Added support for multiple citation formats |
| **Phase 4** | Implement Structured Output | ✅ Completed | Added structured output and fallback notices |
| **Phase 5** | Write tests for core functionality | ✅ Completed | Added unit and integration tests |
| **Phase 6** | Implement Self-Critique Generator | ❌ Deferred | Planned for future implementation |
| **Phase 7** | Implement Streaming Generator | ❌ Deferred | Planned for future implementation |
| **Phase 8** | Write tests for advanced features | ❌ Deferred | Planned for future implementation |

### Completed Implementation Features

The current implementation includes the following features:

1. **Core Functionality** ✅:
   - Abstract `Generator` base class
   - Concrete `RAGGenerator` implementation
   - Context formatting with rich metadata extraction
   - Sophisticated prompt construction
   - Robust response generation

2. **Context Window Management** ✅:
   - Fast token estimation
   - Smart document selection based on relevance and information density
   - Partial document inclusion for high-priority documents
   - Graceful degradation with fallback mechanisms

3. **Citation Support** ✅:
   - Multiple citation formats (inline, footnote, endnote, academic)
   - Rich metadata extraction for citations
   - Citation enforcement through post-processing
   - References section generation

4. **Structured Output** ✅:
   - Clear section organization
   - Fallback notices for transparency
   - Consistent formatting

5. **Error Handling** ✅:
   - Comprehensive error logging
   - Graceful degradation
   - Informative error messages

6. **Performance Optimization** ✅:
   - Token counting caching
   - Fast token estimation
   - Binary search for large document truncation
   - Smart document selection

### Deferred Features and Future Roadmap

The following features have been explicitly deferred for future phases, with a clear roadmap for implementation:

1. **Context Summarization** ❌:
   - Implement summarization fallback for large document sets
   - Add document condensation for token efficiency
   - Preserve key information while reducing token usage
   - Prioritize implementation as the next immediate step

2. **Self-Critique Mechanism** ❌:
   - SelfCritiqueGenerator implementation
   - Critique and refinement logic
   - Quality comparison metrics
   - Two-step generation process

3. **Enhanced Streaming** ❌:
   - StreamingRAGGenerator implementation
   - Progress indicators during context processing
   - Smooth transitions between processing stages
   - Real-time feedback during generation

4. **Performance Benchmarking** ❌:
   - Create benchmark test suite
   - Measure performance with various document sizes
   - Identify bottlenecks and optimization opportunities
   - Establish performance baselines and targets

5. **Enhanced Department Agent Integration** ❌:
   - Department-specific context processing
   - Customized response formatting by department
   - Specialized citation formats for different departments
   - Deeper integration with department-specific knowledge

6. **Advanced Context Management** ❌:
   - Hierarchical context organization
   - Dynamic context selection based on query
   - Context caching for repeated queries
   - Multi-strategy context handling

This focused approach aligns with the proof-of-concept architecture goals and ensures we build a solid foundation that can be extended as the system matures.

## 🎯 Conclusion

The generator component is the final piece of the RAG system, responsible for producing contextually informed responses based on retrieved documents. We have successfully implemented an enhanced version with comprehensive functionality that goes beyond the initial proof-of-concept requirements.

### Implementation Benefits

1. **Accuracy**: The enhanced citation mechanism ensures that responses are properly attributed to their sources, with support for multiple citation formats
2. **Context Management**: Sophisticated context window management ensures that the most relevant information is included in the prompt, even with large document sets
3. **Performance**: Optimized token counting and smart document selection improve performance and reduce token usage
4. **Integration**: Seamless integration with the query analyzer, retriever, and LLM adapter
5. **Flexibility**: The modular design allows for easy extension in future phases
6. **Reliability**: Robust error handling ensures graceful degradation
7. **Transparency**: Fallback notices provide clear information about any limitations

### Key Enhancements

The implementation includes several key enhancements beyond the initial requirements:

1. **Advanced Context Window Management**: Smart document selection based on relevance and information density, with partial document inclusion for high-priority documents
2. **Enhanced Citation Support**: Support for multiple citation formats (inline, footnote, endnote, academic) with rich metadata extraction
3. **Structured Output**: Clear section organization with consistent formatting
4. **Performance Optimization**: Token counting caching, fast token estimation, and binary search for large document truncation
5. **Comprehensive Testing**: Unit and integration tests for all core functionality

### Future Enhancements

While the current implementation is comprehensive, the following enhancements are planned for future phases:

1. **Context Summarization**: Implement the summarization fallback for large document sets that exceed token limits. This would complete the context window management strategy by providing a way to condense information from multiple documents when they can't all fit within the token limit.

2. **Self-Critique Mechanism**: Implement the SelfCritiqueGenerator for improved response quality through a two-step generation process. This would involve generating an initial response, critiquing it for accuracy and completeness, and then generating a refined response.

3. **Enhanced Streaming**: Implement the StreamingRAGGenerator for better user experience, including progress indicators during context processing and smooth transitions between processing stages.

4. **Performance Benchmarking**: Measure and optimize performance with large document sets to identify bottlenecks and areas for improvement. This would involve creating benchmark tests with various document sizes and complexity levels.

5. **Integration with Department Agents**: Further enhance the integration with department agents by adding department-specific context processing and response formatting. While basic integration is already implemented, this would involve deeper customization based on department needs.

6. **Hierarchical Context Organization**: Organize context in a hierarchical structure for more efficient use of token limits, allowing the model to focus on the most relevant sections first.

7. **Dynamic Context Selection**: Adjust context selection based on query complexity and user preferences, potentially using different strategies for different types of queries.

The current implementation provides a solid foundation for these future enhancements, with a modular design that allows for easy extension as the system matures. Each enhancement builds upon the existing architecture and can be implemented incrementally to provide continuous improvement to the RAG system.

