# 🔍 Query Analyzer

This document provides a detailed guide for implementing the enhanced Query Analyzer component for the RAG system.

> **Note:** This is an enhanced version of the existing Python implementation (`backend/app/rag/query_analyzer.py`), building on the TypeScript implementation (`src/genkit/knowledge/queryAnalyzer.ts`), with significant improvements in semantic understanding through embedding-based similarity. This implementation focuses on the core requirements for the Python migration proof-of-concept.

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
   - [Purpose and Benefits](#purpose-and-benefits)
   - [Current vs. Enhanced Implementation](#current-vs-enhanced-implementation)
   - [Integration Points](#integration-points)
2. [🧩 Core Functionality](#-core-functionality)
   - [Department Detection](#department-detection)
   - [Section Detection](#section-detection)
   - [Query Type Classification](#query-type-classification)
3. [📝 API Design](#-api-design)
   - [Class Structure](#class-structure)
   - [Usage Examples](#usage-examples)
4. [⚙️ Implementation Details](#️-implementation-details)
   - [Embedding-Based Similarity](#embedding-based-similarity)
   - [Hybrid Detection Approach](#hybrid-detection-approach)
   - [Error Handling and Fallbacks](#error-handling-and-fallbacks)
   - [Pluggable Detection Strategies](#pluggable-detection-strategies)
   - [Confidence Calibration](#confidence-calibration)
   - [Feedback Loop](#feedback-loop)
   - [External Configuration](#external-configuration)
   - [Enhanced Output Format](#enhanced-output-format)
   - [Advanced Text Preprocessing](#advanced-text-preprocessing)
   - [Dynamic Embedding Dimensionality](#dynamic-embedding-dimensionality)
5. [🧪 Testing Strategy](#-testing-strategy)
   - [Unit Tests](#unit-tests)
   - [Evaluation Metrics](#evaluation-metrics)
   - [Test Implementation Examples](#test-implementation-examples)
6. [🔮 Future Enhancements](#-future-enhancements)

## 🔍 Overview

### Purpose and Benefits

The Query Analyzer is a critical component of the RAG system that analyzes user queries to determine their intent, relevant departments, and target sections. This enhanced version adds embedding-based similarity and more sophisticated analysis techniques.

Key benefits include:

- **🧠 Semantic Understanding**: Understand queries based on meaning, not just keywords
- **🎯 Precise Routing**: Direct queries to the most relevant departments
- **📑 Section Targeting**: Identify specific document sections that are most relevant
- **⚡ Efficient Processing**: Optimize the retrieval process by focusing on relevant content

### Current vs. Enhanced Implementation

The current Python implementation (`query_analyzer.py`) provides basic functionality:
- Simple keyword-based department detection
- Basic query type classification
- LLM-based department detection as a fallback

The enhanced implementation adds:
- Embedding-based similarity for semantic understanding
- Improved department detection using a hybrid approach
- Section detection for targeted knowledge retrieval
- More robust error handling and fallbacks

This aligns with the requirements specified in the proof-of-concept architecture document:
> "Enhance the query analyzer with embedding-based similarity, improve department detection using embeddings, and implement section detection for targeted knowledge retrieval."

### Integration Points

The Query Analyzer integrates with the following components:

1. **🧠 Embedding Model**: For converting text to vector representations
2. **🔍 Knowledge Base**: For retrieving relevant context
3. **🤖 LLM Adapter**: For advanced query understanding

## 🧩 Core Functionality

### Department Detection

The Query Analyzer determines which departments are most relevant to the query:

1. **🔢 Embedding-Based Detection**: Uses vector similarity to find relevant departments
   - Compares query embedding with department description embeddings
   - Ranks departments by similarity score
   - Applies threshold to filter relevant departments

2. **🔤 Keyword-Based Detection**: Uses domain-specific keywords as a fallback
   - Maintains keyword lists for each department
   - Checks for presence of department-specific terminology
   - Weights matches based on keyword importance

3. **🧠 LLM-Based Detection**: Uses LLM for complex cases
   - Analyzes query in context of department responsibilities
   - Handles ambiguous queries that span multiple departments
   - Provides confidence scores for department relevance

### Section Detection

The Query Analyzer identifies specific document sections that are most relevant:

1. **📑 Section Targeting**: Determines if the query targets specific sections
   - Analyzes query for section-specific indicators
   - Identifies section types (executive summary, financial data, etc.)
   - Maps query intent to section categories

2. **🔍 Granularity Detection**: Determines the appropriate level of detail
   - Classifies queries as broad or specific
   - Identifies if document-level or section-level retrieval is more appropriate
   - Adjusts retrieval strategy based on query specificity

#### Technical Implementation Details

**Section Types and Descriptions**:
```python
# In __init__ method
# Default section types if not provided
if section_descriptions is None:
    self.section_descriptions = {
        "executive_summary": "High-level overview of key points, findings, and recommendations.",
        "financial_data": "Financial information including budgets, costs, revenue, and forecasts.",
        "marketing_strategy": "Marketing plans, campaigns, target audiences, and promotional activities.",
        "product_details": "Product specifications, features, capabilities, and technical information.",
        "market_analysis": "Analysis of market trends, competitors, and industry landscape.",
        "performance_metrics": "KPIs, statistics, and performance measurements.",
        "goals_objectives": "Company goals, objectives, targets, and strategic priorities."
    }
else:
    self.section_descriptions = section_descriptions
```

**Section Detection Method**:
```python
async def detect_sections(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant section types for the query.

    Args:
        query: The user query

    Returns:
        List of section type objects with confidence scores
    """
    self.logger.info(f"Detecting sections for query: {query}")

    # Try embedding-based detection first
    try:
        embedding_results = await self._embedding_based_section_detection(query)
    except Exception as e:
        self.logger.error(f"Embedding-based section detection failed: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        embedding_results = []

    # Try keyword-based detection
    try:
        keyword_results = self._keyword_based_section_detection(query)
    except Exception as e:
        self.logger.error(f"Keyword-based section detection failed: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        keyword_results = []

    # Combine results
    combined_results = {}

    # Process embedding results
    for result in embedding_results:
        section_type = result["type"]
        combined_results[section_type] = combined_results.get(section_type, {})
        combined_results[section_type]["embedding_score"] = result["confidence"]

    # Process keyword results
    for result in keyword_results:
        section_type = result["type"]
        combined_results[section_type] = combined_results.get(section_type, {})
        combined_results[section_type]["keyword_score"] = result["confidence"]

    # Calculate final confidence scores
    final_results = []
    for section_type, scores in combined_results.items():
        # Get scores with defaults
        embedding_score = scores.get("embedding_score", 0.0)
        keyword_score = scores.get("keyword_score", 0.0)

        # Calculate weighted average
        # Embedding is more reliable for sections
        embedding_weight = 0.7
        keyword_weight = 0.3

        total_weight = 0
        weighted_sum = 0

        if embedding_score > 0:
            weighted_sum += embedding_score * embedding_weight
            total_weight += embedding_weight

        if keyword_score > 0:
            weighted_sum += keyword_score * keyword_weight
            total_weight += keyword_weight

        # Calculate confidence (avoid division by zero)
        confidence = weighted_sum / total_weight if total_weight > 0 else 0.0

        # Add to results if above threshold
        if confidence > 0.5:  # Section threshold
            final_results.append({
                "type": section_type,
                "confidence": confidence
            })

    # Sort by confidence
    final_results.sort(key=lambda x: x["confidence"], reverse=True)

    # Determine granularity
    granularity = self._determine_query_granularity(query)

    # Adjust results based on granularity
    if granularity == "broad" and final_results:
        # For broad queries, reduce confidence in specific sections
        for result in final_results:
            result["confidence"] *= 0.8
    elif granularity == "specific" and final_results:
        # For specific queries, increase confidence in top section
        final_results[0]["confidence"] = min(0.95, final_results[0]["confidence"] * 1.2)

    self.logger.info(f"Section detection results: {final_results}")
    return final_results
```

**Embedding-Based Section Detection**:
```python
async def _embedding_based_section_detection(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant sections using embedding similarity.

    Args:
        query: The user query

    Returns:
        List of section type objects with similarity scores
    """
    try:
        # Preprocess query
        processed_query = self._preprocess_text(query)

        # Generate query embedding
        query_embedding = await self._get_embedding_with_retry(processed_query)

        # Calculate similarity with each section embedding
        results = []
        for section_type, section_data in self.section_embeddings.items():
            # Calculate cosine similarity
            similarity = self._calculate_cosine_similarity(
                query_embedding,
                section_data["embedding"]
            )

            # Add to results if above threshold
            if similarity > 0.6:  # Lower threshold for sections
                results.append({
                    "type": section_type,
                    "confidence": similarity
                })

        # Sort by confidence score
        results.sort(key=lambda x: x["confidence"], reverse=True)

        return results
    except Exception as e:
        self.logger.error(f"Error in embedding-based section detection: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return []
```

**Keyword-Based Section Detection**:
```python
def _keyword_based_section_detection(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant sections using keyword matching.

    Args:
        query: The user query

    Returns:
        List of section type objects with match scores
    """
    # Section-specific keywords
    section_keywords = {
        "executive_summary": [
            "summary", "overview", "highlights", "key points", "brief",
            "main points", "synopsis", "abstract", "tldr", "executive"
        ],
        "financial_data": [
            "financial", "budget", "cost", "revenue", "profit", "expense",
            "forecast", "projection", "fiscal", "quarter", "annual", "roi",
            "investment", "funding", "price", "pricing", "money"
        ],
        "marketing_strategy": [
            "marketing", "campaign", "promotion", "brand", "advertising",
            "target audience", "market segment", "positioning", "messaging",
            "channel", "social media", "content", "engagement"
        ],
        "product_details": [
            "product", "feature", "specification", "capability", "technical",
            "functionality", "design", "version", "release", "update"
        ],
        "market_analysis": [
            "market", "industry", "competitor", "trend", "analysis",
            "landscape", "segment", "opportunity", "threat", "swot",
            "competition", "market share", "growth"
        ],
        "performance_metrics": [
            "performance", "metric", "kpi", "indicator", "measurement",
            "statistic", "analytics", "data", "dashboard", "report",
            "tracking", "monitor", "benchmark"
        ],
        "goals_objectives": [
            "goal", "objective", "target", "priority", "strategy",
            "mission", "vision", "roadmap", "plan", "initiative",
            "strategic", "direction", "milestone"
        ]
    }

    try:
        # Preprocess query
        query_lower = self._preprocess_text(query)

        # Track matches for each section
        section_matches = {}

        # Check each section's keywords
        for section_type, keywords in section_keywords.items():
            match_count = 0
            matched_keywords = []

            for keyword in keywords:
                if keyword.lower() in query_lower:
                    match_count += 1
                    matched_keywords.append(keyword)

            if match_count > 0:
                # Calculate confidence based on number of matches
                confidence = min(0.9, 0.4 + (match_count / len(keywords)) * 0.5)

                section_matches[section_type] = {
                    "type": section_type,
                    "confidence": confidence,
                    "matched_keywords": matched_keywords
                }

        # Convert to list and sort by confidence
        results = list(section_matches.values())
        results.sort(key=lambda x: x["confidence"], reverse=True)

        return results
    except Exception as e:
        self.logger.error(f"Error in keyword-based section detection: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return []
```

**Query Granularity Detection**:
```python
def _determine_query_granularity(self, query: str) -> str:
    """
    Determine if a query is broad or specific.

    Args:
        query: The user query

    Returns:
        "broad" or "specific"
    """
    # Preprocess query
    query_lower = query.lower()

    # Indicators of specific queries
    specific_indicators = [
        # Specific data requests
        "specific", "exactly", "precisely", "in detail", "breakdown",
        # Numeric requests
        "how much", "how many", "percentage", "number of", "amount",
        # Time-bound requests
        "in q1", "in q2", "in q3", "in q4", "in 2023", "in january",
        # Comparative requests
        "compared to", "versus", "difference between",
        # Detailed requests
        "step by step", "explain how", "technical details"
    ]

    # Indicators of broad queries
    broad_indicators = [
        # General information
        "overview", "general", "about", "tell me about", "what is",
        # High-level requests
        "high level", "big picture", "summary", "briefly",
        # Conceptual requests
        "concept", "idea", "approach", "strategy", "philosophy"
    ]

    # Count indicators
    specific_count = sum(1 for indicator in specific_indicators if indicator in query_lower)
    broad_count = sum(1 for indicator in broad_indicators if indicator in query_lower)

    # Query length is also an indicator (longer queries tend to be more specific)
    word_count = len(query.split())

    # Determine granularity based on indicators and length
    if specific_count > broad_count or word_count > 10:
        return "specific"
    else:
        return "broad"
```

### Query Type Classification

The Query Analyzer classifies queries into different types to guide the retrieval process:

1. **❓ Question Types**: Identifies the type of question being asked
   - Informational queries (what, how, explain)
   - Analytical queries (why, reason, cause)
   - Comparative queries (compare, difference, versus)
   - Request queries (can you, would you, please)

2. **🎯 Intent Recognition**: Determines the user's underlying intent
   - Information seeking
   - Action requesting
   - Problem solving
   - Decision making

#### Technical Implementation Details

**Query Type Detection Method**:
```python
def _detect_query_type(self, query: str) -> str:
    """
    Detect the type of query.

    Args:
        query: The user query

    Returns:
        Query type (informational, analytical, comparative, request, general)
    """
    # Preprocess query
    query_lower = query.lower()

    # Check for informational queries
    informational_patterns = [
        r'\bwhat\b', r'\bhow\b', r'\bexplain\b', r'\bdescribe\b',
        r'\btell me about\b', r'\bdefine\b', r'\bwho\b', r'\bwhen\b',
        r'\bwhere\b', r'\bwhich\b'
    ]

    # Check for analytical queries
    analytical_patterns = [
        r'\bwhy\b', r'\breason\b', r'\bcause\b', r'\beffect\b',
        r'\bimpact\b', r'\banalyze\b', r'\banalysis\b', r'\bexplain why\b'
    ]

    # Check for comparative queries
    comparative_patterns = [
        r'\bcompare\b', r'\bdifference\b', r'\bversus\b', r'\bvs\b',
        r'\bbetter\b', r'\bworse\b', r'\badvantage\b', r'\bdisadvantage\b',
        r'\bpros and cons\b', r'\bsimilarities\b', r'\bdifferences\b'
    ]

    # Check for request queries
    request_patterns = [
        r'\bcan you\b', r'\bcould you\b', r'\bwould you\b', r'\bwill you\b',
        r'\bplease\b', r'\bhelp me\b', r'\bshow me\b', r'\bfind\b',
        r'\bget\b', r'\bprovide\b'
    ]

    # Count pattern matches
    informational_count = sum(1 for pattern in informational_patterns if re.search(pattern, query_lower))
    analytical_count = sum(1 for pattern in analytical_patterns if re.search(pattern, query_lower))
    comparative_count = sum(1 for pattern in comparative_patterns if re.search(pattern, query_lower))
    request_count = sum(1 for pattern in request_patterns if re.search(pattern, query_lower))

    # Determine query type based on pattern matches
    if analytical_count > 0 and analytical_count >= informational_count:
        return "analytical"
    elif comparative_count > 0 and comparative_count >= informational_count:
        return "comparative"
    elif informational_count > 0:
        return "informational"
    elif request_count > 0:
        return "request"
    else:
        return "general"
```

**Intent Recognition Method**:
```python
def _recognize_intent(self, query: str, query_type: str) -> str:
    """
    Recognize the user's underlying intent.

    Args:
        query: The user query
        query_type: The detected query type

    Returns:
        Intent (information_seeking, action_requesting, problem_solving, decision_making)
    """
    # Preprocess query
    query_lower = query.lower()

    # Information seeking indicators
    information_seeking_indicators = [
        "what", "how", "explain", "describe", "tell me", "information",
        "details", "learn", "understand", "know", "find out"
    ]

    # Action requesting indicators
    action_requesting_indicators = [
        "do", "make", "create", "generate", "build", "implement",
        "execute", "perform", "run", "start", "stop", "change"
    ]

    # Problem solving indicators
    problem_solving_indicators = [
        "solve", "fix", "issue", "problem", "error", "bug", "trouble",
        "help", "resolve", "solution", "workaround", "debug"
    ]

    # Decision making indicators
    decision_making_indicators = [
        "should", "better", "recommend", "suggest", "decide", "choice",
        "option", "alternative", "pros and cons", "advantage", "disadvantage"
    ]

    # Count indicator matches
    info_count = sum(1 for indicator in information_seeking_indicators if indicator in query_lower)
    action_count = sum(1 for indicator in action_requesting_indicators if indicator in query_lower)
    problem_count = sum(1 for indicator in problem_solving_indicators if indicator in query_lower)
    decision_count = sum(1 for indicator in decision_making_indicators if indicator in query_lower)

    # Use query type as a hint
    if query_type == "informational":
        info_count += 1
    elif query_type == "request":
        action_count += 1
    elif query_type == "analytical":
        problem_count += 1
    elif query_type == "comparative":
        decision_count += 1

    # Determine intent based on indicator matches
    counts = {
        "information_seeking": info_count,
        "action_requesting": action_count,
        "problem_solving": problem_count,
        "decision_making": decision_count
    }

    # Return the intent with the highest count
    return max(counts.items(), key=lambda x: x[1])[0]
```

**Enhanced Query Analysis with Intent Recognition**:
```python
async def analyze(self, query: str) -> Dict[str, Any]:
    """
    Analyze a query with comprehensive classification.

    Args:
        query: The user query

    Returns:
        Dict containing analysis results
    """
    start_time = time.time()
    self.logger.info(f"Analyzing query: {query}")

    try:
        # Initialize result structure
        result = {
            "query": query,
            "query_type": "general",
            "intent": "information_seeking",
            "relevant_departments": [],
            "target_sections": [],
            "analysis_time_ms": 0,
            "success": True
        }

        # Detect query type
        try:
            result["query_type"] = self._detect_query_type(query)
        except Exception as e:
            self.logger.error(f"Query type detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Recognize intent
        try:
            result["intent"] = self._recognize_intent(query, result["query_type"])
        except Exception as e:
            self.logger.error(f"Intent recognition failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Detect departments
        try:
            result["relevant_departments"] = await self.detect_departments(query)
        except Exception as e:
            self.logger.error(f"Department detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Detect sections
        try:
            result["target_sections"] = await self.detect_sections(query)
        except Exception as e:
            self.logger.error(f"Section detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Adjust retrieval strategy based on query type and intent
        result["retrieval_strategy"] = self._determine_retrieval_strategy(
            result["query_type"],
            result["intent"],
            result["target_sections"]
        )

        # Calculate analysis time
        end_time = time.time()
        result["analysis_time_ms"] = int((end_time - start_time) * 1000)

        self.logger.info(f"Query analysis completed in {result['analysis_time_ms']}ms")
        return result

    except Exception as e:
        # Catch-all for unexpected errors
        self.logger.error(f"Unexpected error in query analysis: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Return minimal result on complete failure
        end_time = time.time()
        return {
            "query": query,
            "query_type": "general",
            "intent": "information_seeking",
            "relevant_departments": [],
            "target_sections": [],
            "analysis_time_ms": int((end_time - start_time) * 1000),
            "success": False,
            "error": str(e)
        }
```

**Retrieval Strategy Determination**:
```python
def _determine_retrieval_strategy(
    self,
    query_type: str,
    intent: str,
    target_sections: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Determine the optimal retrieval strategy based on query analysis.

    Args:
        query_type: The detected query type
        intent: The recognized intent
        target_sections: The detected target sections

    Returns:
        Dict containing retrieval strategy parameters
    """
    # Default strategy
    strategy = {
        "search_type": "hybrid",  # hybrid, vector, or keyword
        "vector_weight": 0.7,     # Weight for vector search in hybrid search
        "keyword_weight": 0.3,    # Weight for keyword search in hybrid search
        "result_count": 5,        # Number of results to return
        "min_similarity": 0.65,   # Minimum similarity threshold
        "include_metadata": True  # Whether to include metadata in results
    }

    # Adjust strategy based on query type
    if query_type == "informational":
        # Informational queries benefit from broader context
        strategy["result_count"] = 7
        strategy["min_similarity"] = 0.6
    elif query_type == "analytical":
        # Analytical queries need more precise results
        strategy["vector_weight"] = 0.8
        strategy["keyword_weight"] = 0.2
        strategy["min_similarity"] = 0.7
    elif query_type == "comparative":
        # Comparative queries need diverse results
        strategy["result_count"] = 8
        strategy["vector_weight"] = 0.6
        strategy["keyword_weight"] = 0.4
    elif query_type == "request":
        # Request queries often need specific information
        strategy["min_similarity"] = 0.75
        strategy["result_count"] = 4

    # Adjust strategy based on intent
    if intent == "information_seeking":
        # Information seeking benefits from more results
        strategy["result_count"] = max(strategy["result_count"], 6)
    elif intent == "problem_solving":
        # Problem solving needs precise results
        strategy["vector_weight"] = 0.8
        strategy["keyword_weight"] = 0.2
        strategy["min_similarity"] = 0.7
    elif intent == "decision_making":
        # Decision making needs diverse results
        strategy["result_count"] = max(strategy["result_count"], 7)
        strategy["vector_weight"] = 0.6
        strategy["keyword_weight"] = 0.4

    # Adjust strategy based on target sections
    if target_sections:
        # If specific sections are targeted, focus search on those sections
        strategy["section_filter"] = [section["type"] for section in target_sections[:2]]

        # If high confidence in a specific section, increase precision
        if target_sections[0]["confidence"] > 0.8:
            strategy["min_similarity"] = 0.7
            strategy["result_count"] = min(strategy["result_count"], 5)

    return strategy
```

## 📝 API Design

### Class Structure

```python
class QueryAnalyzer:
    """
    Analyzer for user queries with embedding-based similarity and section detection.

    This class analyzes user queries to determine their intent, relevant departments,
    and target sections. It uses a combination of embedding-based similarity,
    keyword matching, and LLM-based analysis.
    """

    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
    ):
        """
        Initialize the query analyzer.

        Args:
            llm_adapter: The LLM adapter to use for analysis
            embedding_model: The embedding model for vector similarity
            department_descriptions: Descriptions of departments for embedding-based detection
            section_descriptions: Descriptions of section types for embedding-based detection
        """
        pass

    async def analyze(
        self,
        query: str,
    ) -> Dict[str, Any]:
        """
        Analyze a query to determine its intent, relevant departments, and target sections.

        This method performs a comprehensive analysis of the query using embedding-based
        similarity, keyword matching, and LLM-based analysis. It returns a structured
        result with the analysis findings.

        Args:
            query: The user query

        Returns:
            Dict containing analysis results:
            - query: The original query
            - query_type: The type of query (informational, request, analytical, etc.)
            - relevant_departments: List of relevant departments with confidence scores
            - target_sections: List of relevant section types with confidence scores
        """
        pass

    async def detect_departments(
        self,
        query: str
    ) -> List[Dict[str, Any]]:
        """
        Detect relevant departments for the query.

        This method uses a combination of embedding-based similarity,
        keyword matching, and LLM-based analysis to determine which
        departments are most relevant to the query.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores:
            [
                {"id": "finance", "confidence": 0.92},
                {"id": "marketing", "confidence": 0.45}
            ]
        """
        pass

    async def detect_sections(
        self,
        query: str
    ) -> List[Dict[str, Any]]:
        """
        Detect relevant section types for the query.

        This method analyzes the query to determine which types of document
        sections are most likely to contain relevant information.

        Args:
            query: The user query

        Returns:
            List of section type objects with confidence scores:
            [
                {"type": "executive_summary", "confidence": 0.78},
                {"type": "financial_data", "confidence": 0.85}
            ]
        """
        pass
```

### Usage Examples

```python
# Initialize the analyzer
query_analyzer = QueryAnalyzer(
    llm_adapter=llm_adapter,
    embedding_model=embedding_model,
    department_descriptions={
        "finance": "The finance department handles budgeting, financial reporting, and financial analysis.",
        "marketing": "The marketing department handles campaigns, brand management, and customer acquisition."
    },
    section_descriptions={
        "executive_summary": "High-level overview of key points and findings.",
        "financial_data": "Detailed financial information including budgets and forecasts."
    }
)

# Analyze a query
analysis = await query_analyzer.analyze(
    query="What was our marketing budget for Q2 2023?"
)

# Use the analysis results
print(f"Query type: {analysis['query_type']}")
# Output: "Query type: informational"

print(f"Relevant departments: {analysis['relevant_departments']}")
# Output: [{"id": "marketing", "confidence": 0.92}, {"id": "finance", "confidence": 0.85}]

print(f"Target sections: {analysis['target_sections']}")
# Output: [{"type": "financial_data", "confidence": 0.88}]
```

## ⚙️ Implementation Details

### Embedding-Based Similarity

The embedding-based similarity approach uses vector representations to capture semantic meaning:

1. **📊 Reference Embeddings**: Create embeddings for department and section descriptions
   - Generate embeddings for each department's description and responsibilities
   - Create embeddings for different section types and their characteristics
   - Store these reference embeddings for comparison

2. **🔄 Query Embedding**: Convert the user query to a vector representation
   - Use the same embedding model for consistency
   - Apply any necessary preprocessing
   - Normalize the embedding if required by the similarity metric

3. **📏 Similarity Calculation**: Compare query embedding with reference embeddings
   - Use cosine similarity as the primary metric
   - Calculate similarity scores for each department and section type
   - Apply threshold filtering to remove low-confidence matches

> **⚠️ PoC Implementation Note**: This is a must-have component for the proof-of-concept implementation. The embedding-based similarity approach is essential for accurate department detection and should be implemented as a priority.

#### Technical Implementation Details

**Embedding Model Selection**:
- Use the same embedding model as the knowledge base for consistency
- Recommended model: `intfloat/e5-base-v2` (768 dimensions)
- Fallback model: OpenAI's `text-embedding-3-small` (1536 dimensions)

**Embedding Preprocessing**:
```python
def _preprocess_text(self, text: str) -> str:
    """
    Preprocess text for embedding generation.

    Args:
        text: The text to preprocess

    Returns:
        Preprocessed text
    """
    # Convert to lowercase
    text = text.lower()

    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    # Remove special characters but keep spaces and basic punctuation
    text = re.sub(r'[^\w\s.,?!-]', '', text)

    return text
```

**Embedding Caching**:
```python
# In __init__ method
self.embedding_cache = {}

async def _get_embedding(self, text: str) -> List[float]:
    """
    Get embedding for text with caching.

    Args:
        text: The text to embed

    Returns:
        Embedding vector
    """
    # Create cache key
    cache_key = hashlib.md5(text.encode()).hexdigest()

    # Check cache
    if cache_key in self.embedding_cache:
        return self.embedding_cache[cache_key]

    # Generate embedding
    embedding = await self.embedding_model.embed_query(text)

    # Cache result
    self.embedding_cache[cache_key] = embedding

    return embedding
```

**Similarity Calculation**:
```python
def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate cosine similarity between two vectors.

    Args:
        vec1: First vector
        vec2: Second vector

    Returns:
        Cosine similarity score (0-1)
    """
    # Convert to numpy arrays
    vec1 = np.array(vec1)
    vec2 = np.array(vec2)

    # Calculate dot product
    dot_product = np.dot(vec1, vec2)

    # Calculate magnitudes
    magnitude1 = np.linalg.norm(vec1)
    magnitude2 = np.linalg.norm(vec2)

    # Calculate cosine similarity
    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0

    similarity = dot_product / (magnitude1 * magnitude2)

    # Ensure result is in range [0, 1]
    return max(0.0, min(1.0, similarity))
```

**Complete Embedding-Based Department Detection**:
```python
async def _embedding_based_department_detection(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant departments using embedding similarity.

    Args:
        query: The user query

    Returns:
        List of department objects with similarity scores
    """
    try:
        # Preprocess query
        processed_query = self._preprocess_text(query)

        # Generate query embedding
        query_embedding = await self._get_embedding(processed_query)

        # Calculate similarity with each department embedding
        results = []
        for dept_id, dept_data in self.department_embeddings.items():
            # Calculate cosine similarity
            similarity = self._calculate_cosine_similarity(
                query_embedding,
                dept_data["embedding"]
            )

            # Add to results if above threshold
            if similarity > self.similarity_threshold:
                results.append({
                    "id": dept_id,
                    "confidence": similarity,
                    "method": "embedding"
                })

        # Sort by confidence score
        results.sort(key=lambda x: x["confidence"], reverse=True)

        # Log results
        self.logger.debug(f"Embedding-based department detection results: {results}")

        return results
    except Exception as e:
        self.logger.error(f"Error in embedding-based department detection: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return []
```

**Initialization of Department Embeddings**:
```python
async def _initialize_department_embeddings(self) -> None:
    """
    Initialize embeddings for department descriptions.
    """
    self.department_embeddings = {}

    for dept_id, description in self.department_descriptions.items():
        try:
            # Preprocess description
            processed_description = self._preprocess_text(description)

            # Generate embedding
            embedding = await self._get_embedding(processed_description)

            # Store embedding with metadata
            self.department_embeddings[dept_id] = {
                "embedding": embedding,
                "description": description,
                "embedding_model": self.embedding_model.__class__.__name__,
                "embedding_version": getattr(self.embedding_model, "VERSION", "1.0.0"),
                "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
            }

            self.logger.debug(f"Generated embedding for department: {dept_id}")
        except Exception as e:
            self.logger.error(f"Error generating embedding for department {dept_id}: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
```

### Hybrid Detection Approach

The hybrid detection approach combines multiple methods for robust results:

1. **🔄 Method Combination**: Use multiple detection methods in parallel
   - Run embedding-based, keyword-based, and LLM-based detection
   - Weight the results based on method reliability
   - Combine scores for each department

2. **⚖️ Confidence Calculation**: Determine confidence scores for each department
   - Normalize scores from different methods
   - Apply method-specific weights
   - Calculate weighted average for final confidence

3. **🔍 Threshold Filtering**: Filter results based on confidence
   - Apply minimum confidence threshold
   - Ensure at least one department is returned
   - Handle edge cases with fallback mechanisms

> **⚠️ PoC Implementation Note**: This is a must-have component for the proof-of-concept implementation. The hybrid detection approach ensures robust department detection by combining multiple methods, providing better accuracy than any single method alone.

#### Technical Implementation Details

**Method Weights and Thresholds**:
```python
# In __init__ method
# Method weights (higher = more reliable)
self.embedding_weight = 0.6  # Embedding-based detection weight
self.keyword_weight = 0.3    # Keyword-based detection weight
self.llm_weight = 0.4        # LLM-based detection weight

# Confidence thresholds
self.similarity_threshold = 0.65  # Minimum similarity for embedding-based detection
self.confidence_threshold = 0.5   # Minimum confidence for final results
```

**Keyword-Based Department Detection**:
```python
def _keyword_based_department_detection(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant departments using keyword matching.

    Args:
        query: The user query

    Returns:
        List of department objects with match scores
    """
    try:
        # Preprocess query
        query_lower = self._preprocess_text(query)

        # Track matches for each department
        dept_matches = {}

        # Check each department's keywords
        for dept_id, keywords in self.department_keywords.items():
            match_count = 0
            matched_keywords = []

            for keyword in keywords:
                if keyword.lower() in query_lower:
                    match_count += 1
                    matched_keywords.append(keyword)

            if match_count > 0:
                # Calculate confidence based on number of matches
                # More matches = higher confidence
                confidence = min(0.95, 0.5 + (match_count / len(keywords)) * 0.5)

                dept_matches[dept_id] = {
                    "id": dept_id,
                    "confidence": confidence,
                    "method": "keyword",
                    "matched_keywords": matched_keywords
                }

        # Convert to list and sort by confidence
        results = list(dept_matches.values())
        results.sort(key=lambda x: x["confidence"], reverse=True)

        # Log results
        self.logger.debug(f"Keyword-based department detection results: {results}")

        return results
    except Exception as e:
        self.logger.error(f"Error in keyword-based department detection: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return []
```

**LLM-Based Department Detection**:
```python
async def _llm_based_department_detection(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant departments using LLM analysis.

    Args:
        query: The user query

    Returns:
        List of department objects with confidence scores
    """
    try:
        # Create prompt for LLM
        prompt = [
            {
                "role": "system",
                "content": (
                    "You are a query analyzer that determines which departments a query is relevant to. "
                    "The available departments are: " + ", ".join(self.department_descriptions.keys()) + ". "
                    "For each department, provide a relevance score between 0 and 1, where 1 means highly relevant "
                    "and 0 means not relevant at all. Respond in JSON format with department IDs as keys and "
                    "confidence scores as values."
                )
            },
            {
                "role": "user",
                "content": f"Query: {query}\n\nAnalyze which departments this query is relevant to:"
            }
        ]

        # Get response from LLM
        response = await self.llm_adapter.chat(prompt)

        # Parse JSON response
        try:
            # Extract JSON from response (handle cases where LLM adds extra text)
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                dept_scores = json.loads(json_str)
            else:
                # Fallback parsing for non-JSON responses
                dept_scores = {}
                for dept_id in self.department_descriptions.keys():
                    match = re.search(rf'{dept_id}[^\d]*(\d+(\.\d+)?)', response, re.IGNORECASE)
                    if match:
                        try:
                            score = float(match.group(1))
                            # Normalize score to 0-1 range if needed
                            if score > 1:
                                score = score / 10 if score <= 10 else 1.0
                            dept_scores[dept_id] = score
                        except ValueError:
                            continue

            # Convert to list format
            results = []
            for dept_id, confidence in dept_scores.items():
                if dept_id in self.department_descriptions and confidence > 0.2:  # Minimum threshold
                    results.append({
                        "id": dept_id,
                        "confidence": confidence,
                        "method": "llm"
                    })

            # Sort by confidence
            results.sort(key=lambda x: x["confidence"], reverse=True)

            # Log results
            self.logger.debug(f"LLM-based department detection results: {results}")

            return results
        except json.JSONDecodeError:
            self.logger.warning(f"Failed to parse LLM response as JSON: {response}")
            return []

    except Exception as e:
        self.logger.error(f"Error in LLM-based department detection: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return []
```

**Complete Hybrid Department Detection**:
```python
async def detect_departments(self, query: str) -> List[Dict[str, Any]]:
    """
    Detect relevant departments using a hybrid approach.

    Args:
        query: The user query

    Returns:
        List of department objects with confidence scores
    """
    self.logger.info(f"Detecting departments for query: {query}")

    # Track method successes for fallback logic
    embedding_success = False
    keyword_success = False
    llm_success = False

    # Get results from each method with fallbacks
    try:
        # Try embedding-based detection first
        embedding_results = await self._embedding_based_department_detection(query)
        embedding_success = len(embedding_results) > 0
    except Exception as e:
        self.logger.error(f"Embedding-based detection failed: {e}")
        embedding_results = []

    # Always try keyword-based detection as it's fast and reliable
    keyword_results = self._keyword_based_department_detection(query)
    keyword_success = len(keyword_results) > 0

    # Only use LLM if other methods didn't produce confident results
    llm_results = []
    if not embedding_success and not keyword_success:
        try:
            llm_results = await self._llm_based_department_detection(query)
            llm_success = len(llm_results) > 0
        except Exception as e:
            self.logger.error(f"LLM-based detection failed: {e}")
            llm_results = []

    # Combine results
    combined_results = {}

    # Process embedding results
    for result in embedding_results:
        dept_id = result["id"]
        combined_results[dept_id] = combined_results.get(dept_id, {})
        combined_results[dept_id]["embedding_score"] = result["confidence"]

    # Process keyword results
    for result in keyword_results:
        dept_id = result["id"]
        combined_results[dept_id] = combined_results.get(dept_id, {})
        combined_results[dept_id]["keyword_score"] = result["confidence"]
        if "matched_keywords" in result:
            combined_results[dept_id]["matched_keywords"] = result["matched_keywords"]

    # Process LLM results
    for result in llm_results:
        dept_id = result["id"]
        combined_results[dept_id] = combined_results.get(dept_id, {})
        combined_results[dept_id]["llm_score"] = result["confidence"]

    # Calculate final confidence scores
    final_results = []
    for dept_id, scores in combined_results.items():
        # Get scores with defaults
        embedding_score = scores.get("embedding_score", 0.0)
        keyword_score = scores.get("keyword_score", 0.0)
        llm_score = scores.get("llm_score", 0.0)

        # Calculate weighted average
        total_weight = 0
        weighted_sum = 0

        if embedding_score > 0:
            weighted_sum += embedding_score * self.embedding_weight
            total_weight += self.embedding_weight

        if keyword_score > 0:
            weighted_sum += keyword_score * self.keyword_weight
            total_weight += self.keyword_weight

        if llm_score > 0:
            weighted_sum += llm_score * self.llm_weight
            total_weight += self.llm_weight

        # Calculate confidence (avoid division by zero)
        confidence = weighted_sum / total_weight if total_weight > 0 else 0.0

        # Add to results if above threshold
        if confidence > self.confidence_threshold:
            result = {
                "id": dept_id,
                "confidence": confidence,
                "methods": []
            }

            # Add method details
            if embedding_score > 0:
                result["methods"].append("embedding")
            if keyword_score > 0:
                result["methods"].append("keyword")
                if "matched_keywords" in scores:
                    result["matched_keywords"] = scores["matched_keywords"]
            if llm_score > 0:
                result["methods"].append("llm")

            final_results.append(result)

    # Sort by confidence
    final_results.sort(key=lambda x: x["confidence"], reverse=True)

    # Ensure at least one department if results are empty but we have some data
    if not final_results and combined_results:
        # Get department with highest combined score
        best_dept = max(combined_results.items(),
                        key=lambda x: sum([
                            x[1].get("embedding_score", 0) * self.embedding_weight,
                            x[1].get("keyword_score", 0) * self.keyword_weight,
                            x[1].get("llm_score", 0) * self.llm_weight
                        ]))

        # Calculate confidence
        scores = best_dept[1]
        methods = []
        if "embedding_score" in scores:
            methods.append("embedding")
        if "keyword_score" in scores:
            methods.append("keyword")
        if "llm_score" in scores:
            methods.append("llm")

        final_results.append({
            "id": best_dept[0],
            "confidence": 0.4,  # Lower confidence for fallback
            "methods": methods,
            "fallback": True
        })

    self.logger.info(f"Department detection results: {final_results}")
    return final_results
```

### Error Handling and Fallbacks

The Query Analyzer implements robust error handling and fallback mechanisms:

1. **🛡️ Graceful Degradation**: Fall back to simpler methods when more advanced ones fail
   - If embedding-based detection fails, use keyword-based detection
   - If both embedding and keyword detection fail, use LLM-based detection
   - If all methods fail, return a default result

2. **🔄 Retry Logic**: Implement retry logic for external service calls
   - Retry embedding model calls with exponential backoff
   - Retry LLM calls with appropriate error handling
   - Log failures for monitoring and debugging

3. **📝 Detailed Logging**: Provide comprehensive logging for troubleshooting
   - Log method performance and results
   - Include context for failures
   - Track confidence scores for evaluation

> **⚠️ PoC Implementation Note**: This is a must-have component for the proof-of-concept implementation. Robust error handling and fallbacks ensure the system remains operational even when individual components fail, which is essential for a reliable production system.

### Pluggable Detection Strategies

The Query Analyzer uses a strategy pattern to make detection methods modular and extensible:

1. **🧩 Strategy Interfaces**: Define abstract interfaces for detection strategies
   - Separate interfaces for department and section detection
   - Common method signatures for all strategy implementations
   - Clear separation of concerns

2. **🔌 Concrete Implementations**: Implement specific detection strategies
   - Embedding-based detection strategy
   - Keyword-based detection strategy
   - LLM-based detection strategy

3. **🔄 Strategy Registration**: Allow dynamic registration of strategies
   - Register strategies at initialization
   - Enable/disable strategies as needed
   - Configure strategy priority

#### Technical Implementation Details

**Strategy Interfaces**:
```python
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class DepartmentDetectionStrategy(ABC):
    """
    Abstract base class for department detection strategies.

    This defines the interface that all department detection strategies must implement.
    """

    @abstractmethod
    async def detect(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments for a query.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """
        Get the name of the strategy.

        Returns:
            Strategy name
        """
        pass

    @property
    @abstractmethod
    def priority(self) -> int:
        """
        Get the priority of the strategy (higher = tried first).

        Returns:
            Priority value
        """
        pass

class SectionDetectionStrategy(ABC):
    """
    Abstract base class for section detection strategies.

    This defines the interface that all section detection strategies must implement.
    """

    @abstractmethod
    async def detect(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant sections for a query.

        Args:
            query: The user query

        Returns:
            List of section objects with confidence scores
        """
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """
        Get the name of the strategy.

        Returns:
            Strategy name
        """
        pass

    @property
    @abstractmethod
    def priority(self) -> int:
        """
        Get the priority of the strategy (higher = tried first).

        Returns:
            Priority value
        """
        pass
```

**Concrete Strategy Implementations**:
```python
class EmbeddingDepartmentDetector(DepartmentDetectionStrategy):
    """
    Department detection strategy using embedding similarity.
    """

    def __init__(self, embedding_model, department_embeddings, similarity_threshold=0.65):
        self.embedding_model = embedding_model
        self.department_embeddings = department_embeddings
        self.similarity_threshold = similarity_threshold
        self._name = "embedding"
        self._priority = 100  # Highest priority

    @property
    def name(self) -> str:
        return self._name

    @property
    def priority(self) -> int:
        return self._priority

    async def detect(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using embedding similarity.

        Args:
            query: The user query

        Returns:
            List of department objects with similarity scores
        """
        # Implementation as shown in the embedding-based detection section
        pass

class KeywordDepartmentDetector(DepartmentDetectionStrategy):
    """
    Department detection strategy using keyword matching.
    """

    def __init__(self, department_keywords):
        self.department_keywords = department_keywords
        self._name = "keyword"
        self._priority = 50  # Medium priority

    @property
    def name(self) -> str:
        return self._name

    @property
    def priority(self) -> int:
        return self._priority

    async def detect(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using keyword matching.

        Args:
            query: The user query

        Returns:
            List of department objects with match scores
        """
        # Implementation as shown in the keyword-based detection section
        pass

class LLMDepartmentDetector(DepartmentDetectionStrategy):
    """
    Department detection strategy using LLM analysis.
    """

    def __init__(self, llm_adapter, department_descriptions):
        self.llm_adapter = llm_adapter
        self.department_descriptions = department_descriptions
        self._name = "llm"
        self._priority = 10  # Lowest priority (used as fallback)

    @property
    def name(self) -> str:
        return self._name

    @property
    def priority(self) -> int:
        return self._priority

    async def detect(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using LLM analysis.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        # Implementation as shown in the LLM-based detection section
        pass
```

**Using Strategies in QueryAnalyzer**:
```python
class QueryAnalyzer:
    """
    Analyzer for user queries with pluggable detection strategies.
    """

    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
    ):
        """
        Initialize the query analyzer with detection strategies.
        """
        # Initialize base components
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.department_descriptions = department_descriptions or {}
        self.section_descriptions = section_descriptions or {}

        # Set up logging
        self._setup_logging()

        # Initialize embeddings
        self.department_embeddings = {}
        self.section_embeddings = {}

        # Initialize detection strategies
        self.department_detectors = []
        self.section_detectors = []

        # Register default strategies
        self._register_default_strategies()

    def _register_default_strategies(self):
        """
        Register the default detection strategies.
        """
        # Register department detection strategies
        if self.embedding_model:
            self.register_department_strategy(
                EmbeddingDepartmentDetector(
                    self.embedding_model,
                    self.department_embeddings,
                    similarity_threshold=0.65
                )
            )

        self.register_department_strategy(
            KeywordDepartmentDetector(self.department_keywords)
        )

        self.register_department_strategy(
            LLMDepartmentDetector(
                self.llm_adapter,
                self.department_descriptions
            )
        )

        # Register section detection strategies
        # Similar to department strategies...

    def register_department_strategy(self, strategy: DepartmentDetectionStrategy):
        """
        Register a department detection strategy.

        Args:
            strategy: The strategy to register
        """
        self.department_detectors.append(strategy)
        # Sort by priority (highest first)
        self.department_detectors.sort(key=lambda s: s.priority, reverse=True)
        self.logger.info(f"Registered department detection strategy: {strategy.name}")

    def register_section_strategy(self, strategy: SectionDetectionStrategy):
        """
        Register a section detection strategy.

        Args:
            strategy: The strategy to register
        """
        self.section_detectors.append(strategy)
        # Sort by priority (highest first)
        self.section_detectors.sort(key=lambda s: s.priority, reverse=True)
        self.logger.info(f"Registered section detection strategy: {strategy.name}")

    async def detect_departments(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using registered strategies.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        self.logger.info(f"Detecting departments for query: {query}")

        all_results = {}

        # Try each strategy in priority order
        for strategy in self.department_detectors:
            try:
                results = await strategy.detect(query)
                if results:
                    self.logger.info(f"Strategy {strategy.name} found {len(results)} departments")

                    # Process results
                    for result in results:
                        dept_id = result["id"]
                        all_results[dept_id] = all_results.get(dept_id, {})
                        all_results[dept_id][f"{strategy.name}_score"] = result["confidence"]

            except Exception as e:
                self.logger.error(f"Strategy {strategy.name} failed: {e}")

        # Combine and process results as in the hybrid approach
        # ...

        return final_results
```

**Benefits of the Strategy Pattern**:

1. **🧩 Modularity**: Each detection strategy is self-contained and can be developed, tested, and maintained independently.

2. **🔌 Extensibility**: New detection strategies can be added without modifying existing code.

3. **🔄 Configurability**: Strategies can be enabled, disabled, or prioritized based on runtime conditions.

4. **🧪 Testability**: Each strategy can be tested in isolation, making unit testing more straightforward.

### Confidence Calibration

The Query Analyzer implements a confidence calibration mechanism to improve the reliability of confidence scores:

1. **🔢 Calibration Configuration**: Configure weights and thresholds based on empirical data
   - Adjust weights for different detection methods
   - Fine-tune confidence thresholds for different departments and sections
   - Apply domain-specific calibration

2. **📊 Validation-Based Calibration**: Use validation data to calibrate confidence scores
   - Collect ground truth data for department and section relevance
   - Optimize weights to maximize accuracy on validation set
   - Periodically recalibrate based on new data

3. **🔄 Adaptive Calibration**: Adjust calibration parameters dynamically
   - Track performance metrics over time
   - Adjust weights based on observed accuracy
   - Implement feedback-driven calibration

#### Technical Implementation Details

**Calibration Configuration**:
```python
class CalibrationConfig:
    """
    Configuration for confidence calibration.

    This class holds the weights and thresholds used for calibrating
    confidence scores in the Query Analyzer.
    """

    def __init__(
        self,
        method_weights: Dict[str, float] = None,
        confidence_thresholds: Dict[str, float] = None,
        department_weights: Dict[str, float] = None,
        section_weights: Dict[str, float] = None
    ):
        """
        Initialize calibration configuration.

        Args:
            method_weights: Weights for different detection methods
            confidence_thresholds: Confidence thresholds for different entities
            department_weights: Weights for different departments
            section_weights: Weights for different sections
        """
        # Default method weights
        self.method_weights = method_weights or {
            "embedding": 0.6,
            "keyword": 0.3,
            "llm": 0.4
        }

        # Default confidence thresholds
        self.confidence_thresholds = confidence_thresholds or {
            "department": 0.5,
            "section": 0.5,
            "embedding_similarity": 0.65
        }

        # Department-specific weights (optional)
        self.department_weights = department_weights or {}

        # Section-specific weights (optional)
        self.section_weights = section_weights or {}

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CalibrationConfig':
        """
        Create a calibration configuration from a dictionary.

        Args:
            config_dict: Dictionary containing configuration parameters

        Returns:
            CalibrationConfig instance
        """
        return cls(
            method_weights=config_dict.get("method_weights"),
            confidence_thresholds=config_dict.get("confidence_thresholds"),
            department_weights=config_dict.get("department_weights"),
            section_weights=config_dict.get("section_weights")
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the calibration configuration to a dictionary.

        Returns:
            Dictionary representation of the configuration
        """
        return {
            "method_weights": self.method_weights,
            "confidence_thresholds": self.confidence_thresholds,
            "department_weights": self.department_weights,
            "section_weights": self.section_weights
        }
```

**Using Calibration in QueryAnalyzer**:
```python
class QueryAnalyzer:
    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
        calibration_config: CalibrationConfig = None
    ):
        """
        Initialize the query analyzer with calibration.

        Args:
            llm_adapter: The LLM adapter to use for analysis
            embedding_model: The embedding model for vector similarity
            department_descriptions: Descriptions of departments for embedding-based detection
            section_descriptions: Descriptions of section types for embedding-based detection
            calibration_config: Configuration for confidence calibration
        """
        # Initialize base components
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.department_descriptions = department_descriptions or {}
        self.section_descriptions = section_descriptions or {}

        # Initialize calibration
        self.calibration = calibration_config or CalibrationConfig()

        # Use calibration parameters
        self.embedding_weight = self.calibration.method_weights.get("embedding", 0.6)
        self.keyword_weight = self.calibration.method_weights.get("keyword", 0.3)
        self.llm_weight = self.calibration.method_weights.get("llm", 0.4)

        self.similarity_threshold = self.calibration.confidence_thresholds.get("embedding_similarity", 0.65)
        self.confidence_threshold = self.calibration.confidence_thresholds.get("department", 0.5)

        # Set up logging
        self._setup_logging()

        # Initialize embeddings
        self.department_embeddings = {}
        self.section_embeddings = {}

        # Initialize detection strategies
        self.department_detectors = []
        self.section_detectors = []

        # Register default strategies
        self._register_default_strategies()
```

**Validation-Based Calibration**:
```python
async def calibrate(self, validation_data: List[Dict[str, Any]]) -> CalibrationConfig:
    """
    Calibrate confidence weights using validation data.

    This method optimizes the weights for different detection methods
    to maximize accuracy on the validation set.

    Args:
        validation_data: List of (query, expected_departments, expected_sections) tuples

    Returns:
        Updated calibration configuration
    """
    self.logger.info(f"Calibrating with {len(validation_data)} validation samples")

    # Track best configuration and accuracy
    best_config = self.calibration
    best_accuracy = await self._evaluate_accuracy(validation_data, best_config)

    self.logger.info(f"Initial accuracy: {best_accuracy:.4f}")

    # Define parameter grid for grid search
    param_grid = {
        "embedding": [0.4, 0.5, 0.6, 0.7, 0.8],
        "keyword": [0.2, 0.3, 0.4, 0.5],
        "llm": [0.3, 0.4, 0.5, 0.6],
        "threshold": [0.4, 0.5, 0.6, 0.7]
    }

    # Perform grid search
    for embedding_weight in param_grid["embedding"]:
        for keyword_weight in param_grid["keyword"]:
            for llm_weight in param_grid["llm"]:
                for threshold in param_grid["threshold"]:
                    # Create test configuration
                    test_config = CalibrationConfig(
                        method_weights={
                            "embedding": embedding_weight,
                            "keyword": keyword_weight,
                            "llm": llm_weight
                        },
                        confidence_thresholds={
                            "department": threshold,
                            "section": threshold,
                            "embedding_similarity": threshold + 0.15  # Higher for embedding
                        }
                    )

                    # Evaluate accuracy with this configuration
                    accuracy = await self._evaluate_accuracy(validation_data, test_config)

                    # Update best configuration if accuracy improved
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_config = test_config
                        self.logger.info(f"New best accuracy: {best_accuracy:.4f} with config: {test_config.to_dict()}")

    # Apply best configuration
    self.calibration = best_config
    self.embedding_weight = best_config.method_weights.get("embedding", 0.6)
    self.keyword_weight = best_config.method_weights.get("keyword", 0.3)
    self.llm_weight = best_config.method_weights.get("llm", 0.4)
    self.confidence_threshold = best_config.confidence_thresholds.get("department", 0.5)
    self.similarity_threshold = best_config.confidence_thresholds.get("embedding_similarity", 0.65)

    self.logger.info(f"Calibration complete. Final accuracy: {best_accuracy:.4f}")

    return best_config

async def _evaluate_accuracy(
    self,
    validation_data: List[Dict[str, Any]],
    config: CalibrationConfig
) -> float:
    """
    Evaluate accuracy using a specific calibration configuration.

    Args:
        validation_data: List of validation samples
        config: Calibration configuration to evaluate

    Returns:
        Accuracy score (0-1)
    """
    # Save current configuration
    original_config = CalibrationConfig(
        method_weights={
            "embedding": self.embedding_weight,
            "keyword": self.keyword_weight,
            "llm": self.llm_weight
        },
        confidence_thresholds={
            "department": self.confidence_threshold,
            "embedding_similarity": self.similarity_threshold
        }
    )

    # Apply test configuration
    self.embedding_weight = config.method_weights.get("embedding", 0.6)
    self.keyword_weight = config.method_weights.get("keyword", 0.3)
    self.llm_weight = config.method_weights.get("llm", 0.4)
    self.confidence_threshold = config.confidence_thresholds.get("department", 0.5)
    self.similarity_threshold = config.confidence_thresholds.get("embedding_similarity", 0.65)

    # Evaluate on validation data
    correct = 0
    total = len(validation_data)

    for sample in validation_data:
        query = sample["query"]
        expected_departments = set(sample["expected_departments"])

        # Analyze query
        results = await self.detect_departments(query)
        detected_departments = set(result["id"] for result in results)

        # Check if detection is correct
        if expected_departments == detected_departments:
            correct += 1

    # Restore original configuration
    self.embedding_weight = original_config.method_weights.get("embedding", 0.6)
    self.keyword_weight = original_config.method_weights.get("keyword", 0.3)
    self.llm_weight = original_config.method_weights.get("llm", 0.4)
    self.confidence_threshold = original_config.confidence_thresholds.get("department", 0.5)
    self.similarity_threshold = original_config.confidence_thresholds.get("embedding_similarity", 0.65)

    # Calculate accuracy
    accuracy = correct / total if total > 0 else 0.0

    return accuracy
```

**Benefits of Confidence Calibration**:

1. **🎯 Improved Accuracy**: Calibrated confidence scores lead to more accurate department and section detection.

2. **🔄 Adaptability**: The system can adapt to different domains and query patterns through calibration.

3. **📊 Measurable Performance**: Calibration provides a quantitative way to measure and improve performance.

4. **🧠 Domain-Specific Tuning**: Weights can be adjusted for specific domains or use cases.

### Feedback Loop

The Query Analyzer implements a feedback loop for continuous learning and improvement:

1. **📝 Feedback Collection**: Collect feedback on analysis results
   - Record user feedback on department and section relevance
   - Track system-generated feedback based on user interactions
   - Store feedback for future analysis

2. **🔄 Feedback Integration**: Use feedback to improve future analyses
   - Adjust confidence weights based on feedback
   - Update detection strategies based on performance
   - Fine-tune calibration parameters

3. **📊 Performance Tracking**: Monitor performance over time
   - Track accuracy metrics for different query types
   - Identify patterns in incorrect analyses
   - Generate reports on system performance

#### Technical Implementation Details

**Feedback Store Interface**:
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

class FeedbackStore(ABC):
    """
    Abstract interface for storing and retrieving feedback.

    This defines the interface that all feedback stores must implement.
    """

    @abstractmethod
    async def store_feedback(
        self,
        query: str,
        analysis_result: Dict[str, Any],
        feedback: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Store feedback for a query analysis.

        Args:
            query: The original query
            analysis_result: The result returned by analyze()
            feedback: User or system feedback
            metadata: Additional metadata about the feedback

        Returns:
            Feedback ID
        """
        pass

    @abstractmethod
    async def get_feedback(
        self,
        feedback_id: str
    ) -> Dict[str, Any]:
        """
        Retrieve feedback by ID.

        Args:
            feedback_id: The ID of the feedback to retrieve

        Returns:
            Feedback data
        """
        pass

    @abstractmethod
    async def get_feedback_for_query(
        self,
        query: str
    ) -> List[Dict[str, Any]]:
        """
        Retrieve all feedback for a specific query.

        Args:
            query: The query to retrieve feedback for

        Returns:
            List of feedback data
        """
        pass

    @abstractmethod
    async def get_recent_feedback(
        self,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Retrieve recent feedback.

        Args:
            limit: Maximum number of feedback items to retrieve
            offset: Offset for pagination

        Returns:
            List of feedback data
        """
        pass
```

**In-Memory Feedback Store Implementation**:
```python
class InMemoryFeedbackStore(FeedbackStore):
    """
    In-memory implementation of the feedback store.

    This stores feedback in memory, which is useful for testing
    and development but not suitable for production use.
    """

    def __init__(self):
        """Initialize the in-memory feedback store."""
        self.feedback = {}
        self.feedback_by_query = {}
        self.feedback_list = []

    async def store_feedback(
        self,
        query: str,
        analysis_result: Dict[str, Any],
        feedback: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Store feedback for a query analysis.

        Args:
            query: The original query
            analysis_result: The result returned by analyze()
            feedback: User or system feedback
            metadata: Additional metadata about the feedback

        Returns:
            Feedback ID
        """
        # Generate a unique ID
        feedback_id = str(uuid.uuid4())

        # Create feedback record
        feedback_record = {
            "id": feedback_id,
            "query": query,
            "analysis_result": analysis_result,
            "feedback": feedback,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat()
        }

        # Store feedback
        self.feedback[feedback_id] = feedback_record

        # Store by query
        if query not in self.feedback_by_query:
            self.feedback_by_query[query] = []
        self.feedback_by_query[query].append(feedback_record)

        # Add to list for recent feedback
        self.feedback_list.append(feedback_record)

        return feedback_id

    async def get_feedback(
        self,
        feedback_id: str
    ) -> Dict[str, Any]:
        """
        Retrieve feedback by ID.

        Args:
            feedback_id: The ID of the feedback to retrieve

        Returns:
            Feedback data
        """
        if feedback_id not in self.feedback:
            raise ValueError(f"Feedback with ID {feedback_id} not found")

        return self.feedback[feedback_id]

    async def get_feedback_for_query(
        self,
        query: str
    ) -> List[Dict[str, Any]]:
        """
        Retrieve all feedback for a specific query.

        Args:
            query: The query to retrieve feedback for

        Returns:
            List of feedback data
        """
        return self.feedback_by_query.get(query, [])

    async def get_recent_feedback(
        self,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Retrieve recent feedback.

        Args:
            limit: Maximum number of feedback items to retrieve
            offset: Offset for pagination

        Returns:
            List of feedback data
        """
        # Sort by timestamp (newest first)
        sorted_feedback = sorted(
            self.feedback_list,
            key=lambda x: x["timestamp"],
            reverse=True
        )

        # Apply pagination
        return sorted_feedback[offset:offset + limit]
```

**Using Feedback in QueryAnalyzer**:
```python
class QueryAnalyzer:
    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
        calibration_config: CalibrationConfig = None,
        feedback_store: Optional[FeedbackStore] = None
    ):
        """
        Initialize the query analyzer with feedback collection.

        Args:
            llm_adapter: The LLM adapter to use for analysis
            embedding_model: The embedding model for vector similarity
            department_descriptions: Descriptions of departments for embedding-based detection
            section_descriptions: Descriptions of section types for embedding-based detection
            calibration_config: Configuration for confidence calibration
            feedback_store: Store for feedback collection
        """
        # Initialize base components
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.department_descriptions = department_descriptions or {}
        self.section_descriptions = section_descriptions or {}

        # Initialize calibration
        self.calibration = calibration_config or CalibrationConfig()

        # Initialize feedback store
        self.feedback_store = feedback_store

        # Use calibration parameters
        self.embedding_weight = self.calibration.method_weights.get("embedding", 0.6)
        self.keyword_weight = self.calibration.method_weights.get("keyword", 0.3)
        self.llm_weight = self.calibration.method_weights.get("llm", 0.4)

        self.similarity_threshold = self.calibration.confidence_thresholds.get("embedding_similarity", 0.65)
        self.confidence_threshold = self.calibration.confidence_thresholds.get("department", 0.5)

        # Set up logging
        self._setup_logging()

        # Initialize embeddings
        self.department_embeddings = {}
        self.section_embeddings = {}

        # Initialize detection strategies
        self.department_detectors = []
        self.section_detectors = []

        # Register default strategies
        self._register_default_strategies()

    async def record_feedback(
        self,
        query: str,
        analysis_result: Dict[str, Any],
        feedback: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Record feedback on analysis results for future improvement.

        Args:
            query: The original query
            analysis_result: The result returned by analyze()
            feedback: User or system feedback (e.g., {"correct_departments": ["finance"]})
            metadata: Additional metadata about the feedback

        Returns:
            Feedback ID if feedback store is available, None otherwise
        """
        if self.feedback_store:
            try:
                feedback_id = await self.feedback_store.store_feedback(
                    query=query,
                    analysis_result=analysis_result,
                    feedback=feedback,
                    metadata=metadata
                )
                self.logger.info(f"Recorded feedback with ID: {feedback_id}")
                return feedback_id
            except Exception as e:
                self.logger.error(f"Failed to record feedback: {e}")
                self.logger.debug(f"Error details: {traceback.format_exc()}")
                return None
        else:
            self.logger.warning("No feedback store available, feedback not recorded")
            return None

    async def learn_from_feedback(self, limit: int = 1000) -> bool:
        """
        Learn from collected feedback to improve future analyses.

        This method retrieves recent feedback and uses it to calibrate
        the confidence weights and thresholds.

        Args:
            limit: Maximum number of feedback items to use for learning

        Returns:
            True if learning was successful, False otherwise
        """
        if not self.feedback_store:
            self.logger.warning("No feedback store available, cannot learn from feedback")
            return False

        try:
            # Get recent feedback
            feedback_items = await self.feedback_store.get_recent_feedback(limit=limit)

            if not feedback_items:
                self.logger.info("No feedback available for learning")
                return False

            self.logger.info(f"Learning from {len(feedback_items)} feedback items")

            # Convert feedback to validation data
            validation_data = []

            for item in feedback_items:
                if "feedback" in item and "correct_departments" in item["feedback"]:
                    validation_data.append({
                        "query": item["query"],
                        "expected_departments": item["feedback"]["correct_departments"]
                    })

            if not validation_data:
                self.logger.info("No usable feedback for learning")
                return False

            # Calibrate using validation data
            await self.calibrate(validation_data)

            return True

        except Exception as e:
            self.logger.error(f"Failed to learn from feedback: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            return False
```

**Benefits of Feedback Loop**:

1. **🔄 Continuous Improvement**: The system gets better over time based on real-world usage.

2. **🧠 Adaptive Learning**: The system can adapt to changing query patterns and user needs.

3. **📊 Performance Monitoring**: Feedback provides valuable data for monitoring system performance.

4. **🔍 Error Analysis**: Feedback helps identify patterns in incorrect analyses for targeted improvements.

### External Configuration

The Query Analyzer supports loading configuration from external files:

1. **📄 Configuration Files**: Store configuration in external files
   - Department and section descriptions
   - Calibration parameters
   - Detection strategy settings

2. **🔄 Dynamic Loading**: Load configuration at runtime
   - Load configuration from JSON or YAML files
   - Update configuration without code changes
   - Support environment-specific configurations

3. **🔒 Validation**: Validate configuration against schema
   - Ensure configuration is valid
   - Provide helpful error messages for invalid configurations
   - Apply defaults for missing values

#### Technical Implementation Details

**Configuration Schema**:
```python
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union, Any

class DepartmentConfig(BaseModel):
    """
    Configuration for a department.
    """
    description: str = Field(..., description="Description of the department")
    keywords: List[str] = Field(default_factory=list, description="Keywords associated with the department")
    weight: Optional[float] = Field(None, description="Optional weight for this department")

class SectionConfig(BaseModel):
    """
    Configuration for a section type.
    """
    description: str = Field(..., description="Description of the section type")
    keywords: List[str] = Field(default_factory=list, description="Keywords associated with the section type")
    weight: Optional[float] = Field(None, description="Optional weight for this section type")

class MethodWeights(BaseModel):
    """
    Weights for different detection methods.
    """
    embedding: float = Field(0.6, description="Weight for embedding-based detection")
    keyword: float = Field(0.3, description="Weight for keyword-based detection")
    llm: float = Field(0.4, description="Weight for LLM-based detection")

class ConfidenceThresholds(BaseModel):
    """
    Confidence thresholds for different entities.
    """
    department: float = Field(0.5, description="Confidence threshold for departments")
    section: float = Field(0.5, description="Confidence threshold for sections")
    embedding_similarity: float = Field(0.65, description="Similarity threshold for embedding-based detection")

class CalibrationConfigModel(BaseModel):
    """
    Configuration for confidence calibration.
    """
    method_weights: MethodWeights = Field(default_factory=MethodWeights, description="Weights for different detection methods")
    confidence_thresholds: ConfidenceThresholds = Field(default_factory=ConfidenceThresholds, description="Confidence thresholds for different entities")
    department_weights: Dict[str, float] = Field(default_factory=dict, description="Weights for different departments")
    section_weights: Dict[str, float] = Field(default_factory=dict, description="Weights for different sections")

class QueryAnalyzerConfig(BaseModel):
    """
    Configuration for the Query Analyzer.
    """
    departments: Dict[str, DepartmentConfig] = Field(..., description="Department configurations")
    sections: Dict[str, SectionConfig] = Field(..., description="Section configurations")
    calibration: CalibrationConfigModel = Field(default_factory=CalibrationConfigModel, description="Calibration configuration")
    embedding_model: Optional[str] = Field(None, description="Name of the embedding model to use")
    enable_feedback: bool = Field(False, description="Whether to enable feedback collection")
```

**Configuration Loading**:
```python
import json
import yaml
import os
from typing import Dict, Any, Optional

def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Configuration dictionary
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    with open(config_path, 'r') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            return yaml.safe_load(f)
        elif config_path.endswith('.json'):
            return json.load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path}")

def validate_config(config: Dict[str, Any]) -> QueryAnalyzerConfig:
    """
    Validate configuration against schema.

    Args:
        config: Configuration dictionary

    Returns:
        Validated configuration object
    """
    try:
        return QueryAnalyzerConfig(**config)
    except Exception as e:
        raise ValueError(f"Invalid configuration: {e}")
```

**Using Configuration in QueryAnalyzer**:
```python
class QueryAnalyzer:
    @classmethod
    def from_config_file(cls, config_path: str, llm_adapter) -> 'QueryAnalyzer':
        """
        Create a QueryAnalyzer from a configuration file.

        Args:
            config_path: Path to the configuration file
            llm_adapter: The LLM adapter to use

        Returns:
            QueryAnalyzer instance
        """
        # Load and validate configuration
        config_dict = load_config(config_path)
        config = validate_config(config_dict)

        # Create embedding model if specified
        embedding_model = None
        if config.embedding_model:
            from app.rag.embeddings import EmbeddingFactory
            embedding_model = EmbeddingFactory.create(config.embedding_model)

        # Extract department descriptions
        department_descriptions = {
            dept_id: dept_config.description
            for dept_id, dept_config in config.departments.items()
        }

        # Extract section descriptions
        section_descriptions = {
            section_id: section_config.description
            for section_id, section_config in config.sections.items()
        }

        # Create calibration config
        calibration_config = CalibrationConfig(
            method_weights=config.calibration.method_weights.dict(),
            confidence_thresholds=config.calibration.confidence_thresholds.dict(),
            department_weights=config.calibration.department_weights,
            section_weights=config.calibration.section_weights
        )

        # Create feedback store if enabled
        feedback_store = None
        if config.enable_feedback:
            feedback_store = InMemoryFeedbackStore()

        # Create analyzer
        analyzer = cls(
            llm_adapter=llm_adapter,
            embedding_model=embedding_model,
            department_descriptions=department_descriptions,
            section_descriptions=section_descriptions,
            calibration_config=calibration_config,
            feedback_store=feedback_store
        )

        # Add department keywords
        for dept_id, dept_config in config.departments.items():
            if dept_config.keywords:
                analyzer.department_keywords[dept_id] = dept_config.keywords

        # Add section keywords
        for section_id, section_config in config.sections.items():
            if section_config.keywords:
                analyzer.section_keywords[section_id] = section_config.keywords

        return analyzer
```

**Example Configuration File (YAML)**:
```yaml
departments:
  finance:
    description: "The finance department handles budgeting, financial reporting, and financial analysis."
    keywords:
      - budget
      - financial
      - revenue
      - expense
      - cost
      - profit
      - investment
      - funding
      - forecast
      - projection
    weight: 1.0

  marketing:
    description: "The marketing department handles campaigns, brand management, and customer acquisition."
    keywords:
      - marketing
      - campaign
      - brand
      - customer
      - acquisition
      - promotion
      - advertising
      - social media
      - content
      - engagement
    weight: 1.0

sections:
  executive_summary:
    description: "High-level overview of key points and findings."
    keywords:
      - summary
      - overview
      - highlights
      - key points
      - brief

  financial_data:
    description: "Detailed financial information including budgets and forecasts."
    keywords:
      - financial
      - budget
      - forecast
      - revenue
      - expense
      - cost

calibration:
  method_weights:
    embedding: 0.6
    keyword: 0.3
    llm: 0.4

  confidence_thresholds:
    department: 0.5
    section: 0.5
    embedding_similarity: 0.65

embedding_model: "intfloat/e5-base-v2"
enable_feedback: true
```

**Benefits of External Configuration**:

1. **🔄 Flexibility**: Configuration can be changed without modifying code.

2. **🧩 Modularity**: Different configurations can be used for different environments or use cases.

3. **🔒 Validation**: Configuration is validated against a schema to ensure correctness.

4. **📝 Documentation**: Configuration schema serves as documentation for available options.

### Enhanced Output Format

The Query Analyzer provides detailed output with scoring breakdowns:

1. **📊 Detailed Scores**: Include detailed scoring information
   - Scores from each detection method
   - Confidence calculation breakdown
   - Method-specific details

2. **🔍 Debug Mode**: Enable debug mode for additional information
   - Detailed logs of the detection process
   - Intermediate results and calculations
   - Performance metrics

3. **📋 Structured Output**: Return structured output for easy consumption
   - Consistent JSON format
   - Well-defined schema
   - Typed return values

#### Technical Implementation Details

**Enhanced Output Structure**:
```python
from typing import Dict, List, Any, Optional, TypedDict

class MethodScore(TypedDict):
    """
    Score from a specific detection method.
    """
    score: float
    details: Optional[Dict[str, Any]]

class DepartmentResult(TypedDict):
    """
    Result for a detected department.
    """
    id: str
    confidence: float
    methods: List[str]
    scores: Optional[Dict[str, float]]
    matched_keywords: Optional[List[str]]
    fallback: Optional[bool]

class SectionResult(TypedDict):
    """
    Result for a detected section.
    """
    type: str
    confidence: float
    methods: List[str]
    scores: Optional[Dict[str, float]]
    matched_keywords: Optional[List[str]]

class AnalysisResult(TypedDict):
    """
    Result of query analysis.
    """
    query: str
    query_type: str
    intent: str
    relevant_departments: List[DepartmentResult]
    target_sections: List[SectionResult]
    retrieval_strategy: Dict[str, Any]
    analysis_time_ms: int
    success: bool
    error: Optional[str]
    debug_info: Optional[Dict[str, Any]]
```

**Implementing Debug Mode**:
```python
async def analyze(
    self,
    query: str,
    debug: bool = False
) -> AnalysisResult:
    """
    Analyze a query with comprehensive classification.

    Args:
        query: The user query
        debug: Whether to include debug information in the result

    Returns:
        Analysis result
    """
    start_time = time.time()
    self.logger.info(f"Analyzing query: {query}")

    # Initialize debug info if requested
    debug_info = {} if debug else None

    try:
        # Initialize result structure
        result: AnalysisResult = {
            "query": query,
            "query_type": "general",
            "intent": "information_seeking",
            "relevant_departments": [],
            "target_sections": [],
            "retrieval_strategy": {},
            "analysis_time_ms": 0,
            "success": True,
            "error": None,
            "debug_info": debug_info
        }

        # Detect query type
        try:
            result["query_type"] = self._detect_query_type(query)
            if debug:
                debug_info["query_type_detection"] = {
                    "method": "pattern_matching",
                    "patterns_matched": self._get_matched_patterns(query)
                }
        except Exception as e:
            self.logger.error(f"Query type detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            if debug:
                debug_info["query_type_detection_error"] = str(e)

        # Recognize intent
        try:
            result["intent"] = self._recognize_intent(query, result["query_type"])
            if debug:
                debug_info["intent_recognition"] = {
                    "query_type_hint": result["query_type"],
                    "intent_indicators": self._get_intent_indicators(query)
                }
        except Exception as e:
            self.logger.error(f"Intent recognition failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            if debug:
                debug_info["intent_recognition_error"] = str(e)

        # Detect departments
        try:
            departments = await self.detect_departments(query, debug=debug)
            result["relevant_departments"] = departments
            if debug and "department_detection" not in debug_info:
                debug_info["department_detection"] = {
                    "strategies_used": [s.name for s in self.department_detectors]
                }
        except Exception as e:
            self.logger.error(f"Department detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            if debug:
                debug_info["department_detection_error"] = str(e)

        # Detect sections
        try:
            sections = await self.detect_sections(query, debug=debug)
            result["target_sections"] = sections
            if debug and "section_detection" not in debug_info:
                debug_info["section_detection"] = {
                    "strategies_used": [s.name for s in self.section_detectors]
                }
        except Exception as e:
            self.logger.error(f"Section detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            if debug:
                debug_info["section_detection_error"] = str(e)

        # Determine retrieval strategy
        try:
            result["retrieval_strategy"] = self._determine_retrieval_strategy(
                result["query_type"],
                result["intent"],
                result["target_sections"]
            )
            if debug:
                debug_info["retrieval_strategy_determination"] = {
                    "inputs": {
                        "query_type": result["query_type"],
                        "intent": result["intent"],
                        "target_sections": [s["type"] for s in result["target_sections"]]
                    }
                }
        except Exception as e:
            self.logger.error(f"Retrieval strategy determination failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            if debug:
                debug_info["retrieval_strategy_error"] = str(e)

        # Calculate analysis time
        end_time = time.time()
        result["analysis_time_ms"] = int((end_time - start_time) * 1000)

        if debug:
            debug_info["timing"] = {
                "total_ms": result["analysis_time_ms"],
                "start_time": start_time,
                "end_time": end_time
            }

        self.logger.info(f"Query analysis completed in {result['analysis_time_ms']}ms")
        return result

    except Exception as e:
        # Catch-all for unexpected errors
        self.logger.error(f"Unexpected error in query analysis: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Return minimal result on complete failure
        end_time = time.time()
        error_result: AnalysisResult = {
            "query": query,
            "query_type": "general",
            "intent": "information_seeking",
            "relevant_departments": [],
            "target_sections": [],
            "retrieval_strategy": {},
            "analysis_time_ms": int((end_time - start_time) * 1000),
            "success": False,
            "error": str(e),
            "debug_info": debug_info
        }

        if debug:
            error_result["debug_info"] = {
                "error": str(e),
                "traceback": traceback.format_exc(),
                "timing": {
                    "total_ms": error_result["analysis_time_ms"],
                    "start_time": start_time,
                    "end_time": end_time
                }
            }

        return error_result
```

**Department Detection with Scoring Breakdown**:
```python
async def detect_departments(
    self,
    query: str,
    debug: bool = False
) -> List[DepartmentResult]:
    """
    Detect relevant departments with detailed scoring.

    Args:
        query: The user query
        debug: Whether to include detailed scores in the result

    Returns:
        List of department results with confidence scores
    """
    self.logger.info(f"Detecting departments for query: {query}")

    all_results = {}
    strategy_results = {}

    # Try each strategy in priority order
    for strategy in self.department_detectors:
        try:
            results = await strategy.detect(query)
            if results:
                self.logger.info(f"Strategy {strategy.name} found {len(results)} departments")
                strategy_results[strategy.name] = results

                # Process results
                for result in results:
                    dept_id = result["id"]
                    all_results[dept_id] = all_results.get(dept_id, {})
                    all_results[dept_id][f"{strategy.name}_score"] = result["confidence"]

                    # Store additional details if available
                    if "matched_keywords" in result:
                        all_results[dept_id]["matched_keywords"] = result.get("matched_keywords", [])

        except Exception as e:
            self.logger.error(f"Strategy {strategy.name} failed: {e}")

    # Calculate final confidence scores
    final_results = []
    for dept_id, scores in all_results.items():
        # Get scores with defaults
        embedding_score = scores.get("embedding_score", 0.0)
        keyword_score = scores.get("keyword_score", 0.0)
        llm_score = scores.get("llm_score", 0.0)

        # Calculate weighted average
        total_weight = 0
        weighted_sum = 0

        if embedding_score > 0:
            weighted_sum += embedding_score * self.embedding_weight
            total_weight += self.embedding_weight

        if keyword_score > 0:
            weighted_sum += keyword_score * self.keyword_weight
            total_weight += self.keyword_weight

        if llm_score > 0:
            weighted_sum += llm_score * self.llm_weight
            total_weight += self.llm_weight

        # Calculate confidence (avoid division by zero)
        confidence = weighted_sum / total_weight if total_weight > 0 else 0.0

        # Add to results if above threshold
        if confidence > self.confidence_threshold:
            # Determine which methods were used
            methods = []
            if embedding_score > 0:
                methods.append("embedding")
            if keyword_score > 0:
                methods.append("keyword")
            if llm_score > 0:
                methods.append("llm")

            # Create result
            result: DepartmentResult = {
                "id": dept_id,
                "confidence": confidence,
                "methods": methods
            }

            # Add detailed scores if debug mode is enabled
            if debug:
                result["scores"] = {
                    "embedding": embedding_score,
                    "keyword": keyword_score,
                    "llm": llm_score
                }

            # Add matched keywords if available
            if "matched_keywords" in scores:
                result["matched_keywords"] = scores["matched_keywords"]

            final_results.append(result)

    # Sort by confidence
    final_results.sort(key=lambda x: x["confidence"], reverse=True)

    # Ensure at least one department if results are empty but we have some data
    if not final_results and all_results:
        # Get department with highest combined score
        best_dept = max(all_results.items(),
                        key=lambda x: sum([
                            x[1].get("embedding_score", 0) * self.embedding_weight,
                            x[1].get("keyword_score", 0) * self.keyword_weight,
                            x[1].get("llm_score", 0) * self.llm_weight
                        ]))

        # Calculate methods
        methods = []
        scores = best_dept[1]
        if "embedding_score" in scores:
            methods.append("embedding")
        if "keyword_score" in scores:
            methods.append("keyword")
        if "llm_score" in scores:
            methods.append("llm")

        # Create fallback result
        result: DepartmentResult = {
            "id": best_dept[0],
            "confidence": 0.4,  # Lower confidence for fallback
            "methods": methods,
            "fallback": True
        }

        # Add detailed scores if debug mode is enabled
        if debug:
            result["scores"] = {
                "embedding": scores.get("embedding_score", 0.0),
                "keyword": scores.get("keyword_score", 0.0),
                "llm": scores.get("llm_score", 0.0)
            }

        # Add matched keywords if available
        if "matched_keywords" in scores:
            result["matched_keywords"] = scores["matched_keywords"]

        final_results.append(result)

    self.logger.info(f"Department detection results: {final_results}")
    return final_results
```

**Example Output**:
```json
{
  "query": "What was our marketing budget for Q2 2023?",
  "query_type": "informational",
  "intent": "information_seeking",
  "relevant_departments": [
    {
      "id": "marketing",
      "confidence": 0.92,
      "methods": ["embedding", "keyword"],
      "scores": {
        "embedding": 0.95,
        "keyword": 0.85,
        "llm": 0.0
      },
      "matched_keywords": ["marketing", "budget"]
    },
    {
      "id": "finance",
      "confidence": 0.85,
      "methods": ["embedding", "keyword"],
      "scores": {
        "embedding": 0.82,
        "keyword": 0.9,
        "llm": 0.0
      },
      "matched_keywords": ["budget"]
    }
  ],
  "target_sections": [
    {
      "type": "financial_data",
      "confidence": 0.88,
      "methods": ["embedding", "keyword"],
      "scores": {
        "embedding": 0.85,
        "keyword": 0.95
      },
      "matched_keywords": ["budget"]
    }
  ],
  "retrieval_strategy": {
    "search_type": "hybrid",
    "vector_weight": 0.7,
    "keyword_weight": 0.3,
    "result_count": 6,
    "min_similarity": 0.65,
    "include_metadata": true,
    "section_filter": ["financial_data"]
  },
  "analysis_time_ms": 125,
  "success": true
}
```

**Benefits of Enhanced Output Format**:

1. **🔍 Transparency**: Detailed scores provide transparency into how decisions are made.

2. **🧪 Debugging**: Debug mode makes it easier to identify and fix issues.

3. **📊 Analysis**: Structured output enables detailed analysis of system performance.

4. **🧠 Learning**: Detailed information can be used for continuous learning and improvement.

### Advanced Text Preprocessing

The Query Analyzer implements advanced text preprocessing to improve matching:

1. **🔄 Normalization**: Normalize text for better matching
   - Convert to lowercase
   - Remove excessive whitespace
   - Normalize contractions
   - Handle special characters

2. **🧠 Linguistic Processing**: Apply linguistic processing
   - Tokenization
   - Stemming or lemmatization
   - Stop word removal
   - Entity recognition

3. **📊 Query Expansion**: Expand queries with synonyms
   - Add synonyms for domain-specific terms
   - Handle abbreviations and acronyms
   - Expand common terms

#### Technical Implementation Details

**Enhanced Text Preprocessing**:
```python
import re
import string
from typing import List, Optional

class TextPreprocessor:
    """
    Advanced text preprocessor for query analysis.

    This class provides methods for normalizing and processing text
    to improve matching and analysis.
    """

    def __init__(self, use_stemming: bool = False, remove_stopwords: bool = False):
        """
        Initialize the text preprocessor.

        Args:
            use_stemming: Whether to apply stemming
            remove_stopwords: Whether to remove stopwords
        """
        self.use_stemming = use_stemming
        self.remove_stopwords = remove_stopwords

        # Initialize stemmer if needed
        self.stemmer = None
        if use_stemming:
            try:
                from nltk.stem import PorterStemmer
                self.stemmer = PorterStemmer()
            except ImportError:
                self.use_stemming = False
                print("NLTK not available, stemming disabled")

        # Initialize stopwords if needed
        self.stopwords = set()
        if remove_stopwords:
            try:
                from nltk.corpus import stopwords
                self.stopwords = set(stopwords.words('english'))
            except ImportError:
                self.remove_stopwords = False
                print("NLTK not available, stopword removal disabled")

        # Initialize contraction mapping
        self.contractions = {
            "what's": "what is",
            "who's": "who is",
            "where's": "where is",
            "how's": "how is",
            "it's": "it is",
            "that's": "that is",
            "there's": "there is",
            "he's": "he is",
            "she's": "she is",
            "i'm": "i am",
            "we're": "we are",
            "they're": "they are",
            "you're": "you are",
            "i've": "i have",
            "we've": "we have",
            "they've": "they have",
            "you've": "you have",
            "can't": "cannot",
            "don't": "do not",
            "won't": "will not",
            "isn't": "is not",
            "aren't": "are not",
            "wasn't": "was not",
            "weren't": "were not",
            "hasn't": "has not",
            "haven't": "have not",
            "hadn't": "had not",
            "doesn't": "does not",
            "didn't": "did not",
            "shouldn't": "should not",
            "wouldn't": "would not",
            "couldn't": "could not",
            "mustn't": "must not",
            "let's": "let us",
            "i'll": "i will",
            "we'll": "we will",
            "they'll": "they will",
            "you'll": "you will",
            "he'll": "he will",
            "she'll": "she will",
            "it'll": "it will",
            "i'd": "i would",
            "we'd": "we would",
            "they'd": "they would",
            "you'd": "you would",
            "he'd": "he would",
            "she'd": "she would",
            "it'd": "it would"
        }

        # Initialize domain-specific abbreviations
        self.abbreviations = {
            "q1": "quarter 1",
            "q2": "quarter 2",
            "q3": "quarter 3",
            "q4": "quarter 4",
            "fy": "fiscal year",
            "roi": "return on investment",
            "kpi": "key performance indicator",
            "cac": "customer acquisition cost",
            "ltv": "lifetime value",
            "ctr": "click through rate",
            "cpa": "cost per acquisition",
            "cpc": "cost per click",
            "cpm": "cost per mille",
            "seo": "search engine optimization",
            "sem": "search engine marketing",
            "pr": "public relations",
            "ar": "accounts receivable",
            "ap": "accounts payable",
            "p&l": "profit and loss",
            "ebitda": "earnings before interest taxes depreciation amortization"
        }

    def preprocess(self, text: str) -> str:
        """
        Preprocess text for analysis.

        This method applies a series of preprocessing steps to normalize
        and clean the text for better matching and analysis.

        Args:
            text: The text to preprocess

        Returns:
            Preprocessed text
        """
        if not text:
            return ""

        # Convert to lowercase
        text = text.lower()

        # Expand contractions
        text = self._expand_contractions(text)

        # Expand abbreviations
        text = self._expand_abbreviations(text)

        # Remove punctuation (except for certain characters)
        text = self._remove_punctuation(text)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Apply stemming or stopword removal if enabled
        if self.use_stemming or self.remove_stopwords:
            tokens = text.split()

            if self.remove_stopwords:
                tokens = [token for token in tokens if token not in self.stopwords]

            if self.use_stemming and self.stemmer:
                tokens = [self.stemmer.stem(token) for token in tokens]

            text = ' '.join(tokens)

        return text

    def _expand_contractions(self, text: str) -> str:
        """
        Expand contractions in text.

        Args:
            text: The text to process

        Returns:
            Text with expanded contractions
        """
        # Sort contractions by length (longest first) to avoid partial matches
        for contraction, expansion in sorted(
            self.contractions.items(),
            key=lambda x: len(x[0]),
            reverse=True
        ):
            text = re.sub(r'\b' + contraction + r'\b', expansion, text)

        return text

    def _expand_abbreviations(self, text: str) -> str:
        """
        Expand domain-specific abbreviations.

        Args:
            text: The text to process

        Returns:
            Text with expanded abbreviations
        """
        # Sort abbreviations by length (longest first) to avoid partial matches
        for abbr, expansion in sorted(
            self.abbreviations.items(),
            key=lambda x: len(x[0]),
            reverse=True
        ):
            text = re.sub(r'\b' + abbr + r'\b', expansion, text)

        return text

    def _remove_punctuation(self, text: str) -> str:
        """
        Remove punctuation from text, preserving certain characters.

        Args:
            text: The text to process

        Returns:
            Text with punctuation removed
        """
        # Define characters to keep
        keep_chars = set(' .,?!-')

        # Remove punctuation except for keep_chars
        translator = str.maketrans('', '', ''.join(c for c in string.punctuation if c not in keep_chars))

        return text.translate(translator)

    def tokenize(self, text: str) -> List[str]:
        """
        Tokenize text into words.

        Args:
            text: The text to tokenize

        Returns:
            List of tokens
        """
        # Preprocess text first
        processed_text = self.preprocess(text)

        # Simple whitespace tokenization
        return processed_text.split()
```

**Using Advanced Preprocessing in QueryAnalyzer**:
```python
class QueryAnalyzer:
    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
        calibration_config: CalibrationConfig = None,
        feedback_store: Optional[FeedbackStore] = None,
        use_advanced_preprocessing: bool = True
    ):
        """
        Initialize the query analyzer with advanced preprocessing.

        Args:
            llm_adapter: The LLM adapter to use for analysis
            embedding_model: The embedding model for vector similarity
            department_descriptions: Descriptions of departments for embedding-based detection
            section_descriptions: Descriptions of section types for embedding-based detection
            calibration_config: Configuration for confidence calibration
            feedback_store: Store for feedback collection
            use_advanced_preprocessing: Whether to use advanced text preprocessing
        """
        # Initialize base components
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.department_descriptions = department_descriptions or {}
        self.section_descriptions = section_descriptions or {}

        # Initialize calibration
        self.calibration = calibration_config or CalibrationConfig()

        # Initialize feedback store
        self.feedback_store = feedback_store

        # Initialize text preprocessor
        self.use_advanced_preprocessing = use_advanced_preprocessing
        if use_advanced_preprocessing:
            self.text_preprocessor = TextPreprocessor(
                use_stemming=False,  # Stemming can be too aggressive
                remove_stopwords=False  # Keep stopwords for better context
            )

        # Use calibration parameters
        self.embedding_weight = self.calibration.method_weights.get("embedding", 0.6)
        self.keyword_weight = self.calibration.method_weights.get("keyword", 0.3)
        self.llm_weight = self.calibration.method_weights.get("llm", 0.4)

        self.similarity_threshold = self.calibration.confidence_thresholds.get("embedding_similarity", 0.65)
        self.confidence_threshold = self.calibration.confidence_thresholds.get("department", 0.5)

        # Set up logging
        self._setup_logging()

        # Initialize embeddings
        self.department_embeddings = {}
        self.section_embeddings = {}

        # Initialize detection strategies
        self.department_detectors = []
        self.section_detectors = []

        # Register default strategies
        self._register_default_strategies()

    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for analysis.

        Args:
            text: The text to preprocess

        Returns:
            Preprocessed text
        """
        if self.use_advanced_preprocessing:
            return self.text_preprocessor.preprocess(text)
        else:
            # Basic preprocessing
            text = text.lower()
            text = re.sub(r'\s+', ' ', text).strip()
            text = re.sub(r'[^\w\s.,?!-]', '', text)
            return text
```

**Benefits of Advanced Text Preprocessing**:

1. **🎯 Improved Matching**: Better matching between queries and keywords.

2. **🧠 Semantic Understanding**: Better understanding of query intent through linguistic processing.

3. **🔄 Normalization**: Consistent handling of text variations.

4. **📊 Domain Adaptation**: Customized handling of domain-specific terminology.

### Dynamic Embedding Dimensionality

The Query Analyzer supports dynamic embedding dimensionality:

1. **🔄 Automatic Detection**: Automatically detect embedding dimensions
   - Determine dimensions from the embedding model
   - Handle different embedding models with different dimensions
   - Adapt to model changes

2. **🧩 Model Compatibility**: Support different embedding models
   - Work with models of different dimensions
   - Handle dimension mismatches
   - Normalize embeddings for comparison

3. **📊 Dimension Reduction**: Apply dimension reduction techniques
   - Reduce high-dimensional embeddings for efficiency
   - Preserve semantic meaning
   - Balance performance and accuracy

#### Technical Implementation Details

**Dynamic Dimension Detection**:
```python
import asyncio
import numpy as np
from typing import List, Optional, Any

class QueryAnalyzer:
    def __init__(
        self,
        llm_adapter,
        embedding_model=None,
        department_descriptions: Dict[str, str] = None,
        section_descriptions: Dict[str, str] = None,
        calibration_config: CalibrationConfig = None,
        feedback_store: Optional[FeedbackStore] = None,
        use_advanced_preprocessing: bool = True
    ):
        """
        Initialize the query analyzer with dynamic embedding dimensionality.
        """
        # Initialize base components
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.department_descriptions = department_descriptions or {}
        self.section_descriptions = section_descriptions or {}

        # Initialize calibration
        self.calibration = calibration_config or CalibrationConfig()

        # Initialize feedback store
        self.feedback_store = feedback_store

        # Initialize text preprocessor
        self.use_advanced_preprocessing = use_advanced_preprocessing
        if use_advanced_preprocessing:
            self.text_preprocessor = TextPreprocessor(
                use_stemming=False,
                remove_stopwords=False
            )

        # Use calibration parameters
        self.embedding_weight = self.calibration.method_weights.get("embedding", 0.6)
        self.keyword_weight = self.calibration.method_weights.get("keyword", 0.3)
        self.llm_weight = self.calibration.method_weights.get("llm", 0.4)

        self.similarity_threshold = self.calibration.confidence_thresholds.get("embedding_similarity", 0.65)
        self.confidence_threshold = self.calibration.confidence_thresholds.get("department", 0.5)

        # Set up logging
        self._setup_logging()

        # Initialize embeddings
        self.department_embeddings = {}
        self.section_embeddings = {}

        # Initialize detection strategies
        self.department_detectors = []
        self.section_detectors = []

        # Determine embedding dimension
        self.embedding_dimension = None
        if embedding_model:
            # Try to get dimension from model
            self.embedding_dimension = getattr(embedding_model, "dimension", None)

            # If not available, determine by embedding a test string
            if self.embedding_dimension is None:
                asyncio.create_task(self._determine_embedding_dimension())
        else:
            # Default dimension if no model provided
            self.embedding_dimension = 768  # Default for e5-base-v2

        # Register default strategies
        self._register_default_strategies()

    async def _determine_embedding_dimension(self):
        """
        Determine embedding dimension by embedding a test string.
        """
        try:
            test_embedding = await self.embedding_model.embed_query("test")
            self.embedding_dimension = len(test_embedding)
            self.logger.info(f"Determined embedding dimension: {self.embedding_dimension}")
        except Exception as e:
            self.logger.warning(f"Failed to determine embedding dimension: {e}")
            self.logger.debug(f"Using default dimension: 768")
            self.embedding_dimension = 768  # Default fallback
```

**Handling Different Embedding Models**:
```python
async def _get_embedding(self, text: str) -> List[float]:
    """
    Get embedding for text with dimension handling.

    Args:
        text: The text to embed

    Returns:
        Embedding vector
    """
    # Create cache key
    cache_key = hashlib.md5(text.encode()).hexdigest()

    # Check cache
    if cache_key in self.embedding_cache:
        return self.embedding_cache[cache_key]

    try:
        # Generate embedding
        embedding = await self.embedding_model.embed_query(text)

        # Update dimension if not set
        if self.embedding_dimension is None:
            self.embedding_dimension = len(embedding)
            self.logger.info(f"Updated embedding dimension: {self.embedding_dimension}")

        # Handle dimension mismatch
        elif len(embedding) != self.embedding_dimension:
            self.logger.warning(
                f"Embedding dimension mismatch: expected {self.embedding_dimension}, got {len(embedding)}"
            )

            # Resize embedding if needed
            if len(embedding) > self.embedding_dimension:
                # Truncate
                embedding = embedding[:self.embedding_dimension]
            else:
                # Pad with zeros
                embedding = embedding + [0.0] * (self.embedding_dimension - len(embedding))

        # Normalize embedding
        embedding = self._normalize_embedding(embedding)

        # Cache result
        self.embedding_cache[cache_key] = embedding

        return embedding
    except Exception as e:
        self.logger.error(f"Error generating embedding: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Return zero vector as fallback
        return [0.0] * self.embedding_dimension

def _normalize_embedding(self, embedding: List[float]) -> List[float]:
    """
    Normalize embedding vector to unit length.

    Args:
        embedding: The embedding vector

    Returns:
        Normalized embedding vector
    """
    # Convert to numpy array
    vec = np.array(embedding)

    # Calculate magnitude
    magnitude = np.linalg.norm(vec)

    # Normalize (avoid division by zero)
    if magnitude > 0:
        normalized = vec / magnitude
    else:
        normalized = vec

    # Convert back to list
    return normalized.tolist()
```

**Dimension Reduction (Optional)**:
```python
def _reduce_dimensions(self, embeddings: List[List[float]], target_dim: int = 100) -> List[List[float]]:
    """
    Reduce dimensionality of embeddings.

    Args:
        embeddings: List of embedding vectors
        target_dim: Target dimensionality

    Returns:
        Reduced embeddings
    """
    if not embeddings:
        return []

    # Check if reduction is needed
    current_dim = len(embeddings[0])
    if current_dim <= target_dim:
        return embeddings

    try:
        # Convert to numpy array
        embeddings_array = np.array(embeddings)

        # Apply PCA for dimension reduction
        from sklearn.decomposition import PCA
        pca = PCA(n_components=target_dim)
        reduced = pca.fit_transform(embeddings_array)

        # Convert back to list
        return reduced.tolist()
    except ImportError:
        self.logger.warning("sklearn not available, dimension reduction skipped")
        return embeddings
    except Exception as e:
        self.logger.error(f"Error in dimension reduction: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")
        return embeddings
```

**Benefits of Dynamic Embedding Dimensionality**:

1. **🔌 Flexibility**: Support for different embedding models without code changes.

2. **🧩 Compatibility**: Ability to handle embeddings of different dimensions.

3. **🚀 Performance**: Option to reduce dimensions for better performance.

4. **🔄 Adaptability**: Automatic adaptation to model changes.

#### Technical Implementation Details

**Retry Logic for External Service Calls**:
```python
async def _with_retry(self, func, *args, max_retries=3, **kwargs):
    """
    Execute a function with retry logic.

    Args:
        func: The async function to execute
        *args: Positional arguments to pass to the function
        max_retries: Maximum number of retry attempts
        **kwargs: Keyword arguments to pass to the function

    Returns:
        The result of the function call

    Raises:
        Exception: The last error encountered if all retries fail
    """
    retries = 0
    last_error = None

    while retries <= max_retries:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_error = e

            # Exponential backoff
            wait_time = 0.1 * (2 ** retries)  # 0.1, 0.2, 0.4, 0.8, ...
            self.logger.warning(
                f"Error calling {func.__name__}: {e}. "
                f"Retrying in {wait_time:.2f}s (attempt {retries+1}/{max_retries+1})"
            )

            await asyncio.sleep(wait_time)
            retries += 1

    # If we get here, all retries failed
    self.logger.error(f"All {max_retries+1} attempts failed for {func.__name__}. Last error: {last_error}")
    raise last_error
```

**Embedding Model Call with Retry**:
```python
async def _get_embedding_with_retry(self, text: str) -> List[float]:
    """
    Get embedding for text with retry logic.

    Args:
        text: The text to embed

    Returns:
        Embedding vector
    """
    try:
        return await self._with_retry(
            self.embedding_model.embed_query,
            text,
            max_retries=2
        )
    except Exception as e:
        self.logger.error(f"Failed to get embedding after retries: {e}")
        # Return zero vector as fallback (will result in low similarity)
        return [0.0] * self.embedding_dimension
```

**LLM Call with Retry and Fallback**:
```python
async def _llm_call_with_fallback(self, prompt, max_retries=2):
    """
    Call LLM with retry logic and fallback.

    Args:
        prompt: The prompt to send to the LLM
        max_retries: Maximum number of retry attempts

    Returns:
        LLM response or fallback response
    """
    try:
        return await self._with_retry(
            self.llm_adapter.chat,
            prompt,
            max_retries=max_retries
        )
    except Exception as e:
        self.logger.error(f"LLM call failed after retries: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Return a fallback response
        return "Error: Unable to analyze query. Please try again."
```

**Complete Analyze Method with Error Handling**:
```python
async def analyze(self, query: str) -> Dict[str, Any]:
    """
    Analyze a query with comprehensive error handling.

    Args:
        query: The user query

    Returns:
        Dict containing analysis results
    """
    start_time = time.time()
    self.logger.info(f"Analyzing query: {query}")

    try:
        # Initialize result structure
        result = {
            "query": query,
            "query_type": "general",  # Default type
            "relevant_departments": [],
            "target_sections": [],
            "analysis_time_ms": 0,
            "success": True
        }

        # Detect query type
        try:
            result["query_type"] = self._detect_query_type(query)
        except Exception as e:
            self.logger.error(f"Query type detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            # Continue with default query type

        # Detect departments with fallback mechanisms
        try:
            result["relevant_departments"] = await self.detect_departments(query)
        except Exception as e:
            self.logger.error(f"Department detection failed completely: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            # Set empty departments list (already initialized)

        # Detect sections with fallback mechanisms
        try:
            result["target_sections"] = await self.detect_sections(query)
        except Exception as e:
            self.logger.error(f"Section detection failed: {e}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            # Set empty sections list (already initialized)

        # Calculate analysis time
        end_time = time.time()
        result["analysis_time_ms"] = int((end_time - start_time) * 1000)

        self.logger.info(f"Query analysis completed in {result['analysis_time_ms']}ms")
        return result

    except Exception as e:
        # Catch-all for unexpected errors
        self.logger.error(f"Unexpected error in query analysis: {e}")
        self.logger.debug(f"Error details: {traceback.format_exc()}")

        # Return minimal result on complete failure
        end_time = time.time()
        return {
            "query": query,
            "query_type": "general",
            "relevant_departments": [],
            "target_sections": [],
            "analysis_time_ms": int((end_time - start_time) * 1000),
            "success": False,
            "error": str(e)
        }
```

**Structured Logging Setup**:
```python
def _setup_logging(self):
    """
    Set up structured logging for the query analyzer.
    """
    self.logger = logging.getLogger("query_analyzer")

    # Create a handler if none exists
    if not self.logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    # Set log level based on environment
    self.logger.setLevel(logging.INFO)
    if os.environ.get("DEBUG", "").lower() in ("true", "1", "yes"):
        self.logger.setLevel(logging.DEBUG)
```

## 🧪 Testing Strategy

### Unit Tests

1. **🧩 Component Tests**: Test individual detection methods
   - Test embedding-based detection with known examples
   - Test keyword-based detection with various patterns
   - Test LLM-based detection with complex queries

2. **🔄 Integration Tests**: Test the complete analysis pipeline
   - Test end-to-end query analysis
   - Test with various query types and departments
   - Test error handling and fallbacks

### Evaluation Metrics

1. **📊 Accuracy Metrics**: Measure detection quality
   - Department detection accuracy
   - Section detection accuracy
   - Query type classification accuracy

2. **⏱️ Performance Metrics**: Measure efficiency
   - Analysis latency
   - Resource usage

### Test Implementation Examples

**Component Test for Query Type Detection**:
```python
import pytest
from app.rag.query_analyzer import QueryAnalyzer

class TestQueryTypeDetection:
    def setup_method(self):
        # Create a minimal QueryAnalyzer for testing
        self.analyzer = QueryAnalyzer(
            llm_adapter=None,  # Not needed for this test
            embedding_model=None  # Not needed for this test
        )

    @pytest.mark.parametrize("query,expected_type", [
        # Informational queries
        ("What is our marketing budget?", "informational"),
        ("How do we calculate ROI?", "informational"),
        ("Tell me about our Q2 performance", "informational"),
        ("Who is responsible for the campaign?", "informational"),

        # Analytical queries
        ("Why did our sales decrease last quarter?", "analytical"),
        ("What caused the increase in customer complaints?", "analytical"),
        ("Explain why the campaign underperformed", "analytical"),
        ("What's the reason for the budget change?", "analytical"),

        # Comparative queries
        ("Compare our performance to last year", "comparative"),
        ("What's the difference between these two strategies?", "comparative"),
        ("Pros and cons of increasing the marketing budget", "comparative"),
        ("Which approach is better for customer acquisition?", "comparative"),

        # Request queries
        ("Can you find the latest financial report?", "request"),
        ("Please show me the marketing metrics", "request"),
        ("Help me understand the quarterly results", "request"),
        ("Would you provide the sales figures?", "request"),

        # General queries
        ("Marketing budget", "general"),
        ("Financial report 2023", "general"),
        ("Customer acquisition", "general")
    ])
    def test_query_type_detection(self, query, expected_type):
        # Test that the query type is correctly detected
        detected_type = self.analyzer._detect_query_type(query)
        assert detected_type == expected_type, f"Expected {expected_type} for query: {query}, got {detected_type}"
```

**Integration Test for Department Detection**:
```python
import pytest
import asyncio
from unittest.mock import MagicMock, patch
from app.rag.query_analyzer import QueryAnalyzer

class TestDepartmentDetection:
    @pytest.fixture
    async def analyzer(self):
        # Create a mock LLM adapter
        mock_llm_adapter = MagicMock()
        mock_llm_adapter.chat.return_value = '{"finance": 0.8, "marketing": 0.3}'

        # Create a mock embedding model
        mock_embedding_model = MagicMock()
        mock_embedding_model.embed_query.return_value = [0.1] * 768  # Mock embedding vector

        # Create the analyzer with test data
        analyzer = QueryAnalyzer(
            llm_adapter=mock_llm_adapter,
            embedding_model=mock_embedding_model,
            department_descriptions={
                "finance": "The finance department handles budgeting and financial reporting.",
                "marketing": "The marketing department handles campaigns and brand management.",
                "hr": "The HR department handles employee relations and recruitment."
            }
        )

        # Mock the embedding-based detection to return known results
        async def mock_embedding_detection(query):
            if "budget" in query.lower():
                return [{"id": "finance", "confidence": 0.9, "method": "embedding"}]
            elif "campaign" in query.lower():
                return [{"id": "marketing", "confidence": 0.85, "method": "embedding"}]
            else:
                return []

        analyzer._embedding_based_department_detection = mock_embedding_detection

        # Initialize embeddings (normally done in __init__)
        analyzer.department_embeddings = {
            "finance": {"embedding": [0.1] * 768},
            "marketing": {"embedding": [0.2] * 768},
            "hr": {"embedding": [0.3] * 768}
        }

        return analyzer

    @pytest.mark.asyncio
    async def test_department_detection_finance_query(self, analyzer):
        # Test a finance-related query
        query = "What was our budget for Q2 2023?"
        results = await analyzer.detect_departments(query)

        # Check that finance is detected with high confidence
        assert len(results) > 0, "No departments detected"
        assert results[0]["id"] == "finance", f"Expected finance department, got {results[0]['id']}"
        assert results[0]["confidence"] > 0.7, f"Expected high confidence, got {results[0]['confidence']}"

    @pytest.mark.asyncio
    async def test_department_detection_marketing_query(self, analyzer):
        # Test a marketing-related query
        query = "How is our latest marketing campaign performing?"
        results = await analyzer.detect_departments(query)

        # Check that marketing is detected with high confidence
        assert len(results) > 0, "No departments detected"
        assert results[0]["id"] == "marketing", f"Expected marketing department, got {results[0]['id']}"
        assert results[0]["confidence"] > 0.7, f"Expected high confidence, got {results[0]['confidence']}"

    @pytest.mark.asyncio
    async def test_department_detection_ambiguous_query(self, analyzer):
        # Test an ambiguous query that could relate to multiple departments
        query = "What's our strategy for the next quarter?"

        # Mock the LLM-based detection for this specific test
        async def mock_llm_detection(query):
            return [
                {"id": "finance", "confidence": 0.6, "method": "llm"},
                {"id": "marketing", "confidence": 0.7, "method": "llm"}
            ]

        with patch.object(analyzer, '_llm_based_department_detection', mock_llm_detection):
            results = await analyzer.detect_departments(query)

        # Check that multiple departments are detected
        assert len(results) >= 2, "Expected multiple departments"
        departments = [result["id"] for result in results]
        assert "finance" in departments, "Finance department not detected"
        assert "marketing" in departments, "Marketing department not detected"
```

**Performance Test**:
```python
import pytest
import time
import asyncio
import statistics
from app.rag.query_analyzer import QueryAnalyzer

class TestQueryAnalyzerPerformance:
    @pytest.fixture
    async def analyzer(self):
        # Create the analyzer with real components for performance testing
        from app.core.llm.adapters.openai_adapter import OpenAIAdapter
        from app.rag.embedding_models.huggingface_embedding import HuggingFaceEmbedding

        llm_adapter = OpenAIAdapter()
        embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

        analyzer = QueryAnalyzer(
            llm_adapter=llm_adapter,
            embedding_model=embedding_model,
            department_descriptions={
                "finance": "The finance department handles budgeting, financial reporting, and financial analysis.",
                "marketing": "The marketing department handles campaigns, brand management, and customer acquisition.",
                "hr": "The HR department handles employee relations, recruitment, and workforce management."
            }
        )

        # Initialize embeddings
        await analyzer._initialize_department_embeddings()

        return analyzer

    @pytest.mark.asyncio
    async def test_analysis_latency(self, analyzer):
        # Test queries
        test_queries = [
            "What was our marketing budget for Q2 2023?",
            "Why did our customer acquisition cost increase last month?",
            "Compare our performance to industry benchmarks",
            "Can you provide the latest financial projections?",
            "Employee turnover rate in the sales department"
        ]

        # Measure analysis time for each query
        latencies = []
        for query in test_queries:
            start_time = time.time()
            result = await analyzer.analyze(query)
            end_time = time.time()

            latency = (end_time - start_time) * 1000  # Convert to ms
            latencies.append(latency)

            # Check that the analysis time in the result matches our measurement
            assert abs(result["analysis_time_ms"] - latency) < 10, "Analysis time mismatch"

        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        max_latency = max(latencies)
        min_latency = min(latencies)

        print(f"Average latency: {avg_latency:.2f}ms")
        print(f"Max latency: {max_latency:.2f}ms")
        print(f"Min latency: {min_latency:.2f}ms")

        # Assert performance requirements
        assert avg_latency < 500, f"Average latency ({avg_latency:.2f}ms) exceeds threshold (500ms)"
        assert max_latency < 1000, f"Max latency ({max_latency:.2f}ms) exceeds threshold (1000ms)"
```

**Accuracy Evaluation**:
```python
import pytest
import json
import pandas as pd
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from app.rag.query_analyzer import QueryAnalyzer

class TestQueryAnalyzerAccuracy:
    @pytest.fixture
    def test_data(self):
        # Load test data from JSON file
        with open("tests/data/query_analyzer_test_data.json", "r") as f:
            return json.load(f)

    @pytest.fixture
    async def analyzer(self):
        # Create the analyzer with real components
        from app.core.llm.adapters.openai_adapter import OpenAIAdapter
        from app.rag.embedding_models.huggingface_embedding import HuggingFaceEmbedding

        llm_adapter = OpenAIAdapter()
        embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

        analyzer = QueryAnalyzer(
            llm_adapter=llm_adapter,
            embedding_model=embedding_model,
            department_descriptions={
                "finance": "The finance department handles budgeting, financial reporting, and financial analysis.",
                "marketing": "The marketing department handles campaigns, brand management, and customer acquisition.",
                "hr": "The HR department handles employee relations, recruitment, and workforce management.",
                "operations": "The operations department handles logistics, supply chain, and production processes.",
                "sales": "The sales department handles customer relationships, sales strategies, and revenue generation."
            }
        )

        # Initialize embeddings
        await analyzer._initialize_department_embeddings()

        return analyzer

    @pytest.mark.asyncio
    async def test_department_detection_accuracy(self, analyzer, test_data):
        # Extract department detection test data
        department_tests = test_data["department_detection"]

        # Analyze each query and compare to expected departments
        true_departments = []
        pred_departments = []

        for test in department_tests:
            query = test["query"]
            expected_departments = set(test["expected_departments"])

            # Analyze query
            results = await analyzer.detect_departments(query)
            detected_departments = set(result["id"] for result in results)

            # Store results for metrics calculation
            true_departments.append(expected_departments)
            pred_departments.append(detected_departments)

            # Print individual results
            print(f"Query: {query}")
            print(f"Expected: {expected_departments}")
            print(f"Detected: {detected_departments}")
            print()

        # Calculate accuracy metrics
        correct = sum(1 for true, pred in zip(true_departments, pred_departments)
                     if true == pred)
        accuracy = correct / len(department_tests)

        # Calculate partial match metrics
        partial_matches = sum(1 for true, pred in zip(true_departments, pred_departments)
                             if true.intersection(pred))
        partial_accuracy = partial_matches / len(department_tests)

        print(f"Department detection exact match accuracy: {accuracy:.2f}")
        print(f"Department detection partial match accuracy: {partial_accuracy:.2f}")

        # Assert minimum accuracy requirements
        assert partial_accuracy >= 0.8, f"Partial match accuracy ({partial_accuracy:.2f}) below threshold (0.8)"

    @pytest.mark.asyncio
    async def test_query_type_accuracy(self, analyzer, test_data):
        # Extract query type test data
        query_type_tests = test_data["query_type_classification"]

        # Analyze each query and compare to expected type
        y_true = []
        y_pred = []

        for test in query_type_tests:
            query = test["query"]
            expected_type = test["expected_type"]

            # Analyze query
            result = await analyzer.analyze(query)
            detected_type = result["query_type"]

            # Store results for metrics calculation
            y_true.append(expected_type)
            y_pred.append(detected_type)

            # Print individual results
            print(f"Query: {query}")
            print(f"Expected type: {expected_type}")
            print(f"Detected type: {detected_type}")
            print()

        # Calculate accuracy metrics
        accuracy = accuracy_score(y_true, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_true, y_pred, average='weighted'
        )

        print(f"Query type classification accuracy: {accuracy:.2f}")
        print(f"Query type classification precision: {precision:.2f}")
        print(f"Query type classification recall: {recall:.2f}")
        print(f"Query type classification F1 score: {f1:.2f}")

        # Assert minimum accuracy requirements
        assert accuracy >= 0.75, f"Accuracy ({accuracy:.2f}) below threshold (0.75)"
        assert f1 >= 0.7, f"F1 score ({f1:.2f}) below threshold (0.7)"
```

## 🔮 Future Enhancements

1. **🧠 Advanced Context Handling**: Improve conversation context integration
   - Track entities across conversation turns
   - Maintain topic coherence
   - Handle context switches

2. **🔍 Multi-Intent Detection**: Handle queries with multiple intents
   - Identify compound queries
   - Split and process multiple intents
   - Combine results appropriately

3. **📊 Confidence Calibration**: Improve confidence score reliability
   - Calibrate confidence scores with feedback
   - Adjust thresholds based on performance
   - Implement adaptive weighting
