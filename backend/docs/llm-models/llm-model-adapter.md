# 📚 LLM Adapter Handbook

This handbook provides an in-depth guide for understanding, using, extending, and maintaining the `llm.py` adapter module. It is designed to support developers who want to integrate multiple LLM providers (OpenAI, Anthropic) under a unified interface, while also helping beginner-to-intermediate Python users build a deep understanding of the architecture and implementation.

> **Note**: This documentation serves as a comprehensive reference for the LLM adapter implementation in `backend/app/core/llm.py`. It covers both usage patterns and implementation details to provide a complete understanding of the system.

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🛠️ Getting Started](#️-getting-started)
   - [Installation](#installation)
   - [Basic Usage](#basic-usage)
   - [Configuration](#configuration)
3. [💡 Core Concepts](#-core-concepts)
   - [The Adapter Pattern](#the-adapter-pattern)
   - [Message Formats](#message-formats)
   - [Token Counting](#token-counting)
   - [Streaming Responses](#streaming-responses)
   - [Retry Logic](#retry-logic)
4. [🧩 Adapter Implementations](#-adapter-implementations)
   - [Base Adapter](#base-adapter)
   - [OpenAI Adapter](#openai-adapter)
   - [Anthropic Adapter](#anthropic-adapter)
   - [Mock Adapter](#mock-adapter)
5. [📊 Feature Comparison](#-feature-comparison)
6. [🔌 Factory Pattern](#-factory-pattern)
7. [📝 API Reference](#-api-reference)
8. [📋 Complete Examples](#-complete-examples)
9. [⚠️ Troubleshooting](#️-troubleshooting)
10. [🔒 Security & Performance](#-security--performance)
11. [🔄 Extending the System](#-extending-the-system)
12. [🧪 Testing and Mocking](#-testing-and-mocking)
13. [📖 Glossary of Terms](#-glossary-of-terms)
14. [🔍 Code Walkthrough](#-code-walkthrough)

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🔍 Overview

The LLM Adapter provides a modular and extensible architecture to interface with various LLM APIs (e.g., OpenAI and Anthropic). The goal is to abstract away provider-specific logic while supporting streaming, retry logic, and token estimation.

### Key Features

- **Unified Interface**: Consistent API across different LLM providers
- **Streaming Support**: Real-time response streaming for better UX
- **Token Counting**: Accurate token estimation for cost management
- **Retry Logic**: Automatic retries for transient errors
- **Fallback Mechanism**: Graceful degradation when providers are unavailable
- **Type Safety**: Comprehensive type hints for better IDE support
- **Extensibility**: Easy to add new providers

### Architecture

The adapter follows a classic adapter pattern with:
- Abstract base class (`LLMAdapter`) defining the interface
- Concrete implementations for each provider
- Factory function for creating adapters
- Utility functions for metrics and logging

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🛠️ Getting Started

### Installation

To use the LLM adapter, you need to install the required dependencies:

```bash
# Install the core dependencies
pip install openai anthropic google-generativeai tiktoken tenacity

# Or using uv (recommended)
uv pip install openai anthropic google-generativeai tiktoken tenacity
```

> **Note**: Not all dependencies are required for all adapters. The system will gracefully handle missing dependencies using lazy imports and fallback mechanisms.

### Basic Usage

Here's a simple example of how to use the LLM adapter:

```python
import asyncio
from backend.app.core.llm import get_llm_adapter

async def main():
    # Create an adapter for OpenAI
    adapter = get_llm_adapter(
        provider="openai",
        api_key="your-api-key",
        model="gpt-4"
    )

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Get a response
    response = await adapter.chat(messages, temperature=0.7)
    print(response)

# Run the async function
asyncio.run(main())
```

### Configuration

You can configure the adapters using environment variables:

```bash
# Set API keys
export OPENAI_API_KEY=your-openai-key
export ANTHROPIC_API_KEY=your-anthropic-key

# Set other configuration options
export OPENAI_ORGANIZATION=your-org-id
```

Or pass configuration directly when creating the adapter:

```python
adapter = get_llm_adapter(
    provider="openai",
    api_key="your-api-key",
    model="gpt-4",
    organization="your-org-id",
    max_tokens=500,
    base_url="https://your-proxy.example.com",
    api_version="2023-05-15"  # For Azure OpenAI
)
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 💡 Core Concepts

### The Adapter Pattern

The adapter pattern is a structural design pattern that allows objects with incompatible interfaces to collaborate. In this implementation:

- `LLMAdapter` defines a common interface
- Concrete adapters (OpenAI, Anthropic) implement this interface
- Clients interact with the common interface, not the specific implementations

This allows for easy switching between providers and adding new providers without changing client code.

### Message Formats

The adapter uses a standard message format defined by the `ChatMessage` TypedDict:

```python
class ChatMessage(TypedDict):
    """Standard chat message format."""
    role: Literal["user", "assistant", "system"]
    content: str
```

This format is compatible with OpenAI's API and is converted to the appropriate format for other providers (like Anthropic) internally.

### Token Counting

Token counting is used to:
- Estimate API costs (providers charge per token)
- Ensure you don't exceed context limits
- Optimize prompt design

The `get_token_count` method returns a tuple of `(prompt_tokens, estimated_response_tokens)`:

```python
prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)
total_tokens = prompt_tokens + estimated_response_tokens
```

### Streaming Responses

Streaming allows you to receive responses in real-time, which is useful for:
- Providing a better user experience
- Handling long responses more efficiently
- Implementing features like typewriter effects

To use streaming:

```python
async for chunk in await adapter.chat(messages, stream=True):
    print(chunk, end="", flush=True)
```

### Retry Logic

The adapter includes retry logic for handling transient errors:
- Rate limits
- Timeouts
- Connection issues

This is implemented using the `tenacity` library with exponential backoff and jitter.

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🧩 Adapter Implementations

<details>
<summary><strong>Base Adapter</strong> - Click to expand</summary>

### Base Adapter Class

```python
class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""

    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Send a chat request to the LLM."""
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        pass

    @property
    def client(self):
        """Get the underlying client instance."""
        return getattr(self, "_client", None)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return False
```

The `LLMAdapter` abstract base class defines the interface that all adapters must implement. It includes:

- `chat()`: Send a chat request to the LLM
- `get_token_count()`: Count the number of tokens in the messages
- `client`: Property to access the underlying client
- `supports_streaming`: Property to check if streaming is supported

</details>

<details>
<summary><strong>OpenAI Adapter</strong> - Click to expand</summary>

### OpenAI Adapter

```python
class OpenAIAdapter(LLMAdapter):
    """Adapter for OpenAI's GPT models."""

    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4",
        organization: Optional[str] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        api_version: Optional[str] = None,
        **kwargs,
    ):
        """Initialize the OpenAI adapter."""
        # Implementation details...
```

The `OpenAIAdapter` provides an implementation for OpenAI's GPT models. It includes:

- Support for GPT-3.5, GPT-4, and other OpenAI models
- Support for Azure OpenAI via `base_url` and `api_version` parameters
- Token counting using tiktoken
- Streaming support
- Retry logic for handling transient errors

</details>

<details>
<summary><strong>Anthropic Adapter</strong> - Click to expand</summary>

### Anthropic Adapter

```python
class AnthropicAdapter(LLMAdapter):
    """Adapter for Anthropic's Claude models."""

    def __init__(
        self,
        api_key: str,
        model: str = "claude-3-opus-20240229",
        max_tokens: Optional[int] = None,
        **kwargs,
    ):
        """Initialize the Anthropic adapter."""
        # Implementation details...
```

The `AnthropicAdapter` provides an implementation for Anthropic's Claude models. It includes:

- Support for Claude 3 and other Anthropic models
- Token counting using the Anthropic SDK
- Streaming support
- System message handling (Anthropic doesn't have a native system role)
- Retry logic for handling transient errors

</details>

<details>
<summary><strong>Mock Adapter</strong> - Click to expand</summary>

### Mock Adapter

```python
class MockAdapter(LLMAdapter):
    """Mock adapter for testing."""

    def __init__(self, response: str = "This is a mock response.", **kwargs):
        """Initialize the mock adapter."""
        # Implementation details...
```

The `MockAdapter` is a simple implementation for testing. It includes:

- Simulated responses for testing
- Simulated token counting
- Support for both streaming and non-streaming requests
- No actual API calls are made

</details>

> **Important:** All adapter implementations must inherit from the `LLMAdapter` base class and implement all abstract methods.

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 📊 Feature Comparison

| Feature | OpenAI | Anthropic | Gemini | Mock |
|---------|--------|-----------|--------|------|
| **Streaming** | ✅ | ✅ | ✅ | ✅ |
| **Token Counting** | tiktoken | SDK | Character-based | Heuristic |
| **System Messages** | Native | Converted | Converted | Simulated |
| **Retry Logic** | ✅ | ✅ | ✅ | ❌ |
| **Azure Support** | ✅ | ❌ | ❌ | ❌ |
| **Max Tokens** | ✅ | ✅ | ✅ | ✅ |
| **Temperature** | ✅ | ✅ | ✅ | ❌ |
| **Client Access** | ✅ | ✅ | ✅ | ❌ |
| **Lazy Imports** | ❌ | ❌ | ✅ | ❌ |

### Token Counting Comparison

| Provider | Primary Method | Fallback Method | Accuracy |
|----------|---------------|-----------------|----------|
| OpenAI | tiktoken | Character-based | High |
| Anthropic | SDK | Character-based | High |
| Gemini | Character-based | N/A | Medium |
| Mock | N/A | Character-based | Low |

### System Message Handling

| Provider | Native Support | Implementation |
|----------|---------------|----------------|
| OpenAI | ✅ | Uses native system messages |
| Anthropic | ❌ | Prepends to first user message with "System: " prefix |
| Gemini | ❌ | Converted to chat history format |
| Mock | ✅ | Simulated support |

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🔌 Factory Pattern and Adapter Registry

The adapter uses a factory pattern and a dynamic registry to create instances of the appropriate adapter:

```python
# Global registry of available adapters
ADAPTER_REGISTRY: Dict[str, Type[LLMAdapter]] = {}

def register_adapter(name: str, adapter_class: Type[LLMAdapter]) -> None:
    """Register an adapter class with the registry."""
    ADAPTER_REGISTRY[name] = adapter_class

def get_llm_adapter(
    provider: str = "openai", fallback: bool = True, **kwargs
) -> LLMAdapter:
    """Get an LLM adapter for the specified provider."""
    # Implementation details...
```

### Basic Usage

```python
adapter = get_llm_adapter(provider="openai", api_key="your-api-key")
```

### Fallback Mechanism

If the requested provider is not available and `fallback=True`, the factory will try to fall back to an available provider:

1. Try each registered adapter in order (except the one that failed)
2. Skip adapters whose dependencies are not available
3. Finally fall back to the mock adapter

### Factory Map

You can also get a dictionary of available adapter factories:

```python
def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """Get a dictionary of available LLM adapter factory functions."""
    # Register built-in adapters if they haven't been registered yet
    if "openai" not in ADAPTER_REGISTRY and OPENAI_AVAILABLE:
        register_adapter("openai", OpenAIAdapter)
    if "anthropic" not in ADAPTER_REGISTRY and ANTHROPIC_AVAILABLE:
        register_adapter("anthropic", AnthropicAdapter)
    if "gemini" not in ADAPTER_REGISTRY and GEMINI_AVAILABLE:
        register_adapter("gemini", GeminiAdapter)
    if "mock" not in ADAPTER_REGISTRY:
        register_adapter("mock", MockAdapter)

    # Return a copy of the registry to prevent modification
    return ADAPTER_REGISTRY.copy()
```

This can be used for dynamic loading of adapters:

```python
factory = get_llm_adapter_factory()
adapter_class = factory["openai"]
adapter = adapter_class(api_key="your-api-key")
```

### Registering Custom Adapters

You can register your own custom adapters:

```python
from backend.app.core.llm import register_adapter, LLMAdapter

class MyCustomAdapter(LLMAdapter):
    """Custom adapter implementation."""
    # Implement required methods...

# Register the custom adapter
register_adapter("custom", MyCustomAdapter)

# Use the custom adapter
adapter = get_llm_adapter("custom")
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>
## 📝 API Reference

This section provides a formal API reference for all classes and methods in the LLM adapter implementation.

### LLMAdapter

```python
class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""

    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to the LLM."""
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        pass

    @property
    def client(self):
        """Get the underlying client instance."""
        return getattr(self, "_client", None)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return False

    def get_stream_flag(self, stream: bool) -> bool:
        """Get the appropriate stream flag based on adapter capabilities."""
        return stream and self.supports_streaming
```

#### Methods

| Method | Description | Parameters | Return Type |
|--------|-------------|------------|-------------|
| `chat` | Send a chat request to the LLM | `messages`: List of message dictionaries<br>`temperature`: Controls randomness (0.0-1.0)<br>`max_tokens`: Maximum tokens to generate<br>`stream`: Whether to stream the response | If `stream=False`: `str`<br>If `stream=True`: `AsyncIterator[str]` |
| `get_token_count` | Count tokens in messages | `messages`: List of message dictionaries | `Tuple[int, int]` (prompt_tokens, estimated_response_tokens) |
| `get_stream_flag` | Get appropriate stream flag | `stream`: Whether streaming was requested | `bool` (True if streaming is supported and requested) |

#### Properties

| Property | Description | Return Type |
|----------|-------------|-------------|
| `client` | Get the underlying client instance | The underlying client or `None` |
| `supports_streaming` | Whether streaming is supported | `bool` |

### OpenAIAdapter

```python
class OpenAIAdapter(LLMAdapter):
    """Adapter for OpenAI's GPT models."""

    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4",
        organization: Optional[str] = None,
        max_tokens: Optional[int] = None,
        base_url: Optional[str] = None,
        api_version: Optional[str] = None,
        **kwargs,
    ):
        """Initialize the OpenAI adapter."""
        pass
```

#### Constructor Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `api_key` | `str` | OpenAI API key | Required |
| `model` | `str` | Model to use | `"gpt-4"` |
| `organization` | `Optional[str]` | OpenAI organization ID | `None` |
| `max_tokens` | `Optional[int]` | Default max tokens to generate | `None` |
| `base_url` | `Optional[str]` | Base URL for API requests | `None` |
| `api_version` | `Optional[str]` | API version (for Azure) | `None` |
| `**kwargs` | | Additional arguments for the client | |

### AnthropicAdapter

```python
class AnthropicAdapter(LLMAdapter):
    """Adapter for Anthropic's Claude models."""

    def __init__(
        self,
        api_key: str,
        model: str = "claude-3-opus-20240229",
        max_tokens: Optional[int] = None,
        **kwargs,
    ):
        """Initialize the Anthropic adapter."""
        pass
```

#### Constructor Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `api_key` | `str` | Anthropic API key | Required |
| `model` | `str` | Model to use | `"claude-3-opus-20240229"` |
| `max_tokens` | `Optional[int]` | Default max tokens to generate | `None` |
| `**kwargs` | | Additional arguments for the client | |

### MockAdapter

```python
class MockAdapter(LLMAdapter):
    """Mock adapter for testing."""

    def __init__(self, response: str = "This is a mock response.", **kwargs):
        """Initialize the mock adapter."""
        pass
```

#### Constructor Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `response` | `str` | The response to return | `"This is a mock response."` |
| `**kwargs` | | Additional arguments (ignored) | |

### Factory and Registry Functions

```python
# Global registry of available adapters
ADAPTER_REGISTRY: Dict[str, Type[LLMAdapter]] = {}

def register_adapter(name: str, adapter_class: Type[LLMAdapter]) -> None:
    """Register an adapter class with the registry."""
    pass

def get_llm_adapter(
    provider: str = "openai", fallback: bool = True, **kwargs
) -> LLMAdapter:
    """Get an LLM adapter for the specified provider."""
    pass

def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """Get a dictionary of available LLM adapter factory functions."""
    pass
```

#### Parameters

| Function | Parameter | Type | Description | Default |
|----------|-----------|------|-------------|---------|
| `register_adapter` | `name` | `str` | The name to register the adapter under | Required |
| `register_adapter` | `adapter_class` | `Type[LLMAdapter]` | The adapter class to register | Required |
| `get_llm_adapter` | `provider` | `str` | The LLM provider to use | `"openai"` |
| `get_llm_adapter` | `fallback` | `bool` | Whether to fall back to other providers | `True` |
| `get_llm_adapter` | `**kwargs` | | Additional arguments for the adapter | |

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 📋 Complete Examples

This section provides complete, working examples that can be copied and run.

### Basic Chat Example

```python
import asyncio
import os
from backend.app.core.llm import get_llm_adapter

async def basic_chat_example():
    # Get API key from environment variable
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("Error: OPENAI_API_KEY environment variable not set")
        return

    # Create an adapter
    adapter = get_llm_adapter(
        provider="openai",
        api_key=api_key,
        model="gpt-4"
    )

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Get a response
    try:
        response = await adapter.chat(messages, temperature=0.7)
        print("Response:")
        print(response)

        # Count tokens
        prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)
        print(f"\nPrompt tokens: {prompt_tokens}")
        print(f"Estimated response tokens: {estimated_response_tokens}")
        print(f"Total estimated tokens: {prompt_tokens + estimated_response_tokens}")
    except Exception as e:
        print(f"Error: {e}")

# Run the async function
if __name__ == "__main__":
    asyncio.run(basic_chat_example())
```

### Streaming Example

```python
import asyncio
import os
from backend.app.core.llm import get_llm_adapter

async def streaming_example():
    # Get API key from environment variable
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("Error: OPENAI_API_KEY environment variable not set")
        return

    # Create an adapter
    adapter = get_llm_adapter(
        provider="openai",
        api_key=api_key,
        model="gpt-4"
    )

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Write a short story about a robot."}
    ]

    # Check if streaming is supported
    if not adapter.supports_streaming:
        print("Streaming not supported by this adapter")
        return

    # Get a streaming response
    try:
        print("Response:")
        async for chunk in await adapter.chat(messages, stream=True):
            print(chunk, end="", flush=True)
        print()  # Add a newline at the end
    except Exception as e:
        print(f"\nError: {e}")

# Run the async function
if __name__ == "__main__":
    asyncio.run(streaming_example())
```

### Multiple Providers Example

```python
import asyncio
import os
from backend.app.core.llm import get_llm_adapter, LLM_DEPENDENCIES

async def multiple_providers_example():
    # Check which providers are available
    print("Available providers:")
    for provider, available in LLM_DEPENDENCIES.items():
        print(f"- {provider}: {'✓' if available else '✗'}")
    print()

    # Get API keys from environment variables
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Try OpenAI
    if LLM_DEPENDENCIES["openai"] and openai_api_key:
        print("Using OpenAI:")
        try:
            openai_adapter = get_llm_adapter(
                provider="openai",
                api_key=openai_api_key,
                model="gpt-4"
            )
            response = await openai_adapter.chat(messages)
            print(response)
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("OpenAI not available or API key not set")

    print()

    # Try Anthropic
    if LLM_DEPENDENCIES["anthropic"] and anthropic_api_key:
        print("Using Anthropic:")
        try:
            anthropic_adapter = get_llm_adapter(
                provider="anthropic",
                api_key=anthropic_api_key,
                model="claude-3-opus-20240229"
            )
            response = await anthropic_adapter.chat(messages)
            print(response)
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Anthropic not available or API key not set")

    print()

    # Always try Mock (no API key needed)
    print("Using Mock:")
    mock_adapter = get_llm_adapter(provider="mock")
    response = await mock_adapter.chat(messages)
    print(response)

# Run the async function
if __name__ == "__main__":
    asyncio.run(multiple_providers_example())
```

### Factory Pattern Example

```python
import asyncio
import os
from backend.app.core.llm import get_llm_adapter_factory

async def factory_pattern_example():
    # Get the factory dictionary
    factory = get_llm_adapter_factory()

    # Print available providers
    print("Available providers:")
    for provider in factory:
        print(f"- {provider}")
    print()

    # Get API key from environment variable
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("Warning: OPENAI_API_KEY environment variable not set, using mock adapter")
        provider = "mock"
    else:
        provider = "openai"

    # Create an adapter dynamically
    if provider in factory:
        adapter_class = factory[provider]

        # Create the adapter with appropriate parameters
        if provider == "openai":
            adapter = adapter_class(api_key=api_key, model="gpt-4")
        else:  # mock
            adapter = adapter_class()

        # Define the messages
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ]

        # Get a response
        try:
            response = await adapter.chat(messages)
            print(f"Response from {provider}:")
            print(response)
        except Exception as e:
            print(f"Error: {e}")

# Run the async function
if __name__ == "__main__":
    asyncio.run(factory_pattern_example())
```

### Error Handling Example

```python
import asyncio
from backend.app.core.llm import get_llm_adapter

async def error_handling_example():
    # Try to create an adapter with an invalid API key
    adapter = get_llm_adapter(
        provider="openai",
        api_key="invalid-api-key",
        model="gpt-4"
    )

    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    try:
        print("Sending request with invalid API key...")
        response = await adapter.chat(messages)
        print("Response:")
        print(response)
    except Exception as e:
        print(f"Error caught: {e}")
        print("This is expected behavior with an invalid API key")

# Run the async function
if __name__ == "__main__":
    asyncio.run(error_handling_example())
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>
## ⚠️ Troubleshooting

This section provides solutions for common issues you might encounter when using the LLM adapter.

### Common Issues and Solutions

| Issue | Possible Causes | Solutions |
|-------|----------------|-----------|
| **"ImportError: OpenAI package not installed"** | The OpenAI package is not installed | Run `pip install openai` or `uv pip install openai` |
| **"ImportError: Anthropic package not installed"** | The Anthropic package is not installed | Run `pip install anthropic` or `uv pip install anthropic` |
| **"ImportError: Google Generative AI package not installed"** | The Google Generative AI package is not installed | Run `pip install google-generativeai` or `uv pip install google-generativeai` |
| **Empty response** | Malformed messages<br>Model limitations | Check message format<br>Try a different model<br>Check max_tokens parameter |
| **Streaming not working** | Model doesn't support streaming<br>Adapter doesn't support streaming | Check `adapter.supports_streaming`<br>Use a model that supports streaming |
| **Token counting errors** | Missing tiktoken or SDK | Install tiktoken (`pip install tiktoken`)<br>The adapter will fall back to a character-based heuristic |
| **Rate limit errors** | Too many requests<br>Exceeded quota | Implement rate limiting in your application<br>Check your usage on the provider's dashboard |
| **Authentication errors** | Invalid API key<br>Expired API key | Check your API key<br>Regenerate your API key on the provider's dashboard |
| **Timeout errors** | Request took too long<br>Network issues | Increase timeout settings<br>Check your network connection |

### Debugging Flow

If you encounter issues, follow this debugging flow:

1. **Check Dependencies**
   ```python
   from backend.app.core.llm import LLM_DEPENDENCIES
   print(LLM_DEPENDENCIES)
   ```

2. **Verify API Keys**
   ```python
   import os
   print(f"OpenAI API Key set: {'Yes' if os.environ.get('OPENAI_API_KEY') else 'No'}")
   print(f"Anthropic API Key set: {'Yes' if os.environ.get('ANTHROPIC_API_KEY') else 'No'}")
   print(f"Gemini API Key set: {'Yes' if os.environ.get('GEMINI_API_KEY') else 'No'}")
   ```

3. **Test with Mock Adapter**
   ```python
   adapter = get_llm_adapter(provider="mock")
   response = await adapter.chat([{"role": "user", "content": "Test"}])
   print(response)
   ```

4. **Enable Verbose Logging**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

5. **Check Client Access**
   ```python
   adapter = get_llm_adapter(provider="openai", api_key="your-key")
   client = adapter.client
   print(f"Client available: {client is not None}")
   ```

### Error Messages and Their Meanings

| Error Message | Meaning | Solution |
|---------------|---------|----------|
| `"Provider 'X' not available and fallback is disabled"` | The requested provider is not available and fallback is disabled | Install the required package or enable fallback |
| `"OpenAI API error: 401 - Incorrect API key provided"` | Invalid OpenAI API key | Check your API key |
| `"OpenAI API error: 429 - Rate limit exceeded"` | Too many requests to OpenAI | Implement rate limiting or wait before retrying |
| `"Anthropic API error: 401 - Invalid API key"` | Invalid Anthropic API key | Check your API key |
| `"Anthropic API error: 429 - Too many requests"` | Too many requests to Anthropic | Implement rate limiting or wait before retrying |
| `"Tenacity not installed, retries disabled"` | The tenacity package is not installed | Install tenacity (`pip install tenacity`) |
| `"Multiple system messages found (X). Only the first one will be used."` | Multiple system messages in the input | Use only one system message |

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🔒 Security & Performance

This section covers security best practices and performance considerations when using the LLM adapter.

### API Key Management

API keys should be treated as sensitive credentials:

- **Never hardcode API keys** in your source code
- Use environment variables or a secure secrets manager
- Rotate API keys regularly
- Use different API keys for development and production
- Consider using API key prefixes to identify the source of requests

Example using environment variables:
```python
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get API keys from environment variables
openai_api_key = os.environ.get("OPENAI_API_KEY")
anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")

# Create adapters
openai_adapter = get_llm_adapter(provider="openai", api_key=openai_api_key)
anthropic_adapter = get_llm_adapter(provider="anthropic", api_key=anthropic_api_key)
```

### Rate Limiting and Cost Control

To control costs and avoid rate limits:

- Implement client-side rate limiting
- Set up usage alerts on the provider's dashboard
- Use token counting to estimate costs before sending requests
- Consider implementing a token budget system
- Cache responses for identical or similar requests

Example implementing a simple token budget:
```python
class TokenBudget:
    def __init__(self, max_tokens_per_minute=100000):
        self.max_tokens_per_minute = max_tokens_per_minute
        self.tokens_used = 0
        self.reset_time = time.time() + 60

    async def check_budget(self, adapter, messages):
        # Reset budget if a minute has passed
        if time.time() > self.reset_time:
            self.tokens_used = 0
            self.reset_time = time.time() + 60

        # Estimate token usage
        prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)
        estimated_total = prompt_tokens + estimated_response_tokens

        # Check if we have enough budget
        if self.tokens_used + estimated_total > self.max_tokens_per_minute:
            raise Exception("Token budget exceeded")

        # Update budget
        self.tokens_used += estimated_total
        return True
```

### Performance Optimization

To optimize performance:

- **Client Reuse**: The adapter creates a client once in `__init__` for better performance
- **Async Operations**: Use the async methods to avoid blocking the event loop
- **Batch Requests**: Consider batching multiple requests when possible
- **Streaming**: Use streaming for long responses to start processing earlier
- **Caching**: Implement a caching layer for identical requests

Example implementing a simple cache:
```python
import hashlib
import json

class SimpleCache:
    def __init__(self, max_size=100):
        self.cache = {}
        self.max_size = max_size

    def _get_key(self, messages, temperature, max_tokens):
        # Create a cache key from the request parameters
        data = json.dumps({
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }, sort_keys=True)
        return hashlib.md5(data.encode()).hexdigest()

    async def get_or_set(self, adapter, messages, temperature=0.7, max_tokens=None):
        key = self._get_key(messages, temperature, max_tokens)

        # Return cached response if available
        if key in self.cache:
            return self.cache[key]

        # Get new response
        response = await adapter.chat(messages, temperature, max_tokens)

        # Cache the response
        if len(self.cache) >= self.max_size:
            # Remove a random item if cache is full
            self.cache.pop(next(iter(self.cache)))
        self.cache[key] = response

        return response
```

### Proxy Configuration

For enterprise environments, you may want to use a proxy:

- **OpenAI**: Use the `base_url` parameter
- **Anthropic**: Use the `base_url` parameter (if supported by the SDK)

Example using a proxy:
```python
# OpenAI with proxy
openai_adapter = get_llm_adapter(
    provider="openai",
    api_key="your-api-key",
    base_url="https://your-proxy.example.com/v1"
)

# Azure OpenAI
azure_adapter = get_llm_adapter(
    provider="openai",
    api_key="your-azure-key",
    base_url="https://your-resource.openai.azure.com",
    api_version="2023-05-15",
    model="deployment-name"
)
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🔄 Extending the System

This section covers how to extend the LLM adapter system with new providers or custom functionality.

### Adding a New Provider

To add a new provider:

1. Create a new class that inherits from `LLMAdapter`
2. Implement the required methods: `chat()` and `get_token_count()`
3. Add the new adapter to the factory function

Example adding a new provider:

```python
class NewProviderAdapter(LLMAdapter):
    """Adapter for a new LLM provider."""

    def __init__(self, api_key: str, model: str = "default-model", **kwargs):
        """Initialize the new provider adapter."""
        self.api_key = api_key
        self.model = model

        # Create the client
        self._client = NewProviderClient(api_key=api_key, **kwargs)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return True

    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Send a chat request to the new provider."""
        if stream:
            return self._chat_streaming(messages, temperature, max_tokens)
        else:
            return await self._chat_normal(messages, temperature, max_tokens)

    async def _chat_normal(self, messages, temperature, max_tokens):
        """Send a non-streaming chat request."""
        # Implement the non-streaming chat request
        response = await self._client.generate(
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return response.text

    async def _chat_streaming(self, messages, temperature, max_tokens):
        """Send a streaming chat request."""
        # Implement the streaming chat request
        stream = await self._client.generate_stream(
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )

        async for chunk in stream:
            yield chunk.text

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        # Implement token counting
        try:
            prompt_tokens = await self._client.count_tokens(messages)
            estimated_response_tokens = min(500, prompt_tokens)
            return prompt_tokens, estimated_response_tokens
        except Exception as e:
            logger.warning(f"Error counting tokens: {e}")
            # Fall back to character-based estimation
            prompt_chars = sum(len(m.get("content", "")) for m in messages)
            prompt_tokens = prompt_chars // 4
            estimated_response_tokens = min(500, prompt_tokens)
            return prompt_tokens, estimated_response_tokens
```

Then add it to the factory function:

```python
def get_llm_adapter_factory() -> Dict[str, Callable[..., LLMAdapter]]:
    """Get a dictionary of available LLM adapter factory functions."""
    return {
        "openai": OpenAIAdapter,
        "anthropic": AnthropicAdapter,
        "mock": MockAdapter,
        "new_provider": NewProviderAdapter  # Add the new provider
    }

def get_llm_adapter(provider: str = "openai", fallback: bool = True, **kwargs) -> LLMAdapter:
    """Get an LLM adapter for the specified provider."""
    factory = get_llm_adapter_factory()

    if provider in factory:
        try:
            return factory[provider](**kwargs)
        except ImportError:
            if not fallback:
                raise

    # Fallback logic...
```

### Custom Adapters

You can also create custom adapters that add functionality to existing adapters:

```python
class LoggingAdapter(LLMAdapter):
    """Adapter that logs all requests and responses."""

    def __init__(self, base_adapter: LLMAdapter):
        """Initialize the logging adapter."""
        self.base_adapter = base_adapter

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return self.base_adapter.supports_streaming

    @property
    def client(self):
        """Get the underlying client instance."""
        return self.base_adapter.client

    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Send a chat request to the LLM."""
        logger.info(f"Chat request: {len(messages)} messages, temperature={temperature}, max_tokens={max_tokens}, stream={stream}")

        if stream:
            return self._streaming_wrapper(messages, temperature, max_tokens)

        start_time = time.time()
        response = await self.base_adapter.chat(messages, temperature, max_tokens, stream=False)
        elapsed = time.time() - start_time

        logger.info(f"Chat response: {len(response)} chars, elapsed={elapsed:.2f}s")
        return response

    async def _streaming_wrapper(self, messages, temperature, max_tokens):
        """Wrap the streaming response with logging."""
        logger.info("Starting streaming response")
        start_time = time.time()
        chunk_count = 0
        total_chars = 0

        async for chunk in await self.base_adapter.chat(messages, temperature, max_tokens, stream=True):
            chunk_count += 1
            total_chars += len(chunk)
            yield chunk

        elapsed = time.time() - start_time
        logger.info(f"Streaming complete: {chunk_count} chunks, {total_chars} chars, elapsed={elapsed:.2f}s")

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        prompt_tokens, estimated_response_tokens = await self.base_adapter.get_token_count(messages)
        logger.info(f"Token count: prompt={prompt_tokens}, estimated_response={estimated_response_tokens}")
        return prompt_tokens, estimated_response_tokens
```

Usage:
```python
base_adapter = get_llm_adapter(provider="openai", api_key="your-api-key")
logging_adapter = LoggingAdapter(base_adapter)
response = await logging_adapter.chat(messages)
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🧪 Testing and Mocking

This section covers how to test code that uses the LLM adapter.

### Using the Mock Adapter

The `MockAdapter` is designed for testing:

```python
from backend.app.core.llm import get_llm_adapter

# Create a mock adapter
adapter = get_llm_adapter(provider="mock", response="This is a mock response.")

# Use it like a real adapter
response = await adapter.chat([{"role": "user", "content": "Hello"}])
assert response == "This is a mock response."
```

### Custom Mock Responses

You can customize the mock response:

```python
# Create a mock adapter with a custom response
adapter = get_llm_adapter(
    provider="mock",
    response="I'm a custom mock response."
)

# Use it like a real adapter
response = await adapter.chat([{"role": "user", "content": "Hello"}])
assert response == "I'm a custom mock response."
```

### Testing with pytest

Example using pytest to test code that uses the LLM adapter:

```python
import pytest
from backend.app.core.llm import get_llm_adapter

@pytest.fixture
def mock_adapter():
    """Create a mock adapter for testing."""
    return get_llm_adapter(provider="mock", response="Mock response")

async def test_chat_function(mock_adapter):
    """Test a function that uses the LLM adapter."""
    # Define a function that uses the adapter
    async def chat_function(adapter, query):
        messages = [{"role": "user", "content": query}]
        return await adapter.chat(messages)

    # Test the function with the mock adapter
    response = await chat_function(mock_adapter, "Hello")
    assert response == "Mock response"
```

### Mocking the Factory

You can also mock the factory function:

```python
import pytest
from unittest.mock import patch
from backend.app.core.llm import get_llm_adapter, MockAdapter

@pytest.fixture
def mock_factory():
    """Mock the get_llm_adapter function to always return a mock adapter."""
    with patch("backend.app.core.llm.get_llm_adapter") as mock:
        mock.return_value = MockAdapter(response="Mocked factory response")
        yield mock

async def test_with_mocked_factory(mock_factory):
    """Test code that calls get_llm_adapter directly."""
    # Define a function that calls get_llm_adapter
    async def create_and_chat(query):
        adapter = get_llm_adapter(provider="openai", api_key="fake-key")
        messages = [{"role": "user", "content": query}]
        return await adapter.chat(messages)

    # Test the function with the mocked factory
    response = await create_and_chat("Hello")
    assert response == "Mocked factory response"

    # Verify the factory was called
    mock_factory.assert_called_once()
```

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 📖 Glossary of Terms

| Term | Definition |
|------|------------|
| **LLM** | Large Language Model (e.g., GPT-4 or Claude 3) |
| **Adapter Pattern** | A design pattern that allows objects with incompatible interfaces to collaborate |
| **Token** | A chunk of text used to calculate prompt length or cost (e.g., "Hello" ≈ 1 token) |
| **Streaming** | Receiving output in real-time chunks instead of waiting for the complete response |
| **Retry Logic** | Automatically retrying failed API calls with exponential backoff |
| **Prompt** | The input text sent to the LLM |
| **Completion** | The output text generated by the LLM |
| **Temperature** | A parameter that controls randomness in the LLM's output (0.0 = deterministic, 1.0 = creative) |
| **Max Tokens** | The maximum number of tokens to generate in the response |
| **System Message** | A special message that sets the behavior of the assistant |
| **User Message** | A message from the user to the assistant |
| **Assistant Message** | A message from the assistant to the user |
| **API Key** | A secret key used to authenticate with the LLM provider |
| **Rate Limit** | A limit on the number of requests that can be made to the API in a given time period |
| **Fallback** | A mechanism to use an alternative when the primary option is unavailable |
| **Mock** | A simulated object used for testing |
| **Factory Pattern** | A design pattern that provides an interface for creating objects without specifying their concrete classes |

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>

## 🔍 Code Walkthrough

This section provides a detailed walkthrough of the implementation with diagrams and explanations.

### Architecture Overview

```
┌─────────────────┐     ┌───────────────────┐     ┌───────────────────┐
│                 │     │                   │     │                   │
│  Client Code    │────▶│  Factory Function │────▶│  LLM Adapter      │
│                 │     │                   │     │                   │
└─────────────────┘     └───────────────────┘     └───────────────────┘
                                                          │
                                                          │
                                                          ▼
                        ┌───────────────────┐     ┌───────────────────┐
                        │                   │     │                   │
                        │  Provider SDK     │◀────│  Adapter          │
                        │  (OpenAI,         │     │  Implementation   │
                        │   Anthropic, etc.)│     │                   │
                        └───────────────────┘     └───────────────────┘
```

### Request Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────────┐     ┌──────────────┐
│         │     │             │     │              │     │              │
│ Client  │────▶│ LLM Adapter │────▶│ Provider SDK │────▶│ LLM Provider │
│         │     │             │     │              │     │              │
└─────────┘     └─────────────┘     └──────────────┘     └──────────────┘
     │                 ▲                   │                    │
     │                 │                   │                    │
     │                 └───────────────────┼────────────────────┘
     │                         Response    │
     │                                     │
     └─────────────────────────────────────┘
              Metrics & Logging
```

### Dependency Handling

The module uses try/except blocks to handle optional dependencies:

```python
try:
    import openai
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    openai = None
    AsyncOpenAI = None
    OPENAI_AVAILABLE = False
```

This allows the module to gracefully handle missing dependencies and provide meaningful error messages.

### Factory Pattern Implementation

The factory pattern is implemented using the `get_llm_adapter` function:

```python
def get_llm_adapter(provider: str = "openai", fallback: bool = True, **kwargs) -> LLMAdapter:
    """Get an LLM adapter for the specified provider."""
    if provider == "openai" and OPENAI_AVAILABLE:
        return OpenAIAdapter(**kwargs)
    elif provider == "anthropic" and ANTHROPIC_AVAILABLE:
        return AnthropicAdapter(**kwargs)
    elif provider == "mock":
        return MockAdapter(**kwargs)
    elif fallback:
        # Fallback logic...
    else:
        raise ValueError(f"Provider '{provider}' not available and fallback is disabled")
```

This allows clients to create adapters without knowing the details of each implementation.

### Retry Logic Implementation

The retry logic is implemented using the tenacity library:

```python
@retry(
    retry=retry_if_exception_type(
        (
            openai.RateLimitError,
            openai.APITimeoutError,
            openai.APIConnectionError,
        )
    ),
    wait=wait_exponential(multiplier=1, min=2, max=30),
    stop=stop_after_attempt(5),
)
async def _chat_with_tenacity(attempt):
    return await self._chat_without_retry(
        messages, temperature, max_tokens, attempt=attempt
    )
```

This automatically retries the request with exponential backoff if it fails due to rate limits, timeouts, or connection errors.

### Streaming Implementation

Streaming is implemented using async generators:

```python
async def _chat_streaming(
    self, messages, temperature, max_tokens
) -> AsyncGenerator[str, None]:
    """Send a streaming chat request to OpenAI."""
    # Send the streaming request
    stream = await self._client.chat.completions.create(
        model=self.model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens or self.max_tokens,
        stream=True,
    )

    # Yield chunks as they arrive
    async for chunk in stream:
        if chunk.choices and chunk.choices[0].delta.content:
            yield chunk.choices[0].delta.content
```

This allows clients to process the response as it arrives, rather than waiting for the complete response.

<div align="right"><a href="#-llm-adapter-handbook">⬆️ Back to top</a></div>
