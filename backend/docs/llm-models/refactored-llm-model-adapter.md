# 📚 Refactored LLM Adapter Architecture

This document describes the refactored architecture of the LLM adapter module, which has been transformed from a monolithic file into a modular package structure. The refactoring improves code organization, maintainability, and extensibility while maintaining backward compatibility.

> **Note**: This documentation serves as a comprehensive reference for the refactored LLM adapter implementation in `backend/app/core/llm/`. It covers both the architectural changes and implementation details to provide a complete understanding of the new system.

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🏗️ Directory Structure](#️-directory-structure)
3. [🧩 Module Descriptions](#-module-descriptions)
4. [🔄 Backward Compatibility](#-backward-compatibility)
5. [🚀 Benefits of the Refactoring](#-benefits-of-the-refactoring)
6. [📝 Usage Examples](#-usage-examples)
7. [🔌 Adding New Adapters](#-adding-new-adapters)
8. [🧪 Testing the Refactored Code](#-testing-the-refactored-code)
9. [📊 Feature Comparison](#-feature-comparison)
10. [⚠️ Troubleshooting](#️-troubleshooting)
11. [🔒 Security & Performance](#-security--performance)
12. [📖 Migration Guide](#-migration-guide)

## 🔍 Overview

The LLM adapter module has been refactored from a single monolithic file (`backend/app/core/llm_model_adapter.py`) into a modular package structure (`backend/app/core/llm/`). This refactoring separates concerns, improves code organization, and makes the codebase more maintainable while preserving all functionality and ensuring backward compatibility.

The LLM adapter provides a unified interface for interacting with different Large Language Model providers (OpenAI, Anthropic, Google Gemini) through a consistent API. It handles:

- **Message formatting**: Converting our standard message format to provider-specific formats
- **API communication**: Managing API keys, rate limits, and retries
- **Response processing**: Handling both streaming and non-streaming responses
- **Token counting**: Estimating token usage for both prompts and responses
- **Error handling**: Providing consistent error handling across providers

### Key Changes

- **Modular Structure**: Split the monolithic file into multiple modules with clear responsibilities
- **Separation of Concerns**: Each adapter implementation is now in its own file
- **Backward Compatibility**: Original file now acts as a compatibility layer
- **Improved Testability**: Easier to test individual components
- **Enhanced Extensibility**: Easier to add new adapters or modify existing ones

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🏗️ Directory Structure

### Before and After Comparison

**Before**: Single monolithic file containing all adapter implementations
```
backend/app/core/
└── llm_model_adapter.py  # ~1000 lines of code with all implementations
```

**After**: Modular package structure with separate files for each component
```
backend/app/core/
├── llm_model_adapter.py  # Compatibility layer (re-exports from llm/)
└── llm/                  # New modular package
    ├── __init__.py       # Exports all public symbols
    ├── base.py           # Base classes, types, and utility functions
    ├── openai_adapter.py # OpenAI adapter implementation
    ├── anthropic_adapter.py # Anthropic adapter implementation
    ├── gemini_adapter.py # Gemini adapter implementation
    ├── mock_adapter.py   # Mock adapter for testing
    └── factory.py        # Factory functions for creating adapters
```

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                   llm_model_adapter.py                      │
│                   (Compatibility Layer)                     │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                       llm/__init__.py                       │
│                    (Public API Exports)                     │
└───────┬───────────┬───────────┬────────────┬────────────────┘
        │           │           │            │
        ▼           ▼           ▼            ▼
┌───────────┐ ┌──────────┐ ┌──────────┐ ┌──────────────┐
│  base.py  │ │factory.py│ │ Adapters │ │mock_adapter.py│
│(Interface)│ │(Registry)│ │(Provider │ │  (Testing)   │
└─────┬─────┘ └────┬─────┘ │ Specific)│ └──────────────┘
      │            │       └──┬───┬───┘
      │            │          │   │
      └────────────┴──────────┘   │
                   ▲               │
                   │               │
                   ▼               ▼
┌─────────────────────────┐ ┌─────────────────────────┐
│    Client Applications  │ │        Test Suite       │
└─────────────────────────┘ └─────────────────────────┘
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🧩 Module Descriptions

### base.py

This module contains the core abstractions and utility functions:

- `LLMAdapter` abstract base class defining the interface for all adapters
- `ChatMessage` TypedDict for standardized message format
- `LLMConfig` TypedDict for adapter configuration options
- `log_llm_metrics` decorator for logging API call metrics

```python
class LLMAdapter(ABC):
    """Abstract base class for LLM adapters."""

    @abstractmethod
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to the LLM and get a response."""
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        pass

    @property
    def client(self):
        """Get the underlying client instance."""
        return getattr(self, "_client", None)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return False

    def get_stream_flag(self, stream: bool = False) -> bool:
        """Get the appropriate stream flag based on adapter capabilities."""
        return stream and self.supports_streaming
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### openai_adapter.py

This module implements the OpenAI adapter:

- `OpenAIAdapter` class for interfacing with OpenAI's API
- Handles API key management, client initialization, and configuration
- Implements chat functionality with streaming support
- Provides token counting using tiktoken
- Includes retry logic for handling transient errors

```python
class OpenAIAdapter(LLMAdapter):
    """Adapter for OpenAI's GPT models."""

    def __init__(self, model: str = "gpt-4.1-2025-04-14", api_key: Optional[str] = None, **kwargs):
        """Initialize the OpenAI adapter."""
        # Implementation details...

    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to OpenAI and get a response."""
        # Implementation details...

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        # Implementation details...
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### anthropic_adapter.py

This module implements the Anthropic adapter:

- `AnthropicAdapter` class for interfacing with Anthropic's API
- Handles API key management, client initialization, and configuration
- Implements chat functionality with streaming support
- Provides token counting using Anthropic's SDK
- Includes retry logic for handling transient errors
- Handles system message conversion (Anthropic doesn't have native system messages)

```python
class AnthropicAdapter(LLMAdapter):
    """Adapter for Anthropic's Claude models."""

    def __init__(
        self,
        model: str = "claude-3-7-sonnet-20250219",
        api_key: Optional[str] = None,
        **kwargs,
    ):
        """Initialize the Anthropic adapter."""
        # Implementation details...

    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to Anthropic and get a response."""
        # Implementation details...

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        # Implementation details...
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### gemini_adapter.py

This module implements the Gemini adapter:

- `GeminiAdapter` class for interfacing with Google's Gemini API
- Handles API key management, client initialization, and configuration
- Implements chat functionality with streaming support
- Provides token counting using character-based heuristics
- Includes retry logic for handling transient errors

```python
class GeminiAdapter(LLMAdapter):
    """Adapter for Google's Gemini models."""

    def __init__(
        self,
        model: str = "gemini-2.0-flash",
        api_key: Optional[str] = None,
        **kwargs,
    ):
        """Initialize the Gemini adapter."""
        # Implementation details...

    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to Gemini and get a response."""
        # Implementation details...

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        # Implementation details...
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### mock_adapter.py

This module implements the mock adapter for testing:

- `MockAdapter` class for simulating LLM responses
- Provides configurable responses for testing
- Simulates streaming and token counting
- Useful for unit testing without making actual API calls

```python
class MockAdapter(LLMAdapter):
    """Mock adapter for testing purposes."""

    def __init__(self, responses: Optional[Dict[str, str]] = None, **kwargs):
        """Initialize the mock adapter."""
        # Implementation details...

    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Return a mock response based on the last message content."""
        # Implementation details...

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Mock token counting."""
        # Implementation details...
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### factory.py

This module implements the factory pattern for creating adapters:

- `get_llm_adapter` function for creating adapters with fallback logic
- `get_llm_adapter_factory` function for getting a dictionary of available adapters
- `register_adapter` function for registering custom adapters
- `ADAPTER_REGISTRY` dictionary for storing registered adapters

```python
def get_llm_adapter(
    provider: Literal["openai", "anthropic", "gemini", "mock"] = "openai",
    fallback: bool = True,
    **kwargs
) -> LLMAdapter:
    """Factory function to get an LLM adapter."""
    # Implementation details...

def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """Get a dictionary of available LLM adapter factory functions."""
    # Implementation details...

def register_adapter(name: str, adapter_class: Type[LLMAdapter]) -> None:
    """Register an LLM adapter with the registry."""
    # Implementation details...
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

### __init__.py

This module exports all public symbols from the package:

- Re-exports all classes, functions, and constants from the other modules
- Provides a clean, unified interface for importing from the package
- Groups SDK flags for easier environment validation

```python
# Import from base module
from .base import (
    ChatMessage,
    LLMAdapter,
    LLMConfig,
    log_llm_metrics,
)

# Import from adapter modules
from .openai_adapter import (
    OpenAIAdapter,
    OPENAI_AVAILABLE,
    TIKTOKEN_AVAILABLE,
)

# Import from factory module
from .factory import (
    get_llm_adapter,
    get_llm_adapter_factory,
    register_adapter,
    ADAPTER_REGISTRY,
)

# Group SDK flags for easier environment debug/validation
LLM_DEPENDENCIES = {
    "openai": OPENAI_AVAILABLE,
    "anthropic": ANTHROPIC_AVAILABLE,
    "gemini": GEMINI_AVAILABLE,
    "tiktoken": TIKTOKEN_AVAILABLE,
    "tenacity": TENACITY_AVAILABLE
}
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🔄 Backward Compatibility

The original `llm_model_adapter.py` file has been converted into a compatibility layer that re-exports all symbols from the new modular structure. This ensures that existing code that imports from the original module will continue to work without changes.

```python
"""
LLM Adapter Implementation

This module is now a compatibility layer that re-exports all symbols from the
new modular structure in the `llm` package.

For new code, import directly from the new modules:
    from backend.app.core.llm import get_llm_adapter, LLMAdapter, ChatMessage
"""

# Re-export all symbols from the new modular structure
from .llm import (
    # Base classes and types
    LLMAdapter,
    ChatMessage,
    LLMConfig,
    log_llm_metrics,

    # Adapter implementations
    OpenAIAdapter,
    AnthropicAdapter,
    GeminiAdapter,
    MockAdapter,

    # Factory functions
    get_llm_adapter,
    get_llm_adapter_factory,
    register_adapter,
    ADAPTER_REGISTRY,

    # Availability flags
    OPENAI_AVAILABLE,
    ANTHROPIC_AVAILABLE,
    GEMINI_AVAILABLE,
    TIKTOKEN_AVAILABLE,
    TENACITY_AVAILABLE,
    LLM_DEPENDENCIES,
)
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🚀 Benefits of the Refactoring

### 1. Improved Code Organization

- **Separation of Concerns**: Each adapter is in its own file
- **Reduced File Size**: Smaller files are easier to navigate and understand
- **Logical Grouping**: Related functionality is grouped together

### 2. Enhanced Maintainability

- **Isolated Changes**: Changes to one adapter don't affect others
- **Focused Testing**: Easier to write and maintain tests for specific components
- **Clearer Dependencies**: Dependencies are more explicit and easier to manage

### 3. Better Extensibility

- **Adding New Adapters**: Just create a new file and register the adapter
- **Modifying Existing Adapters**: Changes are isolated to a single file
- **Plugin Architecture**: The registry pattern enables a plugin-like architecture

### 4. Improved Developer Experience

- **Easier Navigation**: Smaller files with focused responsibilities
- **Better IDE Support**: More specific imports and better code completion
- **Clearer Documentation**: Documentation can be more focused and detailed

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 📝 Usage Examples

### Basic Usage (Same as Before)

```python
import asyncio
from backend.app.core.llm import get_llm_adapter

async def main():
    # Create an adapter for OpenAI
    adapter = get_llm_adapter(
        provider="openai",  # Choose from "openai", "anthropic", "gemini", "mock"
        api_key="your-api-key",  # Optional: defaults to environment variable
        model="gpt-4"  # Optional: each adapter has sensible defaults
    )

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Get a response
    response = await adapter.chat(messages, temperature=0.7)
    print(response)

# Run the async function
asyncio.run(main())
```

### Streaming Example

```python
import asyncio
from backend.app.core.llm import get_llm_adapter

async def main():
    # Create an adapter for OpenAI
    adapter = get_llm_adapter(provider="openai", model="gpt-4")

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Write a short poem about coding."}
    ]

    # Stream the response
    async for chunk in await adapter.chat(messages, stream=True):
        print(chunk, end="", flush=True)  # Print each chunk as it arrives
    print()  # Add a newline at the end

# Run the async function
asyncio.run(main())
```

### Token Counting Example

```python
import asyncio
from backend.app.core.llm import get_llm_adapter

async def main():
    # Create an adapter for OpenAI
    adapter = get_llm_adapter(provider="openai", model="gpt-4")

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Explain quantum computing in simple terms."}
    ]

    # Count tokens
    prompt_tokens, estimated_response_tokens = await adapter.get_token_count(messages)

    print(f"Prompt tokens: {prompt_tokens}")
    print(f"Estimated response tokens: {estimated_response_tokens}")
    print(f"Estimated total tokens: {prompt_tokens + estimated_response_tokens}")

    # This is useful for:
    # 1. Cost estimation
    # 2. Ensuring you don't exceed token limits
    # 3. Optimizing prompts

# Run the async function
asyncio.run(main())
```

### Using Direct Imports from the New Structure

```python
import asyncio
from backend.app.core.llm import OpenAIAdapter

async def main():
    # Create an adapter directly
    adapter = OpenAIAdapter(
        api_key="your-api-key",
        model="gpt-4"
    )

    # Define the messages
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Get a response
    response = await adapter.chat(messages, temperature=0.7)
    print(response)

# Run the async function
asyncio.run(main())
```

### Using the Factory Pattern with Custom Adapters

```python
import asyncio
from backend.app.core.llm import LLMAdapter, register_adapter, get_llm_adapter
from typing import List, Tuple, Union, AsyncIterator

class CustomAdapter(LLMAdapter):
    """Custom adapter implementation."""

    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to the LLM."""
        return "This is a custom response."

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        return 10, 20

# Register the custom adapter
register_adapter("custom", CustomAdapter)

async def main():
    # Create an adapter using the factory
    adapter = get_llm_adapter("custom")

    # Define the messages
    messages = [
        {"role": "user", "content": "Hello, how are you?"}
    ]

    # Get a response
    response = await adapter.chat(messages)
    print(response)

# Run the async function
asyncio.run(main())
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🔌 Adding New Adapters

The refactored architecture makes it easier to add new adapters:

1. Create a new file in the `backend/app/core/llm/` directory (e.g., `new_provider_adapter.py`)
2. Implement the adapter class that inherits from `LLMAdapter`
3. Register the adapter in `factory.py` or use the `register_adapter` function

Example:

```python
# new_provider_adapter.py
from typing import List, Tuple, Union, AsyncIterator, Optional
from .base import LLMAdapter, ChatMessage, log_llm_metrics

class NewProviderAdapter(LLMAdapter):
    """Adapter for a new LLM provider."""

    def __init__(self, api_key: Optional[str] = None, **kwargs):
        """Initialize the new provider adapter."""
        self.api_key = api_key
        # Additional initialization...

    @log_llm_metrics
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to the new provider."""
        # Implementation...
        return "Response from new provider"

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        # Implementation...
        return 100, 50

# Register in factory.py
from .new_provider_adapter import NewProviderAdapter

def get_llm_adapter_factory() -> Dict[str, Type[LLMAdapter]]:
    """Get a dictionary of available LLM adapter factory functions."""
    # Register built-in adapters if they haven't been registered yet
    if "openai" not in ADAPTER_REGISTRY and OPENAI_AVAILABLE:
        register_adapter("openai", OpenAIAdapter)
    if "anthropic" not in ADAPTER_REGISTRY and ANTHROPIC_AVAILABLE:
        register_adapter("anthropic", AnthropicAdapter)
    if "gemini" not in ADAPTER_REGISTRY and GEMINI_AVAILABLE:
        register_adapter("gemini", GeminiAdapter)
    if "mock" not in ADAPTER_REGISTRY:
        register_adapter("mock", MockAdapter)
    if "new_provider" not in ADAPTER_REGISTRY:
        register_adapter("new_provider", NewProviderAdapter)

    # Return a copy of the registry to prevent modification
    return ADAPTER_REGISTRY.copy()
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🧪 Testing the Refactored Code

The refactored architecture makes testing easier:

### Unit Testing Individual Adapters

```python
import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from backend.app.core.llm import OpenAIAdapter

@pytest.mark.asyncio
async def test_openai_adapter():
    """Test the OpenAI adapter with mocked API calls."""
    # Mock the OpenAI client
    mock_response = MagicMock()
    mock_response.choices = [MagicMock()]
    mock_response.choices[0].message.content = "This is a test response."

    # Create a mock client
    mock_client = AsyncMock()
    mock_client.chat.completions.create.return_value = mock_response

    # Mock the OpenAI client
    with patch("backend.app.core.llm.openai_adapter.AsyncOpenAI", return_value=mock_client):
        adapter = OpenAIAdapter(api_key="test_key")

        # Test with a simple message
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = await adapter.chat(messages)

        # Verify the response
        assert response == "This is a test response."

        # Verify the API call
        mock_client.chat.completions.create.assert_awaited_once()
```

### Integration Testing with the Factory

```python
import pytest
from backend.app.core.llm import get_llm_adapter, MockAdapter

@pytest.mark.asyncio
async def test_factory_with_mock():
    """Test the factory function with the mock adapter."""
    # Create a mock adapter using the factory
    adapter = get_llm_adapter(provider="mock", response="Custom mock response")

    # Verify it's the right type
    assert isinstance(adapter, MockAdapter)

    # Test with a simple message
    messages = [{"role": "user", "content": "Hello, how are you?"}]
    response = await adapter.chat(messages)

    # Verify the response
    assert response == "Custom mock response"
```

### Testing Backward Compatibility

```python
import pytest
from backend.app.core import llm_model_adapter

@pytest.mark.asyncio
async def test_backward_compatibility():
    """Test that the old imports still work."""
    # Create a mock adapter using the old import
    adapter = llm_model_adapter.get_llm_adapter(provider="mock")

    # Verify it's the right type
    assert isinstance(adapter, llm_model_adapter.MockAdapter)

    # Test with a simple message
    messages = [{"role": "user", "content": "Hello, how are you?"}]
    response = await adapter.chat(messages)

    # Verify the response
    assert response == "This is a mock response."
```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 📊 Feature Comparison

This section compares the features between the original monolithic implementation and the refactored modular structure.

| Feature | Original Monolithic | Refactored Modular | Notes |
|---------|--------------------|--------------------|-------|
| **Functionality** | ✅ | ✅ | All functionality preserved |
| **Backward Compatibility** | N/A | ✅ | Compatibility layer ensures no breaking changes |
| **Code Organization** | ❌ | ✅ | Improved separation of concerns |
| **Maintainability** | ❌ | ✅ | Easier to maintain and update |
| **Extensibility** | ⚠️ | ✅ | Much easier to add new adapters |
| **Testability** | ⚠️ | ✅ | Easier to test individual components |
| **IDE Support** | ⚠️ | ✅ | Better code navigation and completion |
| **Documentation** | ✅ | ✅ | Comprehensive documentation for both |

### Adapter Implementation Comparison

| Adapter | Original Location | Refactored Location | Changes |
|---------|------------------|---------------------|---------|
| **Base Adapter** | `llm_model_adapter.py` | `llm/base.py` | Cleaner interface, same functionality |
| **OpenAI** | `llm_model_adapter.py` | `llm/openai_adapter.py` | Isolated implementation, same functionality |
| **Anthropic** | `llm_model_adapter.py` | `llm/anthropic_adapter.py` | Isolated implementation, same functionality |
| **Gemini** | `llm_model_adapter.py` | `llm/gemini_adapter.py` | Isolated implementation, same functionality |
| **Mock** | `llm_model_adapter.py` | `llm/mock_adapter.py` | Isolated implementation, same functionality |
| **Factory** | `llm_model_adapter.py` | `llm/factory.py` | Enhanced registry pattern |

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## ⚠️ Troubleshooting

This section provides solutions for common issues you might encounter when working with the refactored LLM adapter.

### Common Issues and Solutions

| Issue | Possible Causes | Solutions |
|-------|----------------|-----------|
| **Import errors after refactoring** | Using old import paths | Update imports to use the new module structure or use the compatibility layer |
| **Missing dependencies** | Required packages not installed | Install the necessary packages with `pip install openai anthropic google-generativeai tiktoken tenacity` |
| **Type errors** | Using incorrect type annotations | Ensure you're using the correct types from the new modules |
| **Backward compatibility issues** | Using features not re-exported by the compatibility layer | Import directly from the new module structure |
| **Test failures** | Tests using old import paths or mocking old paths | Update test imports and mocks to use the new module structure |

### Debugging Flow

If you encounter issues with the refactored code, follow this debugging flow:

1. **Check imports**: Ensure you're importing from the correct modules
   ```python
   # Old way (still works via compatibility layer)
   from backend.app.core.llm_model_adapter import get_llm_adapter

   # New way (preferred)
   from backend.app.core.llm import get_llm_adapter
   ```

2. **Verify dependencies**: Make sure all required dependencies are installed
   ```python
   from backend.app.core.llm import LLM_DEPENDENCIES
   print(LLM_DEPENDENCIES)  # Should show which providers are available
   ```

3. **Check adapter availability**: Verify that the adapter you're trying to use is available
   ```python
   from backend.app.core.llm import get_llm_adapter_factory
   factory = get_llm_adapter_factory()
   print(factory.keys())  # Should show available adapters
   ```

4. **Test with mock adapter**: If you're having issues with a specific provider, try using the mock adapter
   ```python
   from backend.app.core.llm import get_llm_adapter
   adapter = get_llm_adapter(provider="mock")
   ```

5. **Enable verbose logging**: Turn on debug logging to see more information
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 🔒 Security & Performance

The refactored architecture maintains all the security and performance features of the original implementation while adding some improvements.

### Security Considerations

- **API Key Management**: The refactored code maintains the same secure API key handling
  ```python
  # Using environment variables (recommended)
  adapter = get_llm_adapter(provider="openai")  # Uses OPENAI_API_KEY from environment

  # Passing directly (use with caution)
  adapter = get_llm_adapter(provider="openai", api_key="your-api-key")
  ```

- **Error Handling**: Improved error handling with more specific error messages
  ```python
  try:
      response = await adapter.chat(messages)
  except Exception as e:
      logger.error(f"Error calling LLM API: {e}")
      # Handle the error appropriately
  ```

- **Dependency Isolation**: Better isolation of dependencies reduces security risks
  ```python
  # Each adapter only imports what it needs
  # If one provider's SDK has a security issue, it doesn't affect others
  ```

### Performance Improvements

- **Lazy Loading**: The refactored code uses lazy loading for better startup performance
  ```python
  # Dependencies are only imported when needed
  # This reduces startup time and memory usage
  ```

- **Client Reuse**: The adapters still create clients once and reuse them
  ```python
  # Client is created in __init__ and reused for all requests
  # This improves performance by avoiding client creation overhead
  ```

- **Optimized Imports**: More specific imports reduce memory usage
  ```python
  # Only import what's needed from each module
  # This reduces memory usage and improves performance
  ```

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

## 📖 Migration Guide

This section provides guidance on migrating from the original monolithic implementation to the refactored modular structure.

### Step 1: Update Imports

The compatibility layer allows you to keep using the old imports, but it's recommended to update to the new structure:

```python
# Old imports (still work via compatibility layer)
from backend.app.core.llm_model_adapter import get_llm_adapter, OpenAIAdapter

# New imports (recommended)
from backend.app.core.llm import get_llm_adapter, OpenAIAdapter
```

### Step 2: Update Tests

If you have tests that mock the LLM adapter, update the mock paths:

```python
# Old mock path
with patch("backend.app.core.llm_model_adapter.AsyncOpenAI", return_value=mock_client):
    # Test code...

# New mock path
with patch("backend.app.core.llm.openai_adapter.AsyncOpenAI", return_value=mock_client):
    # Test code...
```

### Step 3: Update Custom Adapters

If you've created custom adapters that inherit from `LLMAdapter`, update the import:

```python
# Old import
from backend.app.core.llm_model_adapter import LLMAdapter

# New import
from backend.app.core.llm import LLMAdapter
```

### Step 4: Register Custom Adapters

If you've created custom adapters, register them with the new registry:

```python
from backend.app.core.llm import register_adapter, LLMAdapter

class CustomAdapter(LLMAdapter):
    # Implementation...

# Register the adapter
register_adapter("custom", CustomAdapter)
```

### Step 5: Update Documentation

If you have documentation that references the LLM adapter, update it to reflect the new structure:

```markdown
# Old documentation
The LLM adapter is implemented in `backend/app/core/llm_model_adapter.py`.

# New documentation
The LLM adapter is implemented as a modular package in `backend/app/core/llm/`.
```

## 📅 Deprecation Timeline

This section outlines the planned evolution of the compatibility layer (`llm_model_adapter.py`) as the codebase transitions to the new modular structure.

### Phase 1: Compatibility Layer (Current)

In the current phase, `llm_model_adapter.py` serves as a full compatibility layer that re-exports all symbols from the new modular structure. This allows existing code to continue working without changes while new code can start using the new imports.

```python
# Current implementation - silent re-export
from .llm import (
    LLMAdapter,
    OpenAIAdapter,
    # ... other symbols
)
```

### Phase 2: Deprecation Warnings (Next Release)

In the next release, deprecation warnings will be added to alert users when they import from the old module. The file will continue to function as before, but will encourage migration to the new structure.

```python
# Phase 2 implementation - adds warnings
from .llm import (
    LLMAdapter,
    OpenAIAdapter,
    # ... other symbols
)

import warnings
warnings.warn(
    "The 'llm_model_adapter' module is deprecated. "
    "Please import directly from 'backend.app.core.llm' instead.",
    DeprecationWarning,
    stacklevel=2
)
```

### Phase 3: Import Redirection (Future Release)

After a suitable period with deprecation warnings, the file may be updated to dynamically redirect imports to the new module. This maintains backward compatibility while simplifying the codebase.

```python
# Phase 3 implementation - redirects imports
import sys
import warnings
from importlib import import_module

warnings.warn(
    "The 'llm_model_adapter' module is deprecated and will be removed in version X.X. "
    "Please import directly from 'backend.app.core.llm' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Dynamically redirect imports to the new module
new_module = import_module('backend.app.core.llm')
sys.modules[__name__] = new_module
```

### Phase 4: Complete Removal (Major Version Bump)

In a future major version release (e.g., v2.0.0), after sufficient notice and deprecation period, the compatibility layer may be removed entirely. This would be clearly communicated in release notes and migration guides.

### Timeline Considerations

The specific timeline for these phases will depend on:

1. **Adoption Rate**: How quickly users migrate to the new import structure
2. **Release Schedule**: The project's planned release cadence
3. **Breaking Changes**: Whether other breaking changes are planned for the same releases
4. **User Feedback**: Input from users about the migration process

### Recommended Actions

To prepare for these changes:

1. **Start using the new imports now**: Begin migrating code to import from `backend.app.core.llm` instead of `llm_model_adapter.py`
2. **Update tests**: Ensure tests use the new import paths and mock locations
3. **Monitor deprecation warnings**: Once Phase 2 begins, address any warnings that appear in your code
4. **Stay updated**: Watch for announcements about timeline changes in release notes

<div align="right"><a href="#-refactored-llm-adapter-architecture">⬆️ Back to top</a></div>

---

This refactoring represents a significant improvement in the architecture of the LLM adapter module, making it more maintainable, extensible, and easier to understand while preserving all functionality and ensuring backward compatibility.
