# 🔄 Timeout Utilities

This document describes the timeout utilities in the Python migration proof-of-concept. These utilities provide a unified approach to handling timeouts, retries, and circuit breaking across the application.

> **Note**: This document focuses on the **structural organization** of timeout utilities. For detailed information about the functionality, usage patterns, and implementation details, see [Timeout and Retry Implementation](../langgraph/timeout-and-retry-implementation.md).

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 📋 Table of Contents

1. [🔍 Overview](#-overview)
2. [🏗️ Architecture](#-architecture)
3. [🧩 Components](#-components)
4. [🔄 Migration Guide](#-migration-guide)
5. [⚙️ Configuration](#-configuration)
6. [🚀 Usage Examples](#-usage-examples)
7. [🧪 Testing](#-testing)
8. [📊 Observability](#-observability)
9. [📝 Best Practices](#-best-practices)
10. [📚 References](#-references)

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🔍 Overview

The timeout utilities have been reorganized into a modular structure to improve maintainability and reduce code duplication. This reorganization addresses several issues in the previous implementation:

1. **Code Duplication**: Similar timeout and retry logic was duplicated across multiple files
2. **Inconsistent Patterns**: Different components used slightly different patterns for timeout and retry
3. **Limited Extensibility**: Adding new timeout utilities required modifying existing files
4. **Unclear Boundaries**: The responsibilities of different timeout utilities were not clearly defined

The new modular structure provides:

1. **Clear Separation of Concerns**: Each module focuses on a specific component
2. **Consistent Patterns**: All timeout utilities follow the same patterns and conventions
3. **Improved Extensibility**: New timeout utilities can be added without modifying existing files
4. **Better Testability**: Each module can be tested independently

### Key Features

- **Unified Interface**: Consistent interface across all timeout utilities
- **Configurable Timeouts**: Timeout durations configurable via environment variables
- **Exponential Backoff**: Retry with exponential backoff to avoid overwhelming services
- **Circuit Breaking**: Circuit breaker pattern to prevent cascading failures
- **Error Mapping**: Mapping of timeout and retry errors to appropriate error types
- **Fallback Mechanisms**: Fallback mechanisms for timeout and retry failures
- **Observability**: Comprehensive logging and metrics for timeout and retry events

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🏗️ Architecture

### Folder Structure

The new modular structure organizes timeout utilities into a dedicated package:

```
backend/app/core/timeout/
  __init__.py        # Package exports
  base.py            # Base timeout and retry utilities
  llm.py             # LLM-specific timeout utilities
  rag.py             # RAG-specific timeout utilities
  graph.py           # LangGraph-specific timeout utilities
```

### Compatibility Modules

To ensure backward compatibility, the following modules have been updated to re-export the utilities from the new modular structure:

```
backend/app/
├── core/
│   ├── timeout_utils.py  # Re-exports base utilities
│   └── ...
├── rag/
│   ├── timeout.py        # Re-exports RAG-specific utilities
│   └── ...
└── langgraph/
    ├── timeout.py        # Re-exports LangGraph-specific utilities
    └── ...
```

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────┐
│                  Application Components                  │
├─────────────┬─────────────┬─────────────┬───────────────┤
│  LangGraph  │     RAG     │    LLM      │   External    │
│    Nodes    │ Operations  │   Calls     │   Services    │
└──────┬──────┴──────┬──────┴──────┬──────┴───────┬───────┘
       │             │             │              │
       ▼             ▼             ▼              ▼
┌─────────────┬─────────────┬─────────────┬───────────────┐
│  graph.py   │   rag.py    │   llm.py    │    base.py    │
│  Node       │  RAG        │  LLM        │  Circuit      │
│  Timeout    │  Timeout    │  Timeout    │  Breaker      │
│  Decorators │  Utilities  │  Utilities  │  & Core Utils │
└──────┬──────┴──────┬──────┴──────┬──────┴───────┬───────┘
       │             │             │              │
       └─────────────┼─────────────┼──────────────┘
                     ▼             ▼
         ┌───────────────────────────────────┐
         │          __init__.py              │
         │       Package Exports             │
         └───────────────────────────────────┘
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🧩 Components

### Base Utilities (`base.py`)

The base utilities provide the core functionality for timeout and retry handling:

- **`with_timeout`**: Execute an async function with a timeout
- **`with_retry`**: Decorator for retrying async functions with exponential backoff
- **`with_timeout_and_retry`**: Execute an async function with both timeout and retry logic
- **`CircuitBreaker`**: Implementation of the circuit breaker pattern for external services
- **`DEFAULT_TIMEOUTS`**: Default timeout values for different operations
- **`DEFAULT_RETRY_CONFIG`**: Default retry configuration

### LLM Utilities (`llm.py`)

The LLM-specific utilities provide timeout handling for LLM operations:

- **`with_llm_timeout`**: Execute an LLM function with timeout and retry logic
- **`with_llm_timeout_decorator`**: Decorator for adding timeout and retry logic to LLM functions
- **`LLM_TIMEOUTS`**: LLM-specific timeout values

### RAG Utilities (`rag.py`)

The RAG-specific utilities provide timeout handling for RAG components:

- **`with_embedding_timeout`**: Execute an embedding generation function with timeout and retry logic
- **`with_vector_search_timeout`**: Execute a vector search function with timeout, retry, and fallback logic
- **`with_rag_timeout`**: Decorator for adding timeout, retry, and fallback logic to RAG functions
- **`with_embedding_timeout_decorator`**: Decorator for embedding generation with timeout
- **`with_vector_search_timeout_decorator`**: Decorator for vector search with timeout and fallback
- **`with_keyword_search_timeout_decorator`**: Decorator for keyword search with timeout and fallback
- **`with_hybrid_search_timeout_decorator`**: Decorator for hybrid search with timeout and fallback
- **`RAG_TIMEOUTS`**: RAG-specific timeout values

### LangGraph Utilities (`graph.py`)

The LangGraph-specific utilities provide timeout handling for LangGraph nodes:

- **`with_node_timeout_and_retry`**: Execute a LangGraph node function with timeout and retry logic
- **`with_node_timeout`**: Decorator for adding timeout and retry logic to LangGraph node functions
- **`with_analyze_query_timeout`**: Decorator for analyze_query node with timeout
- **`with_retrieve_knowledge_timeout`**: Decorator for retrieve_knowledge node with timeout
- **`with_route_to_departments_timeout`**: Decorator for route_to_departments node with timeout
- **`with_generate_response_timeout`**: Decorator for generate_response node with timeout
- **`NODE_TIMEOUTS`**: Node-specific timeout values

### Package Exports (`__init__.py`)

The package exports provide a unified interface for all timeout utilities:

```python
# Import base utilities
from .base import (
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG,
    CircuitBreaker,
    ServiceState,
    with_timeout,
    with_retry,
    with_timeout_and_retry
)

# Import component-specific utilities
from .llm import (
    with_llm_timeout,
    with_llm_timeout_decorator
)

from .rag import (
    RAG_TIMEOUTS,
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,
    with_embedding_timeout_decorator,
    with_vector_search_timeout_decorator,
    with_keyword_search_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

from .graph import (
    NODE_TIMEOUTS,
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout
)
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🔄 Migration Guide

### Updating Imports

To migrate from the old timeout utilities to the new modular structure, update your imports:

#### Old Imports

```python
# Base utilities
from app.core.timeout_utils import (
    with_timeout,
    with_retry,
    with_timeout_and_retry,
    CircuitBreaker
)

# RAG utilities
from app.rag.timeout import (
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout
)

# LangGraph utilities
from app.langgraph.timeout import (
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout
)
```

#### New Imports

```python
# All utilities from a single import
from app.core.timeout import (
    # Base utilities
    with_timeout,
    with_retry,
    with_timeout_and_retry,
    CircuitBreaker,

    # RAG utilities
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,

    # LangGraph utilities
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout
)

# Or import specific modules
from app.core.timeout.base import with_timeout, with_retry
from app.core.timeout.rag import with_embedding_timeout
from app.core.timeout.graph import with_node_timeout
```

### Updating Function Calls

The function signatures remain the same, so no changes are needed to function calls:

```python
# Old code
result = await with_timeout_and_retry(
    async_function,
    *args,
    timeout_seconds=10,
    operation_name="example_operation",
    max_attempts=3,
    **kwargs
)

# New code (same as old)
result = await with_timeout_and_retry(
    async_function,
    *args,
    timeout_seconds=10,
    operation_name="example_operation",
    max_attempts=3,
    **kwargs
)
```

### Updating Decorators

The decorator signatures remain the same, so no changes are needed to decorators:

```python
# Old code
@with_node_timeout(timeout_seconds=30, max_attempts=3, node_name="example_node")
async def my_node(state: AgentState) -> AgentState:
    # Node implementation
    return state

# New code (same as old)
@with_node_timeout(timeout_seconds=30, max_attempts=3, node_name="example_node")
async def my_node(state: AgentState) -> AgentState:
    # Node implementation
    return state
```

### Migration Timeline

1. **Phase 1: Update Imports (Completed)** - All imports have been updated to use the new modular structure
2. **Phase 2: Remove Deprecated Imports (Completed)** - All deprecated imports have been removed
3. **Phase 3: Remove Compatibility Modules (Pending)** - The compatibility modules will be removed after a transition period (2-4 weeks)

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## ⚙️ Configuration

The timeout utilities use environment variables for configuration:

### Base Timeout Values

```python
DEFAULT_TIMEOUTS = {
    "llm": int(os.getenv("TIMEOUT_LLM", "30")),
    "rag_search": int(os.getenv("TIMEOUT_RAG_SEARCH", "10")),
    "rag_embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "agent_node": int(os.getenv("TIMEOUT_AGENT_NODE", "40")),
    "external_service": int(os.getenv("TIMEOUT_EXTERNAL_SERVICE", "10")),
    "database": int(os.getenv("TIMEOUT_DATABASE", "5")),
}
```

### Retry Configuration

```python
DEFAULT_RETRY_CONFIG = {
    "max_attempts": int(os.getenv("RETRY_MAX_ATTEMPTS", "3")),
    "min_wait": float(os.getenv("RETRY_MIN_WAIT", "1.0")),
    "max_wait": float(os.getenv("RETRY_MAX_WAIT", "10.0")),
    "multiplier": float(os.getenv("RETRY_MULTIPLIER", "2.0")),
}
```

### LLM-Specific Timeout Values

```python
LLM_TIMEOUTS = {
    "chat": int(os.getenv("TIMEOUT_LLM_CHAT", "30")),
    "completion": int(os.getenv("TIMEOUT_LLM_COMPLETION", "20")),
    "embedding": int(os.getenv("TIMEOUT_LLM_EMBEDDING", "15")),
    "token_count": int(os.getenv("TIMEOUT_LLM_TOKEN_COUNT", "5")),
    "default": int(os.getenv("TIMEOUT_LLM_DEFAULT", "30")),
}
```

### RAG-Specific Timeout Values

```python
RAG_TIMEOUTS = {
    "embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "vector_search": int(os.getenv("TIMEOUT_RAG_VECTOR_SEARCH", "10")),
    "keyword_search": int(os.getenv("TIMEOUT_RAG_KEYWORD_SEARCH", "5")),
    "hybrid_search": int(os.getenv("TIMEOUT_RAG_HYBRID_SEARCH", "12")),
    "rerank": int(os.getenv("TIMEOUT_RAG_RERANK", "8")),
    "default": int(os.getenv("TIMEOUT_RAG_DEFAULT", "10")),
}
```

### Node-Specific Timeout Values

```python
NODE_TIMEOUTS = {
    "analyze_query": int(os.getenv("TIMEOUT_NODE_ANALYZE_QUERY", "30")),
    "retrieve_knowledge": int(os.getenv("TIMEOUT_NODE_RETRIEVE_KNOWLEDGE", "20")),
    "route_to_departments": int(os.getenv("TIMEOUT_NODE_ROUTE_TO_DEPARTMENTS", "40")),
    "generate_response": int(os.getenv("TIMEOUT_NODE_GENERATE_RESPONSE", "30")),
    "co_ceo": int(os.getenv("TIMEOUT_NODE_CO_CEO", "45")),
    "finance_department": int(os.getenv("TIMEOUT_NODE_FINANCE", "40")),
    "marketing_department": int(os.getenv("TIMEOUT_NODE_MARKETING", "40")),
    "fallback": int(os.getenv("TIMEOUT_NODE_FALLBACK", "30")),
    "default": int(os.getenv("TIMEOUT_NODE_DEFAULT", "40")),
}
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 📝 Best Practices

1. **Use the new modular structure**: Always import from `app.core.timeout` or its submodules instead of the deprecated compatibility modules:
   ```python
   # Preferred
   from app.core.timeout import with_timeout, with_retry

   # Or for specific modules
   from app.core.timeout.rag import with_embedding_timeout

   # Avoid (deprecated)
   # from app.core.timeout_utils import with_timeout
   # from app.rag.timeout import with_embedding_timeout
   ```

2. **Use the appropriate utility for the task**: Choose the utility that best matches the operation you're performing (LLM, RAG, LangGraph, etc.).
3. **Set appropriate timeouts**: Set timeouts that are appropriate for the operation. Operations that are expected to take longer should have longer timeouts.
4. **Use fallback mechanisms**: Provide fallback mechanisms for operations that can fail gracefully.
5. **Log timeout and retry events**: The utilities log timeout and retry events, but you can add additional logging as needed.
6. **Configure circuit breakers appropriately**: Set appropriate failure thresholds and recovery timeouts for circuit breakers based on the service characteristics.
7. **Test timeout and retry logic**: Write tests for timeout and retry logic to ensure it works as expected.
8. **Monitor timeout and retry events**: Monitor timeout and retry events to identify potential issues.
9. **Tune timeout and retry parameters**: Tune timeout and retry parameters based on production experience.
10. **Document timeout and retry configuration**: Document timeout and retry configuration for future reference.

### Common Patterns

#### Timeout with Fallback

```python
try:
    result = await with_timeout(
        async_function,
        *args,
        timeout_seconds=10,
        operation_name="example_operation",
        **kwargs
    )
    return result
except TimeoutError:
    # Use fallback
    return fallback_result
```

#### Retry with Exponential Backoff

```python
@with_retry(
    retry_on=[TimeoutError, RateLimitError],
    max_attempts=3,
    min_wait=1.0,
    max_wait=10.0,
    multiplier=2.0
)
async def my_function_with_retry(*args, **kwargs):
    # Function implementation
    pass
```

#### Circuit Breaker for External Services

```python
# Initialize circuit breaker
circuit_breaker = CircuitBreaker.get_instance(
    "external_service",
    failure_threshold=5,
    recovery_timeout=60.0
)

# Use circuit breaker
async def call_external_service(*args, **kwargs):
    try:
        return await circuit_breaker.execute(
            external_service.call,
            *args,
            fallback=fallback_service.call,
            **kwargs
        )
    except ExternalServiceError:
        # Handle service unavailable
        pass
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 📚 References

- [Timeout and Retry Implementation](../langgraph/timeout-and-retry-implementation.md) - Detailed documentation of timeout and retry functionality
- [Python asyncio.timeout](https://docs.python.org/3/library/asyncio-task.html#timeouts) - Python documentation for asyncio.timeout
- [Tenacity](https://tenacity.readthedocs.io/) - Python library for retrying operations with exponential backoff
- [Circuit Breaker Pattern](https://martinfowler.com/bliki/CircuitBreaker.html) - Martin Fowler's description of the Circuit Breaker pattern
- [Python Error Handling](https://docs.python.org/3/tutorial/errors.html) - Python documentation for error handling

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🚀 Usage Examples

### Base Timeout and Retry

```python
from app.core.timeout import with_timeout, with_retry, with_timeout_and_retry

# Execute a function with a timeout
result = await with_timeout(
    my_async_function,
    *args,
    timeout_seconds=10,
    operation_name="my_operation",
    **kwargs
)

# Decorate a function with retry logic
@with_retry(
    retry_on=[TimeoutError, RateLimitError],
    max_attempts=3,
    min_wait=1.0,
    max_wait=10.0,
    multiplier=2.0
)
async def my_function_with_retry(*args, **kwargs):
    # Function implementation
    pass

# Execute a function with both timeout and retry
result = await with_timeout_and_retry(
    my_async_function,
    *args,
    timeout_seconds=10,
    operation_name="my_operation",
    max_attempts=3,
    **kwargs
)
```

### LLM Timeout

```python
from app.core.timeout import with_llm_timeout, with_llm_timeout_decorator

# Execute an LLM function with timeout and retry
result = await with_llm_timeout(
    llm_client.chat_completion,
    *args,
    timeout_seconds=30,
    max_attempts=3,
    operation_name="chat_completion",
    **kwargs
)

# Decorate an LLM function with timeout and retry
@with_llm_timeout_decorator(operation_type="chat", max_attempts=3)
async def my_llm_function(*args, **kwargs):
    # Function implementation
    pass
```

### RAG Timeout

```python
from app.core.timeout import with_embedding_timeout, with_vector_search_timeout, with_rag_timeout

# Execute an embedding function with timeout and retry
embeddings = await with_embedding_timeout(
    embedding_model.embed_query,
    query,
    timeout_seconds=15,
    max_attempts=3,
    operation_name="embed_query"
)

# Execute a vector search function with timeout, retry, and fallback
results = await with_vector_search_timeout(
    vector_store.search,
    query_embedding,
    timeout_seconds=10,
    max_attempts=3,
    operation_name="vector_search",
    fallback_func=fallback_search
)

# Decorate a RAG function with timeout, retry, and fallback
@with_rag_timeout(operation_type="hybrid_search", max_attempts=3, fallback_func=fallback_search)
async def my_rag_function(*args, **kwargs):
    # Function implementation
    pass
```

### LangGraph Timeout

```python
from app.core.timeout import with_node_timeout, with_analyze_query_timeout

# Decorate a LangGraph node function with timeout and retry
@with_node_timeout(timeout_seconds=30, max_attempts=3, node_name="my_node")
async def my_node_function(state, *args, **kwargs):
    # Node implementation
    return state

# Use a convenience decorator for a specific node type
@with_analyze_query_timeout(timeout_seconds=30, max_attempts=3)
async def analyze_query_node(state, *args, **kwargs):
    # Node implementation
    return state
```

### Circuit Breaker Pattern

```python
from app.core.timeout import CircuitBreaker

# Get or create a circuit breaker instance for a service
circuit_breaker = CircuitBreaker.get_instance(
    "my_service",
    failure_threshold=5,
    recovery_timeout=60.0
)

# Execute a function with circuit breaker protection
result = await circuit_breaker.execute(
    my_async_function,
    *args,
    fallback=my_fallback_function,
    **kwargs
)
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 🧪 Testing

The timeout utilities include comprehensive testing to ensure correct behavior across different scenarios.

### Testing Strategy

The testing strategy for timeout utilities follows these principles:

1. **Isolation**: Tests are isolated from external dependencies for deterministic results
2. **Comprehensive Coverage**: Tests cover both happy paths and error scenarios
3. **Realistic Scenarios**: Tests simulate realistic timeout and retry scenarios
4. **Performance Validation**: Tests verify that timeout and retry mechanisms don't add significant overhead

### Test Implementation

The tests are implemented in the following files:

```
backend/tests/
├── core/
│   └── timeout/
│       ├── test_base.py     # Tests for base timeout utilities
│       ├── test_llm.py      # Tests for LLM timeout utilities
│       ├── test_rag.py      # Tests for RAG timeout utilities
│       └── test_graph.py    # Tests for LangGraph timeout utilities
└── integration/
    └── test_timeout_integration.py  # Integration tests for timeout utilities
```

### Example Tests

```python
# Test basic timeout functionality
@pytest.mark.asyncio
async def test_basic_timeout():
    """Test that timeout works as expected."""
    # Create a function that takes longer than the timeout
    async def slow_function():
        await asyncio.sleep(0.5)  # This should timeout
        return "This should not be returned"

    # Verify that the function times out
    with pytest.raises(TimeoutError):
        await with_timeout(
            slow_function,
            timeout_seconds=0.1,
            operation_name="test_operation"
        )

# Test retry logic
@pytest.mark.asyncio
async def test_retry_logic():
    """Test that retry logic works as expected."""
    # Create a mock that fails twice then succeeds
    mock_function = AsyncMock()
    mock_function.side_effect = [
        Exception("First failure"),
        Exception("Second failure"),
        "Success"
    ]

    # Create a function that uses the mock
    async def retried_function():
        return await mock_function()

    # Verify that the function retries and eventually succeeds
    result = await with_retry(
        retry_on=[Exception],
        max_attempts=3
    )(retried_function)()

    assert result == "Success"
    assert mock_function.call_count == 3  # Called 3 times (1 initial + 2 retries)
```

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 📊 Observability

The timeout utilities include comprehensive logging and metrics for observability:

### Logging

```python
# Log timeout events
logger.warning(
    f"Timeout in {operation_name} after {timeout_seconds}s",
    extra={
        "operation": operation_name,
        "timeout_seconds": timeout_seconds,
        "thread_id": state.metadata.get("thread_id"),
        "user_id": state.metadata.get("user_id")
    }
)

# Log retry events
logger.info(
    f"Retry attempt {attempts}/{max_attempts} for {operation_name} after error: {str(e)}. "
    f"Waiting {wait_time:.2f}s...",
    extra={
        "operation": operation_name,
        "attempt": attempts,
        "max_attempts": max_attempts,
        "wait_time": wait_time,
        "error": str(e)
    }
)

# Log circuit breaker events
logger.warning(
    f"Circuit for {service_name} is now OPEN after {failure_count} failures",
    extra={
        "service": service_name,
        "failure_count": failure_count,
        "state": "OPEN"
    }
)
```

### Metrics

The timeout utilities can be integrated with metrics systems to track:

- **Timeout Count**: Number of timeouts by operation
- **Retry Count**: Number of retries by operation
- **Circuit Breaker State**: State of circuit breakers by service
- **Latency**: Latency of operations with timeout and retry

### Error Reporting

The timeout utilities integrate with the error handling system to report:

- **Timeout Errors**: Errors caused by timeouts
- **Retry Failures**: Errors that persist after retries
- **Circuit Breaker Failures**: Errors caused by open circuit breakers

<div align="right"><a href="#-timeout-utilities">⬆️ Back to top</a></div>

## 📝 Best Practices

### Base Timeout and Retry

```python
from app.core.timeout import with_timeout, with_retry, with_timeout_and_retry

# Execute a function with a timeout
result = await with_timeout(
    my_async_function,
    *args,
    timeout_seconds=10,
    operation_name="my_operation",
    **kwargs
)

# Decorate a function with retry logic
@with_retry(
    retry_on=[TimeoutError, RateLimitError],
    max_attempts=3,
    min_wait=1.0,
    max_wait=10.0,
    multiplier=2.0
)
async def my_function_with_retry(*args, **kwargs):
    # Function implementation
    pass

# Execute a function with both timeout and retry
result = await with_timeout_and_retry(
    my_async_function,
    *args,
    timeout_seconds=10,
    operation_name="my_operation",
    max_attempts=3,
    **kwargs
)
```

### LLM Timeout

```python
from app.core.timeout import with_llm_timeout, with_llm_timeout_decorator

# Execute an LLM function with timeout and retry
result = await with_llm_timeout(
    llm_client.chat_completion,
    *args,
    timeout_seconds=30,
    max_attempts=3,
    operation_name="chat_completion",
    **kwargs
)

# Decorate an LLM function with timeout and retry
@with_llm_timeout_decorator(operation_type="chat", max_attempts=3)
async def my_llm_function(*args, **kwargs):
    # Function implementation
    pass
```

### RAG Timeout

```python
from app.core.timeout import with_embedding_timeout, with_vector_search_timeout, with_rag_timeout

# Execute an embedding function with timeout and retry
embeddings = await with_embedding_timeout(
    embedding_model.embed_query,
    query,
    timeout_seconds=15,
    max_attempts=3,
    operation_name="embed_query"
)

# Execute a vector search function with timeout, retry, and fallback
results = await with_vector_search_timeout(
    vector_store.search,
    query_embedding,
    timeout_seconds=10,
    max_attempts=3,
    operation_name="vector_search",
    fallback_func=fallback_search
)

# Decorate a RAG function with timeout, retry, and fallback
@with_rag_timeout(operation_type="hybrid_search", max_attempts=3, fallback_func=fallback_search)
async def my_rag_function(*args, **kwargs):
    # Function implementation
    pass
```

### LangGraph Timeout

```python
from app.core.timeout import with_node_timeout, with_analyze_query_timeout

# Decorate a LangGraph node function with timeout and retry
@with_node_timeout(timeout_seconds=30, max_attempts=3, node_name="my_node")
async def my_node_function(state, *args, **kwargs):
    # Node implementation
    return state

# Use a convenience decorator for a specific node type
@with_analyze_query_timeout(timeout_seconds=30, max_attempts=3)
async def analyze_query_node(state, *args, **kwargs):
    # Node implementation
    return state
```

## Circuit Breaker Pattern

The `CircuitBreaker` class implements the circuit breaker pattern to prevent repeated calls to failing external services:

```python
from app.core.timeout import CircuitBreaker

# Get or create a circuit breaker instance for a service
circuit_breaker = CircuitBreaker.get_instance(
    "my_service",
    failure_threshold=5,
    recovery_timeout=60.0
)

# Execute a function with circuit breaker protection
result = await circuit_breaker.execute(
    my_async_function,
    *args,
    fallback=my_fallback_function,
    **kwargs
)
```

## Configuration

The timeout utilities use environment variables for configuration:

- `TIMEOUT_LLM`: Default timeout for LLM operations (default: 30 seconds)
- `TIMEOUT_RAG_SEARCH`: Default timeout for RAG search operations (default: 10 seconds)
- `TIMEOUT_RAG_EMBEDDING`: Default timeout for RAG embedding operations (default: 15 seconds)
- `TIMEOUT_AGENT_NODE`: Default timeout for agent node operations (default: 40 seconds)
- `TIMEOUT_EXTERNAL_SERVICE`: Default timeout for external service operations (default: 10 seconds)
- `TIMEOUT_DATABASE`: Default timeout for database operations (default: 5 seconds)
- `RETRY_MAX_ATTEMPTS`: Default maximum number of retry attempts (default: 3)
- `RETRY_MIN_WAIT`: Default minimum wait time between retries (default: 1.0 seconds)
- `RETRY_MAX_WAIT`: Default maximum wait time between retries (default: 10.0 seconds)
- `RETRY_MULTIPLIER`: Default multiplier for exponential backoff (default: 2.0)

Additional environment variables are available for specific components. See the respective module documentation for details.

## Best Practices

1. **Use the appropriate utility for the task**: Choose the utility that best matches the operation you're performing (LLM, RAG, LangGraph, etc.).
2. **Set appropriate timeouts**: Set timeouts that are appropriate for the operation. Operations that are expected to take longer should have longer timeouts.
3. **Use fallback mechanisms**: Provide fallback mechanisms for operations that can fail gracefully.
4. **Log timeout and retry events**: The utilities log timeout and retry events, but you can add additional logging as needed.
5. **Configure circuit breakers appropriately**: Set appropriate failure thresholds and recovery timeouts for circuit breakers based on the service characteristics.
6. **Use the new modular structure**: Use the new modular structure instead of the deprecated compatibility modules.

## Migration Guide

To migrate from the old timeout utilities to the new modular structure:

1. Update imports to use the new modular structure:

   ```python
   # Old imports
   from app.core.timeout_utils import with_timeout, with_retry, with_timeout_and_retry
   from app.rag.timeout import with_embedding_timeout, with_vector_search_timeout
   from app.langgraph.timeout import with_node_timeout, with_analyze_query_timeout

   # New imports
   from app.core.timeout import (
       with_timeout, with_retry, with_timeout_and_retry,
       with_embedding_timeout, with_vector_search_timeout,
       with_node_timeout, with_analyze_query_timeout
   )
   ```

2. Update function calls to use the new utilities (the function signatures are the same, so no changes are needed).

3. Update decorators to use the new utilities (the decorator signatures are the same, so no changes are needed).

4. Remove any direct imports from the deprecated compatibility modules.

## Conclusion

The new modular timeout utilities provide a unified approach to handling timeouts, retries, and circuit breaking across the application. By using these utilities, you can ensure consistent timeout and retry behavior, improve error handling, and prevent cascading failures.
