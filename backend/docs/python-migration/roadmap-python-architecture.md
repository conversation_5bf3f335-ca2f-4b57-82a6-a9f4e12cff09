# BusinessLM: Python Migration Roadmap

This document presents a consolidated roadmap for migrating BusinessLM from TypeScript to a Python-centric architecture. It combines the granular phase structure and integrated testing approach from the MVP roadmap with the architectural context and broader vision from the full roadmap.

> **IMPORTANT**: This roadmap prioritizes maintaining feature parity with the existing implementation while enabling a smooth transition to Python. It uses feature flags and compatibility layers to ensure continuous operation during the migration process, with no downgrade in functionality at any stage.

## 📅 Migration Timeline

| **Phase**  | **Timeline**    | **Key Deliverables** |
|------------|-----------------|----------------------|
| **Sprint 0** | Week 1, Days 1–5 | • Feature flag system with versioned JSON blobs per environment<br>• Pydantic validation for configuration<br>• Server-side flag management with Redis/Firebase persistence<br>• Client-side React hooks with local caching<br>• Admin UI for flag management with role-based access<br>• Monitoring for flag usage and errors<br>• Compatibility layer with standardized JSON schemas |
| **Phase 1** | Weeks 2–4        | • FastAPI application with factory pattern and middleware<br>• Firebase Auth integration with JWT verification<br>• Firebase storage adapter with error handling and retries<br>• mTLS between services with OPA policy sidecar<br>• Prometheus metrics for request count, latency, and errors<br>• X-Request-ID propagation for cross-service tracing<br>• Dual-write mechanism with logical clocks for data consistency |
| **Phase 2** | Weeks 5–7        | • Custom LLM client abstraction with vendor-specific adapters<br>• Model router with fallback chains and tier-based routing<br>• SSE streaming with 30-second chunks and reconnection support<br>• Co-CEO agent migrated to LangGraph with feature flag control<br>• `/api/agents/invoke` endpoint with feature flag routing<br>• Token budget management with quota enforcement<br>• Circuit breaker pattern with TypeScript fallback |
| **Phase 3** | Weeks 8–11       | • Section-aware document processor with format support (TXT, MD, PDF, DOCX)<br>• HuggingFace embeddings with batching and caching<br>• PostgreSQL + pgvector for vector storage with Pinecone fallback<br>• Hybrid retrieval combining BM25 + vector search<br>• Knowledge cache service with smart content classification<br>• Query analyzer with intent detection<br>• Document classifier with type detection |
| **Phase 4** | Weeks 12–15      | • Department agent base class with common functionality<br>• Individual department agents (Finance, HR, Sales, etc.)<br>• LangGraph-based agent orchestration with nodes and edges<br>• Cross-department coordination with graph-based routing<br>• Agent memory management with Redis persistence<br>• Memory Layer Matrix implementation (short-term, persistent, semantic, shared)<br>• Department knowledge access with section prioritization |
| **Phase 5** | Weeks 16–19      | • Integration framework with plugin architecture<br>• Google Drive integration with folder connection and document syncing<br>• Drive update connector with change monitoring<br>• Notion integration with document syncing<br>• Webhook management for various services (Slack, n8n, Zapier)<br>• OAuth token management with storage, refresh, and validation<br>• A2A Protocol Bridge (optional) with feature flag control |
| **Phase 6** | Weeks 20–24      | • Frontend updates for Python-based SSE streaming<br>• Agent UI integration with Python backend<br>• Feature flag UI for migration management<br>• GitHub Actions workflows for testing and deployment<br>• Kubernetes manifests and Helm charts<br>• Vector operations optimization with NumPy/SciPy<br>• Database indexing and connection pooling<br>• Observability tiers implementation (Sentry, LangSmith, Prometheus, Grafana) |

```
Week:    | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 |10 |11 |12 |13 |14 |15 |16 |17 |18 |19 |20 |21 |22 |23 |24 |
         |---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
Sprint 0 |XXX|   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |
Phase 1  |   |XXXXXXXXXXX|   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |
Phase 2  |   |   |   |   |XXXXXXXXXXX|   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |
Phase 3  |   |   |   |   |   |   |   |XXXXXXXXXXXXXXX|   |   |   |   |   |   |   |   |   |   |   |   |   |
Phase 4  |   |   |   |   |   |   |   |   |   |   |   |XXXXXXXXXXXXXXX|   |   |   |   |   |   |   |   |   |
Phase 5  |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |XXXXXXXXXXXXXXX|   |   |   |   |   |
Phase 6  |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |XXXXXXXXXXXXXXXXXXX|
```

This timeline provides a visual representation of the migration phases, showing their duration and overlap. The entire migration is expected to take approximately 24 weeks (6 months) to complete.

## Table of Contents

- [Executive Summary](#-executive-summary)
- [Architectural Vision](#-architectural-vision)
- [Migration Strategy Overview](#-migration-strategy-overview)
- [Migration Phases](#-migration-phases)
  - [Sprint 0: Feature Flag Foundation](#sprint-0-feature-flag-foundation-week-1-days-1-3)
  - [Phase 1: Core Infrastructure](#phase-1-core-infrastructure-weeks-2-4)
  - [Phase 2: LLM & Agent Foundation](#phase-2-llm--agent-foundation-weeks-5-7)
  - [Phase 3: RAG Pipeline](#phase-3-rag-pipeline-weeks-8-11)
  - [Phase 4: Multi-Agent System](#phase-4-multi-agent-system-weeks-12-15)
  - [Phase 5: External Integrations](#phase-5-external-integrations-weeks-16-19)
  - [Phase 6: Frontend Integration & Deployment](#phase-6-frontend-integration--deployment-weeks-20-24)
- [Key Principles](#-key-principles)
- [Appendix: Standards & Conventions](#-appendix-standards--conventions)

---

## 🚀 Executive Summary

- **Goal:** Migrate the existing TypeScript multi-agent RAG system to a Python-centric architecture while preserving and enhancing all functionality.
- **Approach:**
  1. **Feature-flagged migration** starting with core infrastructure and the `/api/agents/invoke` flow.
  2. **Preserve section-aware RAG** capabilities and all existing agent functionality.
  3. **Maintain Co-CEO and department agents** while transitioning to LangGraph.
  4. **Implement compatibility layers** to ensure smooth transition between systems.
  5. **Integrate testing and observability** into each phase of the migration.
  6. **Provide architectural flexibility** for future scaling and enhancement.
- **Outcome:** A more scalable, maintainable, and extensible Python-based implementation with all existing functionality preserved and enhanced.

---

## 🏗️ Architectural Vision

### System Architecture Overview

The migration will transform BusinessLM into a scalable, Python-centric multi-agent RAG architecture with the following high-level components:

```
BusinessLM: Scalable Multi-Agent RAG Architecture

├── Front-End: Web UI Layer (TypeScript / React)
│   ├── React Frontend (Vite | *Switch to Next.js if SSR becomes necessary)
│   │   ├── UI Components and Hooks
│   │   ├── Auth Integration (Supabase / Firebase)
│   │   ├── Multi-Tenant Support & Role-Based Views
│   │   ├── Prompt Template UI (Goals, Priorities, etc.)
│   │   └── Agent Output Streams (SSE -> LLM Streaming / WebSocket -> Bidirectional features)
│   │
│   └── Browser File Integrations
│       ├── Google Drive OAuth2 Flow
│       ├── Notion OAuth2 & GraphQL Client
│       └── Dropzone for Doc Upload (PDF, DOCX, TXT)
│
├── Edge/API Gateway (Node.js | *Switch to hybrid integration with Bun for performance-critical endpoints)
│   ├── CORS & Auth Proxy Layer
│   ├── Webhook Ingress (n8n, Zapier, Slack, etc.)
│   ├── API Request Router (OpenAPI-based)
│   └── Rate Limiting / Usage Monitoring (e.g., Upstash Redis)
│
├── Back-End: Python Services (Core LangGraph System)
│   ├── API Server (FastAPI + Pydantic)
│   │   ├── REST + WebSocket Endpoints
│   │   ├── Auth Verification (Firebase | *Switch to JWT or Supabase for flexibility)
│   │   ├── Webhook Callbacks (async tasks)
│   │   └── Ingress for UI + External Systems
│
│   ├── Multi-Agent System (LangGraph Backbone)
│   │   ├── Co-CEO Node (Orchestration / Planning)
│   │   ├── Departmental Agents (Finance, HR, Sales, Product, etc.)
│   │   ├── Agent-to-Agent Routing (LangGraph edges)
│   │   ├── Shared Memory (context/state persistence)
│   │   ├── Tool Delegation Per Agent
│   │   └── **Optional: A2A Protocol Bridge for external/federated agent interoperability**
│
│   ├── Enhanced RAG Pipeline
│   │   ├── Custom Text Chunker (Markdown, DOCX, PDF)
│   │   ├── Embeddings (HuggingFace | *InstructorXL when instruction-tuned embeddings provide better results)
│   │   ├── Section-aware metadata
│   │   ├── Hybrid Retrieval (BM25 + Vector)
│   │   ├── Doc Source Plugins (Notion, Sheets, GDrive, Word)
│   │   └── Semantic Cache (Redis -> General caching | GPTCache -> semantic, LLM-driven caching)
│
│   ├── Storage & Memory Layer
│   │   ├── Vector Store (PostgreSQL + pgvector | Pinecone as fallback)
│   │   ├── Metadata + Prompt Templates (PostgreSQL)
│   │   └── Agent Memory + Rate Limiting (Redis)
│
│   ├── Agent Tooling & Registry
│   │   ├── Pydantic-based Tool Abstractions (replacing LangChain Tool)
│   │   ├── Department-Scoped Toolsets
│   │   ├── Internal APIs (forecasting, simulations, charts)
│   │   └── Event Triggers (async Webhook → ToolExec)
│
│   ├── External Integrations (n8n, Slack, Notion, etc.)
│   │   ├── n8n-compatible FastAPI endpoints
│   │   ├── OAuth2 Tokens (Notion, GDrive, etc.)
│   │   ├── Webhook callbacks (Slack, Discord, etc.)
│   │   ├── Agent-driven outbound webhooks
│   │   └── **Consider: Expose A2A Protocol endpoints to enable federation with third‑party agent ecosystems**
│
│   ├── LLM Gateway & Streaming
│   │   ├── Custom LLMClient Abstraction (OpenAI, Claude, Gemini, Local)
│   │   ├── Vendor Fallback Router (Model-tier, cost, availability)
│   │   └── Streaming Output (SSE / WebSocket)
│
│   ├── Observability & Monitoring
│   │   ├── LangSmith + OpenTelemetry integration
│   │   ├── Prometheus Metrics (requests, latency, token usage)
│   │   ├── Sentry (frontend/backend error tracking)
│   │   └── Event Replay for Debugging
│
│   └── CI/CD & Deployment Stack
│       ├── GitHub Actions
│       ├── Docker + Kubernetes (prod) / Cloud Run (staging)
│       ├── API Gateway (Traefik)
│       └── Secrets Management (Doppler -> Development | GCP Secret Manager -> Production)
```

### Core Architecture Decisions

#### Monolith vs. Microservices
BusinessLM will follow a **modular monolith** approach initially, with clear boundaries between components to enable future migration to microservices as needed. This approach provides:
- Faster development and simpler deployment in the early stages
- Lower operational overhead while maintaining modularity
- Clear path to microservices when scaling demands it

#### Synchronous vs. Asynchronous Processing
The system implements a **hybrid architecture** combining:
- Synchronous processing for user-triggered actions and immediate feedback
- Asynchronous processing for background tasks like document processing and embedding generation
- Real-time streaming for LLM responses via WebSockets/SSE

#### State Management Strategy
BusinessLM uses a **phased migration approach** for state management:

##### Cost Management
- Implement cost caps with WARN_AT_USD / BLOCK_AT_USD per tier in feature-flag blob
- Return 402 Payment Required response when BLOCK_AT_USD threshold is reached
- Track token usage and cost per organization with daily/monthly limits

##### Data Migration & Consistency
- Initially maintain Firebase compatibility for continuity (leveraging existing Blaze credits)
- Implement dual-write mechanism with logical clocks `(org_id, last_updated_at, op_id)` for data consistency
- Use Pub/Sub with ordering keys to ensure write sequence (single-region initially, multi-region only after proving ordering math)
- Add nightly reconciliation tasks to detect and resolve data inconsistencies with specific SLO (drift < 0.1% within 5 minutes)
- Implement "panic mode" runbook with `writes_blocked` flag in migration-control table
- Require explicit human acknowledgment via admin endpoint (/ops/migrations/unblock) to resume writes
- Implement automatic alerting but manual recovery to prevent cascading failures
- Implement back-pressure mechanisms to prevent data drift during high load
- Unit test logical-clock race conditions to ensure concurrent writes in different regions agree on winner

##### Caching & Performance
- Gradually introduce Redis for short-term, ephemeral state (agent memory, caching) when performance metrics indicate need (p95 > 400ms)
- Separately track first_byte_latency vs steady-state p95; pre-warm Redis connection pool in startup() hook if cold-start delta > 100ms
- Add proactive instance scaling: set Cloud Run min-instances = ceil(current_burst/4) whenever Redis queue_depth > 200
- Design all cache interfaces with in-process fallbacks to maintain serverless compatibility
- Use separate Redis DBs with different eviction policies (volatile-lru for memory, noeviction for quotas)

##### Vector Storage
- Progressively adopt PostgreSQL + pgvector for persistent, structured data and vector storage
- Benchmark PostgreSQL + pgvector with 1M documents to validate performance (P99 < 150ms)
- Test multi-tenant scenarios with varying org counts (1, 10, 100) and collection sizes (100 orgs × 10k docs) to test HNSW shard overhead
- Measure CPU and index memory per tenant; run benchmark on every pgvector version update
- Implement weekly REINDEX CONCURRENTLY on clone table with table name swapping to avoid downtime
- Add monitoring for index:data ratio with alerts when > 3×
- Measure Recall@5 and indexing throughput to ensure production readiness
- Prepare Typesense adapter as contingency for full-text search if PostgreSQL FTS performance is insufficient
- Use abstraction layers to enable smooth transitions between storage technologies
- Implement data residency options for GDPR compliance with region-specific resource provisioning

#### Deployment Model
The system follows a **hybrid deployment strategy** with gradual migration:
- Begin with Firebase integration to maintain existing functionality
- Introduce containerized Python services that work with Firebase
- Deploy to Cloud Run for MVP and staging environments
- Eventually adopt Kubernetes for production in Phase B

### Technology Choices

#### AI & Embedding Models
- **Primary LLM Providers**: OpenAI (GPT-4.1, GPT-4 Mini), Anthropic (Claude 3.7 Sonnet, Claude 3.7 Thinking), Google (Gemini 2.0 Flash, Gemini 2.5 Pro), Perplexity (Sonar Reasoning Pro, Deep Research)
- **Primary Embedding Models**: HuggingFace (BAAI/bge-large-en-v1.5)
- **Specialized Embedding**: InstructorXL when instruction-tuned embeddings provide better results
- **Vector Storage**: PostgreSQL + pgvector with Pinecone as fallback
- **Caching**: Redis for general caching, GPTCache for semantic, LLM-driven caching

#### Vector Search & RAG Stack
- **Document Processing**: Custom section-aware chunking for various formats
- **Retrieval**: Hybrid approach combining BM25 and vector search
- **Reranking**: Cross-encoders and contextual reranking
- **Metadata**: Rich metadata extraction and filtering

#### Multi-Agent Orchestration
- **Framework**: LangGraph for agent orchestration
- **Agent Structure**: Co-CEO as orchestrator with department agents
- **Communication**: Graph-based agent-to-agent routing
- **Memory**: Shared context and conversation history

#### Memory Layer Matrix
| Memory Type | Storage | Purpose | Lifetime |
|------------|---------|---------|----------|
| **Short-term state** | Redis | Conversation context, active goals | Session |
| **Persistent memory** | PostgreSQL | User preferences, past interactions | Permanent |
| **Semantic memory** | GPTCache | Similar past responses, cached reasoning | Configurable TTL |
| **Shared context** | LangGraph state | Cross-agent knowledge, task handoffs | Workflow duration |

#### API & Communication
- **API Framework**: FastAPI with Pydantic models
- **Real-time**: SSE for LLM streaming, WebSockets for bidirectional features
- **Documentation**: OpenAPI with comprehensive schema

#### DevOps & Deployment
- **CI/CD**: GitHub Actions for automated testing and deployment
- **Containers**: Docker for local development and production
- **Orchestration**: Kubernetes for production, Cloud Run for staging
- **Monitoring**: Prometheus, Grafana, and OpenTelemetry

---

## 🔄 Migration Strategy Overview

The migration will follow a phased approach (Sprint 0, Phase 1-6), with each phase building on the previous one while maintaining feature parity with the existing TypeScript implementation. The strategy includes:

1. **Feature Flags First**: Implement a comprehensive feature flag system before any migration work
2. **Gradual Component Migration**: Migrate one component at a time with proper testing and agent-scoped routing
3. **Compatibility Layers**: Create adapters to ensure smooth transition between systems with circuit breaker patterns
4. **Integrated Testing**: Include testing and observability in each phase with clear performance metrics
5. **No Functionality Loss**: Ensure all existing features are preserved throughout the migration
6. **Data Consistency**: Implement dual-write mechanisms with logical clocks and reconciliation tasks
7. **Security First**: Prioritize security with mTLS, OPA policies, and proper data residency controls

Each phase will include:
- Detailed implementation tasks with specific file paths
- Integration with existing TypeScript components
- Comprehensive testing and observability with request ID propagation
- Feature flags for safe rollout and rollback
- Performance benchmarks with clear decision criteria
- Reconciliation mechanisms to ensure data consistency

## 📊 Migration Feature Matrix

This matrix provides a clear view of which features are being migrated, their current TypeScript location, which phase they'll be implemented in, and their current status. It also indicates which components are working well and should be preserved versus those needing significant enhancement.

| Feature | TypeScript Location | Python Phase | Current Status | Implementation Notes |
|---------|-------------------|-------------|----------------|---------------------|
| **Feature Flag System** | N/A (New) | Sprint 0 | ⬜ Not started | New component to enable safe migration |
| **FastAPI Backend** | N/A (New) | Phase 1 | ⬜ Not started | New Python foundation, will coexist with Node.js initially |
| **Firebase Auth Integration** | `src/frontend/auth/AuthContext.tsx` | Phase 1 | ✅ Working well | Preserve existing Firebase auth with Python adapter |
| **Firebase Storage Adapter** | `src/services/firebaseService.ts` | Phase 1 | ✅ Working well | Create Python adapter for existing Firebase storage |
| **LLM Client Abstraction** | `src/genkit/llm/llmClient.ts` | Phase 2 | ✅ Working well | Port to Python with same interface, add observability |
| **Model Router** | `src/genkit/llm/modelRouter.ts` | Phase 2 | ✅ Working well | Port to Python with enhanced fallback capabilities |
| **Streaming Implementation** | `src/genkit/llm/streamHandler.ts` | Phase 2 | ✅ Working well | Focus on SSE initially, WebSocket later if needed |
| **Co-CEO Agent** | `src/genkit/agents/coCEOAgent.ts` | Phase 2 | ⚠️ Needs enhancement | Migrate to LangGraph with improved orchestration |
| **Agent Invoke Endpoint** | `src/api/agents/invoke.ts` | Phase 2 | ✅ Working well | Port to FastAPI with feature flag routing |
| **Document Processing** | `src/genkit/knowledge/documentProcessor.ts` | Phase 3 | ✅ Working well | Port to Python with same capabilities |
| **Embedding Service** | `src/genkit/knowledge/embeddingService.ts` | Phase 3 | ⚠️ Needs enhancement | Migrate to HuggingFace models with better batching |
| **Vector Storage** | `src/genkit/knowledge/vectorStore.ts` | Phase 3 | ⚠️ Needs enhancement | Migrate to PostgreSQL + pgvector for better performance |
| **Hybrid Retrieval** | `src/genkit/knowledge/hybridRetrieval.ts` | Phase 3 | ⚠️ Needs enhancement | Enhance with BM25 + vector fusion algorithms |
| **Section-Aware Chunking** | `src/genkit/knowledge/chunker.ts` | Phase 3 | ✅ Working well | Preserve existing logic with Python implementation |
| **Knowledge Cache Service** | `src/genkit/knowledge/KnowledgeCacheService.ts` | Phase 3 | ✅ Working well | Port to Python with same capabilities |
| **Query Analyzer** | `src/genkit/knowledge/queryAnalyzer.ts` | Phase 3 | ✅ Working well | Port to Python with same capabilities |
| **Document Classifier** | `src/genkit/knowledge/documentClassifier.ts` | Phase 3 | ✅ Working well | Port to Python with same capabilities |
| **Department Agent Base** | `src/genkit/agents/departmentAgentBase.ts` | Phase 4 | ⚠️ Needs enhancement | Migrate to LangGraph with improved orchestration |
| **Department Agents** | `src/genkit/agents/departmentAgents.ts` | Phase 4 | ⚠️ Needs enhancement | Migrate to LangGraph with improved orchestration |
| **Department Knowledge Access** | `src/genkit/agents/departmentKnowledgeAccess.ts` | Phase 4 | ✅ Working well | Port to Python with same capabilities |
| **Cross-Department Coordination** | `src/genkit/knowledge/crossDepartmentCoordinator.ts` | Phase 4 | ⚠️ Needs enhancement | Enhance with LangGraph routing capabilities |
| **Agent Memory Management** | `src/genkit/agents/agentMemory.ts` | Phase 4 | ✅ Working well | Port to Python with Redis for persistence |
| **Google Drive Integration** | `src/services/googleDriveService.ts` | Phase 5 | ✅ Working well | Port to Python with same capabilities |
| **Drive Update Connector** | `src/services/GoogleDriveUpdateConnector.ts` | Phase 5 | ✅ Working well | Port to Python with same capabilities |
| **Notion Integration** | `src/services/notionService.ts` | Phase 5 | ✅ Working well | Port to Python with same capabilities |
| **Webhook Management** | `src/services/webhookService.ts` | Phase 5 | ✅ Working well | Port to Python with enhanced security |
| **OAuth Token Management** | `src/services/oauthService.ts` | Phase 5 | ✅ Working well | Port to Python with enhanced security |
| **SSE Client Integration** | `src/frontend/hooks/useAgentStream.tsx` | Phase 6 | ✅ Working well | Update to work with Python backend |
| **Agent UI Integration** | `src/frontend/components/AgentPlayground.tsx` | Phase 6 | ✅ Working well | Update to work with Python backend |
| **Feature Flag UI** | N/A (New) | Phase 6 | ⬜ Not started | New component for migration management |
| **CI/CD Pipeline** | `.github/workflows/` | Phase 6 | ⚠️ Needs enhancement | Enhance for Python + TypeScript hybrid |
| **Kubernetes Deployment** | N/A (New) | Phase 6 | ⬜ Not started | New component for production scaling |
| **Performance Optimization** | Various | Phase 6 | ⚠️ Needs enhancement | Focus on vector operations and database |

### Technology Transition Map

This map shows which technologies will be maintained, which will be transitioned, and which new ones will be introduced during the migration.

| Component | Current Technology | Target Technology | Transition Strategy | Decision Criteria |
|-----------|-------------------|-------------------|---------------------|-------------------|
| **Frontend** | React + TypeScript | React + TypeScript | Maintain existing frontend with updates for Python backend | N/A - Maintain existing |
| **Edge Layer** | Node.js | Node.js | Package Node gateway as its own Cloud Run revision; enable traffic switching at Load Balancer level | Benchmark Node 20 vs Bun with real workloads (JWT + Redis); only adopt Bun if delta > 15ms; otherwise use Node.js only |
| **API Server** | Node.js Express | FastAPI + Pydantic | Gradual migration with feature flags and agent-scoped routing | Enable per-agent feature flags for incremental migration |
| **Authentication** | Firebase Auth | Firebase Auth | Freeze on Firebase for initial phases; document Supabase migration path | Revisit after Phase 1 feedback; avoid hybrid auth in a single request path |
| **Storage** | Firebase Firestore | Firebase → PostgreSQL | Dual-write with logical clocks, Pub/Sub ordering keys, and nightly reconciliation | Monitor reconciliation reports; switch to PostgreSQL-only when drift < 0.01% |
| **Vector Storage** | Firebase/Custom | PostgreSQL + pgvector | Benchmark with 1M docs; prepare Typesense adapter for full-text search if needed; use REINDEX CONCURRENTLY on clone table with table name swapping | If P99 > 150ms or Recall@5 < 85%, implement specialized search solution |
| **Caching** | In-memory/Custom | In-memory with Redis option | Design all cache interfaces with in-process fallbacks | Implement Redis when p95 latency > 400ms; use separate Redis DBs with different eviction policies (volatile-lru for memory, noeviction for quotas) |
| **Agent Orchestration** | Custom TypeScript | LangGraph | Migrate with feature flags and agent-scoped routing | Implement circuit breaker pattern with fallback to TypeScript |
| **Embedding Models** | OpenAI/Custom | HuggingFace (BAAI/bge-large-en-v1.5) | Migrate with quality benchmarking | Compare Recall@5 and latency metrics before switching |
| **Streaming** | Custom | SSE with 30s chunks | Implement chunked SSE with reconnection tokens and jittered retry (±3s); use X-Accel-Buffering: no; ship GA on SSE only | WebSockets only as post-GA feature if customer demand justifies; Kubernetes becomes post-GA consideration |
| **Observability** | Basic logging | Prometheus + Sentry + LangSmith with X-Request-ID correlation | Implement request ID propagation through all services including WebSocket/SSE and background tasks | Generate X-Request-ID in API gateway; include in first SSE event, WebSocket subprotocol header, Celery task headers, all logs and LangSmith metadata; configure OpenTelemetry SpanProcessor |
| **Deployment** | Custom/Manual | Cloud Run | Start with Cloud Run; monitor cold-start impact | Move to Kubernetes if cold-starts affect >10% of requests |
| **Secrets Management** | Environment variables | Doppler (local dev), GCP Secret Manager (all other envs) | Use GCP Secret Manager for non-local environments; add pre-commit hook to prevent credential leaks; use read-only Doppler service tokens for local dev | Implement monthly secret rotation via GitHub Actions; export minimal dev keys in .envrc |
| **Security** | Basic | mTLS + OPA policy | Implement mTLS between services; add OPA sidecar with initial "allow-all" policy; benchmark OPA to ensure < 2ms per request; mount /opa/policies as ConfigMap in staging for hot-reload | Required from Phase 1; use preloaded policies or Wasm compilation for prod; mount /tmp/tools read-only; stub requests session with allowlisted domains; A2A bridge validates peer-agent claim → org_id mapping through JWKS |

---

## 🎯 Migration Phases

### Sprint 0: Feature Flag Foundation (Week 1, Days 1-5)

#### Architectural Context
Feature flags are a critical component of the migration strategy, enabling gradual rollout and instant rollback capabilities. The feature flag system will be implemented as a foundational layer before any migration work begins, ensuring all subsequent phases can be safely deployed and tested without disrupting the existing system.

#### Technical Specification

##### Feature Flag System Design

1. **Core Components**
   - **Config Staging Pattern**: Single versioned JSON blob per environment (alpha.json, beta.json)
   - **Validation**: Pydantic models for configuration validation
   - **Versioning**: Roll forward by bumping blob version number
   - **Server-side Flag Management**: Python module with Redis/Firebase persistence
   - **Client-side Flag Management**: React hook with local caching
   - **Admin Interface**: UI for managing flags with role-based access
   - **Monitoring**: Metrics collection for flag usage and errors

2. **Flag Types**
   - **Boolean Flags**: Simple on/off toggles for features
   - **Percentage Rollout Flags**: Gradual rollout to percentage of users
   - **User-targeted Flags**: Enable features for specific users/organizations
   - **Environment-specific Flags**: Different values per environment (dev/staging/prod)
   - **Agent-scoped Flags**: Enable Python implementation for specific agent types
   - **Rate Limiter Scope**: Allow limiter to switch scope based on endpoint type (user_id, IP, custom token)

3. **Persistence Layer**
   - Initial storage in Firebase Firestore for compatibility
   - Schema design for flag definitions:
     ```json
     {
       "flag_id": "python_finance_agent",
       "name": "Python Finance Agent",
       "description": "Routes Finance Agent queries to Python implementation",
       "enabled": false,
       "agent_type": "finance",
       "rollout_percentage": 0,
       "targeted_users": [],
       "targeted_orgs": [],
       "created_at": "2023-06-01T00:00:00Z",
       "updated_at": "2023-06-01T00:00:00Z",
       "owner": "migration-team"
     }
     ```
   - Real-time updates via Firebase listeners

4. **Integration Architecture**
   - **Backend Integration**:
     - Python decorator `@feature_flag_enabled("flag_name")` for API routes
     - Middleware for request-level flag evaluation
     - Circuit breaker pattern with fallback to TypeScript implementation
     - Agent-specific routing based on agent type in request
   - **Frontend Integration**:
     - React hook `useFeatureFlag("flag_name")` for UI components
     - Higher-order component `withFeatureFlag("flag_name")(Component)`
     - Fallback UI components for disabled features

##### Compatibility Layer Specification

1. **TypeScript-Python Interface**
   - **Data Formats**: Standardized JSON schemas for all cross-language communication
   - **API Contracts**: OpenAPI specification for all endpoints
   - **Error Handling**: Consistent error format across both implementations
   - **X-Request-ID Propagation**: Request ID headers passed between all services

2. **Routing Mechanism**
   - **API Gateway**: Routes requests based on feature flag state and agent type
   - **Fallback Logic**: Automatic retry with TypeScript implementation on Python errors
   - **Response Validation**: Ensures both implementations return compatible responses
   - **Circuit Breaker**: Automatically falls back to TypeScript after error threshold

3. **State Synchronization**
   - **Shared Database Access**: Both implementations read/write to same Firebase collections
   - **Logical Clock**: Use `(org_id, last_updated_at, op_id)` tuple for ordering
   - **Pub/Sub Topic**: Define message schema with ordering keys for dual-write consistency (single-region initially)
   - **Reconciliation Task**: Nightly Celery task to compare and resolve inconsistencies
   - **Sync SLO**: Maintain drift < 0.1% within 5 minutes with alerting
   - **Panic Mode Runbook**: Define clear actions with `writes_blocked` flag in migration-control table
   - **Unblock Process**: Require explicit human acknowledgment via admin endpoint (/ops/migrations/unblock) to resume writes
   - **Back-pressure Mechanism**: Cap queue length and fall back to single-writer mode if lag exceeds threshold

##### Testing Strategy

1. **Feature Parity Testing**
   - **Automated Comparison**: Side-by-side testing of TypeScript vs Python implementations
   - **Response Diff Testing**: Automated comparison of response structures
   - **Performance Benchmarking**: Latency and throughput comparison

2. **Test Plan Components**
   - **Unit Tests**: For individual Python components
   - **Integration Tests**: For Python components working together
   - **Parity Tests**: Comparing TypeScript and Python implementations
   - **Load Tests**: Ensuring Python implementation meets performance requirements
   - **Reconciliation Tests**: Verifying data consistency between Firebase and PostgreSQL

3. **Metrics for Comparison**
   - **Functional Correctness**: Response accuracy compared to TypeScript
   - **Performance**: Response time, throughput, and resource usage
   - **Error Rate**: Frequency and types of errors
   - **Token Usage**: Efficiency of LLM token consumption
   - **Data Consistency**: Drift percentage between Firebase and PostgreSQL

##### Benchmarking Framework

1. **Node.js Gateway Performance**
   - Implement `scripts/benchmark_node_gateway.py` to measure p95 latency
   - Add instrumentation to existing Node.js gateway
   - Separately track first_byte_latency vs steady-state p95; alert on values > 600ms
   - Benchmark Node 20 vs Bun with real workloads (JWT + Redis); only adopt Bun if delta > 15ms
   - Package Node gateway as its own Cloud Run revision; enable traffic switching at Load Balancer level
   - Define performance thresholds for runtime decisions
   - Document findings in Architecture Decision Record (ADR)

2. **PostgreSQL + pgvector Benchmark**
   - Create `scripts/benchmark_pgvector.py` with the following metrics:
     - P95/P99 latency for hybrid queries with 1M documents
     - Recall@5 on a labeled QA dataset
     - Indexing throughput (documents per hour)
     - Multi-tenant scenarios with varying org counts (1, 10, 100) and collection sizes (100 orgs × 10k docs) to test HNSW shard overhead
     - CPU and index memory usage per tenant
   - Commit benchmark to `/bench/pgvector_multi_tenant.py` and run on every pgvector version update
   - Implement weekly REINDEX CONCURRENTLY on clone table with table name swapping to avoid downtime
   - Add monitoring for index:data ratio with alerts when > 3×
   - Define clear thresholds for database strategy decisions
   - Prepare contingency plans for specialized search solutions if needed

3. **OPA Policy Performance**
   - Benchmark OPA sidecar to ensure < 2ms per request latency
   - Test with various policy complexities
   - Mount /opa/policies as ConfigMap in staging for hot-reload during development
   - Evaluate preloaded policies vs. Wasm compilation options for production
   - Compile to Wasm only for production environments

4. **Dual-Write Consistency Testing**
   - Create unit tests for logical-clock race conditions (P0 merge blocker before Alpha start)
   - Simulate concurrent writes in different regions
   - Verify consistent winner selection across distributed systems
   - Measure drift percentage and reconciliation effectiveness
   - Implement `writes_blocked` flag in migration-control table
   - Create admin endpoint (/ops/migrations/unblock) for explicit human acknowledgment to resume writes

#### Implementation Tasks

1. **Feature Flag Infrastructure**
   - Implement `backend/config/feature_flags.py` for server-side flag management
   - Create React hook `useFeatureFlag()` in frontend for client-side flags
   - Set up persistence layer for flag states (Firebase initially)
   - Implement admin API endpoints for flag management
   - Add proper error handling and logging

2. **Flag Integration Points**
   - Identify all API endpoints that will need feature flags
   - Create wrapper components for flagged UI elements
   - Implement fallback mechanisms for all flagged features
   - Add monitoring for flag usage and errors
   - Create documentation for flag management

**Testing & Metrics**
- Unit tests for flag system and integration points
- Smoke test for flag toggling and fallback behavior
- Basic metrics for flag usage and errors
- Logging for all flag-related events
- Dashboard for flag status monitoring

#### Success Criteria
- Feature flag system is operational in both frontend and backend
- All identified integration points have flag wrappers
- Monitoring and logging are in place for flag usage
- Documentation is available for the team

**Go Criteria**
- 100% of identified endpoints have flag wrappers
- All flag tests passing in CI pipeline
- Flag dashboard shows accurate status

**No-Go Triggers**
- Any flag-related errors in production
- Missing documentation for flag management
- Incomplete test coverage for flag system

---

### Phase 1: Core Infrastructure (Weeks 2-4)

#### Architectural Context
Phase 1 establishes the foundation for the Python backend, including the FastAPI application structure, authentication integration, and storage compatibility layers. This phase focuses on creating the basic building blocks that will support all subsequent migration work while ensuring compatibility with the existing TypeScript implementation. Rather than replacing existing components, this phase builds alongside them, creating a parallel Python infrastructure that can gradually take over functionality.

#### Technology Decisions

1. **FastAPI vs. Other Frameworks**
   - **Selected**: FastAPI with Pydantic
   - **Rationale**: Combines high performance with strong typing and automatic documentation
   - **Alternatives Considered**: Flask (less performant), Django (too heavyweight), Starlette (too low-level)

2. **Authentication Strategy**
   - **Selected**: Firebase Auth with Python adapter
   - **Rationale**: Maintains compatibility with existing auth system while enabling future flexibility
   - **Migration Path**: Abstract auth layer to allow future transition to JWT or Supabase
   - **Hard Rule**: No feature flags may create hybrid auth (Firebase+Supabase) in a single request path

3. **Storage Approach**
   - **Selected**: Firebase adapter with PostgreSQL preparation
   - **Rationale**: Leverages existing Firebase Blaze credits while preparing for future scalability
   - **Migration Path**: Dual-write with logical clocks and Pub/Sub ordering keys
   - **Reconciliation**: Nightly tasks to detect and resolve inconsistencies
   - **Success Metric**: Switch to PostgreSQL-only when drift < 0.01%

4. **Deployment Strategy**
   - **Selected**: Cloud Run for serverless deployment
   - **Rationale**: Simplifies initial deployment and scaling
   - **Limitations**: 30-second connection limit requires chunked SSE implementation
   - **Edge Layer Strategy**: Package Node gateway as its own Cloud Run revision; enable traffic switching at Load Balancer level
   - **Migration Path**: Monitor cold-start impact; move to GKE if affecting >10% of requests
   - **Environment Consistency**: All environments deployed with the same Terraform module; only instance class and replica count change

5. **Observability Foundation**
   - **Selected**: Prometheus + Sentry + LangSmith with X-Request-ID correlation
   - **Rationale**: Provides essential metrics, error tracking, and LLM tracing
   - **Implementation**: Generate X-Request-ID in API gateway; propagate through all services including WebSocket/SSE
   - **Streaming Correlation**: Include X-Request-ID in first SSE event and WebSocket subprotocol header
   - **Background Tasks**: Inject X-Request-ID into Celery task headers; configure OpenTelemetry SpanProcessor
   - **Performance Metrics**: Track first_byte_latency separately from steady-state p95; alert on values > 600ms
   - **Cost Tracking**: Implement WARN_AT_USD / BLOCK_AT_USD per tier in feature-flag blob
   - **Payment Required**: Return 402 response when BLOCK_AT_USD threshold is reached
   - **Integration**: Include X-Request-ID in LangSmith metadata for cross-service tracing

6. **Security Foundation**
   - **Selected**: mTLS between services + OPA policy sidecar
   - **Rationale**: Ensures secure service-to-service communication and prepares for fine-grained access control
   - **Implementation**: Generate client certificates for service-to-service communication
   - **Performance**: Benchmark OPA sidecar to ensure < 2ms per request latency
   - **Development**: Mount /opa/policies as ConfigMap in staging for hot-reload
   - **Optimization**: Use preloaded policies or Wasm compilation for production
   - **Initial Policy**: Start with "allow-all" policy stub for future extension
   - **Secrets Protection**: Add pre-commit hook (bin/guard-secrets.sh) to prevent credential leaks
   - **Doppler Security**: Use read-only service token with minimal dev keys in .envrc
   - **Tool Sandboxing**: Mount /tmp/tools read-only; stub requests session with allowlisted domains
   - **Long-term Sandboxing**: Explore Firecracker/VM-pool for untrusted code execution
   - **A2A/MCP Security**: Add integration_trust_level enum to classify different integration types
   - **A2A Authentication**: A2A bridge validates peer-agent claim → org_id mapping through JWKS of the peer mesh

#### Implementation Tasks

1. **FastAPI Project Structure**
   - Create `backend/` with FastAPI app factory pattern
   - Implement `backend/app.py` with application factory function
   - Add `backend/config.py` for environment-based configuration
   - Create `backend/api/` package for all API endpoints
   - Implement health check and version endpoints
   - Add CORS middleware to allow React frontend access
   - Set up multi-stage Dockerfile and docker-compose for local development
   - Add basic Prometheus metrics for request count, latency, and error rate
   - Implement comprehensive logging with correlation IDs

2. **Firebase Auth Integration**
   - Create `backend/auth/firebase_auth.py` for JWT verification using firebase-admin SDK
   - Implement token verification and validation with proper error handling
   - Add middleware to inject user context into requests
   - Create authentication dependency for FastAPI routes
   - Test compatibility with existing frontend auth flow in `src/frontend/auth/AuthContext.tsx`
   - Implement role-based access control decorators (`@requires_role`, `@scoped_to_organization`)
   - Create comprehensive audit logging for authentication events

3. **Firebase Compatibility Layer**
   - Implement `backend/storage/firebase_adapter.py` for Firestore access
   - Create `FirestoreClient` class with methods matching current access patterns
   - Add proper error handling and retries for Firebase operations
   - Implement document and collection abstractions matching current usage
   - Create compatibility layer for vector database access
   - Add abstraction layer for future migration to PostgreSQL

**Testing & Metrics**
- Unit tests for auth middleware and Firebase adapter
- Integration test for authentication flow
- Metrics for Firebase operations and latency
- Logging for all auth and storage operations
- Health check endpoint with dependency status

#### Success Criteria
- FastAPI application is running with proper configuration
- Firebase authentication is working with the existing frontend
- Firebase storage adapter can read and write data
- Tests are passing for all implemented components
- Metrics and logging are providing visibility into the system

**Go Criteria**
- FastAPI health check endpoint returns 200 OK
- Authentication flow works with existing frontend
- Firebase adapter passes all integration tests
- 90% test coverage for new Python modules

**No-Go Triggers**
- Authentication failures with existing frontend
- Data inconsistency between Firebase adapters
- Performance degradation compared to TypeScript implementation
- Security vulnerabilities in authentication flow

---

### Phase 2: LLM & Agent Foundation (Weeks 5-7)

#### Architectural Context
Phase 2 establishes the core components for language model interaction and agent orchestration. This includes creating abstraction layers for different LLM providers, implementing streaming capabilities, and migrating the Co-CEO agent to Python while maintaining compatibility with the existing TypeScript implementation.

#### Technology Decisions

1. **LLM Client Abstraction**
   - **Selected**: Custom Python client with vendor-specific adapters
   - **Rationale**: Provides flexibility for multiple LLM providers (GPT, Claude, Gemini)
   - **Implementation**: Abstract base class with provider-specific implementations
   - **Fallback Strategy**: Implement automatic fallback between models based on availability and cost

2. **Streaming Implementation**
   - **Selected**: Server-Sent Events (SSE) with 30-second chunks
   - **Rationale**: Compatible with Cloud Run's connection limitations while providing streaming capability
   - **Implementation**: Chunked SSE with secure reconnection tokens and jittered retry (±3s) to prevent reconnection storms
   - **Performance**: Use X-Accel-Buffering: no header to reduce buffering latency
   - **Trace Propagation**: Include X-Request-ID in first SSE event for correlation
   - **Client Strategy**: Implement client-side reconnection with exponential backoff
   - **WebSockets**: Ship GA on SSE only; consider WebSockets only as post-GA feature if customer demand justifies

3. **Agent Orchestration**
   - **Selected**: LangGraph with agent-scoped feature flags
   - **Rationale**: Provides sophisticated agent orchestration with gradual migration capability
   - **Implementation**: Start with Co-CEO agent, enable per-agent routing
   - **Circuit Breaker**: Implement automatic fallback to TypeScript on error threshold

4. **Token Budget Management**
   - **Selected**: Middleware-based quota enforcement
   - **Rationale**: Prevents unexpected costs from LLM usage
   - **Implementation**: Check org.token_spent_today < org.daily_quota before requests
   - **Alerting**: Prometheus rule for token consumption rate

#### Implementation Tasks

1. **LLM Client Abstraction**
   - Create `backend/llm/base.py` with `BaseLLMClient` abstract class
   - Implement `OpenAIClient`, `AnthropicClient`, and `GeminiClient` classes
   - Add token counting, usage tracking, and rate limiting
   - Implement proper error handling, retries, and circuit breakers
   - Create comprehensive logging for all LLM operations
   - Add cost tracking and optimization

2. **Model Router & Streaming**
   - Implement `backend/llm/router.py` for model selection and fallback
   - Add support for model-tier routing based on task complexity
   - Create fallback chains for model unavailability
   - Implement `backend/llm/stream.py` for SSE streaming
   - Add heartbeat mechanism and reconnection support
   - Create compatibility with existing frontend streaming
   - Implement rate limiting and authentication

3. **Co-CEO Agent Migration**
   - Create `backend/agents/co_ceo.py` matching functionality in `src/genkit/agents/coCEOAgent.ts`
   - Implement conversation context management
   - Add proper error handling and logging
   - Create feature flag for routing between TypeScript and Python implementations
   - Implement `/api/agents/invoke` endpoint with feature flag routing
   - Add fallback to existing TypeScript implementation
   - Create compatibility layer for seamless transition

**Testing & Metrics**
- Unit tests for LLM clients and router
- Integration test for streaming and agent invoke flow
- Metrics for token usage, latency, and completion rate
- Parity tests comparing TypeScript and Python implementations
- Streaming performance benchmarks

#### Success Criteria
- LLM clients are operational with proper error handling and fallbacks
- Co-CEO agent is functioning with feature flag control
- Streaming is working correctly with the existing frontend
- Tests are passing for all implemented components
- Metrics and logging are providing visibility into the system
- Performance is comparable to or better than the existing implementation

**Go Criteria**
- Python `/api/agents/invoke` live behind feature flag
- Streaming latency < 200ms 95% of the time
- 95% of TypeScript-to-Python parity tests pass
- No SEV1 errors in last 48 hours

**No-Go Triggers**
- Feature flag rollback needed for > 5% of users
- Streaming failures or significant latency increase
- Agent response quality degradation
- Security vulnerabilities in LLM client implementation

---

### Phase 3: RAG Pipeline (Weeks 8-11)

#### Architectural Context
The RAG Pipeline phase focuses on migrating the retrieval-augmented generation components to Python. This includes implementing section-aware document processing, embedding generation, vector storage, and hybrid retrieval capabilities. The goal is to maintain the sophisticated RAG functionality of the existing system while enabling future enhancements through the Python architecture.

#### Technology Decisions

1. **Document Processing**
   - **Selected**: Custom Python-based document processor with section awareness
   - **Rationale**: Maintains the sophisticated section-aware retrieval from current implementation
   - **Implementation**: Port existing TypeScript chunking logic with enhancements
   - **Formats**: Support for TXT, MD, PDF, DOCX with consistent metadata extraction

2. **Embedding Generation**
   - **Selected**: HuggingFace (BAAI/bge-large-en-v1.5) with InstructorXL option
   - **Rationale**: Provides high-quality embeddings with flexibility for specialized cases
   - **Implementation**: Efficient batching and caching for performance
   - **Benchmarking**: Compare Recall@5 and latency metrics before full migration

3. **Vector Storage**
   - **Selected**: PostgreSQL + pgvector with Typesense contingency
   - **Rationale**: Simplifies infrastructure while providing robust vector operations
   - **Implementation**: Benchmark with 1M documents to validate performance
   - **Contingency**: Prepare Typesense adapter for full-text search if PostgreSQL FTS performance is insufficient

4. **Hybrid Retrieval**
   - **Selected**: Combined BM25 + vector search with fusion algorithms
   - **Rationale**: Provides better retrieval quality than either approach alone
   - **Implementation**: Adaptive weighting based on query characteristics
   - **Performance**: Monitor P99 latency; implement specialized solutions if exceeding 150ms

#### Implementation Tasks

1. **Document Processing & Embedding**
   - Create `backend/rag/document_processor.py` with section-aware chunking
   - Implement support for various document formats (TXT, MD, PDF, DOCX)
   - Add metadata extraction and section detection
   - Implement `backend/rag/embeddings.py` with HuggingFace models
   - Add support for BAAI/bge-large-en-v1.5 as primary model
   - Create fallback to sentence-transformers/all-mpnet-base-v2 for faster processing
   - Implement batching and caching for efficient embedding generation

2. **Vector Storage & Retrieval**
   - Create `backend/rag/vector_store.py` with abstraction layer
   - Implement PostgreSQL + pgvector adapter as primary storage
   - Add Pinecone adapter as fallback for specialized needs
   - Create `backend/rag/retrieval.py` with hybrid retrieval capabilities
   - Implement BM25 search using PostgreSQL's full-text search
   - Add vector search using pgvector with appropriate indexes
   - Create fusion algorithms for combining BM25 and vector results

3. **Knowledge Management**
   - Implement `backend/rag/cache.py` matching functionality in `src/genkit/knowledge/KnowledgeCacheService.ts`
   - Add smart content classification for cache decisions
   - Create `backend/rag/query_analyzer.py` matching functionality in `src/genkit/knowledge/queryAnalyzer.ts`
   - Implement intent detection and classification
   - Create `backend/rag/document_classifier.py` matching functionality in `src/genkit/knowledge/documentClassifier.ts`
   - Add document type detection and classification

**Testing & Metrics**
- Unit tests for document processing and embedding
- Integration test for retrieval pipeline
- Metrics for embedding generation time and retrieval latency
- Retrieval quality benchmarks (precision, recall)
- Parity tests comparing TypeScript and Python implementations

#### Success Criteria
- Section-aware document processing is working correctly
- Embedding generation is efficient and accurate
- Vector storage is operational with proper error handling
- Hybrid retrieval is providing high-quality results
- Knowledge cache is functioning with proper invalidation
- Query analysis and document classification are accurate
- Tests are passing for all implemented components
- Metrics and logging are providing visibility into the system
- Performance is comparable to or better than the existing implementation

**Go Criteria**
- Retrieval precision/recall ≥ TypeScript implementation
- Embedding generation < 500ms per document chunk
- Hybrid retrieval latency < 300ms for 95% of queries
- Cache hit rate > 70% for repeated queries

**No-Go Triggers**
- Retrieval quality degradation compared to TypeScript
- Embedding generation bottlenecks
- Cache invalidation failures
- Document processing errors for supported formats

---

### Phase 4: Multi-Agent System (Weeks 12-15)

#### Architectural Context
The Multi-Agent System phase focuses on migrating the departmental agents and agent orchestration to Python using LangGraph. This includes implementing the department agent base class, individual department agents, department knowledge access, and cross-department coordination. The goal is to maintain the sophisticated multi-agent capabilities of the existing system while enabling future enhancements through the LangGraph architecture.

#### Technology Decisions

1. **Agent Framework**
   - **Selected**: LangGraph for agent orchestration
   - **Rationale**: Provides sophisticated graph-based agent orchestration with state management
   - **Implementation**: Create nodes for Co-CEO and department agents with proper edges
   - **Migration**: Agent-scoped feature flags for gradual transition

2. **Department Agent Implementation**
   - **Selected**: Python-based department agents with LangGraph integration
   - **Rationale**: Maintains existing agent capabilities while enabling future enhancements
   - **Implementation**: Port existing TypeScript agents with improved orchestration
   - **Fallback**: Circuit breaker pattern with automatic fallback to TypeScript

3. **Cross-Department Coordination**
   - **Selected**: LangGraph-based coordination with shared memory
   - **Rationale**: Provides more sophisticated routing than current implementation
   - **Implementation**: Graph-based routing with state persistence
   - **Enhancement**: Improved context sharing between agents

4. **Agent Memory Management**
   - **Selected**: Redis-backed memory with in-process fallback
   - **Rationale**: Provides persistent memory with serverless compatibility
   - **Implementation**: Abstract memory interface with Redis and in-memory implementations
   - **Performance**: Monitor memory access patterns; optimize based on usage

#### Implementation Tasks

1. **LangGraph Spike (Week 12, Days 1-2)**
   - Create proof-of-concept for LangGraph-based agent orchestration
   - Test agent-to-agent communication patterns
   - Validate state management approach
   - Identify potential integration challenges
   - Document findings and implementation strategy

2. **Department Agents**
   - Create `backend/agents/department/base.py` with base department agent class
   - Implement common functionality for all department agents
   - Create `backend/agents/department/finance.py`, `hr.py`, `sales.py`, etc.
   - Match functionality in `src/genkit/agents/departmentAgents.ts`
   - Add department-specific prompt templates and tools
   - Implement `backend/agents/department/knowledge.py` matching functionality in `src/genkit/agents/departmentKnowledgeAccess.ts`
   - Add section prioritization and filtering

3. **Agent Orchestration**
   - Implement `backend/agents/graph.py` for LangGraph-based agent orchestration
   - Create nodes for Co-CEO and department agents
   - Add edges for agent-to-agent communication
   - Create `backend/agents/coordinator.py` matching functionality in `src/genkit/knowledge/crossDepartmentCoordinator.ts`
   - Implement coordination strategies and routing
   - Create `backend/agents/memory.py` matching functionality in `src/genkit/agents/agentMemory.ts`
   - Add conversation history and context management

**Testing & Metrics**
- Unit tests for department agents and LangGraph integration
- Integration test for multi-agent orchestration
- Metrics for agent communication latency and memory usage
- Response quality evaluation for department agents
- Parity tests comparing TypeScript and Python implementations

#### Success Criteria
- Department agents are functioning with proper error handling
- LangGraph orchestration is working correctly
- Cross-department coordination is effective
- Agent memory management is reliable
- Tests are passing for all implemented components
- Metrics and logging are providing visibility into the system
- Performance is comparable to or better than the existing implementation

**Go Criteria**
- All department agents pass parity tests with TypeScript
- LangGraph orchestration successfully routes queries
- Cross-department coordination produces coherent responses
- Memory management correctly maintains conversation context

**No-Go Triggers**
- Department agent response quality degradation
- LangGraph orchestration failures
- Memory leaks or context loss
- Significant latency increase in agent communication

---

### Phase 5: External Integrations (Weeks 16-19)

#### Architectural Context
The External Integrations phase focuses on migrating the integration points with external systems to Python. This includes implementing Google Drive integration, Notion integration, webhook management, and OAuth token management. The goal is to maintain the existing integration capabilities while enabling future enhancements through the Python architecture.

#### Technology Decisions

1. **External Integration Framework**
   - **Selected**: Python-based integration framework with plugin architecture
   - **Rationale**: Provides flexibility for current and future integrations
   - **Implementation**: Abstract base classes with provider-specific implementations
   - **Security**: Comprehensive OAuth token management with proper encryption

2. **Google Drive & Notion Integration**
   - **Selected**: Native Python clients for Google and Notion APIs
   - **Rationale**: Provides direct access to APIs without Node.js dependencies
   - **Implementation**: Port existing TypeScript integration logic to Python
   - **Enhancement**: Add improved change detection and real-time updates

3. **Webhook Management**
   - **Selected**: FastAPI-based webhook endpoints with authentication
   - **Rationale**: Provides secure and scalable webhook handling
   - **Implementation**: Standardized webhook format with provider-specific adapters
   - **Security**: Proper authentication and validation for all webhooks

4. **Deployment Infrastructure**
   - **Selected**: Cloud Run initially with GKE option based on metrics
   - **Rationale**: Balances simplicity with scalability options
   - **Decision Point**: Move to GKE if cold-starts affect >10% of requests
   - **Infrastructure as Code**: Terraform modules for all environments

#### Implementation Tasks

1. **Integration Framework**
   - Create `backend/integrations/base.py` with common integration patterns
   - Implement plugin architecture for future integrations
   - Add event system for integration notifications
   - Create proper error handling and recovery mechanisms
   - Implement `backend/auth/oauth.py` for OAuth token management
   - Add token storage, refresh, and validation

2. **Google Drive Integration**
   - Create `backend/integrations/google_drive.py` matching functionality in `src/services/googleDriveService.ts`
   - Implement folder connection and document syncing
   - Add OAuth flow for authentication
   - Create `backend/integrations/drive_updates.py` matching functionality in `src/services/GoogleDriveUpdateConnector.ts`
   - Add change monitoring and real-time updates

3. **Notion & Webhook Integration**
   - Implement `backend/integrations/notion.py` for Notion API integration
   - Add document syncing and change detection
   - Create `backend/integrations/webhooks.py` for webhook handling
   - Implement endpoints for various services (Slack, n8n, Zapier)
   - Add authentication and validation for webhooks
   - Create outbound webhook capabilities

**Testing & Metrics**
- Unit tests for integration components
- Integration tests for document syncing
- Security tests for OAuth implementation
- Metrics for API call latency and error rates
- Webhook reliability and delivery metrics

#### Success Criteria
- Google Drive integration is functioning with proper error handling
- Notion integration is working correctly
- Webhook management is reliable and secure
- OAuth token management is secure and effective
- Tests are passing for all implemented components
- Metrics and logging are providing visibility into the system
- Performance is comparable to or better than the existing implementation

**Go Criteria**
- Google Drive sync completes within 10% of TypeScript implementation time
- Notion integration successfully syncs all document types
- OAuth token refresh works reliably
- Webhook delivery success rate > 99%

**No-Go Triggers**
- OAuth security vulnerabilities
- Document sync failures or data loss
- Webhook delivery reliability < 95%
- Integration performance degradation > 20%

---

### Phase 6: Frontend Integration & Deployment (Weeks 20-24)

#### Architectural Context
The Frontend Integration & Deployment phase focuses on ensuring the Python backend works seamlessly with the existing React frontend and establishing a robust infrastructure for the Python backend. This includes updating the frontend to work with the Python-based SSE streaming, agent UI, and feature flag UI, as well as setting up CI/CD pipelines, Kubernetes deployment, and secrets management.

#### Implementation Tasks

1. **Frontend Integration**
   - Update frontend to work with Python-based SSE streaming
   - Implement reconnection and error handling
   - Update department agent playground to work with Python backend
   - Create admin interface for flag management
   - Implement gradual rollout controls
   - Add monitoring dashboard for flag status

2. **CI/CD & Infrastructure**
   - Implement GitHub Actions workflows for testing and deployment
   - Add staging and production environments
   - Create blue/green deployment capabilities
   - Implement Kubernetes manifests and Helm charts
   - Add service mesh for communication
   - Set up Doppler for development secrets
   - Implement GCP Secret Manager for production

3. **Performance Optimization**
   - Implement optimized vector operations with NumPy/SciPy
   - Add vector compression and dimensionality reduction
   - Create approximate nearest neighbor algorithms
   - Implement database indexing and optimization
   - Add connection pooling and query optimization
   - Create horizontal scaling for stateless components
   - Implement load balancing and traffic management

**Testing & Metrics**
- Unit tests for frontend components
- End-to-end tests for critical user journeys
- Load testing with simulated user traffic
- Infrastructure tests for deployment pipeline
- Performance benchmarks for key operations

#### Success Criteria
- Frontend is working seamlessly with the Python backend
- CI/CD pipeline is operational with automated testing and deployment
- Kubernetes deployment is scalable and reliable
- Secrets management is secure and effective
- Performance optimizations are providing measurable improvements
- Tests are passing for all implemented components
- System can scale to handle production loads

**Go Criteria**
- End-to-end tests pass for all critical user journeys
- System handles 2x expected load without degradation
- CI/CD pipeline completes in < 15 minutes
- Zero security vulnerabilities in deployed infrastructure

**No-Go Triggers**
- End-to-end test failures in critical paths
- Performance degradation under load
- Security vulnerabilities in deployment
- CI/CD pipeline instability

---

## 🔑 Key Principles

1. **Maintain Feature Parity** - Ensure no functionality is lost during migration
2. **Gradual Migration** - Move one component at a time with proper testing
3. **Compatibility Layers** - Create adapters to ensure smooth transition
4. **Feature Flags First** - Implement feature flags before any migration work
5. **Test & Observe Each Phase** - Add testing and observability to every phase
6. **Incremental Integration** - Break large integrations into manageable sprints
7. **Security First** - Implement proper security measures throughout the migration
8. **Performance Optimization** - Continuously monitor and optimize performance
9. **Documentation** - Maintain comprehensive documentation throughout the migration
10. **User Experience** - Ensure a smooth user experience during the transition

---

## 📑 Appendix: Standards & Conventions

See this appendix for all cross-cutting guidelines on authentication, embeddings, vector storage, caching, and other implementation standards that apply across all phases of the migration.

### Authentication & Authorization

- **Primary Technology**: Firebase Authentication initially
- **Migration Path**: Abstract authentication layer to allow future migration to JWT or Supabase
- **Hard Rule**: No feature flags may create hybrid auth (Firebase+Supabase) in a single request path
- **Implementation Approach**:
  - Create a provider-agnostic authentication interface
  - Implement Firebase authentication first for compatibility with existing users
  - Use Redis for session management and token caching
  - Add comprehensive audit logging for all authentication events
  - Prepare JWT and Supabase adapters for future migration
  - Implement monthly secret rotation via GitHub Actions

### Embedding & Vector Operations

- **Primary Technology**: HuggingFace models (BAAI/bge-large-en-v1.5)
- **Migration Path**: InstructorXL for specialized cases requiring instruction-tuned embeddings
- **Implementation Approach**:
  - Create a modular embedding system with provider abstraction
  - Implement efficient batching and caching for embedding generation
  - Use Redis for embedding caching with appropriate TTL
  - Add monitoring and circuit breakers for embedding services
  - Implement fallback chains for model unavailability

### Vector Storage

- **Primary Technology**: PostgreSQL + pgvector
- **Migration Path**: Typesense for full-text search if PostgreSQL FTS performance is insufficient
- **Benchmarking Requirements**:
  - Run benchmark with 1M documents to validate P99 latency < 150ms
  - Test multi-tenant scenarios with varying org counts (1, 10, 100) and collection sizes (100 orgs × 10k docs) to test HNSW shard overhead
  - Measure CPU and index memory usage per tenant to identify scaling issues
  - Measure Recall@5 on labeled dataset (target > 85%)
  - Test indexing throughput (documents per hour)
  - Commit benchmark to `/bench/pgvector_multi_tenant.py` and run on every pgvector version update
- **Implementation Approach**:
  - Create a vector store abstraction layer with provider-agnostic interface
  - Implement PostgreSQL + pgvector adapter as primary storage
  - Prepare Typesense adapter for full-text search contingency
  - Use dual-write capability with logical clocks during migration from Firebase
  - Use single-region Pub/Sub ordering keys initially; move to multi-region only after proving ordering math
  - Implement nightly reconciliation tasks to detect and resolve inconsistencies
  - Define "panic mode" runbook with `writes_blocked` flag in migration-control table
  - Require explicit human acknowledgment via admin endpoint (/ops/migrations/unblock) to resume writes
  - Monitor drift percentage between Firebase and PostgreSQL (target < 0.01%)
  - Unit test logical-clock race conditions to ensure concurrent writes in different regions agree on winner

### Caching Strategy

- **Primary Technology**: In-memory with Redis option when performance requires it
- **Performance Threshold**: Implement Redis when p95 latency > 400ms
- **Cold-Start Handling**: Separately track first_byte_latency vs steady-state p95; pre-warm Redis connection pool in startup() hook if cold-start delta > 100ms
- **Eviction Policy**: Use separate Redis DBs with different eviction policies (volatile-lru for memory, noeviction for quotas)
- **Proactive Scaling**: Set Cloud Run min-instances = ceil(current_burst/4) whenever Redis queue_depth > 200
- **Implementation Approach**:
  - Create a unified caching interface with provider-agnostic methods
  - Design all cache interfaces with in-process fallbacks to maintain serverless compatibility
  - Implement Redis adapter for general-purpose caching when performance metrics indicate need
  - Add GPTCache adapter for semantic, LLM-driven caching
  - Implement cache warming strategies for frequently accessed content
  - Create cache invalidation mechanisms for real-time updates
  - Monitor cache hit rates and performance impact
  - Add Prometheus alert for first_byte_latency > 600ms

### Hybrid Retrieval

- **Primary Technology**: PostgreSQL's built-in full-text search + pgvector
- **Implementation Approach**:
  - Create a unified retrieval interface with multiple retrieval strategies
  - Implement BM25 search using PostgreSQL's full-text search capabilities
  - Add vector search using pgvector with appropriate indexes
  - Create fusion algorithms for combining BM25 and vector results
  - Implement adaptive weighting based on query characteristics

### Multi-Agent Orchestration

- **Primary Technology**: LangGraph for agent orchestration
- **Version Pinning**: Pin exact langchain==0.1.17 and gate upgrades via Renovate PR + canary
- **Implementation Approach**:
  - Create a graph-based orchestration system with LangGraph
  - Implement nodes for Co-CEO and department agents
  - Add edges for agent-to-agent communication
  - Create proper state management and persistence
  - Implement comprehensive logging and tracing
  - Add cost tracking with WARN_AT_USD / BLOCK_AT_USD thresholds
  - Return 402 Payment Required response when BLOCK_AT_USD threshold is reached

### API & Communication

- **Primary Technology**: FastAPI for REST endpoints, SSE for LLM streaming, WebSockets only if customer demand justifies after GA
- **CLI Interface**: Thin businesslm CLI (Typer) that hits the Edge gateway with the same JWT for power users (see [CLI Interface documentation](./cli-interface.md))
- **Implementation Approach**:
  - Create a comprehensive OpenAPI schema for all endpoints
  - Use Pydantic models for request/response validation
  - Implement SSE with 30-second chunks and secure reconnection tokens
  - Add jittered retry (±3s) to prevent reconnection storms
  - Use X-Accel-Buffering: no header to reduce buffering latency
  - Include X-Request-ID in first SSE event and WebSocket subprotocol header for trace correlation
  - Consider WebSockets only after GA if customer demand justifies
  - Add comprehensive error handling and logging
  - Implement server-side Retry-After: random(2-5) on 5xx responses

### Deployment & Infrastructure

- **Primary Technology**: Cloud Run initially, with GKE option based on metrics
- **Decision Point**: Move to GKE if cold-starts affect >10% of requests
- **WebSockets**: Ship GA on SSE only; consider WebSockets only as post-GA feature if customer demand justifies
- **Environment Consistency**: All environments deployed with the same Terraform module; only instance class and replica count change
- **Implementation Approach**:
  - Create multi-stage Dockerfiles for efficient builds
  - Implement docker-compose for local development
  - Use GitHub Actions for CI/CD pipeline
  - Deploy to Cloud Run for all environments initially
  - Package Node gateway as its own Cloud Run revision; enable traffic switching at Load Balancer level
  - Prepare Kubernetes manifests for future migration if needed
  - Implement mTLS between services for secure communication
  - Add OPA policy sidecar with initial "allow-all" policy; mount /opa/policies as ConfigMap in staging
  - Use Doppler for local development secrets only with read-only service token
  - Export minimal dev keys in .envrc to prevent access to production secrets
  - Implement GCP Secret Manager for all non-local environments
  - Create region-specific resource provisioning for data residency compliance

---

## 📌 Final Acceptance Criteria

The following criteria must be met before the migration is considered complete:

1. **Benchmarks Committed**: `scripts/benchmark_pgvector.py` (including 100-org variant) and other benchmark scripts are merged to main and run regularly in CI.

2. **Trace ID Propagation**: X-Request-ID is present in every log entry, Sentry breadcrumb, LangSmith run metadata, and propagated through SSE/WebSocket connections.

3. **Authentication Consistency**: No feature flags create hybrid auth (Firebase+Supabase) in a single request path.

4. **Infrastructure as Code**: Terraform modules show one module per region, per phase, with consistent configuration.

5. **Failover Documentation**: Clear runbook exists for failing over from Python agent service to legacy TypeScript or vice-versa.

6. **Data Consistency**: Reconciliation tasks show < 0.01% drift between Firebase and PostgreSQL; logical-clock race condition tests pass.

7. **Performance Validation**: All performance benchmarks meet or exceed the TypeScript implementation; first_byte_latency metrics implemented with alerts.

8. **Security Verification**: All services communicate via mTLS, OPA latency < 2ms per request, and tool execution uses proper sandboxing.

9. **Secret Protection**: Pre-commit hook implemented to prevent credential leaks; monthly secret rotation automated.

10. **Cost Cap Enforcement**: System raises 402 Payment Required after BLOCK_AT_USD threshold is reached; verified with Postman test.

## 📌 Phase 1 Exit Criteria

The following checklist must be completed before exiting Phase 1:

| Checkbox | Owner | Proof |
|----------|-------|-------|
| writes_blocked fire-drill runs in staging, halts all orgs in < 150 ms | Ops | Playwright test & Grafana alert screen-shot |
| p95 cold start < 600 ms with min-instances=1 | Platform | Cloud Run metrics export |
| End-to-end trace (Edge → Tool → A2A bridge → Webhook) visible in Tempo | DevEx | Trace URL |
| OPA latency < 2 ms p95 under 1k rps synthetic test | Security | k6 report |
| pgvector benchmark: 1M docs · Recall@5 ≥ 85% · P99 ≤ 150 ms | Data | Jupyter nb artifact |
| Cost cap enforcement raises 402 after BLOCK_AT_USD reached | Billing | Postman run |

**This migration roadmap ensures that all existing functionality is preserved while transitioning to a more scalable, Python-centric architecture. By following a gradual, component-by-component approach with proper compatibility layers and feature flags, we can ensure a smooth migration without disrupting the current system.**

---

