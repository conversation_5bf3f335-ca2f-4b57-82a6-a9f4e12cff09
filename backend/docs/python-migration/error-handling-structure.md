# Error Handling Structure

This document describes the error handling structure in the Python migration proof of concept. It explains the organization of error handling utilities, their purpose, and how to use them.

## Overview

The error handling system is designed to provide a consistent, structured approach to handling errors across the application. It includes:

1. **Custom exception types** for different categories of errors
2. **Error handling decorators** for functions and async functions
3. **Context managers** for structured error handling
4. **Utility functions** for error conversion, logging, and response formatting

The system is organized into a modular structure in the `app.core.errors` package, with specialized modules for different types of errors.

## Directory Structure

```
backend/app/core/errors/
  __init__.py          # Exports all error handling utilities
  base.py              # Core error handling classes and utilities
  db.py                # Database-specific error handling
  llm.py               # LLM-specific error handling
  api.py               # API-specific error handling
```

## Core Error Classes

The core error classes are defined in `app.core.errors.base.py`:

### Base Error Class

`AppError` is the base exception class for all application-specific errors. It provides a consistent structure for all application errors, including metadata for better error handling and reporting.

```python
class AppError(Exception):
    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        status_code: Optional[int] = None
    ):
        # ...
```

### Specific Error Types

The system includes several specific error types for common scenarios:

- `ValidationError`: For input validation failures
- `AuthenticationError`: For authentication failures
- `AuthorizationError`: For authorization failures
- `ResourceNotFoundError`: For when a requested resource is not found
- `ExternalServiceError`: For external service call failures
- `DatabaseError`: For database operation failures
- `TimeoutError`: For operation timeouts
- `RateLimitError`: For rate limit exceeded errors

## Error Handling Decorators

The system provides decorators for handling errors in functions and async functions:

### Base Decorators

- `handle_errors`: For handling errors in synchronous functions
- `handle_async_errors`: For handling errors in asynchronous functions

```python
@handle_errors(fallback_return="fallback", reraise=False)
def my_function():
    # ...

@handle_async_errors(fallback_return="fallback", reraise=False)
async def my_async_function():
    # ...
```

### Specialized Decorators

- `handle_db_errors`: For handling database errors in functions
- `handle_async_db_errors`: For handling database errors in async functions
- `handle_llm_errors`: For handling LLM errors in functions
- `handle_async_llm_errors`: For handling LLM errors in async functions
- `handle_api_errors`: For handling errors in FastAPI route handlers

```python
@handle_db_errors(fallback_return=None, reraise=False)
def my_db_function():
    # ...

@handle_llm_errors(
    fallback_return=None,
    retry_count=3,
    retry_delay=1.0,
    retry_backoff=2.0
)
def my_llm_function():
    # ...

@handle_api_errors()
async def my_api_route():
    # ...
```

## Context Managers

The system provides context managers for structured error handling:

### Base Context Manager

- `ErrorContext`: For handling errors with consistent formatting

```python
with ErrorContext("Processing file", reraise=False) as ctx:
    process_file(file_path)

if ctx.error:
    # Handle the error
    print(f"Error: {ctx.error}")
```

### Specialized Context Managers

- `DBErrorContext`: For handling database errors
- `LLMErrorContext`: For handling LLM errors

```python
with DBErrorContext("Querying user data", reraise=False) as ctx:
    user = session.query(User).filter_by(id=user_id).one()

with LLMErrorContext("Generating response", provider="openai", model="gpt-4") as ctx:
    response = await llm_adapter.chat(messages)
```

## Utility Functions

The system provides utility functions for error handling:

- `convert_exception_to_app_error`: Convert a standard exception to an AppError
- `format_error_for_response`: Format an error for inclusion in an API response
- `log_error`: Log an error with consistent formatting
- `create_error_response`: Create a standardized error response for APIs
- `create_api_error_response`: Create a standardized error response for APIs with API-specific fields

## Usage Examples

### Basic Error Handling

```python
from app.core.errors import AppError, ValidationError, handle_errors

@handle_errors(fallback_return=None)
def process_data(data):
    if not data:
        raise ValidationError(message="Data cannot be empty")
    # Process data...
    return result
```

### Database Error Handling

```python
from app.core.errors import handle_db_errors, DBErrorContext

@handle_db_errors(fallback_return=[])
def get_users():
    return session.query(User).all()

def get_user(user_id):
    with DBErrorContext("Getting user", reraise=False) as ctx:
        user = session.query(User).filter_by(id=user_id).one()
        return user
    
    if ctx.error:
        # Handle the error
        return None
```

### LLM Error Handling

```python
from app.core.errors import handle_llm_errors, LLMErrorContext

@handle_llm_errors(
    fallback_return="I'm sorry, I couldn't generate a response.",
    retry_count=3,
    retry_delay=1.0,
    retry_backoff=2.0
)
def generate_response(prompt):
    return llm_client.generate(prompt)

async def chat(messages):
    with LLMErrorContext("Generating chat response", provider="openai", model="gpt-4") as ctx:
        response = await llm_adapter.chat(messages)
        return response
    
    if ctx.error:
        # Handle the error
        return "I'm sorry, I couldn't generate a response."
```

### API Error Handling

```python
from fastapi import FastAPI
from app.core.errors import handle_api_errors, ValidationError

app = FastAPI()

@app.get("/users/{user_id}")
@handle_api_errors()
async def get_user(user_id: int):
    if user_id <= 0:
        raise ValidationError(message="User ID must be positive")
    
    user = await db.get_user(user_id)
    if not user:
        raise ResourceNotFoundError(message="User not found", resource_id=str(user_id))
    
    return user
```

## Best Practices

1. **Use the appropriate error type** for the specific error scenario
2. **Include relevant details** in the error to help with debugging
3. **Use decorators for repetitive error handling** to avoid boilerplate code
4. **Use context managers for structured error handling** when you need more control
5. **Log errors with appropriate log levels** based on their severity
6. **Include correlation IDs** in error details for distributed tracing
7. **Use fallback values** for graceful degradation
8. **Implement retry logic** for transient errors
9. **Provide user-friendly error messages** in API responses

## Migration from Legacy Error Handling

The legacy error handling modules (`error_handling.py`, `db_error_handling.py`, and `llm_error_handling.py`) have been deprecated and will be removed in a future version. They currently re-export the utilities from the new modular structure.

To migrate from the legacy error handling to the new structure:

1. Update imports to use the new structure:

```python
# Old imports
from app.core.error_handling import AppError, ValidationError
from app.core.db_error_handling import handle_db_errors
from app.core.llm_error_handling import handle_llm_errors

# New imports
from app.core.errors import AppError, ValidationError, handle_db_errors, handle_llm_errors
# Or more specifically
from app.core.errors.base import AppError, ValidationError
from app.core.errors.db import handle_db_errors
from app.core.errors.llm import handle_llm_errors
```

2. Update any code that uses the old error handling utilities to use the new ones

3. Run tests to ensure everything is working correctly

## Conclusion

The new error handling structure provides a more modular, maintainable, and consistent approach to error handling across the application. It separates concerns into specialized modules while maintaining a unified interface through the `app.core.errors` package.

By using this structure, you can ensure that errors are handled consistently, logged appropriately, and presented to users in a user-friendly way.
