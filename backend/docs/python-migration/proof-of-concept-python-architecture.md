# BusinessLM Python Migration: Proof of Concept

This document outlines the architecture and implementation plan for the BusinessLM Python migration proof of concept (PoC). The PoC will focus on implementing the core components of the Python-centric architecture with a limited scope, specifically the Co-CEO agent and two department agents (Finance and Marketing).

## 1. Overview

### 1.1 Purpose

The purpose of this proof of concept is to:

1. Validate the feasibility of migrating the BusinessLM TypeScript backend to Python
2. Implement core components of the Python architecture (RAG, multi-agent orchestration, tools)
3. Establish best practices for the full migration
4. Identify potential challenges and solutions

### 1.2 Scope

The PoC will include:

- Co-CEO agent implementation with LangGraph
- Two department agents: Finance and Marketing
- RAG pipeline with section-aware retrieval and HuggingFace embeddings
- 1-2 tools per agent
- PostgreSQL + pgvector integration for unified data and vector storage
- API endpoints for agent interactions

Note: 🚧 [DEFERRED: streaming] Real-time streaming (e.g., SSE) is not included in the scope of this PoC. See Section 9.1 for a complete list of out-of-scope items.

### 1.3 Success Criteria

The PoC will be considered successful if:

1. The Co-CEO agent can analyze queries and route to appropriate department agents
2. Department agents can process queries and use tools
3. The RAG pipeline can retrieve relevant knowledge from the existing knowledge base
4. The system can handle basic user interactions similar to the current TypeScript implementation
5. The architecture demonstrates clear separation of concerns and modularity

### 1.4 Tech Stack Summary

- **Language:** Python 3.11+
- **Web Framework:** FastAPI
- **Agent Orchestration:** LangGraph, Langchain
- **Embedding Models:** HuggingFace sentence-transformers
- **Vector Storage:** PostgreSQL with pgvector
- **Database:** PostgreSQL
- **Authentication:** JWT-based authentication
- **LLM Providers:** OpenAI, Anthropic, Google Gemini (pluggable)
- **Infra:** Docker (optional), PostgreSQL (local or containerized)

## 2. Architecture

### 2.1 High-Level Architecture

The PoC will implement a Python backend with PostgreSQL and pgvector for data storage and vector embeddings. This new architecture will replace the current Firebase-based system, providing improved scalability and performance for vector search operations.

### 2.2 Key Components

#### 2.2.1 LLM Adapter Interface

The LLM Adapter provides a unified interface for interacting with different LLM providers:

```python
class LLMAdapter(ABC):
    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> Union[str, AsyncIterator[str]]:
        """Send a chat request to the LLM and get a response."""
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """Count the number of tokens in the messages."""
        pass

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        pass

    def get_stream_flag(self, stream: bool) -> bool:
        """Get the appropriate stream flag based on adapter capabilities."""
        return stream and self.supports_streaming
```

The implementation includes concrete adapters for:

1. **OpenAI**:
   - Default model: `gpt-4.1-2025-04-14`
   - Streaming support
   - Accurate token counting with tiktoken

2. **Anthropic**:
   - Default model: `claude-3-7-sonnet-********`
   - Streaming support
   - Token counting with Anthropic SDK

3. **Google Gemini**:
   - Models: `gemini-2.0-flash` (default), `gemini-2.5-pro-exp-03-25`
   - Streaming support
   - Character-based token counting heuristic with safety margins
     - Uses 80% of theoretical token limits to account for estimation errors
     - Implements fallback chunking when approaching limits
     - Includes buffer mechanism for handling estimation inaccuracies
   - Lazy imports for optional dependency

4. **Mock Adapter**:
   - For testing and development
   - Configurable responses
   - Simulated latency

The adapter factory provides a unified way to create adapters with fallback support:

```python
# Create an adapter with default settings
adapter = get_llm_adapter("openai")

# Create an adapter with specific settings
adapter = get_llm_adapter(
    provider="anthropic",
    api_key="your-api-key",
    model="claude-3-7-sonnet-********",
    temperature=0.5
)

# Use the adapter
response = await adapter.chat([
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
])
```

The system also includes a dynamic adapter registry for extensibility:

```python
# Register a custom adapter
from backend.app.core.llm import register_adapter, LLMAdapter

class CustomAdapter(LLMAdapter):
    # Implementation...
    pass

register_adapter("custom", CustomAdapter)

# Use the registered adapter
adapter = get_llm_adapter("custom")
```

#### 2.2.2 Knowledge Base Service

The Knowledge Base Service abstracts the data storage layer:

```python
class KnowledgeBaseService:
    def __init__(self, db_connection):
        self.db = db_connection

    async def get_document(self, table: str, doc_id: str) -> Dict:
        """Get a document by ID."""
        pass

    async def vector_search(self, embedding: List[float], table: str, limit: int = 5) -> List[Dict]:
        """Search for documents by vector similarity."""
        pass
```

#### 2.2.3 Tool Registry

The Tool Registry manages the available tools:

```python
from typing import Dict

TOOL_REGISTRY: Dict[str, BaseTool]
```

#### 2.2.4 LangGraph Orchestration

The LangGraph orchestration provides modular DAG construction:

Graph = analyze → retrieve → route → respond.

#### 2.2.5 Tool Integration Framework

The Tool Integration Framework provides a standardized way to define, execute, and process results from tools:

```python
class ToolCall(BaseModel):
    """Standardized tool call format compatible with OpenAI's function calling."""
    name: str
    arguments: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for LLM consumption."""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "arguments": json.dumps(self.arguments)
            }
        }

class ToolResult(BaseModel):
    """Standardized tool result format."""
    name: str
    result: Any
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @property
    def success(self) -> bool:
        """Whether the tool execution was successful."""
        return self.error is None
```

Tools are registered in the tool registry and can be executed asynchronously by agents:

## 3. Implementation Plan

### 3.1 Current Progress Assessment

Before proceeding with implementation, it's important to assess what has already been done:

- **Core Infrastructure**:
  - ✅ **LLM Adapter**: Complete implementation with support for OpenAI, Anthropic, and Google Gemini models
  - ⚠️ **PostgreSQL Client**: Needs to be implemented with pgvector support
  - ✅ **Knowledge Base Service**: Enhanced with vector search capabilities and section-aware retrieval
- **Agent Structure**: Skeleton implementations of Co-CEO and department agents exist
- **Tools**: Basic implementations of Budget Analysis and Campaign Analysis tools exist

However, several critical components are missing or incomplete:

- **RAG Implementation**:
  - ✅ **Embeddings Service**: Implemented with HuggingFace and OpenAI support
  - ⚠️ **Vector Store**: Needs to be reimplemented with PostgreSQL and pgvector
  - ✅ **Query Analyzer**: Enhanced with embedding-based similarity and intent detection
  - ⚠️ **Retriever**: Needs to be updated to work with PostgreSQL/pgvector
  - ✅ **Generator**: Enhanced implementation with context window management, multiple citation formats, structured output, and performance optimizations
  - ⚠️ **RAG Service**: Needs to be updated to work with PostgreSQL/pgvector
- **Agent Orchestration**: LangGraph integration is not fully implemented
- **Agent Logic**: The agent implementations are mostly placeholders
- **API Endpoints**: No FastAPI endpoints for interacting with the system

### 3.2 Phase 1: Complete RAG Implementation (Highest Priority)

1. **Embeddings Service**
   - Implement HuggingFace embeddings using sentence-transformers (intfloat/e5-base-v2)
   - Create functions to generate embeddings for documents and queries
   - Implement vector storage with PostgreSQL and pgvector
   - Add timeout and retry logic for embedding generation and vector search

2. **PostgreSQL Database Setup**
   - Set up PostgreSQL database schema for document storage
   - Configure pgvector extension for vector embeddings
   - Create tables with vector columns for efficient similarity search
   - Implement similarity metrics (cosine, L2, dot product) with appropriate indexing strategies (HNSW, IVFFlat)
   - Optimize index parameters based on dataset size and query patterns
   - Implement database migration scripts
   - Create enhanced user table schema:
     ```sql
     CREATE TABLE users (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       email VARCHAR(255) UNIQUE NOT NULL,
       hashed_password VARCHAR(255) NOT NULL,
       is_active BOOLEAN DEFAULT TRUE,
       email_verified BOOLEAN DEFAULT FALSE,
       roles TEXT[] DEFAULT '{}',
       permissions TEXT[] DEFAULT '{}',
       profile_data JSONB DEFAULT '{}',
       last_login TIMESTAMP WITH TIME ZONE,
       account_locked BOOLEAN DEFAULT FALSE,
       lock_reason VARCHAR(255),
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
     );
     ```
   - Implement hybrid search combining vector similarity with full-text search:
     ```sql
     -- Create tsvector column for full-text search alongside vector embeddings
     ALTER TABLE documents ADD COLUMN text_search tsvector
       GENERATED ALWAYS AS (to_tsvector('english', content)) STORED;

     -- Create GIN index for fast full-text search
     CREATE INDEX idx_documents_text_search ON documents USING GIN(text_search);

     -- Example hybrid search query
     SELECT *,
       (embedding <=> query_vector) * 0.7 +
       ts_rank(text_search, plainto_tsquery('english', query_text)) * 0.3 AS score
     FROM documents
     WHERE text_search @@ plainto_tsquery('english', query_text)
     ORDER BY score
     LIMIT 10;
     ```


3. **Enhanced Knowledge Base Service**
   - Add vector search capabilities using pgvector
   - Implement section-aware retrieval
   - Create functions for document chunking and embedding

4. **Improved Query Analysis**
   - Enhance the query analyzer with embedding-based similarity
   - Improve department detection using embeddings with confidence thresholds
     - Implement minimum confidence threshold (0.65 cosine similarity)
     - Add fallback classification for ambiguous queries
     - Create clarification flow for low-confidence routing
   - Implement section detection for targeted knowledge retrieval
   - Add intent classification for better query understanding

5. **Retriever Implementation**
   - Implement the Retriever abstract base class
   - Create the PostgreSQLRetriever that uses pgvector for similarity search
   - Implement hybrid search combining vector similarity and keyword filtering
   - Implement the ContextWindowManager for managing context window size

6. **Generator Implementation** ✅
   - Implement the Generator abstract base class
   - Create the RAGGenerator for combining retrieved context with LLM generation
   - Add support for multiple citation formats (inline, footnote, endnote, academic)
   - Implement sophisticated context window management with smart document selection
   - Add structured output formatting with clear section organization
   - Implement performance optimizations for token counting and context processing
   - Add integration with department agents for enhanced response generation
   - Create comprehensive unit and integration tests

> **Testing Approach**: Write unit tests for the embeddings service and vector search functionality alongside implementation. Create integration tests for the RAG service to verify the complete pipeline. Test with sample documents to ensure accurate retrieval.

> **Documentation Approach**: Document the embeddings service API and usage examples as it's implemented. Update the knowledge base service documentation with vector search capabilities. Create documentation for the retriever and generator components.

### 3.3 Phase 2: Enhance Agent Orchestration with LangGraph

1. **LangGraph Integration**
   - Implement proper LangGraph DAG for agent orchestration
   - Define the flow between Co-CEO and department agents
   - Set up state management and context passing
   - Implement timeout and retry logic for LangGraph nodes
   - Implement checkpointing mechanism for long conversations:
     ```python
     class CheckpointStore(ABC):
         """Abstract interface for conversation state persistence."""

         @abstractmethod
         async def save_checkpoint(self, thread_id: str, state: Dict[str, Any]) -> str:
             """Save a checkpoint of the conversation state."""
             pass

         @abstractmethod
         async def load_checkpoint(self, thread_id: str) -> Optional[Dict[str, Any]]:
             """Load a checkpoint of the conversation state."""
             pass

         @abstractmethod
         async def delete_checkpoint(self, thread_id: str) -> bool:
             """Delete a checkpoint."""
             pass

     class PostgreSQLCheckpointStore(CheckpointStore):
         """PostgreSQL implementation of checkpoint store."""

         def __init__(self, db_connection, table_name: str = "conversation_checkpoints"):
             self.db = db_connection
             self.table_name = table_name

         async def save_checkpoint(self, thread_id: str, state: Dict[str, Any]) -> str:
             """Save checkpoint to PostgreSQL."""
             # Serialize state to JSON
             serialized_state = json.dumps(state)

             # Save to database with timestamp
             query = f"""
                 INSERT INTO {self.table_name} (thread_id, state, created_at)
                 VALUES ($1, $2, NOW())
                 ON CONFLICT (thread_id) DO UPDATE
                 SET state = $2, updated_at = NOW()
                 RETURNING checkpoint_id
             """
             result = await self.db.fetchval(query, thread_id, serialized_state)
             return result
     ```
   - Create persistence layer for conversation state using PostgreSQL

2. **Agent Communication Protocol**
   - Define the message format for inter-agent communication
   - Implement context preservation between agents
   - Create error handling for agent communication

3. **Agent State Management**
   - Implement state tracking for multi-turn conversations
   - Create session management for user interactions
   - Implement history tracking for conversation context

> **Testing Approach**: Create integration tests for the agent graph to ensure proper flow between agents. Test with sample queries to verify correct routing and state management.

> **Documentation Approach**: Create diagrams showing the agent orchestration flow. Document the state management approach and message format for inter-agent communication.

### 3.4 Phase 3: Complete Agent Implementations

1. **Co-CEO Agent Enhancement**
   - Implement proper query routing logic
   - Create response generation capabilities
   - Implement context management for multi-turn conversations

2. **Finance Agent Implementation**
   - Complete the Finance agent with domain-specific logic
   - Implement knowledge retrieval for finance queries
   - Create response generation with finance expertise

3. **Marketing Agent Implementation**
   - Complete the Marketing agent with domain-specific logic
   - Implement knowledge retrieval for marketing queries
   - Create response generation with marketing expertise

> **Testing Approach**: Write unit tests for each agent's core functionality. Create integration tests for end-to-end query processing. Test with sample queries from each domain.

> **Documentation Approach**: Document each agent's capabilities, prompts, and domain-specific logic. Create examples of queries each agent can handle.

### 3.5 Phase 4: API and Integration

1. **FastAPI Endpoints**
   - Create endpoints for agent interaction
   - Implement authentication and authorization
   - Set up CORS and middleware

2. **JWT Authentication Middleware**
   - Implement JWT token verification
   - Create role-based access control for protected routes
   - Support multiple token sources (Authorization header, custom header, cookie)
   - Inject user context into request state
   - Integrate with the existing error handling system
   - Store user credentials in PostgreSQL database
   - Implement token issuance logic (login → token generation)
   - Create comprehensive token management system:
     ```python
     class TokenManager:
         """Manages JWT token issuance, validation, and refresh."""

         def __init__(
             self,
             secret_key: str,
             algorithm: str = "HS256",
             access_token_expire_minutes: int = 30,
             refresh_token_expire_days: int = 7,
             db_connection = None
         ):
             self.secret_key = secret_key
             self.algorithm = algorithm
             self.access_token_expire = timedelta(minutes=access_token_expire_minutes)
             self.refresh_token_expire = timedelta(days=refresh_token_expire_days)
             self.db = db_connection

         async def create_access_token(self, data: Dict[str, Any]) -> str:
             """Create a new access token."""
             to_encode = data.copy()
             expire = datetime.utcnow() + self.access_token_expire
             to_encode.update({"exp": expire})
             return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

         async def create_refresh_token(self, user_id: str) -> str:
             """Create a new refresh token and store in database."""
             # Generate unique token
             token = secrets.token_urlsafe(32)

             # Store in database with expiration
             expires_at = datetime.utcnow() + self.refresh_token_expire
             await self.db.execute(
                 """
                 INSERT INTO refresh_tokens (user_id, token, expires_at)
                 VALUES ($1, $2, $3)
                 """,
                 user_id, token, expires_at
             )

             return token

         async def refresh_access_token(self, refresh_token: str) -> Optional[str]:
             """Create new access token from refresh token."""
             # Verify refresh token exists and is not expired
             result = await self.db.fetchrow(
                 """
                 SELECT user_id, expires_at, is_revoked
                 FROM refresh_tokens
                 WHERE token = $1
                 """,
                 refresh_token
             )

             if not result or result["is_revoked"]:
                 return None

             # Check if token is expired
             if result["expires_at"] < datetime.utcnow():
                 # Revoke expired token
                 await self.revoke_refresh_token(refresh_token)
                 return None

             # Get user data
             user = await self.db.fetchrow(
                 "SELECT id, email, roles FROM users WHERE id = $1",
                 result["user_id"]
             )

             # Create new access token
             return await self.create_access_token({
                 "sub": str(user["id"]),
                 "email": user["email"],
                 "roles": user["roles"]
             })

         async def revoke_refresh_token(self, token: str) -> bool:
             """Revoke a refresh token."""
             await self.db.execute(
                 "UPDATE refresh_tokens SET is_revoked = TRUE WHERE token = $1",
                 token
             )
             return True
     ```
   - Implement token rotation and revocation strategy
   - Document token flow in auth/token-flow.md
   - Add rate limiting for authentication endpoints:
     ```python
     # Rate limiting middleware for authentication endpoints
     @app.middleware("http")
     async def rate_limit_auth_middleware(request: Request, call_next):
         if request.url.path.startswith("/api/auth"):
             # Get client IP
             client_ip = request.client.host

             # Check rate limit (10 requests per minute)
             rate_key = f"rate:auth:{client_ip}"
             current = await redis.get(rate_key)

             if current and int(current) >= 10:
                 return JSONResponse(
                     status_code=429,
                     content={"error": "Too many authentication attempts"}
                 )

             # Increment counter
             await redis.incr(rate_key)
             # Set expiry if not exists
             await redis.expire(rate_key, 60)

         return await call_next(request)
     ```

3. **Streaming Support (Deferred)**
   - > 🚧 [DEFERRED: streaming] Real-time streaming (e.g., SSE) is explicitly excluded from the PoC
   - All LLM responses will be returned as standard HTTP JSON responses
   - Streaming capabilities may be introduced in a future phase if required

4. **Error Handling, Timeout, and Logging**
   - Implement comprehensive error handling
   - Create structured logging
   - Implement request ID propagation
   - Implement unified timeout and retry utilities
   - Add circuit breaker pattern for external services

> **Testing Approach**: Create API tests for each endpoint. Test authentication, error handling, and streaming capabilities. Create load tests for performance assessment.

> **Documentation Approach**: Create OpenAPI documentation for all endpoints. Document authentication requirements, error codes, and response formats.

### 3.6 Phase 5: Connect Tools to Agents (Lower Priority)

1. **Tool Integration**
   - Enhance the tool registry for better agent access
   - Implement tool calling logic in agents
   - Create error handling for tool usage

2. **Tool Result Processing**
   - Implement result parsing and formatting
   - Create context integration for tool results
   - Implement error handling for tool failures

3. **Tool Documentation**
   - Document each tool's capabilities and parameters
   - Create examples of tool usage
   - Implement tool discovery mechanisms

> **Testing Approach**: Write integration tests for tool usage by agents. Test error handling and result processing. Create examples of tool usage in different scenarios.

> **Documentation Approach**: Document each tool's API, parameters, and return values. Create examples of tool usage in different contexts.

## 4. Project Structure

```
backend/
  app/
    main.py                # FastAPI application entry point
    api/                   # API routes and middleware
      middleware/          # Middleware components
        auth.py            # JWT Authentication Middleware
      routes/              # API routes
        agents.py          # Agent routes
        rag.py             # RAG routes
        protected.py       # Protected routes (requires authentication)
    agents/                # Agent implementations
      co_ceo.py            # Co-CEO agent
      finance.py           # Finance agent
      marketing.py         # Marketing agent
    core/
      database.py          # PostgreSQL client with pgvector support
      llm/                 # LLM adapters and related utilities
        __init__.py        # Package exports
        base.py            # Base adapter interface
        factory.py         # Adapter factory
        openai_adapter.py  # OpenAI adapter
        anthropic_adapter.py # Anthropic adapter
        gemini_adapter.py  # Google Gemini adapter
        mock_adapter.py    # Mock adapter for testing
      errors/              # Error handling utilities
        __init__.py        # Package exports
        base.py            # Base error handling utilities
        db.py              # Database error handling
        llm.py             # LLM error handling
        api.py             # API error handling
      timeout/             # Timeout utilities
        __init__.py        # Package exports
        base.py            # Base timeout utilities
        llm.py             # LLM-specific timeout utilities
        rag.py             # RAG-specific timeout utilities
        graph.py           # LangGraph-specific timeout utilities
    rag/
      knowledge_base.py    # Knowledge base service
      query_analyzer.py    # Query analyzer
      embeddings.py        # Embeddings service
      vector_store.py      # Vector storage implementations
      retriever.py         # Query processing and document retrieval
      generator.py         # Context-enhanced response generation
    langgraph/             # LangGraph components
      graph.py             # Graph definitions
      state.py             # State management
      checkpointing.py     # Checkpointing utilities
    config/                # Configuration
      settings.py          # Application settings
  tests/
    test_smoke.py          # 2–3 assertions: query -> non‑empty answer
    test_rag/              # Tests for RAG components
    test_agents/           # Tests for agent components
    test_api/              # Tests for API endpoints
      test_auth_middleware.py  # Tests for Firebase Authentication Middleware
  scripts/
Makefile
requirements.txt          # autogenerated lockfile for CI/CD
.env.example
docs/
  auth/                   # Authentication documentation
    jwt-auth-middleware.md       # JWT Authentication Middleware documentation
    auth-example.md              # Example usage of the middleware
    token-flow.md                # JWT token issuance and validation flow
    user-schema.md               # User database schema documentation
  langgraph/              # LangGraph documentation
    conditional-routing.md  # Detailed conditional routing documentation
    departmental-knowledge-setup.md  # Department knowledge setup
    timeout-and-retry-implementation.md  # Timeout and retry implementation
    checkpointing.md        # LangGraph checkpointing for long conversations
  python-migration/
    proof-of-concept-python-architecture.md  # this file
    timeout-utilities.md  # Timeout utilities documentation
    error-handling-structure.md  # Error handling structure documentation
    error-handling-migration-plan.md  # Error handling migration plan
  rag/                    # RAG documentation
    rag-implementation.md  # Detailed RAG documentation
    vector-indexing.md     # pgvector indexing strategies and optimization
    similarity-metrics.md  # Comparison of similarity metrics (cosine, L2, dot product)
```

> 💡 Run `uv pip freeze > requirements.txt` to generate a lockfile for deployment or CI.


## 5. Development Workflow

### 5.1 Setting Up the Environment

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd businesslM-betav2-python-PoC

# Create a new branch
git checkout -b python-migration-poc

# Create virtual environment with uv or python
if command -v uv &> /dev/null; then
    uv venv venv
else
    python -m venv venv
fi

source venv/bin/activate   # On Windows: venv\Scripts\activate

# Install dependencies from backend/requirements.in
if command -v uv &> /dev/null; then
    uv pip install -r backend/requirements.in
else
    pip install -r backend/requirements.in
fi

# (Optional) Freeze for reproducibility or deployment
if command -v uv &> /dev/null; then
    uv pip freeze > requirements.txt
else
    pip freeze > requirements.txt
fi
```

### 5.2 Development Commands

The Makefile will provide the following commands:

```bash
# Start the FastAPI server with auto-reload
make dev

# Run the test suite
make test

# Run specific test categories
make test-rag
make test-agents
make test-api

# Run tests for specific components
make test-embeddings
make test-vector-store
```

### 5.3 Package Management

The project supports both `uv` and `pip` for package management, with automatic fallback:

```makefile
# Check if uv is available, otherwise use pip
ifeq ($(shell which uv 2>/dev/null),)
    PIP_INSTALL = pip install -r requirements.txt
    PIP_COMPILE = pip-compile requirements.in
else
    PIP_INSTALL = uv pip install -r requirements.txt
    PIP_COMPILE = uv pip compile requirements.in
endif

install:
	$(PIP_INSTALL)

update-deps:
	$(PIP_COMPILE)
	$(PIP_INSTALL)
```

### 5.4 Rate Limiting and Abuse Prevention

The API implements comprehensive rate limiting to prevent abuse:

1. **Global Rate Limiting**
   - Limits requests per IP address across all endpoints
   - Configurable limits based on authentication status

2. **Endpoint-Specific Rate Limiting**
   - Higher limits for read operations
   - Stricter limits for write operations
   - Very strict limits for authentication endpoints

3. **Monitoring and Alerting**
   - Logs excessive usage patterns
   - Alerts on potential abuse attempts
   - Automatic temporary IP banning for repeated abuse

### 5.5 Testing and Documentation Workflow

The PoC will follow these best practices for testing and documentation:

1. **Test-Driven Development (When Possible)**
   - Write tests before implementing features for critical components
   - This is especially valuable for well-defined interfaces like the embeddings service

2. **Continuous Testing**
   - Add tests alongside each component as it's developed
   - Don't wait until the end to add all tests

3. **Documentation-Driven Development**
   - For complex components, sketch the API and usage examples before implementation
   - This helps clarify the design and ensures usability

4. **Progressive Documentation**
   - Start with essential documentation (function signatures, class purposes)
   - Expand with examples and edge cases as the code matures
   - Add troubleshooting sections based on actual issues encountered

## 6. Integration with Existing System

The PoC will implement a PostgreSQL database with pgvector extension for efficient vector storage and similarity search, along with JWT-based authentication for user management.

## 7. Future Considerations

- Full migration of all department agents
- Feature flags for gradual migration
- Production deployment
- Performance optimization
- Advanced RAG techniques:
  - Context summarization for large document sets
  - Self-critique mechanism for improved response quality
  - Enhanced streaming with progress indicators
  - Performance benchmarking with large document sets
  - Hierarchical context organization for efficient token usage
  - Dynamic context selection based on query complexity
- Integration with additional data sources
- Advanced PostgreSQL optimization and tuning:
  - Fine-tuning HNSW and IVFFlat index parameters
  - Partitioning strategies for large vector datasets
  - Hybrid search optimization (combining full-text and vector search)
- Full observability stack with OpenTelemetry
- 🚧 [DEFERRED: streaming] Streaming responses using SSE (including chunked or token-by-token LLM outputs)
  - This is a UX enhancement and not required to validate system correctness or architectural integrity
  - Evaluate the feasibility and ROI of integrating streaming responses via SSE in a post-PoC phase, based on frontend needs and user feedback
- Enhanced LangGraph checkpointing for multi-agent conversations

For a detailed roadmap of RAG and multi-agent orchestration improvements, see [rag-multi-agent-next-steps.md](./rag-multi-agent-next-steps.md).

## 8. Conclusion

This proof of concept will validate the feasibility of migrating the BusinessLM TypeScript backend to Python with PostgreSQL and pgvector for data storage and vector embeddings. By focusing on the core components (RAG, multi-agent orchestration, tools) and implementing them for the Co-CEO agent and two department agents (Finance and Marketing), we can establish best practices for the full migration and identify potential challenges and solutions. The PostgreSQL with pgvector approach will provide a robust, scalable foundation for vector search operations, while JWT-based authentication will ensure secure user management.

## 9. PoC Implementation Details

### 9.1 Out-of-Scope Items

The following items are explicitly excluded from the PoC scope:

- 🚧 [DEFERRED: streaming] Streaming responses using SSE (including chunked or token-by-token LLM outputs)
  - See also Sections 3.5.3 and 7 for more details on streaming deferral
- Advanced RAG techniques (context summarization, self-critique mechanism)
- Full migration of all department agents
- Production deployment configuration
- Advanced PostgreSQL optimization and tuning

### 9.2 Environment & Secrets Management

The PoC requires several environment variables for external services:

```
# Sample .env.example
# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=businesslm
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-password

# JWT Configuration
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# LLM API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GEMINI_API_KEY=your-gemini-api-key  # For Google Gemini models

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
```

### 9.3 Key Dependencies

```
# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.23.2
pydantic>=2.4.2  # Data validation for FastAPI and tool schemas

# Agent Orchestration
langchain>=0.0.335
langgraph>=0.0.15  # For agent orchestration

# LLM & Embeddings
openai>=1.3.0  # For GPT-4 access
anthropic>=0.5.0  # For Claude access
google-generativeai>=0.3.0  # For Gemini access
sentence-transformers>=2.2.2  # HuggingFace embeddings for RAG

# Database and Vector Storage
psycopg2-binary>=2.9.9  # PostgreSQL adapter
sqlalchemy>=2.0.23  # SQL toolkit and ORM
alembic>=1.12.1  # Database migrations
pgvector>=0.2.3  # pgvector extension for PostgreSQL

# Authentication
python-jose>=3.3.0  # JWT token handling
passlib>=1.7.4  # Password hashing

# Observability
structlog>=23.1.0  # Structured logging
langsmith>=0.0.43  # LLM observability (optional)
```

### 9.4 Authentication and Authorization

The PoC implements a simplified approach for authentication:

#### 9.4.1 Backend: JWT Authentication Implementation

The JWT Authentication Middleware (`app/api/middleware/jwt_auth.py`) provides:

- JWT token generation and validation
- Role-based access control for protected routes
- Support for multiple token sources (Authorization header, custom header, cookie)
- User context injection into request state
- Integration with the existing error handling system
- User management with PostgreSQL database

#### 9.4.2 Frontend: Simplified Authentication Approach

For the frontend integration, we've decided to take a simpler approach for the PoC:

1. **Keep Using Firebase Authentication**
   - The Firebase authentication is currently working
   - Changing it introduces risk without immediate benefit for the PoC
   - Focus on core PoC requirements instead

2. **Non-disruptive Mock Firebase Config**
   - Created a mock Firebase config that doesn't throw errors
   - Allows components that use Firebase to continue working
   - Provides graceful fallbacks for Firebase methods

3. **JWT Authentication Backend**
   - The JWT authentication backend is implemented and ready
   - It will be used for the PostgreSQL + pgvector integration
   - The frontend will continue using Firebase authentication for now

This approach provides several benefits:
- Simplifies the implementation by using what's already working
- Minimizes disruption to existing functionality
- Allows us to focus on core PoC requirements
- Provides a clear path for future authentication migration

#### 9.4.2 Authentication Dependencies

The middleware provides dependency injection functions for FastAPI routes:

```python
# Get the current authenticated user
@app.get("/api/protected")
async def protected_route(user = Depends(get_current_user())):
    return {"user_id": user.id}

# Get the current user with role-based access control
@app.get("/api/admin")
async def admin_route(user = Depends(get_current_user(required_roles=["admin"]))):
    return {"user_id": user.id}

# Get the current user if authenticated, or None if not
@app.get("/api/public")
async def public_route(user = Depends(get_optional_user())):
    if user:
        return {"authenticated": True, "user_id": user.id}
    return {"authenticated": False}
```

#### 9.4.3 Error Handling

| error_code       | HTTP Status | Cause                    | Client Action       |
|------------------|-------------|---------------------------|---------------------|
| invalid_request  | 400         | Invalid request parameters| Fix request format  |
| authentication   | 401         | Authentication failed     | Provide valid token |
| authorization    | 403         | Insufficient permissions  | Request access      |
| internal_error   | 500         | Unexpected server error   | Contact support     |

```json
{
  "success": false,
  "error": {
    "message": "Authentication required",
    "type": "authentication",
    "code": 401
  }
}
```

### 9.4 Local PostgreSQL Setup

To run PostgreSQL locally with Docker:

```bash
# Start PostgreSQL with pgvector extension
docker run --name postgres-pgvector -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d pgvector/pgvector:pg16

# Create the database
docker exec -it postgres-pgvector psql -U postgres -c "CREATE DATABASE businesslm;"

# Enable pgvector extension
docker exec -it postgres-pgvector psql -U postgres -d businesslm -c "CREATE EXTENSION vector;"
```

### 9.5 OpenAPI Sample

**Request:**
```http
POST /api/agents/invoke HTTP/1.1
Content-Type: application/json
Authorization: Bearer <jwt-token>

{
  "query": "What is our marketing budget for Q4?",
  "session_id": "session-123",
  "user_id": "user-456"
}
```


### 9.6 Out-of-Scope Items

The following items are explicitly out of scope for this PoC:

- Kubernetes deployment
- Performance optimization beyond basic functionality
- Full observability stack
- Advanced streaming response features
- User management UI
- OAuth integration

### 9.7 PoC Milestones

| Phase | Milestone | Status | Acceptance Criteria |
|-------|-----------|--------|---------------------|
| 1 | RAG Implementation | 🔄 In Progress | Vector search retrieves relevant documents for sample queries; Retriever and Generator components work together to provide enhanced responses |
| 1.1 | Embeddings Service | ✅ Completed | HuggingFace embeddings generate accurate vector representations |
| 1.2 | Vector Store | 🔄 In Progress | PostgreSQL with pgvector stores and retrieves vectors efficiently |
| 1.3 | Query Analyzer | ✅ Completed | Analyzer enhances queries for better retrieval |
| 1.4 | Retriever | 🔄 In Progress | PostgreSQL-based retriever combines vector similarity and keyword filtering |
| 1.5 | Generator | ✅ Completed | Enhanced generator with context management, multiple citation formats, and structured output |
| 2 | Agent Orchestration | 🔄 In Progress | Co-CEO agent routes queries to appropriate department agents |
| 3 | Agent Implementation | 🔄 In Progress | Department agents generate coherent responses using knowledge base |
| 4 | API Integration | 🔄 In Progress | API endpoints allow interaction with the agent system |
| 4.1 | Authentication Middleware | 🔄 In Progress | JWT Authentication Middleware verifies tokens and protects routes |
| 5 | Tool Integration | 🔄 In Progress | Agents can use tools to enhance responses |
