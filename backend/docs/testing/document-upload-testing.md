# Document Upload Testing Guide

This guide provides instructions for testing the document upload functionality through the browser UI frontend.

## Prerequisites

Before starting the tests, ensure you have:

1. PostgreSQL with pgvector extension set up and running
2. Backend server running
3. Frontend server running
4. A user account created in the system

## Test Cases

### 1. User Authentication

**Objective**: Verify that users can authenticate and access the document upload functionality.

**Steps**:
1. Open the frontend application in your browser (typically http://localhost:3000)
2. Click on the "Login" button
3. Enter your credentials (email and password)
4. Click "Login"

**Expected Result**: You should be logged in and redirected to the dashboard or main page.

### 2. Accessing Document Upload

**Objective**: Verify that authenticated users can access the document upload functionality.

**Steps**:
1. After logging in, navigate to the knowledge management section
   - Look for a "Knowledge Base" or "Documents" link in the navigation menu
2. Look for an "Upload Document" or "Add Document" button

**Expected Result**: You should see the document upload interface.

### 3. Uploading a Text Document

**Objective**: Verify that users can upload plain text documents.

**Steps**:
1. In the document upload interface, click "Upload" or "Add Document"
2. Select a plain text (.txt) file from your computer
3. Add metadata (if required):
   - Title: "Test Text Document"
   - Category: "Test"
4. Click "Upload" or "Submit"

**Expected Result**: 
- The document should be uploaded successfully
- You should see a success message
- The document should appear in the list of documents

### 4. Uploading a PDF Document

**Objective**: Verify that users can upload PDF documents.

**Steps**:
1. In the document upload interface, click "Upload" or "Add Document"
2. Select a PDF (.pdf) file from your computer
3. Add metadata (if required):
   - Title: "Test PDF Document"
   - Category: "Test"
4. Click "Upload" or "Submit"

**Expected Result**: 
- The document should be uploaded successfully
- You should see a success message
- The document should appear in the list of documents

### 5. Uploading a Word Document

**Objective**: Verify that users can upload Microsoft Word documents.

**Steps**:
1. In the document upload interface, click "Upload" or "Add Document"
2. Select a Word (.docx) file from your computer
3. Add metadata (if required):
   - Title: "Test Word Document"
   - Category: "Test"
4. Click "Upload" or "Submit"

**Expected Result**: 
- The document should be uploaded successfully
- You should see a success message
- The document should appear in the list of documents

### 6. Viewing Uploaded Documents

**Objective**: Verify that users can view the list of uploaded documents.

**Steps**:
1. Navigate to the document list or knowledge base section
2. Look for the documents you uploaded in the previous tests

**Expected Result**: 
- You should see a list of all uploaded documents
- Each document should display its title, category, and upload date

### 7. Searching Documents

**Objective**: Verify that users can search for documents using the RAG system.

**Steps**:
1. Navigate to the chat or query interface
2. Enter a search query related to the content of one of your uploaded documents
3. Submit the query

**Expected Result**: 
- The system should retrieve relevant information from your uploaded documents
- The response should include content from the documents
- The response may include citations or references to the source documents

### 8. Deleting Documents

**Objective**: Verify that users can delete documents.

**Steps**:
1. Navigate to the document list
2. Find a document you want to delete
3. Click on the delete button or option
4. Confirm the deletion if prompted

**Expected Result**: 
- The document should be deleted successfully
- You should see a success message
- The document should no longer appear in the list

## Troubleshooting

If you encounter issues during testing, check the following:

1. **Upload Failures**:
   - Check the file size (there may be a size limit)
   - Verify that the file format is supported
   - Check the browser console for JavaScript errors
   - Check the backend logs for server-side errors

2. **Search Issues**:
   - Verify that the document was properly processed and indexed
   - Check if the search query is relevant to the document content
   - Check the backend logs for any errors in the RAG pipeline

3. **Authentication Issues**:
   - Clear browser cookies and try logging in again
   - Check if your session has expired
   - Verify that your account has the necessary permissions

## Backend Verification

To verify that documents are properly stored in the database:

1. Connect to the PostgreSQL database:
   ```bash
   psql -d businesslm
   ```

2. Check the documents table:
   ```sql
   SELECT id, title, user_id, created_at FROM documents;
   ```

3. Check the document chunks table:
   ```sql
   SELECT id, document_id, chunk_index, text FROM document_chunks LIMIT 10;
   ```

4. Verify that embeddings are created:
   ```sql
   SELECT id, document_id, embedding IS NOT NULL AS has_embedding 
   FROM document_chunks LIMIT 10;
   ```

This will help confirm that the documents are properly stored, chunked, and embedded in the database.
