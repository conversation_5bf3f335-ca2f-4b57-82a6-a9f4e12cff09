# Manual Testing Guide: PostgreSQL + pgvector Integration

This document provides step-by-step instructions for manually testing the PostgreSQL + pgvector integration in the BusinessLM application.

## Prerequisites

Before starting the tests, ensure you have:

1. PostgreSQL 15+ installed and running
2. pgvector extension installed in PostgreSQL
3. Python 3.10+ with all dependencies installed
4. A clean database for testing

## Setup

1. **Create a test database**:
   ```bash
   createdb businesslm_test
   ```

2. **Set up environment variables**:
   ```bash
   export DATABASE_URL=postgresql://username:password@localhost:5432/businesslm_test
   export VECTOR_DIMENSION=768
   export JWT_SECRET_KEY=test-secret-key
   ```

3. **Run migrations**:
   ```bash
   cd backend
   python scripts/run_migrations.py
   ```

## Test Cases

### 1. Database Connection and Schema

**Objective**: Verify that the application can connect to PostgreSQL and the schema is correctly set up.

**Steps**:
1. Run the following command to check the database schema:
   ```bash
   psql -d businesslm_test -c "\dt"
   ```

2. Verify that the following tables exist:
   - users
   - refresh_tokens
   - documents
   - document_chunks
   - conversation_checkpoints
   - query_logs

3. Check the pgvector extension:
   ```bash
   psql -d businesslm_test -c "SELECT extname FROM pg_extension WHERE extname = 'vector'"
   ```

**Expected Result**: All tables should be present and the pgvector extension should be installed.

### 2. Vector Store Initialization

**Objective**: Verify that the PGVectorStore can be initialized correctly.

**Steps**:
1. Create a Python script `test_vector_store.py` with the following content:
   ```python
   import asyncio
   import os
   from app.rag.pgvector_store import PGVectorStore

   async def test_init():
       # Initialize the vector store
       vector_store = PGVectorStore(
           dimension=768,
           table_name="test_embeddings",
           distance_metric="cosine",
           index_type="hnsw"
       )
       print("Vector store initialized successfully")

       # Check if the table exists
       count = await vector_store.count
       print(f"Number of embeddings: {count}")

   if __name__ == "__main__":
       asyncio.run(test_init())
   ```

2. Run the script:
   ```bash
   cd backend
   python test_vector_store.py
   ```

**Expected Result**: The script should run without errors and report that the vector store was initialized successfully.

### 3. Adding and Retrieving Embeddings

**Objective**: Verify that embeddings can be added to and retrieved from the vector store.

**Steps**:
1. Create a Python script `test_add_embeddings.py` with the following content:
   ```python
   import asyncio
   import os
   import random
   from app.rag.pgvector_store import PGVectorStore

   async def test_add_retrieve():
       # Initialize the vector store
       vector_store = PGVectorStore(
           dimension=768,
           table_name="test_embeddings",
           distance_metric="cosine",
           index_type="hnsw"
       )

       # Create some test embeddings
       embeddings = []
       texts = []
       metadatas = []

       for i in range(5):
           # Create a random embedding vector
           embedding = [random.uniform(-1, 1) for _ in range(768)]
           embeddings.append(embedding)

           # Create a test text
           text = f"Test document {i}"
           texts.append(text)

           # Create metadata
           metadata = {"index": i, "category": "test"}
           metadatas.append(metadata)

       # Add embeddings to the vector store
       ids = await vector_store.add_embeddings(embeddings, texts, metadatas)
       print(f"Added {len(ids)} embeddings with IDs: {ids}")

       # Retrieve embeddings
       results = await vector_store.get(ids)
       print(f"Retrieved {len(results)} embeddings")

       # Search for similar embeddings
       query_embedding = embeddings[0]  # Use the first embedding as a query
       search_results = await vector_store.search(query_embedding, limit=3)
       print(f"Search results: {search_results}")

       # Clean up
       await vector_store.delete(ids)
       print("Deleted test embeddings")

   if __name__ == "__main__":
       asyncio.run(test_add_retrieve())
   ```

2. Run the script:
   ```bash
   cd backend
   python test_add_embeddings.py
   ```

**Expected Result**: The script should add embeddings, retrieve them, search for similar embeddings, and delete them without errors.

### 4. Knowledge Base Service Integration

**Objective**: Verify that the PgVectorKnowledgeBaseService works correctly with the vector store.

**Steps**:
1. Create a Python script `test_knowledge_base.py` with the following content:
   ```python
   import asyncio
   import os
   from app.rag.pgvector_store import PGVectorStore
   from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService
   from app.rag.embedding_utils import get_embedding_model

   async def test_knowledge_base():
       # Initialize the embedding model
       embedding_model = get_embedding_model()

       # Initialize the vector store
       vector_store = PGVectorStore(
           dimension=768,
           table_name="test_kb_embeddings",
           distance_metric="cosine",
           index_type="hnsw"
       )

       # Initialize the knowledge base service
       knowledge_base = PgVectorKnowledgeBaseService(
           vector_store=vector_store,
           embedding_model=embedding_model,
           vector_weight=0.7,
           keyword_weight=0.3,
           use_reranking=False
       )

       # Add a test document
       document = {
           "title": "Test Document",
           "content": "This is a test document about artificial intelligence and machine learning.",
           "metadata": {
               "author": "Test Author",
               "category": "AI"
           }
       }

       doc_id = await knowledge_base.add_document(document)
       print(f"Added document with ID: {doc_id}")

       # Search for documents
       results = await knowledge_base.search("artificial intelligence", limit=3)
       print(f"Search results: {results}")

       # Clean up
       await knowledge_base.delete_document(doc_id)
       print("Deleted test document")

   if __name__ == "__main__":
       asyncio.run(test_knowledge_base())
   ```

2. Run the script:
   ```bash
   cd backend
   python test_knowledge_base.py
   ```

**Expected Result**: The script should add a document to the knowledge base, search for it, and delete it without errors.

### 5. Query Logging

**Objective**: Verify that the query logging system works correctly.

**Steps**:
1. Create a Python script `test_query_logging.py` with the following content:
   ```python
   import asyncio
   import os
   from app.rag.query_logging import log_query, get_query_logs

   async def test_query_logging():
       # Log a test query
       await log_query(
           query_text="What is artificial intelligence?",
           user_id="test-user",
           thread_id="test-thread",
           session_id="test-session",
           department="AI",
           results=[
               {"id": "doc1", "text": "AI is a branch of computer science...", "score": 0.95},
               {"id": "doc2", "text": "Machine learning is a subset of AI...", "score": 0.85}
           ],
           execution_time_ms=150
       )
       print("Logged test query")

       # Retrieve query logs
       logs = await get_query_logs(session_id="test-session")
       print(f"Retrieved {len(logs)} query logs")

       # Print the first log
       if logs:
           print(f"Query: {logs[0]['query_text']}")
           print(f"User ID: {logs[0]['user_id']}")
           print(f"Thread ID: {logs[0]['thread_id']}")
           print(f"Session ID: {logs[0]['session_id']}")
           print(f"Department: {logs[0]['department']}")
           print(f"Execution Time: {logs[0]['execution_time_ms']} ms")
           print(f"Top Results: {logs[0]['top_results']}")

       # Clean up
       # Note: In a real application, you might want to keep the logs
       # This is just for testing purposes
       # await delete_query_logs(session_id="test-session")
       # print("Deleted test query logs")

   if __name__ == "__main__":
       asyncio.run(test_query_logging())
   ```

2. Run the script:
   ```bash
   cd backend
   python test_query_logging.py
   ```

**Expected Result**: The script should log a query and retrieve it without errors.

### 6. Embedding Statistics and Introspection

**Objective**: Verify that the embedding statistics and introspection tools work correctly.

**Steps**:
1. Create a Python script `test_embedding_stats.py` with the following content:
   ```python
   import asyncio
   import os
   import random
   from app.rag.pgvector_store import PGVectorStore
   from app.rag.embedding_stats import get_embedding_stats, validate_pgvector_index

   async def test_embedding_stats():
       # Initialize the vector store
       vector_store = PGVectorStore(
           dimension=768,
           table_name="test_stats_embeddings",
           distance_metric="cosine",
           index_type="hnsw"
       )

       # Create some test embeddings
       embeddings = []
       texts = []
       metadatas = []

       for i in range(10):
           # Create a random embedding vector
           embedding = [random.uniform(-1, 1) for _ in range(768)]
           embeddings.append(embedding)

           # Create a test text
           text = f"Test document {i}"
           texts.append(text)

           # Create metadata
           metadata = {"index": i, "category": "test"}
           metadatas.append(metadata)

       # Add embeddings to the vector store
       ids = await vector_store.add_embeddings(embeddings, texts, metadatas)
       print(f"Added {len(ids)} embeddings with IDs: {ids}")

       # Get embedding statistics
       stats = await get_embedding_stats()
       print(f"Dimension: {stats['dimension']}")
       print(f"Document Count: {stats['document_count']}")
       print(f"Average Norm: {stats['average_norm']}")
       print(f"Similarity Stats: {stats['similarity_stats']}")

       # Validate pgvector index
       validation = await validate_pgvector_index()
       print(f"Index Type: {validation['index_type']}")
       print(f"Index Parameters: {validation['index_parameters']}")
       print(f"Query Performance: {validation['query_performance']} ms")
       print(f"Valid: {validation['valid']}")

       # Clean up
       await vector_store.delete(ids)
       print("Deleted test embeddings")

   if __name__ == "__main__":
       asyncio.run(test_embedding_stats())
   ```

2. Run the script:
   ```bash
   cd backend
   python test_embedding_stats.py
   ```

**Expected Result**: The script should add embeddings, get statistics, validate the index, and delete the embeddings without errors.

### 7. Vector Visualization

**Objective**: Verify that the vector visualization tools work correctly.

**Steps**:
1. Create a Jupyter notebook `test_vector_visualization.ipynb` with the following content:
   ```python
   import asyncio
   import os
   import random
   import numpy as np
   import matplotlib.pyplot as plt
   import plotly.express as px
   from app.rag.pgvector_store import PGVectorStore
   from app.notebooks.vector_visualization import (
       load_vectors_from_db,
       visualize_clusters,
       visualize_query,
       explore_vector_space
   )

   # Initialize the vector store
   vector_store = PGVectorStore(
       dimension=768,
       table_name="test_viz_embeddings",
       distance_metric="cosine",
       index_type="hnsw"
   )

   # Create some test embeddings
   embeddings = []
   texts = []
   metadatas = []

   for i in range(20):
       # Create a random embedding vector
       embedding = [random.uniform(-1, 1) for _ in range(768)]
       embeddings.append(embedding)

       # Create a test text
       text = f"Test document {i}"
       texts.append(text)

       # Create metadata
       category = "category_A" if i < 10 else "category_B"
       metadata = {"index": i, "category": category}
       metadatas.append(metadata)

   # Add embeddings to the vector store
   async def add_embeddings():
       ids = await vector_store.add_embeddings(embeddings, texts, metadatas)
       print(f"Added {len(ids)} embeddings with IDs: {ids}")
       return ids

   # Run the async function
   ids = asyncio.run(add_embeddings())

   # Load vectors from the database
   connection_string = os.environ.get("DATABASE_URL")
   vectors, metadata = load_vectors_from_db(
       connection_string=connection_string,
       table_name="test_viz_embeddings"
   )

   # Visualize document clusters
   fig = visualize_clusters(vectors, metadata, method="pca", n_components=3, color_by="category")
   fig.show()

   # Visualize query
   query_embedding = embeddings[0]  # Use the first embedding as a query
   fig = visualize_query(query_embedding, vectors, metadata)
   fig.show()

   # Create interactive vector space explorer
   explorer = explore_vector_space(vectors, metadata)
   explorer.show()

   # Clean up
   async def delete_embeddings():
       await vector_store.delete(ids)
       print("Deleted test embeddings")

   # Run the async function
   asyncio.run(delete_embeddings())
   ```

2. Run the notebook:
   ```bash
   cd backend
   jupyter notebook test_vector_visualization.ipynb
   ```

**Expected Result**: The notebook should add embeddings, visualize clusters, visualize a query, create an interactive explorer, and delete the embeddings without errors.

## Troubleshooting

If you encounter any issues during testing, check the following:

1. **Database Connection Issues**:
   - Verify that PostgreSQL is running: `pg_isready`
   - Check the connection parameters in the DATABASE_URL
   - Ensure the database exists: `psql -l | grep businesslm_test`

2. **pgvector Extension Issues**:
   - Verify that pgvector is installed: `psql -d businesslm_test -c "SELECT * FROM pg_extension WHERE extname = 'vector'"`
   - Check the PostgreSQL logs for any errors related to pgvector

3. **Vector Store Issues**:
   - Check that the vector dimension matches the model's dimension
   - Verify that the embedding model is initialized correctly
   - Check for any errors in the application logs

4. **Query Logging Issues**:
   - Verify that the query_logs table exists: `psql -d businesslm_test -c "\d query_logs"`
   - Check that the query logging functions are properly imported
   - Ensure that the user has permission to write to the query_logs table

5. **Embedding Statistics Issues**:
   - Verify that the embedding_stats functions are properly imported
   - Check that the database has sufficient data for meaningful statistics
   - Ensure that the pgvector index is properly configured

6. **Vector Visualization Issues**:
   - Verify that all required Python packages are installed (plotly, scikit-learn, etc.)
   - Check that Jupyter notebook is properly configured
   - Ensure that the database connection is correctly set up in the notebook

## Conclusion

This manual testing guide provides a comprehensive set of tests for verifying the PostgreSQL + pgvector integration, including the new query logging, embedding statistics, and vector visualization features. By following these steps, you can ensure that all components of the system are working correctly with PostgreSQL and pgvector.
