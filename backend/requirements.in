# --- runtime ---
fastapi>=0.115.0            # web framework
uvicorn[standard]>=0.34.0   # ASGI server
langchain>=0.3.0            # LLM/RAG helpers
langgraph>=0.3.0            # orchestration DAG
langchain-core>=0.3.0       # Core LangChain components
python-dotenv>=1.0.0        # loads .env for local dev
pydantic-settings>=2.9.0    # settings management for Pydantic v2
typer[all]>=0.9.0           # CLI interface
rich>=13.7.0                # Rich text and formatting for terminal output
requests>=2.31.0            # HTTP client for API calls

# --- Database ---
sqlalchemy>=2.0.23          # SQL toolkit and ORM
psycopg2-binary>=2.9.9      # PostgreSQL adapter
alembic>=1.12.1             # Database migrations
pgvector>=0.2.3             # pgvector extension for PostgreSQL

# --- Authentication ---
python-jose>=3.3.0          # JWT token handling
passlib[bcrypt]>=1.7.4      # Password hashing
python-multipart>=0.0.6     # Form data parsing for login

# --- LLM providers ---
openai>=1.70.0              # OpenAI API client
anthropic>=0.50.0           # Anthropic API client (includes token counting)
google-generativeai>=0.8.0  # Google Gemini API client
tiktoken>=0.9.0             # OpenAI tokenizer
tenacity>=9.0.0             # Retry logic

# --- RAG components ---
# Note: Using specific versions to avoid compatibility issues
sentence-transformers>=2.2.2  # HuggingFace embeddings
# NOTE: If you encounter issues with HuggingFaceEmbedding, you may need to install compatible versions:
# pip install sentence-transformers==2.2.2 huggingface-hub==0.16.4
numpy==1.24.3                 # Vector operations (specific version for compatibility)
faiss-cpu==1.7.4              # In-memory vector store for localhost testing
chromadb>=0.4.0               # Alternative vector store with persistence
# --- Cross-encoder reranking ---
transformers>=4.38.0          # Hugging Face Transformers for cross-encoder models
torch>=2.2.0                  # PyTorch for cross-encoder models

# --- Legacy (will be removed) ---
firebase-admin>=6.8.0       # Firestore + Auth (legacy, will be removed)

# --- visualization ---
matplotlib>=3.7.0       # Plotting library
plotly>=5.14.0          # Interactive visualizations
scikit-learn>=1.2.0     # For dimensionality reduction (t-SNE, PCA)
umap-learn>=0.5.3       # UMAP dimensionality reduction
ipywidgets>=8.0.0       # Interactive widgets for Jupyter notebooks
jupyter>=1.0.0          # Jupyter notebook

# --- dev / test ---
pytest>=8.0.0
pytest-asyncio>=0.26.0
black>=24.0.0
isort>=5.12.0
flake8>=7.0.0
pip-tools>=7.0.0  # Used to compile requirements.txt from requirements.in
uv>=0.6.0         # Fast Python package manager (used instead of pip)