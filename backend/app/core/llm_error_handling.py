"""
LLM Error Handling Utilities (Compatibility Module)

This module provides backward compatibility with the old LLM error handling utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.errors.llm instead.
"""

import warnings
import logging

# Show deprecation warning
warnings.warn(
    "The app.core.llm_error_handling module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.errors.llm instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all symbols from the new modular structure
from app.core.errors.llm import (
    LLMError,
    LLMTimeoutError,
    LLMRateLimitError,
    handle_llm_errors,
    handle_async_llm_errors,
    LLMErrorContext
)

# Set up logging
logger = logging.getLogger(__name__)

__all__ = [
    "LLMError",
    "LLMTimeoutError",
    "LLMRateLimitError",
    "handle_llm_errors",
    "handle_async_llm_errors",
    "LLMErrorContext"
]
