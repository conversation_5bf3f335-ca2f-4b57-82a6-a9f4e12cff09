"""
Utility functions for the application.

This module contains utility functions used throughout the application.
"""
import numpy as np
from typing import List, Dict, Any, Union, Optional
import logging

logger = logging.getLogger(__name__)


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate the cosine similarity between two vectors.

    Args:
        vec1: First vector
        vec2: Second vector

    Returns:
        Cosine similarity between the vectors (between -1 and 1)
    """
    # Convert to numpy arrays
    a = np.array(vec1)
    b = np.array(vec2)
    
    # Calculate cosine similarity
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)
    
    # Handle zero division
    if norm_a == 0 or norm_b == 0:
        return 0.0
    
    return dot_product / (norm_a * norm_b)


def format_as_json(data: Dict[str, Any], indent: int = 2) -> str:
    """
    Format a dictionary as a JSON string.

    Args:
        data: The dictionary to format
        indent: The indentation level

    Returns:
        Formatted JSON string
    """
    import json
    return json.dumps(data, indent=indent)


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    Truncate text to a maximum length.

    Args:
        text: The text to truncate
        max_length: The maximum length

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    Safely get a value from a dictionary.

    Args:
        data: The dictionary
        key: The key to get
        default: The default value if the key is not found

    Returns:
        The value or default
    """
    return data.get(key, default)


def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two dictionaries.

    Args:
        dict1: First dictionary
        dict2: Second dictionary

    Returns:
        Merged dictionary
    """
    result = dict1.copy()
    result.update(dict2)
    return result
