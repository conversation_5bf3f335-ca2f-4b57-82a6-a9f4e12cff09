"""
Tracing Collector

This module provides the TracingCollector class for collecting, storing, and exporting
trace events during system execution. It is designed to provide observability into
the multi-agent orchestration process.
"""

import enum
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

class TracingLevel(enum.Enum):
    """Tracing detail level."""
    NONE = 0       # No tracing
    BASIC = 1      # Node transitions only
    STANDARD = 2   # Node transitions + key state changes
    DETAILED = 3   # Full state diffs + internal operations
    DEBUG = 4      # Everything including LLM calls and raw responses

class TracingCollector:
    """Collector for tracing events in the system."""

    def __init__(
        self,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        granularity: TracingLevel = TracingLevel.STANDARD
    ):
        """Initialize the tracing collector.

        Args:
            session_id: Unique identifier for the session
            user_id: Identifier for the user
            thread_id: Identifier for the conversation thread
            granularity: Level of detail to capture
        """
        self.session_id = session_id or str(uuid.uuid4())
        self.user_id = user_id
        self.thread_id = thread_id
        self.granularity = granularity
        self.traces: List[Dict[str, Any]] = []

    def add_trace(
        self,
        node_id: str,
        event_type: str,
        state_before: Optional[Any] = None,
        state_after: Optional[Any] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a trace event.

        Args:
            node_id: Identifier for the node
            event_type: Type of event (node_start, node_end, etc.)
            state_before: State before the event
            state_after: State after the event
            metadata: Additional metadata
        """
        if self.granularity == TracingLevel.NONE:
            return

        # Skip detailed events if granularity is too low
        if event_type in ["llm_call", "rag_retrieval"] and self.granularity.value < TracingLevel.DETAILED.value:
            return

        trace = {
            "timestamp": datetime.now().isoformat(),
            "correlation": {
                "thread_id": self.thread_id,
                "user_id": self.user_id,
                "session_id": self.session_id,
            },
            "node_id": node_id,
            "event_type": event_type,
            "metadata": metadata or {},
        }

        # Add state information based on granularity
        if self.granularity.value >= TracingLevel.STANDARD.value:
            if state_before is not None:
                trace["state_before"] = self._serialize_state(state_before)
            if state_after is not None:
                trace["state_after"] = self._serialize_state(state_after)

        self.traces.append(trace)

        # Log the trace event for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Added trace event: node_id={node_id}, event_type={event_type}")

    def _serialize_state(self, state: Any) -> Dict[str, Any]:
        """Serialize state object to a dictionary.

        Args:
            state: State object to serialize

        Returns:
            Serialized state as a dictionary
        """
        # Handle Pydantic models
        if hasattr(state, "model_dump"):
            return state.model_dump()
        # Handle dataclasses
        if hasattr(state, "__dataclass_fields__"):
            return {field: getattr(state, field) for field in state.__dataclass_fields__}
        # Handle dictionaries
        if isinstance(state, dict):
            return state
        # Handle other objects
        return {"value": str(state)}

    def export_json(self, file_path: Optional[str] = None) -> str:
        """Export traces as JSON.

        Args:
            file_path: Path to save the JSON file

        Returns:
            JSON string of traces
        """
        json_str = json.dumps(self.traces, indent=2)

        if file_path:
            with open(file_path, "w") as f:
                f.write(json_str)

        return json_str

    def export_ndjson(self, file_path: Optional[str] = None) -> str:
        """Export traces as newline-delimited JSON.

        Args:
            file_path: Path to save the NDJSON file

        Returns:
            NDJSON string of traces
        """
        ndjson_lines = [json.dumps(trace) for trace in self.traces]
        ndjson_str = "\n".join(ndjson_lines)

        if file_path:
            with open(file_path, "w") as f:
                f.write(ndjson_str)

        return ndjson_str

    def export_langgraph_format(self, file_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """Export traces in LangGraph-compatible format.

        Args:
            file_path: Path to save the LangGraph format file

        Returns:
            List of traces in LangGraph format
        """
        langgraph_events = []

        for trace in self.traces:
            event = {
                "id": str(uuid.uuid4()),
                "type": "node_execution" if trace["event_type"] == "node_start" else "state_update",
                "name": trace["node_id"],
                "data": {
                    "inputs": trace.get("state_before"),
                    "outputs": trace.get("state_after"),
                    "metadata": trace.get("metadata", {})
                },
                "timestamp": trace["timestamp"]
            }
            langgraph_events.append(event)

        if file_path:
            with open(file_path, "w") as f:
                json.dump(langgraph_events, f, indent=2)

        return langgraph_events

    def clear(self) -> None:
        """Clear all traces."""
        self.traces = []

    def to_serializable(self) -> Dict[str, Any]:
        """Convert the collector to a serializable dictionary.

        Returns:
            Dictionary representation of the collector
        """
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "thread_id": self.thread_id,
            "granularity": self.granularity.name,
            "traces": self.traces
        }
