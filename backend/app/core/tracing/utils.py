"""
Tracing Utilities

This module provides utility functions for storing, loading, and filtering trace events.
"""

import enum
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from app.core.tracing.collector import TracingCollector

class TracingEncoder(json.JSONEncoder):
    """Custom JSON encoder for tracing objects."""

    def default(self, obj):
        if isinstance(obj, TracingCollector):
            return obj.to_serializable()
        elif isinstance(obj, enum.Enum):
            return obj.name
        return super().default(obj)

def save_traces(
    traces: Union[List[Dict[str, Any]], TracingCollector],
    directory: str = "traces",
    prefix: str = "trace",
    format: str = "json"
) -> str:
    """Save traces to a file.

    Args:
        traces: List of trace events or a TracingCollector instance
        directory: Directory to save the file
        prefix: Prefix for the filename
        format: File format (json or ndjson)

    Returns:
        Path to the saved file
    """
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    filename = f"{prefix}-{timestamp}.{format}"
    file_path = os.path.join(directory, filename)

    # Extract traces from TracingCollector if needed
    if isinstance(traces, TracingCollector):
        trace_data = traces.traces
    else:
        trace_data = traces

    # Save traces
    if format == "json":
        with open(file_path, "w") as f:
            json.dump(traces, f, indent=2, cls=TracingEncoder)
    elif format == "ndjson":
        with open(file_path, "w") as f:
            if isinstance(traces, TracingCollector):
                trace_list = traces.traces
            else:
                trace_list = traces

            for trace in trace_list:
                f.write(json.dumps(trace) + "\n")
    else:
        raise ValueError(f"Unsupported format: {format}")

    return file_path

def load_traces(file_path: str) -> List[Dict[str, Any]]:
    """Load traces from a file.

    Args:
        file_path: Path to the trace file

    Returns:
        List of trace events
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Trace file not found: {file_path}")

    # Determine format from file extension
    if file_path.endswith(".json"):
        with open(file_path, "r") as f:
            return json.load(f)
    elif file_path.endswith(".ndjson"):
        traces = []
        with open(file_path, "r") as f:
            for line in f:
                if line.strip():
                    traces.append(json.loads(line))
        return traces
    else:
        raise ValueError(f"Unsupported file format: {file_path}")

def filter_traces(
    traces: List[Dict[str, Any]],
    node_id: Optional[str] = None,
    event_type: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    thread_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """Filter traces based on criteria.

    Args:
        traces: List of trace events
        node_id: Filter by node ID
        event_type: Filter by event type
        start_time: Filter by start time (ISO format)
        end_time: Filter by end time (ISO format)
        session_id: Filter by session ID
        user_id: Filter by user ID
        thread_id: Filter by thread ID

    Returns:
        Filtered list of trace events
    """
    filtered_traces = traces

    if node_id:
        filtered_traces = [t for t in filtered_traces if t.get("node_id") == node_id]

    if event_type:
        filtered_traces = [t for t in filtered_traces if t.get("event_type") == event_type]

    if start_time:
        start_dt = datetime.fromisoformat(start_time)
        filtered_traces = [
            t for t in filtered_traces
            if datetime.fromisoformat(t.get("timestamp", "")) >= start_dt
        ]

    if end_time:
        end_dt = datetime.fromisoformat(end_time)
        filtered_traces = [
            t for t in filtered_traces
            if datetime.fromisoformat(t.get("timestamp", "")) <= end_dt
        ]

    if session_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("session_id") == session_id
        ]

    if user_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("user_id") == user_id
        ]

    if thread_id:
        filtered_traces = [
            t for t in filtered_traces
            if t.get("correlation", {}).get("thread_id") == thread_id
        ]

    return filtered_traces

def get_latest_trace_file(directory: str = "traces", prefix: str = "trace") -> Optional[str]:
    """Get the path to the latest trace file.

    Args:
        directory: Directory containing trace files
        prefix: Prefix for trace filenames

    Returns:
        Path to the latest trace file, or None if no files found
    """
    if not os.path.exists(directory):
        return None

    files = [
        os.path.join(directory, f)
        for f in os.listdir(directory)
        if f.startswith(prefix) and (f.endswith(".json") or f.endswith(".ndjson"))
    ]

    if not files:
        return None

    # Sort by modification time (newest first)
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    return files[0]
