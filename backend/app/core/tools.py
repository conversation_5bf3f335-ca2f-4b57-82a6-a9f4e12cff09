"""
Tool Registry and Base Tool Implementation

This module implements the core tool registry and base tool class.
It provides the infrastructure for registering, retrieving, and using tools
across the system, but does not contain specific tool implementations.

Tool implementations should be placed in the appropriate department-specific
modules in the `app.agents.tools` package.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class BaseTool(ABC):
    """
    Abstract base class for tools.

    All tools should inherit from this class and implement the `run` method.
    This class provides the core functionality that all tools must implement,
    including input/output validation and execution.
    """

    name: str
    description: str
    department: str

    class InputSchema(BaseModel):
        """Base input schema for tools."""
        pass

    class OutputSchema(BaseModel):
        """Base output schema for tools."""
        result: str = Field(description="The result of the tool execution")

    def __init__(self, name: str, description: str, department: str):
        """
        Initialize a tool.

        Args:
            name: The name of the tool
            description: A description of what the tool does
            department: The department the tool belongs to
        """
        self.name = name
        self.description = description
        self.department = department
        logger.info(f"Tool initialized: {name} ({department})")

    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """
        Run the tool with the given parameters.

        Args:
            **kwargs: Tool-specific parameters

        Returns:
            The result of the tool execution
        """
        pass

    def validate_input(self, **kwargs) -> Dict[str, Any]:
        """
        Validate the input parameters.

        Args:
            **kwargs: Tool-specific parameters

        Returns:
            Validated parameters
        """
        return self.InputSchema(**kwargs).model_dump()

    def validate_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate the output result.

        Args:
            result: The result of the tool execution

        Returns:
            Validated result
        """
        return self.OutputSchema(**result).model_dump()


# Global tool registry
TOOL_REGISTRY: Dict[str, BaseTool] = {}


def register_tool(tool: BaseTool, override: bool = False) -> None:
    """
    Register a tool in the global registry.

    Args:
        tool: The tool to register
        override: Whether to override an existing tool with the same ID

    Raises:
        ValueError: If a tool with the same ID already exists and override is False
    """
    tool_id = f"{tool.department}.{tool.name}"

    if tool_id in TOOL_REGISTRY and not override:
        raise ValueError(f"Tool {tool_id} already registered")

    TOOL_REGISTRY[tool_id] = tool
    logger.info(f"Tool registered: {tool_id}")


def get_tool(tool_id: str) -> Optional[BaseTool]:
    """
    Get a tool by ID.

    Args:
        tool_id: The tool ID

    Returns:
        The tool or None if not found
    """
    tool = TOOL_REGISTRY.get(tool_id)
    if tool is None:
        logger.warning(f"Tool not found: {tool_id}")
    return tool


def get_tools_by_department(department: str) -> List[BaseTool]:
    """
    Get all tools for a department.

    Args:
        department: The department ID

    Returns:
        List of tools for the department
    """
    tools = [
        tool for tool in TOOL_REGISTRY.values()
        if tool.department == department
    ]
    logger.debug(f"Found {len(tools)} tools for department: {department}")
    return tools


def list_all_tools() -> Dict[str, List[str]]:
    """
    Get a dictionary of all registered tools grouped by department.

    Returns:
        Dictionary mapping department names to lists of tool names
    """
    result = {}
    for _, tool in TOOL_REGISTRY.items():
        if tool.department not in result:
            result[tool.department] = []
        result[tool.department].append(tool.name)
    return result
