"""
Benchmarks Package

This package contains components for benchmarking and evaluating the
RAG and multi-agent orchestration system.

The benchmarking system is designed to:
1. Execute standardized test queries
2. Collect and analyze performance metrics
3. Generate reports on system performance
4. Track improvements over time
"""

from .runner import BenchmarkRunner
from .utils import load_benchmark_queries, save_benchmark_results

__all__ = [
    "BenchmarkRunner",
    "load_benchmark_queries",
    "save_benchmark_results"
]
