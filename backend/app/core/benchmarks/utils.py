"""
Benchmark Utilities

This module provides utility functions for loading benchmark queries and
saving benchmark results.
"""

import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def load_benchmark_queries(path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load benchmark queries from a JSON file.

    Args:
        path: Path to the benchmark queries file

    Returns:
        Dictionary containing benchmark queries
    """
    # Use default path if none provided
    if path is None:
        path = os.path.join("backend", "data", "benchmarks", "benchmark_queries.json")

    # Check if file exists
    if not os.path.exists(path):
        logger.warning(f"Benchmark queries file not found: {path}")
        return {"version": "1.0", "categories": {}}

    # Load benchmark queries
    try:
        with open(path, "r") as f:
            benchmarks = json.load(f)
        logger.info(f"Loaded benchmark queries from {path}")
        return benchmarks
    except Exception as e:
        logger.error(f"Error loading benchmark queries: {e}")
        return {"version": "1.0", "categories": {}}


def save_benchmark_results(
    results: Dict[str, Any],
    path: Optional[str] = None,
    include_timestamp: bool = True
) -> str:
    """
    Save benchmark results to a JSON file.

    Args:
        results: Dictionary containing benchmark results
        path: Path to save the results to (directory or file)
        include_timestamp: Whether to include a timestamp in the filename

    Returns:
        Path to the saved file
    """
    # Determine file path
    if path is None:
        # Use default path
        results_dir = os.path.join("backend", "data", "benchmarks", "results")
        os.makedirs(results_dir, exist_ok=True)

        # Create filename with timestamp if requested
        if include_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        else:
            filename = "benchmark_results.json"

        path = os.path.join(results_dir, filename)
    elif os.path.isdir(path):
        # Path is a directory, create filename
        if include_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        else:
            filename = "benchmark_results.json"

        path = os.path.join(path, filename)

    # Add metadata to results
    results["metadata"] = {
        "timestamp": datetime.now().isoformat(),
        "version": "1.0"
    }

    # Save results to file
    try:
        with open(path, "w") as f:
            json.dump(results, f, indent=2)
        logger.info(f"Saved benchmark results to {path}")
        return path
    except Exception as e:
        logger.error(f"Error saving benchmark results: {e}")
        return ""


def get_benchmark_summary(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate a summary of benchmark results.

    Args:
        results: Dictionary containing benchmark results

    Returns:
        Dictionary containing benchmark summary
    """
    summary = {
        "total_queries": 0,
        "categories": {},
        "overall": {
            "avg_precision": 0.0,
            "avg_recall": 0.0,
            "avg_f1": 0.0,
            "avg_time": 0.0,
            "success_rate": 0.0
        }
    }

    # Process each category
    precision_sum = 0.0
    recall_sum = 0.0
    f1_sum = 0.0
    time_sum = 0.0
    success_count = 0
    total_count = 0

    for category, category_results in results.items():
        if category == "metadata":
            continue

        category_summary = {
            "query_count": len(category_results),
            "avg_precision": 0.0,
            "avg_recall": 0.0,
            "avg_f1": 0.0,
            "avg_time": 0.0,
            "success_rate": 0.0
        }

        # Calculate category averages
        cat_precision_sum = 0.0
        cat_recall_sum = 0.0
        cat_f1_sum = 0.0
        cat_time_sum = 0.0
        cat_success_count = 0

        for result in category_results:
            # Check if result was successful
            is_success = result.get("success", False)
            if is_success:
                cat_success_count += 1
                success_count += 1

            metrics = result.get("metrics", {})
            routing = metrics.get("routing", {})
            overall = metrics.get("overall", {})

            # Get metrics with defaults
            precision = routing.get("precision", 0.0)
            recall = routing.get("recall", 0.0)
            f1 = routing.get("f1", 0.0)
            time = overall.get("total_time", 0.0)

            # Add to sums
            cat_precision_sum += precision
            cat_recall_sum += recall
            cat_f1_sum += f1
            cat_time_sum += time

            # Add to overall sums
            precision_sum += precision
            recall_sum += recall
            f1_sum += f1
            time_sum += time
            total_count += 1

        # Calculate category averages
        if category_summary["query_count"] > 0:
            category_summary["avg_precision"] = cat_precision_sum / category_summary["query_count"]
            category_summary["avg_recall"] = cat_recall_sum / category_summary["query_count"]
            category_summary["avg_f1"] = cat_f1_sum / category_summary["query_count"]
            category_summary["avg_time"] = cat_time_sum / category_summary["query_count"]
            category_summary["success_rate"] = cat_success_count / category_summary["query_count"]

        # Add to summary
        summary["categories"][category] = category_summary
        summary["total_queries"] += category_summary["query_count"]

    # Calculate overall averages
    if total_count > 0:
        summary["overall"]["avg_precision"] = precision_sum / total_count
        summary["overall"]["avg_recall"] = recall_sum / total_count
        summary["overall"]["avg_f1"] = f1_sum / total_count
        summary["overall"]["avg_time"] = time_sum / total_count
        summary["overall"]["success_rate"] = success_count / total_count

    return summary
