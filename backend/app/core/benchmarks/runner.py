"""
Benchmark Runner

This module provides the BenchmarkRunner class for executing benchmark queries
and collecting performance metrics.
"""

import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime

from ..metrics import MetricsCollector
from .utils import load_benchmark_queries, save_benchmark_results, get_benchmark_summary

logger = logging.getLogger(__name__)


class BenchmarkRunner:
    """
    Runner for executing benchmark queries and collecting metrics.
    """

    def __init__(self, benchmark_path: Optional[str] = None):
        """
        Initialize the benchmark runner.

        Args:
            benchmark_path: Path to the benchmark queries file
        """
        self.benchmark_path = benchmark_path
        self.benchmarks = load_benchmark_queries(benchmark_path)
        self.results = {}
        logger.info(f"Initialized benchmark runner with {len(self.benchmarks.get('categories', {}))} categories")

    async def run_category(
        self,
        category: str,
        agent_system: Any,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Run all benchmarks in a category.

        Args:
            category: The category to run
            agent_system: The agent system to test
            **kwargs: Additional arguments to pass to the agent system

        Returns:
            List of benchmark results
        """
        if category not in self.benchmarks.get("categories", {}):
            logger.warning(f"Category {category} not found in benchmarks")
            return []

        logger.info(f"Running benchmark category: {category}")
        results = []

        # Get queries for the category
        queries = self.benchmarks["categories"][category].get("queries", [])

        # Run each query
        for query in queries:
            logger.info(f"Running benchmark query: {query.get('id')} - {query.get('text')}")
            result = await self.run_query(query, agent_system, **kwargs)
            results.append(result)

        # Store results in the results dictionary
        self.results[category] = results

        logger.info(f"Completed benchmark category: {category} ({len(results)} queries)")
        return results

    async def run_query(
        self,
        query: Dict[str, Any],
        agent_system: Any,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Run a single benchmark query.

        Args:
            query: The query to run
            agent_system: The agent system to test
            **kwargs: Additional arguments to pass to the agent system

        Returns:
            Benchmark result
        """
        # Create metrics collector
        metrics_collector = MetricsCollector(session_id=query.get("id"))

        # Extract query text and expected departments
        query_text = query.get("text", "")
        expected_departments = query.get("expected_departments", [])

        # Add metrics collector to kwargs
        kwargs["metrics_collector"] = metrics_collector

        # Run the query
        start_time = time.time()
        try:
            # Process the query
            response = await agent_system.process_query(
                query=query_text,
                **kwargs
            )

            # Calculate elapsed time
            elapsed_time = time.time() - start_time

            # Add overall metrics
            metrics_collector.metrics["overall"]["total_time"] = elapsed_time

            # Get actual departments from response
            actual_departments = response.get("departments", [])

            # Track routing metrics with expected departments
            if "routing" not in metrics_collector.metrics or "precision" not in metrics_collector.metrics["routing"]:
                metrics_collector.track_routing(
                    state=None,
                    departments=actual_departments,
                    confidence_scores={},
                    expected_departments=expected_departments
                )

            # Add query and response to metrics
            metrics_collector.metrics["overall"]["query"] = query_text
            metrics_collector.metrics["overall"]["response"] = response.get("response", "")

            # Finalize metrics
            metrics_collector.finalize()

            # Create result
            result = {
                "query": query,
                "response": response,
                "metrics": metrics_collector.metrics,
                "events": metrics_collector.events,
                "success": True
            }

            logger.info(f"Benchmark query completed: {query.get('id')} - {elapsed_time:.2f}s")

        except Exception as e:
            # Handle errors
            elapsed_time = time.time() - start_time
            logger.error(f"Error running benchmark query {query.get('id')}: {e}")

            # Add error metrics
            metrics_collector.metrics["overall"]["total_time"] = elapsed_time
            metrics_collector.metrics["overall"]["error"] = str(e)
            metrics_collector.finalize()

            # Create result with error
            result = {
                "query": query,
                "error": str(e),
                "metrics": metrics_collector.metrics,
                "events": metrics_collector.events,
                "success": False
            }

        return result

    async def run_all(
        self,
        agent_system: Any,
        **kwargs
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Run all benchmarks.

        Args:
            agent_system: The agent system to test
            **kwargs: Additional arguments to pass to the agent system

        Returns:
            Dictionary of benchmark results by category
        """
        all_results = {}

        # Run each category
        for category in self.benchmarks.get("categories", {}):
            logger.info(f"Running benchmark category: {category}")
            all_results[category] = await self.run_category(category, agent_system, **kwargs)

        self.results = all_results
        logger.info(f"Completed all benchmarks: {len(all_results)} categories")
        return all_results

    def generate_report(
        self,
        format: str = "text",
        include_details: bool = True
    ) -> Union[str, Dict[str, Any]]:
        """
        Generate a report of benchmark results.

        Args:
            format: The format of the report ("text", "json", "dict")
            include_details: Whether to include detailed results

        Returns:
            The benchmark report in the specified format
        """
        # Generate summary
        summary = get_benchmark_summary(self.results)

        if format == "text":
            # Generate text report
            report = []
            report.append(f"Benchmark Report ({datetime.now().isoformat()})")
            report.append(f"Total Queries: {summary['total_queries']}")
            report.append("")

            # Add overall metrics
            report.append("Overall Metrics:")
            report.append(f"  Avg Precision: {summary['overall']['avg_precision']:.4f}")
            report.append(f"  Avg Recall: {summary['overall']['avg_recall']:.4f}")
            report.append(f"  Avg F1: {summary['overall']['avg_f1']:.4f}")
            report.append(f"  Avg Time: {summary['overall']['avg_time']:.4f}s")
            report.append("")

            # Add category metrics
            report.append("Category Metrics:")
            for category, category_summary in summary["categories"].items():
                report.append(f"  {category}:")
                report.append(f"    Queries: {category_summary['query_count']}")
                report.append(f"    Avg Precision: {category_summary['avg_precision']:.4f}")
                report.append(f"    Avg Recall: {category_summary['avg_recall']:.4f}")
                report.append(f"    Avg F1: {category_summary['avg_f1']:.4f}")
                report.append(f"    Avg Time: {category_summary['avg_time']:.4f}s")

            # Add detailed results if requested
            if include_details:
                report.append("")
                report.append("Detailed Results:")
                for category, category_results in self.results.items():
                    if category == "metadata":
                        continue

                    report.append(f"  {category}:")
                    for i, result in enumerate(category_results):
                        query = result.get("query", {})
                        metrics = result.get("metrics", {})

                        report.append(f"    Query {i+1}: {query.get('id')} - {query.get('text')}")
                        report.append(f"      Success: {result.get('success', False)}")

                        if "routing" in metrics:
                            routing = metrics["routing"]
                            report.append(f"      Precision: {routing.get('precision', 0.0):.4f}")
                            report.append(f"      Recall: {routing.get('recall', 0.0):.4f}")
                            report.append(f"      F1: {routing.get('f1', 0.0):.4f}")

                        if "overall" in metrics:
                            overall = metrics["overall"]
                            report.append(f"      Time: {overall.get('total_time', 0.0):.4f}s")

                        if "error" in result:
                            report.append(f"      Error: {result['error']}")

                        report.append("")

            return "\n".join(report)

        elif format == "json":
            # Return JSON string
            return json.dumps({
                "summary": summary,
                "results": self.results if include_details else None
            }, indent=2)

        elif format == "dict":
            # Return Python dictionary
            return {
                "summary": summary,
                "results": self.results if include_details else None
            }

        else:
            raise ValueError(f"Unsupported format: {format}")

    def save_results(
        self,
        path: Optional[str] = None,
        include_timestamp: bool = True
    ) -> str:
        """
        Save benchmark results to a file.

        Args:
            path: Path to save the results to
            include_timestamp: Whether to include a timestamp in the filename

        Returns:
            Path to the saved file
        """
        return save_benchmark_results(self.results, path, include_timestamp)
