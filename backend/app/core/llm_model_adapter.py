"""
LLM Adapter Implementation

This module is now a compatibility layer that re-exports all symbols from the
new modular structure in the `llm` package -> backend/app/core/llm.

For new code, import directly from the new modules:
    from backend.app.core.llm import get_llm_adapter, LLMAdapter, ChatMessage

Example usage:
    >>> adapter = get_llm_adapter("openai")
    >>> response = await adapter.chat([{"role": "user", "content": "Hello!"}])
    >>> print(response)  # response is a string

Streaming example:
    >>> adapter = get_llm_adapter("openai")
    >>> # When stream=True, the response is an async generator
    >>> async for chunk in adapter.chat([{"role": "user", "content": "Hello!"}], stream=True):
    >>>     print(chunk, end="", flush=True)
"""

# Import required types for type hinting and backward compatibility
from typing import (
    AsyncIterator,  # For asynchronous iteration over streams (e.g., streaming responses)
    Dict,           # For type hinting dictionaries (key-value pairs)
    List,           # For type hinting lists
    Literal,        # For restricting variables to a specific set of literal values
    Optional,       # For optional parameters that may be None
    Tuple,          # For type hinting tuples (fixed-length sequences)
    Type,           # For hinting classes/types themselves (e.g., Type[EmbeddingModel])
    TypedDict,      # For dictionaries with a fixed set of fields (schema enforcement)
    Union,          # For type hinting values that could be multiple types (e.g., str or None)
)

# Import base classes for building abstract base classes (interfaces)
from abc import ABC, abstractmethod  # For defining abstract classes and methods

# Import decorators and utility functions
from functools import wraps  # For creating function decorators that preserve metadata

# Import async programming tools
import asyncio  # For writing concurrent, non-blocking code (async/await patterns)

# Import logging utilities
import logging  # For application logging (debugging, monitoring)

# Import environment variable handling
import os  # For accessing environment variables (e.g., API keys)

# Import random number utilities
import random  # For generating random numbers (useful for fallback logic, retries)

# Import time utilities
import time  # For measuring execution time, delays, retries, or performance logging

# Re-export all symbols from the new modular structure
from .llm import (
    # Base classes and types
    LLMAdapter,
    ChatMessage,
    LLMConfig,
    log_llm_metrics,
    
    # Adapter implementations
    OpenAIAdapter,
    AnthropicAdapter,
    GeminiAdapter,
    MockAdapter,
    
    # Factory functions
    get_llm_adapter,
    get_llm_adapter_factory,
    register_adapter,
    ADAPTER_REGISTRY,
    
    # Availability flags
    OPENAI_AVAILABLE,
    ANTHROPIC_AVAILABLE,
    GEMINI_AVAILABLE,
    TIKTOKEN_AVAILABLE,
    TENACITY_AVAILABLE,
    LLM_DEPENDENCIES,
)

# Define the list of exported symbols
__all__ = [
    "LLMAdapter",
    "OpenAIAdapter",
    "AnthropicAdapter",
    "GeminiAdapter",
    "MockAdapter",
    "get_llm_adapter",
    "get_llm_adapter_factory",
    "register_adapter",
    "ChatMessage",
    "LLMConfig",
    "OPENAI_AVAILABLE",
    "ANTHROPIC_AVAILABLE",
    "GEMINI_AVAILABLE",
    "TIKTOKEN_AVAILABLE",
    "TENACITY_AVAILABLE",
    "LLM_DEPENDENCIES",
    "ADAPTER_REGISTRY",
    "log_llm_metrics",
]
