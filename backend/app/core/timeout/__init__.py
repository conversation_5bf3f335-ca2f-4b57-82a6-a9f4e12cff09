"""
Timeout Utilities Package

This package provides unified timeout and retry utilities for the application.
It includes:
1. Base timeout and retry utilities
2. Component-specific timeout utilities
3. Circuit breaker implementation for external services

These utilities ensure consistent timeout and retry behavior across the codebase.
"""

# Import base utilities
from .base import (
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG,
    CircuitBreaker,
    ServiceState,
    with_timeout,
    with_retry,
    with_timeout_and_retry
)

# Import component-specific utilities
from .llm import (
    with_llm_timeout,
    with_llm_timeout_decorator
)

from .rag import (
    RAG_TIMEOUTS,
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,
    with_embedding_timeout_decorator,
    with_vector_search_timeout_decorator,
    with_keyword_search_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

from .graph import (
    NODE_TIMEOUTS,
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout
)

__all__ = [
    # Base utilities
    "DEFAULT_TIMEOUTS",
    "DEFAULT_RETRY_CONFIG",
    "CircuitBreaker",
    "ServiceState",
    "with_timeout",
    "with_retry",
    "with_timeout_and_retry",
    
    # LLM utilities
    "with_llm_timeout",
    "with_llm_timeout_decorator",
    
    # RAG utilities
    "RAG_TIMEOUTS",
    "with_embedding_timeout",
    "with_vector_search_timeout",
    "with_rag_timeout",
    "with_embedding_timeout_decorator",
    "with_vector_search_timeout_decorator",
    "with_keyword_search_timeout_decorator",
    "with_hybrid_search_timeout_decorator",
    
    # Graph utilities
    "NODE_TIMEOUTS",
    "with_node_timeout_and_retry",
    "with_node_timeout",
    "with_analyze_query_timeout",
    "with_retrieve_knowledge_timeout",
    "with_route_to_departments_timeout",
    "with_generate_response_timeout"
]
