"""
Base Timeout and Retry Utilities

This module provides the core timeout and retry utilities used across the application.
It includes:
1. Configurable timeout parameters
2. Timeout wrapper functions for async functions
3. Retry decorators with exponential backoff
4. Circuit breaker implementation for external services

These utilities are designed to be used across the codebase to ensure consistent
timeout and retry behavior.
"""

import asyncio
import functools
import logging
import os
import time
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

# Import tenacity for retry logic
try:
    from tenacity import (
        retry,
        retry_if_exception_type,
        stop_after_attempt,
        wait_exponential,
        before_sleep_log,
        RetryError
    )
    TENACITY_AVAILABLE = True
except ImportError:
    TENACITY_AVAILABLE = False
    # Define dummy versions to prevent runtime errors
    def retry(*args, **kwargs):
        return lambda func: func

    def retry_if_exception_type(*args):
        return None

    def stop_after_attempt(*args):
        return None

    def wait_exponential(*args, **kwargs):
        return None

    def before_sleep_log(*args, **kwargs):
        return None

    class RetryError(Exception):
        pass

# Import error handling utilities
from app.core.errors.base import (
    AppError,
    TimeoutError,
    RateLimitError,
    ExternalServiceError,
    log_error
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

# Default timeout values (in seconds)
DEFAULT_TIMEOUTS = {
    "llm": int(os.getenv("TIMEOUT_LLM", "30")),
    "rag_search": int(os.getenv("TIMEOUT_RAG_SEARCH", "10")),
    "rag_embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "agent_node": int(os.getenv("TIMEOUT_AGENT_NODE", "40")),
    "external_service": int(os.getenv("TIMEOUT_EXTERNAL_SERVICE", "10")),
    "database": int(os.getenv("TIMEOUT_DATABASE", "5")),
}

# Default retry configuration
DEFAULT_RETRY_CONFIG = {
    "max_attempts": int(os.getenv("RETRY_MAX_ATTEMPTS", "3")),
    "min_wait": float(os.getenv("RETRY_MIN_WAIT", "1.0")),
    "max_wait": float(os.getenv("RETRY_MAX_WAIT", "10.0")),
    "multiplier": float(os.getenv("RETRY_MULTIPLIER", "2.0")),
}


class ServiceState(Enum):
    """Enum for circuit breaker service states."""
    CLOSED = "closed"  # Normal operation, requests allowed
    OPEN = "open"      # Circuit is open, requests are not allowed
    HALF_OPEN = "half_open"  # Testing if service is back online


class CircuitBreaker:
    """
    Circuit breaker implementation for external services.

    This class implements the circuit breaker pattern to prevent repeated calls to
    failing external services. It tracks failures and opens the circuit when a
    threshold is reached, preventing further calls until a recovery period has elapsed.

    Attributes:
        service_name: Name of the service being protected
        failure_threshold: Number of failures before opening the circuit
        recovery_timeout: Time in seconds before attempting recovery
        state: Current state of the circuit (CLOSED, OPEN, HALF_OPEN)
        failure_count: Current count of consecutive failures
        last_failure_time: Timestamp of the last failure
        last_success_time: Timestamp of the last successful call
    """

    # Class-level dictionary to track circuit breakers by service name
    _instances: Dict[str, 'CircuitBreaker'] = {}

    @classmethod
    def get_instance(cls, service_name: str, **kwargs) -> 'CircuitBreaker':
        """
        Get or create a circuit breaker instance for a service.

        Args:
            service_name: Name of the service
            **kwargs: Additional arguments for CircuitBreaker constructor

        Returns:
            CircuitBreaker instance
        """
        if service_name not in cls._instances:
            cls._instances[service_name] = CircuitBreaker(service_name, **kwargs)
        return cls._instances[service_name]

    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0
    ):
        """
        Initialize the circuit breaker.

        Args:
            service_name: Name of the service being protected
            failure_threshold: Number of failures before opening the circuit
            recovery_timeout: Time in seconds before attempting recovery
        """
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = ServiceState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.last_success_time = 0.0

    def record_success(self):
        """Record a successful call to the service."""
        self.failure_count = 0
        self.last_success_time = time.time()
        if self.state == ServiceState.HALF_OPEN:
            logger.info(f"Circuit for {self.service_name} is now CLOSED after successful recovery")
            self.state = ServiceState.CLOSED

    def record_failure(self):
        """Record a failed call to the service."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.state == ServiceState.CLOSED and self.failure_count >= self.failure_threshold:
            logger.warning(
                f"Circuit for {self.service_name} is now OPEN after {self.failure_count} failures"
            )
            self.state = ServiceState.OPEN

    def is_allowed(self) -> bool:
        """
        Check if a call to the service is allowed.

        Returns:
            True if the call is allowed, False otherwise
        """
        if self.state == ServiceState.CLOSED:
            return True

        if self.state == ServiceState.OPEN:
            # Check if recovery timeout has elapsed
            if time.time() - self.last_failure_time > self.recovery_timeout:
                logger.info(f"Circuit for {self.service_name} is now HALF_OPEN, testing recovery")
                self.state = ServiceState.HALF_OPEN
                return True
            return False

        # HALF_OPEN state - allow one test request
        return True

    async def execute(
        self,
        func: Callable[..., Awaitable[T]],
        *args,
        fallback: Optional[Callable[..., Awaitable[T]]] = None,
        **kwargs
    ) -> T:
        """
        Execute a function with circuit breaker protection.

        Args:
            func: Async function to execute
            *args: Arguments for the function
            fallback: Optional fallback function to call if circuit is open
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the function or fallback

        Raises:
            ExternalServiceError: If circuit is open and no fallback is provided
        """
        if not self.is_allowed():
            logger.warning(f"Circuit for {self.service_name} is OPEN, call not allowed")
            if fallback:
                return await fallback(*args, **kwargs)
            raise ExternalServiceError(
                message=f"Service {self.service_name} is unavailable (circuit open)",
                service=self.service_name
            )

        try:
            result = await func(*args, **kwargs)
            self.record_success()
            return result
        except Exception as e:
            self.record_failure()
            logger.error(
                f"Call to {self.service_name} failed, circuit state: {self.state.value}",
                exc_info=True
            )
            if fallback:
                return await fallback(*args, **kwargs)
            raise


async def with_timeout(
    func: Callable[..., Awaitable[T]],
    *args,
    timeout_seconds: Optional[float] = None,
    operation_name: Optional[str] = None,
    timeout_error_message: Optional[str] = None,
    **kwargs
) -> T:
    """
    Execute an async function with a timeout.

    Args:
        func: Async function to execute
        *args: Arguments for the function
        timeout_seconds: Timeout in seconds
        operation_name: Name of the operation for error reporting
        timeout_error_message: Custom error message for timeout
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function

    Raises:
        TimeoutError: If the function execution times out
    """
    operation_name = operation_name or func.__name__
    timeout_seconds = timeout_seconds or DEFAULT_TIMEOUTS.get("external_service", 10)
    timeout_error_message = timeout_error_message or f"Operation {operation_name} timed out after {timeout_seconds} seconds"

    try:
        # For Python 3.11+, use asyncio.timeout
        # For Python 3.10, use asyncio.wait_for
        if hasattr(asyncio, 'timeout'):
            async with asyncio.timeout(timeout_seconds):
                return await func(*args, **kwargs)
        else:
            return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
    except asyncio.TimeoutError:
        logger.warning(
            f"Timeout in {operation_name} after {timeout_seconds}s",
            extra={"operation": operation_name, "timeout_seconds": timeout_seconds}
        )

        # Create and log a timeout error
        error = TimeoutError(
            message=timeout_error_message,
            operation=operation_name,
            timeout_seconds=timeout_seconds
        )
        log_error(error, {"operation": operation_name}, logging.WARNING)

        # Raise the error
        raise error


def with_retry(
    retry_on: Optional[List[Type[Exception]]] = None,
    max_attempts: Optional[int] = None,
    min_wait: Optional[float] = None,
    max_wait: Optional[float] = None,
    multiplier: Optional[float] = None,
    reraise: bool = True,
    operation_name: Optional[str] = None
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for retrying async functions with exponential backoff.

    Args:
        retry_on: List of exception types to retry on
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries in seconds
        max_wait: Maximum wait time between retries in seconds
        multiplier: Multiplier for exponential backoff
        reraise: Whether to reraise the last exception after all retries fail
        operation_name: Name of the operation for logging

    Returns:
        Decorated function
    """
    # Use default values if not provided
    retry_on = retry_on or [TimeoutError, RateLimitError, ConnectionError]
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]
    min_wait = min_wait or DEFAULT_RETRY_CONFIG["min_wait"]
    max_wait = max_wait or DEFAULT_RETRY_CONFIG["max_wait"]
    multiplier = multiplier or DEFAULT_RETRY_CONFIG["multiplier"]

    def decorator(func: AsyncF) -> AsyncF:
        """Decorator function."""
        func_name = operation_name or func.__name__

        if TENACITY_AVAILABLE:
            @functools.wraps(func)
            @retry(
                retry=retry_if_exception_type(tuple(retry_on)),
                stop=stop_after_attempt(max_attempts),
                wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                before_sleep=before_sleep_log(logger, logging.INFO),
                reraise=reraise
            )
            async def wrapper(*args, **kwargs):
                return await func(*args, **kwargs)

            return cast(AsyncF, wrapper)
        else:
            # Manual retry implementation if tenacity is not available
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                attempts = 0
                last_exception = None
                wait_time = min_wait

                while attempts < max_attempts:
                    try:
                        return await func(*args, **kwargs)
                    except tuple(retry_on) as e:
                        attempts += 1
                        last_exception = e

                        if attempts >= max_attempts:
                            break

                        logger.info(
                            f"Retry attempt {attempts}/{max_attempts} for {func_name} after error: {str(e)}. "
                            f"Waiting {wait_time:.2f}s..."
                        )

                        await asyncio.sleep(wait_time)
                        wait_time = min(max_wait, wait_time * multiplier)

                if reraise and last_exception:
                    raise last_exception
                return None  # Type safety requires a return, but this is unreachable if reraise=True

            return cast(AsyncF, wrapper)

    return decorator


async def with_timeout_and_retry(
    func: Callable[..., Awaitable[T]],
    *args,
    timeout_seconds: Optional[float] = None,
    operation_name: Optional[str] = None,
    retry_on: Optional[List[Type[Exception]]] = None,
    max_attempts: Optional[int] = None,
    **kwargs
) -> T:
    """
    Execute an async function with both timeout and retry logic.

    This function combines timeout and retry functionality to provide robust
    error handling for async operations.

    Args:
        func: Async function to execute
        *args: Arguments for the function
        timeout_seconds: Timeout in seconds
        operation_name: Name of the operation for error reporting
        retry_on: List of exception types to retry on
        max_attempts: Maximum number of retry attempts
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function

    Raises:
        TimeoutError: If the function execution times out after all retries
        Exception: Other exceptions that occur during execution
    """
    operation_name = operation_name or func.__name__
    timeout_seconds = timeout_seconds or DEFAULT_TIMEOUTS.get("external_service", 10)
    retry_on = retry_on or [TimeoutError, RateLimitError, ConnectionError]
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]

    # Define the function to execute with timeout
    async def execute_with_timeout():
        return await with_timeout(
            func,
            *args,
            timeout_seconds=timeout_seconds,
            operation_name=operation_name,
            **kwargs
        )

    # Apply retry logic
    if TENACITY_AVAILABLE:
        try:
            # Use tenacity for retry logic
            @retry(
                retry=retry_if_exception_type(tuple(retry_on)),
                stop=stop_after_attempt(max_attempts),
                wait=wait_exponential(
                    multiplier=DEFAULT_RETRY_CONFIG["multiplier"],
                    min=DEFAULT_RETRY_CONFIG["min_wait"],
                    max=DEFAULT_RETRY_CONFIG["max_wait"]
                ),
                before_sleep=before_sleep_log(logger, logging.INFO),
                reraise=True
            )
            async def execute_with_retry():
                return await execute_with_timeout()

            return await execute_with_retry()
        except RetryError as e:
            # Extract the last exception from the retry error
            if e.__cause__:
                raise e.__cause__
            raise
    else:
        # Manual retry implementation
        attempts = 0
        last_exception = None
        wait_time = DEFAULT_RETRY_CONFIG["min_wait"]

        while attempts < max_attempts:
            try:
                return await execute_with_timeout()
            except tuple(retry_on) as e:
                attempts += 1
                last_exception = e

                if attempts >= max_attempts:
                    break

                logger.info(
                    f"Retry attempt {attempts}/{max_attempts} for {operation_name} after error: {str(e)}. "
                    f"Waiting {wait_time:.2f}s..."
                )

                await asyncio.sleep(wait_time)
                wait_time = min(
                    DEFAULT_RETRY_CONFIG["max_wait"],
                    wait_time * DEFAULT_RETRY_CONFIG["multiplier"]
                )

        if last_exception:
            raise last_exception

        # This should never be reached if last_exception is set
        raise RuntimeError(f"Unexpected error in {operation_name} after {max_attempts} attempts")
