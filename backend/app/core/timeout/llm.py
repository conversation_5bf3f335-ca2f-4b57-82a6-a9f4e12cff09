"""
LLM-Specific Timeout Utilities

This module provides timeout utilities specifically for LLM operations.
It includes:
1. Timeout wrappers for LLM API calls
2. Retry logic for handling rate limits and transient errors
3. Specialized error handling for LLM operations

These utilities ensure that LLM operations don't hang indefinitely and provide
proper error handling and fallback mechanisms.
"""

import asyncio
import functools
import logging
import os
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

from app.core.errors.base import TimeoutError, RateLimitError
from .base import (
    with_timeout,
    with_retry,
    with_timeout_and_retry,
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

# LLM-specific timeout values (in seconds)
LLM_TIMEOUTS = {
    "chat": int(os.getenv("TIMEOUT_LLM_CHAT", "30")),
    "completion": int(os.getenv("TIMEOUT_LLM_COMPLETION", "20")),
    "embedding": int(os.getenv("TIMEOUT_LLM_EMBEDDING", "15")),
    "token_count": int(os.getenv("TIMEOUT_LLM_TOKEN_COUNT", "5")),
    "default": int(os.getenv("TIMEOUT_LLM_DEFAULT", "30")),
}


async def with_llm_timeout(
    func: Callable[..., Awaitable[T]],
    *args,
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    operation_name: Optional[str] = None,
    **kwargs
) -> T:
    """
    Execute an LLM function with timeout and retry logic.

    Args:
        func: LLM function to execute
        *args: Arguments for the function
        timeout_seconds: Timeout in seconds
        max_attempts: Maximum number of retry attempts
        operation_name: Name of the operation for error reporting
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function

    Raises:
        TimeoutError: If the function execution times out after all retries
    """
    operation_name = operation_name or func.__name__
    timeout_seconds = timeout_seconds or LLM_TIMEOUTS.get("default", DEFAULT_TIMEOUTS["llm"])
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]

    # Define retry exceptions specific to LLM operations
    retry_on = [TimeoutError, RateLimitError, ConnectionError]

    return await with_timeout_and_retry(
        func,
        *args,
        timeout_seconds=timeout_seconds,
        operation_name=operation_name,
        retry_on=retry_on,
        max_attempts=max_attempts,
        **kwargs
    )


def with_llm_timeout_decorator(
    operation_type: str = "default",
    max_attempts: Optional[int] = None
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for adding timeout and retry logic to LLM functions.

    Args:
        operation_type: Type of LLM operation (chat, completion, embedding, token_count)
        max_attempts: Maximum number of retry attempts

    Returns:
        Decorated function
    """
    timeout_seconds = LLM_TIMEOUTS.get(operation_type, LLM_TIMEOUTS["default"])

    def decorator(func: AsyncF) -> AsyncF:
        """Decorator function."""
        operation_name = func.__name__

        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            """Wrapper function."""
            return await with_llm_timeout(
                func,
                *args,
                timeout_seconds=timeout_seconds,
                max_attempts=max_attempts,
                operation_name=operation_name,
                **kwargs
            )

        return cast(AsyncF, wrapper)

    return decorator
