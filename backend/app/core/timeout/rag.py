"""
RAG-Specific Timeout Utilities

This module provides timeout utilities specifically for RAG components.
It includes:
1. Timeout wrappers for embedding generation
2. Timeout handling for vector search operations
3. Fallback mechanisms for timeout scenarios

These utilities ensure that RAG operations don't hang indefinitely and provide
proper error handling and fallback mechanisms.
"""

import asyncio
import functools
import logging
import os
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

from app.core.errors.base import TimeoutError, RateLimitError
from .base import (
    with_timeout,
    with_retry,
    with_timeout_and_retry,
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

# RAG-specific timeout values (in seconds)
RAG_TIMEOUTS = {
    "embedding": int(os.getenv("TIMEOUT_RAG_EMBEDDING", "15")),
    "vector_search": int(os.getenv("TIMEOUT_RAG_VECTOR_SEARCH", "10")),
    "keyword_search": int(os.getenv("TIMEOUT_RAG_KEYWORD_SEARCH", "5")),
    "hybrid_search": int(os.getenv("TIMEOUT_RAG_HYBRID_SEARCH", "12")),
    "rerank": int(os.getenv("TIMEOUT_RAG_RERANK", "8")),
    "default": int(os.getenv("TIMEOUT_RAG_DEFAULT", "10")),
}


async def with_embedding_timeout(
    func: Callable[..., Awaitable[T]],
    *args,
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    operation_name: Optional[str] = None,
    **kwargs
) -> T:
    """
    Execute an embedding generation function with timeout and retry logic.

    Args:
        func: Embedding function to execute
        *args: Arguments for the function
        timeout_seconds: Timeout in seconds
        max_attempts: Maximum number of retry attempts
        operation_name: Name of the operation for error reporting
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function

    Raises:
        TimeoutError: If the function execution times out after all retries
    """
    operation_name = operation_name or func.__name__
    timeout_seconds = timeout_seconds or RAG_TIMEOUTS["embedding"]
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]

    return await with_timeout_and_retry(
        func,
        *args,
        timeout_seconds=timeout_seconds,
        operation_name=operation_name,
        max_attempts=max_attempts,
        **kwargs
    )


async def with_vector_search_timeout(
    func: Callable[..., Awaitable[T]],
    *args,
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    operation_name: Optional[str] = None,
    fallback_func: Optional[Callable[..., Awaitable[T]]] = None,
    **kwargs
) -> T:
    """
    Execute a vector search function with timeout, retry, and fallback logic.

    Args:
        func: Vector search function to execute
        *args: Arguments for the function
        timeout_seconds: Timeout in seconds
        max_attempts: Maximum number of retry attempts
        operation_name: Name of the operation for error reporting
        fallback_func: Optional fallback function to call if all retries fail
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the function or fallback

    Raises:
        TimeoutError: If the function execution times out and no fallback is provided
    """
    operation_name = operation_name or func.__name__
    timeout_seconds = timeout_seconds or RAG_TIMEOUTS["vector_search"]
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]

    try:
        return await with_timeout_and_retry(
            func,
            *args,
            timeout_seconds=timeout_seconds,
            operation_name=operation_name,
            max_attempts=max_attempts,
            **kwargs
        )
    except (TimeoutError, Exception) as e:
        logger.warning(
            f"Vector search {operation_name} failed after {max_attempts} attempts: {str(e)}. "
            f"Using fallback if available."
        )

        if fallback_func:
            logger.info(f"Using fallback for {operation_name}")
            return await fallback_func(*args, **kwargs)

        # Re-raise the original exception if no fallback is provided
        raise


def with_rag_timeout(
    operation_type: str = "default",
    max_attempts: Optional[int] = None,
    fallback_func: Optional[Callable[..., Awaitable[Any]]] = None
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for adding timeout, retry, and fallback logic to RAG functions.

    Args:
        operation_type: Type of RAG operation (embedding, vector_search, etc.)
        max_attempts: Maximum number of retry attempts
        fallback_func: Optional fallback function to call if all retries fail

    Returns:
        Decorated function
    """
    timeout_seconds = RAG_TIMEOUTS.get(operation_type, RAG_TIMEOUTS["default"])

    def decorator(func: AsyncF) -> AsyncF:
        """Decorator function."""
        operation_name = func.__name__

        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            """Wrapper function."""
            if operation_type == "embedding":
                return await with_embedding_timeout(
                    func,
                    *args,
                    timeout_seconds=timeout_seconds,
                    max_attempts=max_attempts,
                    operation_name=operation_name,
                    **kwargs
                )
            elif operation_type in ["vector_search", "keyword_search", "hybrid_search"]:
                return await with_vector_search_timeout(
                    func,
                    *args,
                    timeout_seconds=timeout_seconds,
                    max_attempts=max_attempts,
                    operation_name=operation_name,
                    fallback_func=fallback_func,
                    **kwargs
                )
            else:
                # Default timeout and retry
                try:
                    return await with_timeout_and_retry(
                        func,
                        *args,
                        timeout_seconds=timeout_seconds,
                        operation_name=operation_name,
                        max_attempts=max_attempts,
                        **kwargs
                    )
                except Exception as e:
                    if fallback_func:
                        logger.info(f"Using fallback for {operation_name} after error: {str(e)}")
                        return await fallback_func(*args, **kwargs)
                    raise

        return cast(AsyncF, wrapper)

    return decorator


# Convenience decorators for specific RAG operations
def with_embedding_timeout_decorator(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[AsyncF], AsyncF]:
    """Decorator for embedding generation with timeout."""
    return with_rag_timeout(
        operation_type="embedding",
        max_attempts=max_attempts
    )


def with_vector_search_timeout_decorator(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    fallback_func: Optional[Callable[..., Awaitable[Any]]] = None
) -> Callable[[AsyncF], AsyncF]:
    """Decorator for vector search with timeout and fallback."""
    return with_rag_timeout(
        operation_type="vector_search",
        max_attempts=max_attempts,
        fallback_func=fallback_func
    )


def with_keyword_search_timeout_decorator(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    fallback_func: Optional[Callable[..., Awaitable[Any]]] = None
) -> Callable[[AsyncF], AsyncF]:
    """Decorator for keyword search with timeout and fallback."""
    return with_rag_timeout(
        operation_type="keyword_search",
        max_attempts=max_attempts,
        fallback_func=fallback_func
    )


def with_hybrid_search_timeout_decorator(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    fallback_func: Optional[Callable[..., Awaitable[Any]]] = None
) -> Callable[[AsyncF], AsyncF]:
    """Decorator for hybrid search with timeout and fallback."""
    return with_rag_timeout(
        operation_type="hybrid_search",
        max_attempts=max_attempts,
        fallback_func=fallback_func
    )
