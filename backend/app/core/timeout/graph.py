"""
LangGraph-Specific Timeout Utilities

This module provides timeout utilities specifically for LangGraph nodes.
It includes:
1. Node-specific timeout wrapper functions
2. Integration with state management for error reporting
3. Configurable timeout durations per node type

These utilities ensure that LangGraph nodes don't hang indefinitely and provide
proper error handling and reporting through the state object.
"""

import asyncio
import functools
import logging
import os
from typing import Any, Awaitable, Callable, Dict, List, Optional, Type, TypeVar, cast

from app.core.errors.base import TimeoutError, RateLimitError
from .base import (
    with_timeout_and_retry,
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
AsyncF = TypeVar('AsyncF', bound=Callable[..., Awaitable[Any]])

# Forward reference for AgentState to avoid circular imports
# This will be properly resolved when the module is imported
AgentState = Any
NodeFunc = Callable[[AgentState, ...], Awaitable[AgentState]]

# Node-specific timeout values (in seconds)
NODE_TIMEOUTS = {
    "analyze_query": int(os.getenv("TIMEOUT_NODE_ANALYZE_QUERY", "30")),
    "retrieve_knowledge": int(os.getenv("TIMEOUT_NODE_RETRIEVE_KNOWLEDGE", "20")),
    "route_to_departments": int(os.getenv("TIMEOUT_NODE_ROUTE_TO_DEPARTMENTS", "40")),
    "generate_response": int(os.getenv("TIMEOUT_NODE_GENERATE_RESPONSE", "30")),
    "co_ceo": int(os.getenv("TIMEOUT_NODE_CO_CEO", "45")),
    "finance_department": int(os.getenv("TIMEOUT_NODE_FINANCE", "40")),
    "marketing_department": int(os.getenv("TIMEOUT_NODE_MARKETING", "40")),
    "fallback": int(os.getenv("TIMEOUT_NODE_FALLBACK", "30")),
    "default": int(os.getenv("TIMEOUT_NODE_DEFAULT", "40")),
}


async def with_node_timeout_and_retry(
    func: Callable[[AgentState, ...], Awaitable[AgentState]],
    state: AgentState,
    *args,
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    node_name: Optional[str] = None,
    **kwargs
) -> AgentState:
    """
    Execute a LangGraph node function with timeout and retry logic.

    This function wraps a LangGraph node function with timeout and retry logic,
    and handles errors by updating the state object with error messages.

    Args:
        func: Node function to execute
        state: Current state object
        *args: Additional arguments for the node function
        timeout_seconds: Timeout in seconds
        max_attempts: Maximum number of retry attempts
        node_name: Name of the node for error reporting
        **kwargs: Additional keyword arguments for the node function

    Returns:
        Updated state object
    """
    node_name = node_name or func.__name__
    timeout_seconds = timeout_seconds or NODE_TIMEOUTS.get(
        node_name, NODE_TIMEOUTS["default"]
    )
    max_attempts = max_attempts or DEFAULT_RETRY_CONFIG["max_attempts"]

    try:
        # Use the base timeout and retry utility
        return await with_timeout_and_retry(
            func,
            state,
            *args,
            timeout_seconds=timeout_seconds,
            operation_name=node_name,
            max_attempts=max_attempts,
            **kwargs
        )
    except TimeoutError as e:
        logger.warning(
            f"Node {node_name} timed out after {timeout_seconds}s and {max_attempts} attempts. "
            f"Creating error state."
        )

        # Create a copy of the state with an error message
        updated_state = state.model_copy(deep=True)

        # Add error message to state
        updated_state.messages.append({
            "role": "system",
            "content": f"Operation {node_name} timed out after {timeout_seconds} seconds.",
            "metadata": {
                "error": "timeout",
                "node": node_name,
                "timeout_seconds": timeout_seconds
            }
        })

        # Set error in metadata
        if "errors" not in updated_state.metadata:
            updated_state.metadata["errors"] = []

        updated_state.metadata["errors"].append({
            "type": "timeout",
            "node": node_name,
            "message": str(e),
            "timeout_seconds": timeout_seconds
        })

        return updated_state
    except Exception as e:
        logger.error(
            f"Node {node_name} failed with error: {str(e)}",
            exc_info=True
        )

        # Create a copy of the state with an error message
        updated_state = state.model_copy(deep=True)

        # Add error message to state
        updated_state.messages.append({
            "role": "system",
            "content": f"Operation {node_name} failed with error: {str(e)}",
            "metadata": {
                "error": "exception",
                "node": node_name,
                "error_type": type(e).__name__
            }
        })

        # Set error in metadata
        if "errors" not in updated_state.metadata:
            updated_state.metadata["errors"] = []

        updated_state.metadata["errors"].append({
            "type": "exception",
            "node": node_name,
            "message": str(e),
            "error_type": type(e).__name__
        })

        return updated_state


def with_node_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None,
    node_name: Optional[str] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """
    Decorator for adding timeout and retry logic to LangGraph node functions.

    Args:
        timeout_seconds: Timeout in seconds
        max_attempts: Maximum number of retry attempts
        node_name: Name of the node for error reporting

    Returns:
        Decorated function
    """
    def decorator(func: NodeFunc) -> NodeFunc:
        """Decorator function."""
        func_name = node_name or func.__name__

        @functools.wraps(func)
        async def wrapper(state: AgentState, *args, **kwargs) -> AgentState:
            """Wrapper function."""
            return await with_node_timeout_and_retry(
                func,
                state,
                *args,
                timeout_seconds=timeout_seconds,
                max_attempts=max_attempts,
                node_name=func_name,
                **kwargs
            )

        return cast(NodeFunc, wrapper)

    return decorator


# Convenience functions for specific node types
def with_analyze_query_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for analyze_query node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["analyze_query"],
        max_attempts,
        "analyze_query"
    )


def with_retrieve_knowledge_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for retrieve_knowledge node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["retrieve_knowledge"],
        max_attempts,
        "retrieve_knowledge"
    )


def with_route_to_departments_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for route_to_departments node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["route_to_departments"],
        max_attempts,
        "route_to_departments"
    )


def with_generate_response_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for generate_response node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["generate_response"],
        max_attempts,
        "generate_response"
    )


def with_co_ceo_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for co_ceo node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["co_ceo"],
        max_attempts,
        "co_ceo"
    )


def with_finance_department_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for finance_department node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["finance_department"],
        max_attempts,
        "finance_department"
    )


def with_marketing_department_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for marketing_department node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["marketing_department"],
        max_attempts,
        "marketing_department"
    )


def with_fallback_timeout(
    timeout_seconds: Optional[float] = None,
    max_attempts: Optional[int] = None
) -> Callable[[NodeFunc], NodeFunc]:
    """Decorator for fallback node with timeout."""
    return with_node_timeout(
        timeout_seconds or NODE_TIMEOUTS["fallback"],
        max_attempts,
        "fallback"
    )
