"""
Core Package

This package contains core functionality used throughout the application.
"""

from .llm_model_adapter import (
    LLMAdapter, OpenAIAdapter, AnthropicAdapter, GeminiAdapter, MockAdapter,
    get_llm_adapter, get_llm_adapter_factory, register_adapter, ADAPTER_REGISTRY
)
from .tools import <PERSON>Tool, TOOL_REGISTRY, get_tool, get_tools_by_department, register_tool, list_all_tools

# Import error handling utilities
from .errors import (
    # Base error classes
    ErrorSeverity, ErrorCategory, AppError, ValidationError, AuthenticationError,
    AuthorizationError, ResourceNotFoundError, ExternalServiceError, DatabaseError,
    TimeoutError, RateLimitError,

    # Base error utilities
    handle_errors, handle_async_errors, convert_exception_to_app_error,
    format_error_for_response, log_error, create_error_response, ErrorContext,

    # Database error handling
    handle_db_errors, handle_async_db_errors, DBErrorContext,

    # LLM error handling
    LLMError, LLMTimeoutError, LLMRateLimitError,
    handle_llm_errors, handle_async_llm_errors, LLMErrorContext,

    # API error handling
    APIError, handle_api_errors, create_api_error_response
)

# Import timeout utilities
from .timeout_utils import (
    with_timeout, with_retry, with_timeout_and_retry,
    CircuitBreaker, DEFAULT_TIMEOUTS, DEFAULT_RETRY_CONFIG
)

__all__ = [
    # Firebase
    "FirebaseClient",

    # LLM adapters
    "LLMAdapter",
    "OpenAIAdapter",
    "AnthropicAdapter",
    "GeminiAdapter",
    "MockAdapter",
    "get_llm_adapter",
    "get_llm_adapter_factory",
    "register_adapter",
    "ADAPTER_REGISTRY",

    # Tools
    "BaseTool",
    "TOOL_REGISTRY",
    "get_tool",
    "get_tools_by_department",
    "register_tool",
    "list_all_tools",

    # Error handling
    "AppError",
    "ValidationError",
    "AuthenticationError",
    "AuthorizationError",
    "ResourceNotFoundError",
    "ExternalServiceError",
    "DatabaseError",
    "TimeoutError",
    "RateLimitError",
    "ErrorCategory",
    "ErrorSeverity",
    "handle_errors",
    "handle_async_errors",
    "convert_exception_to_app_error",
    "format_error_for_response",
    "log_error",
    "create_error_response",
    "ErrorContext",

    # Database error handling
    "handle_db_errors",
    "handle_async_db_errors",
    "DBErrorContext",

    # LLM error handling
    "LLMError",
    "LLMTimeoutError",
    "LLMRateLimitError",
    "handle_llm_errors",
    "handle_async_llm_errors",
    "LLMErrorContext",

    # API error handling
    "APIError",
    "handle_api_errors",
    "create_api_error_response",

    # Timeout utilities
    "with_timeout",
    "with_retry",
    "with_timeout_and_retry",
    "CircuitBreaker",
    "DEFAULT_TIMEOUTS",
    "DEFAULT_RETRY_CONFIG",
]
