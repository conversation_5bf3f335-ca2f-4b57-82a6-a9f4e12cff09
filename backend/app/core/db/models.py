"""
Database Models

This module defines the SQLAlchemy ORM models for the application.
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy import Column, <PERSON>, <PERSON>olean, DateTime, ForeignKey, Text, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.db.database import Base
from app.core.db.pgvector import Vector
from app.config import get_settings
# Import password functions directly to avoid circular imports
from passlib.context import CryptContext
from app.config import get_settings

# Create password context for hashing and verification
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=get_settings().PASSWORD_BCRYPT_ROUNDS
)

def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash."""
    return pwd_context.verify(plain_password, hashed_password)


class User(Base):
    """User model for authentication and authorization."""

    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    email_verified = Column(Boolean, default=False)
    roles = Column(ARRAY(String), default=[])
    permissions = Column(ARRAY(String), default=[])
    profile_data = Column(JSON, default={})
    last_login = Column(DateTime(timezone=True), nullable=True)
    account_locked = Column(Boolean, default=False)
    lock_reason = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    refresh_tokens = relationship("RefreshToken", back_populates="user", cascade="all, delete-orphan")
    query_logs = relationship("QueryLog", back_populates="user")

    def __init__(self, email: str, password: Optional[str] = None, hashed_password: Optional[str] = None, **kwargs):
        """
        Initialize a new user.

        Args:
            email: User's email address
            password: Plain text password (will be hashed)
            hashed_password: Pre-hashed password
            **kwargs: Additional user attributes
        """
        self.email = email

        # Set password (either hash the provided password or use the provided hash)
        if password:
            self.hashed_password = hash_password(password)
        elif hashed_password:
            self.hashed_password = hashed_password
        else:
            raise ValueError("Either password or hashed_password must be provided")

        # Set additional attributes
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def verify_password(self, password: str) -> bool:
        """
        Verify a password against the stored hash.

        Args:
            password: Plain text password to verify

        Returns:
            True if the password matches, False otherwise
        """
        return verify_password(password, self.hashed_password)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the user to a dictionary.

        Returns:
            Dictionary representation of the user
        """
        return {
            "id": str(self.id),
            "email": self.email,
            "is_active": self.is_active,
            "email_verified": self.email_verified,
            "roles": self.roles,
            "permissions": self.permissions,
            "profile_data": self.profile_data,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class RefreshToken(Base):
    """Refresh token model for JWT authentication."""

    __tablename__ = "refresh_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    token = Column(String(255), unique=True, nullable=False, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_revoked = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="refresh_tokens")


class Document(Base):
    """Document model for storing text documents with vector embeddings."""

    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    meta_info = Column(JSON, default={})  # Renamed from metadata to avoid SQLAlchemy reserved keyword
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")


class DocumentChunk(Base):
    """Document chunk model for storing text chunks with vector embeddings."""

    __tablename__ = "document_chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    meta_info = Column(JSON, default={})  # Renamed from metadata to avoid SQLAlchemy reserved keyword
    # Use the Vector type from pgvector with the correct dimension
    embedding = Column(Vector(get_settings().VECTOR_DIMENSION))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    document = relationship("Document", back_populates="chunks")

    # Indexes
    __table_args__ = (
        # Composite index for document_id and chunk_index
        # This will be created in a migration
    )


class ConversationCheckpoint(Base):
    """Conversation checkpoint model for storing LangGraph conversation state."""

    __tablename__ = "conversation_checkpoints"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(String(255), unique=True, nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    state = Column(JSON, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
