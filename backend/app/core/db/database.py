"""
Database Connection

This module provides the SQLAlchemy database connection and session management.
It supports both PostgreSQL with pgvector extension and SQLite for development.
"""

from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import logging
import os

from app.config import get_settings
from app.core.db.pgvector import setup_pgvector

logger = logging.getLogger(__name__)

# Get database URL from settings
DATABASE_URL = get_settings().DATABASE_URL

# Check if we're using SQLite (for development/testing)
if DATABASE_URL.startswith("sqlite"):
    # SQLite connection
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=get_settings().DEBUG,  # Echo SQL in debug mode
    )
    logger.info("Using SQLite database for development/testing")
else:
    # PostgreSQL connection with connection pooling
    engine = create_engine(
        DATABASE_URL,
        pool_size=get_settings().DATABASE_POOL_SIZE,
        max_overflow=get_settings().DATABASE_MAX_OVERFLOW,
        pool_timeout=get_settings().DATABASE_POOL_TIMEOUT,
        pool_recycle=get_settings().DATABASE_POOL_RECYCLE,
        echo=get_settings().DEBUG,  # Echo SQL in debug mode
    )
    logger.info(f"Using PostgreSQL database: {DATABASE_URL}")

    # Set up pgvector with the engine
    try:
        setup_pgvector(engine)
        logger.info("Set up pgvector with SQLAlchemy engine")

        # Verify pgvector extension is available
        with engine.connect() as conn:
            result = conn.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
            if result.fetchone():
                logger.info("pgvector extension is installed and available")
            else:
                logger.warning("pgvector extension is not installed in the database")
                logger.warning("Vector operations will not work correctly")
                logger.warning("Please run 'CREATE EXTENSION vector;' in your PostgreSQL database")
    except Exception as e:
        logger.error(f"Error setting up pgvector: {e}")
        logger.warning("Vector operations may not work correctly")
        logger.warning("Please ensure pgvector extension is installed in PostgreSQL")

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()


def get_db():
    """
    Dependency for FastAPI to get a database session.

    This function creates a new database session for each request
    and closes it when the request is finished.

    Yields:
        SQLAlchemy session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@contextmanager
def get_db_context():
    """
    Context manager for getting a database session.

    This function creates a new database session and closes it
    when the context is exited.

    Yields:
        SQLAlchemy session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """
    Initialize the database by creating all tables.

    This function should be called when the application starts.
    """
    try:
        # First, try to initialize pgvector extension
        try:
            with get_db_context() as db:
                db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
                db.commit()
                logger.info("pgvector extension initialized successfully")

                # Verify pgvector extension is available
                result = db.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
                if result.fetchone():
                    logger.info("Verified pgvector extension is installed and available")
                else:
                    logger.warning("pgvector extension appears to be missing despite initialization attempt")
                    logger.warning("Vector operations may not work correctly")
        except Exception as e:
            logger.error(f"Error initializing pgvector extension: {e}")
            logger.warning("Vector operations may not work correctly")
            logger.warning("Please ensure pgvector is installed in PostgreSQL")
            logger.warning("You may need to run: CREATE EXTENSION vector;")

        # Create tables (proceed even if pgvector extension failed)
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")

    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise
