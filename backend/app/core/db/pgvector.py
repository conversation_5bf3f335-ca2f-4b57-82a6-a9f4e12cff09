"""
PostgreSQL pgvector Integration for SQLAlchemy

This module provides integration between SQLAlchemy and the pgvector extension for PostgreSQL.
It registers the vector type with SQLAlchemy and provides utilities for working with vector data.
"""

import logging
from sqlalchemy import event
from sqlalchemy.types import UserDefinedType

logger = logging.getLogger(__name__)


class Vector(UserDefinedType):
    """
    PostgreSQL vector type for SQLAlchemy.

    This class represents the vector type from the pgvector extension in SQLAlchemy.
    It can be used to define vector columns in SQLAlchemy models.

    Example:
        ```python
        from sqlalchemy import Column, Integer
        from sqlalchemy.ext.declarative import declarative_base
        from app.core.db.pgvector import Vector

        Base = declarative_base()

        class Embedding(Base):
            __tablename__ = "embeddings"
            id = Column(Integer, primary_key=True)
            embedding = Column(Vector(768))
        ```
    """

    def __init__(self, dimensions):
        """
        Initialize the vector type.

        Args:
            dimensions: Number of dimensions for the vector
        """
        self.dimensions = dimensions

    def get_col_spec(self, **_):
        """
        Get the column specification for the vector type.

        Returns:
            Column specification string
        """
        return f"vector({self.dimensions})"

    def bind_processor(self, _):
        """
        Process the value before binding to SQL.

        Args:
            _: SQLAlchemy dialect (unused)

        Returns:
            Binding processor function
        """
        def process(value):
            if value is None:
                return None
            if isinstance(value, list):
                return f"[{','.join(str(x) for x in value)}]"
            return value
        return process

    def result_processor(self, _, __):
        """
        Process the value after retrieving from SQL.

        Args:
            _: SQLAlchemy dialect (unused)
            __: Column type (unused)

        Returns:
            Result processor function
        """
        def process(value):
            if value is None:
                return None
            return list(value)
        return process


def register_vector_psycopg2(dbapi_connection, _):
    """
    Register the vector type with psycopg2.

    This function should be registered as an event listener for the 'connect' event
    of the SQLAlchemy engine.

    Args:
        dbapi_connection: DBAPI connection
        _: Connection record (unused)
    """
    try:
        from pgvector.psycopg2 import register_vector
        register_vector(dbapi_connection, arrays=True)
        logger.info("Registered pgvector with psycopg2")
    except ImportError:
        logger.warning("pgvector.psycopg2 not available, vector operations may not work correctly")
    except Exception as e:
        logger.error(f"Error registering pgvector with psycopg2: {e}")


def register_vector_psycopg3_async(dbapi_connection, _):
    """
    Register the vector type with psycopg3 for async connections.

    This function should be registered as an event listener for the 'connect' event
    of the SQLAlchemy engine.

    Args:
        dbapi_connection: DBAPI connection
        _: Connection record (unused)
    """
    try:
        from pgvector.psycopg import register_vector_async
        dbapi_connection.run_async(register_vector_async)
        logger.info("Registered pgvector with psycopg3 async")
    except ImportError:
        logger.warning("pgvector.psycopg not available, vector operations may not work correctly")
    except Exception as e:
        logger.error(f"Error registering pgvector with psycopg3 async: {e}")


def setup_pgvector(engine):
    """
    Set up pgvector with the SQLAlchemy engine.

    This function registers the vector type with the SQLAlchemy engine
    and ensures the pgvector extension is created in the database.

    Args:
        engine: SQLAlchemy engine
    """
    # Register the vector type with psycopg2
    event.listen(engine, 'connect', register_vector_psycopg2)

    # Try to register with psycopg3 as well if available
    try:
        # Import and use the same function for async connections
        event.listen(engine, 'connect', register_vector_psycopg3_async)
        logger.info("Added event listener for psycopg3 async connections")
    except ImportError:
        logger.debug("pgvector.psycopg not available, skipping psycopg3 registration")

    # Create the pgvector extension if it doesn't exist
    from sqlalchemy import text
    with engine.connect() as conn:
        try:
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            conn.commit()
            logger.info("Created pgvector extension")

            # Verify the extension was created
            result = conn.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
            if result.fetchone():
                logger.info("Verified pgvector extension is available")
            else:
                logger.warning("pgvector extension appears to be missing despite creation attempt")
                logger.warning("This may indicate a permission issue or that pgvector is not installed in PostgreSQL")
        except Exception as e:
            logger.error(f"Error creating pgvector extension: {e}")
            logger.warning("This may indicate that pgvector is not installed in PostgreSQL")
            logger.warning("Please ensure the pgvector extension is installed on your PostgreSQL server")
            logger.warning("You may need to run: sudo apt-get install postgresql-15-pgvector (or equivalent)")
            raise
