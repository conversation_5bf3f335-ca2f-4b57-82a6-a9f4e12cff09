"""
Metrics Package

This package contains components for collecting, analyzing, and reporting metrics
for the RAG and multi-agent orchestration system.

The metrics system is designed to:
1. Track performance metrics for retrieval, routing, and generation
2. Provide insights into system behavior and quality
3. Support benchmarking and evaluation
4. Enable visualization of system performance
"""

from .collector import MetricsCollector
from .utils import calculate_precision_recall, format_metrics_table

__all__ = [
    "MetricsCollector",
    "calculate_precision_recall",
    "format_metrics_table"
]
