"""
Metrics Collector

This module provides the MetricsCollector class for collecting, analyzing, and reporting
metrics for the RAG and multi-agent orchestration system.
"""

import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import os
import logging

from .utils import calculate_precision_recall, format_metrics_table

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    Lightweight metrics collection for RAG and multi-agent orchestration.
    
    This class collects metrics during system execution and provides
    reporting capabilities for CLI output.
    """
    
    def __init__(self, session_id: Optional[str] = None):
        """
        Initialize the metrics collector.
        
        Args:
            session_id: Optional session ID for tracking
        """
        self.session_id = session_id or str(uuid.uuid4())
        self.start_time = time.time()
        self.metrics = {
            "retrieval": {},
            "routing": {},
            "generation": {},
            "overall": {
                "start_time": datetime.now().isoformat(),
                "session_id": self.session_id
            }
        }
        self.events = []  # For tracking execution flow
        logger.debug(f"Initialized metrics collector with session ID: {self.session_id}")
    
    def track_retrieval(
        self,
        query: str,
        results: List[Dict[str, Any]],
        strategy: Optional[Dict[str, Any]] = None,
        timing: Optional[float] = None,
        **kwargs
    ) -> None:
        """
        Track retrieval metrics.
        
        Args:
            query: The query text
            results: The retrieved documents
            strategy: The retrieval strategy used
            timing: Time taken for retrieval
            **kwargs: Additional metrics
        """
        # Basic metrics
        doc_count = len(results)
        avg_relevance = sum(doc.get("score", 0) for doc in results) / doc_count if doc_count > 0 else 0
        
        # Store metrics
        self.metrics["retrieval"].update({
            "query": query,
            "doc_count": doc_count,
            "avg_relevance": avg_relevance,
            "timing": timing or time.time() - self.start_time,
            "strategy": strategy or {}
        })
        
        # Add any additional metrics
        self.metrics["retrieval"].update(kwargs)
        
        # Add event
        self.add_event(
            component="retriever",
            action="retrieve",
            details={
                "doc_count": doc_count,
                "avg_relevance": avg_relevance,
                "timing": timing
            }
        )
        
        logger.debug(f"Tracked retrieval metrics: {doc_count} documents, avg relevance: {avg_relevance:.4f}")
    
    def track_routing(
        self,
        state: Any,
        departments: List[str],
        confidence_scores: Dict[str, float],
        expected_departments: Optional[List[str]] = None,
        **kwargs
    ) -> None:
        """
        Track routing metrics.
        
        Args:
            state: The current state
            departments: The departments routed to
            confidence_scores: Confidence scores for departments
            expected_departments: Expected departments (for evaluation)
            **kwargs: Additional metrics
        """
        # Store basic routing metrics
        self.metrics["routing"].update({
            "departments": departments,
            "confidence_scores": confidence_scores,
            "department_count": len(departments)
        })
        
        # Calculate accuracy metrics if expected departments are provided
        if expected_departments is not None:
            precision, recall, f1 = calculate_precision_recall(departments, expected_departments)
            self.metrics["routing"].update({
                "expected_departments": expected_departments,
                "precision": precision,
                "recall": recall,
                "f1": f1
            })
        
        # Add any additional metrics
        self.metrics["routing"].update(kwargs)
        
        # Add event
        self.add_event(
            component="router",
            action="route",
            details={
                "departments": departments,
                "confidence_scores": confidence_scores
            }
        )
        
        logger.debug(f"Tracked routing metrics: departments={departments}")
    
    def track_generation(
        self,
        query: str,
        response: str,
        context_docs: List[Dict[str, Any]],
        timing: Optional[float] = None,
        **kwargs
    ) -> None:
        """
        Track generation metrics.
        
        Args:
            query: The query text
            response: The generated response
            context_docs: The context documents used
            timing: Time taken for generation
            **kwargs: Additional metrics
        """
        # Basic metrics
        response_length = len(response)
        context_length = sum(len(doc.get("text", "")) for doc in context_docs)
        
        # Store metrics
        self.metrics["generation"].update({
            "response_length": response_length,
            "context_length": context_length,
            "timing": timing or time.time() - self.start_time,
            "context_doc_count": len(context_docs)
        })
        
        # Add any additional metrics
        self.metrics["generation"].update(kwargs)
        
        # Add event
        self.add_event(
            component="generator",
            action="generate",
            details={
                "response_length": response_length,
                "timing": timing
            }
        )
        
        logger.debug(f"Tracked generation metrics: response_length={response_length}")
    
    def add_event(
        self,
        component: str,
        action: str,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add an event to the execution flow.
        
        Args:
            component: The component that generated the event
            action: The action performed
            details: Additional details about the event
        """
        event = {
            "timestamp": datetime.now().isoformat(),
            "component": component,
            "action": action,
            "details": details or {}
        }
        self.events.append(event)
        logger.debug(f"Added event: {component}.{action}")
    
    def finalize(self) -> None:
        """
        Finalize metrics collection.
        
        This method calculates overall metrics and should be called
        at the end of the session.
        """
        end_time = time.time()
        total_time = end_time - self.start_time
        
        # Update overall metrics
        self.metrics["overall"].update({
            "end_time": datetime.now().isoformat(),
            "total_time": total_time,
            "event_count": len(self.events)
        })
        
        logger.info(f"Finalized metrics collection: total_time={total_time:.4f}s, events={len(self.events)}")
    
    def get_report(self, format: str = "text") -> Union[str, Dict[str, Any]]:
        """
        Get a report of the collected metrics.
        
        Args:
            format: The format of the report ("text", "json", "dict")
            
        Returns:
            The metrics report in the specified format
        """
        # Finalize metrics if not already done
        if "total_time" not in self.metrics["overall"]:
            self.finalize()
        
        if format == "text":
            # Create a formatted text report
            table = format_metrics_table(self.metrics, title=f"Metrics Report (Session: {self.session_id})")
            return str(table)
        elif format == "json":
            # Return JSON string
            return json.dumps({
                "metrics": self.metrics,
                "events": self.events
            }, indent=2)
        elif format == "dict":
            # Return Python dictionary
            return {
                "metrics": self.metrics,
                "events": self.events
            }
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def save(self, path: Optional[str] = None) -> str:
        """
        Save metrics to a file.
        
        Args:
            path: The path to save the metrics to (directory or file)
            
        Returns:
            The path to the saved file
        """
        # Finalize metrics if not already done
        if "total_time" not in self.metrics["overall"]:
            self.finalize()
        
        # Determine file path
        if path is None:
            # Use default path
            metrics_dir = os.path.join("backend", "data", "metrics")
            os.makedirs(metrics_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"metrics_{self.session_id}_{timestamp}.json"
            path = os.path.join(metrics_dir, filename)
        elif os.path.isdir(path):
            # Path is a directory, create filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"metrics_{self.session_id}_{timestamp}.json"
            path = os.path.join(path, filename)
        
        # Save metrics to file
        with open(path, "w") as f:
            json.dump({
                "metrics": self.metrics,
                "events": self.events
            }, f, indent=2)
        
        logger.info(f"Saved metrics to {path}")
        return path
