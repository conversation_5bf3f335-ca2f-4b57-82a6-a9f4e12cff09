"""
Metrics Utilities

This module provides utility functions for calculating and formatting metrics.
"""

import json
from typing import Dict, List, Any, Optional, Tuple, Union
from rich.table import Table


def calculate_precision_recall(
    actual: List[str],
    expected: List[str]
) -> Tuple[float, float, float]:
    """
    Calculate precision, recall, and F1 score.

    Args:
        actual: List of actual items (e.g., departments)
        expected: List of expected items

    Returns:
        Tuple of (precision, recall, f1_score)
    """
    if not actual and not expected:
        return 1.0, 1.0, 1.0  # Perfect match if both are empty
    
    if not actual:
        return 0.0, 0.0, 0.0  # No actual items
    
    if not expected:
        return 0.0, 1.0, 0.0  # No expected items, but recall is perfect
    
    # Calculate true positives (items in both lists)
    true_positives = len(set(actual) & set(expected))
    
    # Calculate precision (true positives / actual)
    precision = true_positives / len(actual) if actual else 0.0
    
    # Calculate recall (true positives / expected)
    recall = true_positives / len(expected) if expected else 1.0
    
    # Calculate F1 score (harmonic mean of precision and recall)
    f1_score = (
        2 * precision * recall / (precision + recall)
        if precision + recall > 0 else 0.0
    )
    
    return precision, recall, f1_score


def format_metrics_table(metrics: Dict[str, Any], title: str = "Metrics") -> Table:
    """
    Format metrics as a Rich table.

    Args:
        metrics: Dictionary of metrics
        title: Table title

    Returns:
        Rich Table object
    """
    table = Table(title=title)
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    # Add metrics to table
    for category, category_metrics in metrics.items():
        if isinstance(category_metrics, dict):
            # Add category header
            table.add_row(f"[bold]{category.upper()}[/bold]", "")
            
            # Add category metrics
            for metric_name, metric_value in category_metrics.items():
                formatted_value = _format_metric_value(metric_value)
                table.add_row(f"  {metric_name.replace('_', ' ').title()}", formatted_value)
        else:
            # Add simple metric
            formatted_value = _format_metric_value(category_metrics)
            table.add_row(category.replace('_', ' ').title(), formatted_value)
    
    return table


def _format_metric_value(value: Any) -> str:
    """
    Format a metric value for display.

    Args:
        value: The metric value

    Returns:
        Formatted string
    """
    if isinstance(value, float):
        return f"{value:.4f}"
    elif isinstance(value, list):
        if all(isinstance(x, str) for x in value):
            return ", ".join(value)
        else:
            return str(value)
    elif isinstance(value, dict):
        return json.dumps(value, indent=2)
    else:
        return str(value)
