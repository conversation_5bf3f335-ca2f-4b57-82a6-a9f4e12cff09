"""
Error Handling Utilities (Compatibility Module)

This module provides backward compatibility with the old error handling utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.errors instead.
"""

import warnings
import logging
from typing import TypeVar, Callable, Any

# Show deprecation warning
warnings.warn(
    "The app.core.error_handling module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.errors instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all symbols from the new modular structure
from app.core.errors.base import (
    ErrorSeverity,
    ErrorCategory,
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ExternalServiceError,
    DatabaseError,
    TimeoutError,
    RateLimitError,
    handle_errors,
    handle_async_errors,
    convert_exception_to_app_error,
    format_error_for_response,
    log_error,
    create_error_response,
    ErrorContext
)

# Set up logging
logger = logging.getLogger(__name__)

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')
R = TypeVar('R')

__all__ = [
    "ErrorSeverity",
    "ErrorCategory",
    "AppError",
    "ValidationError",
    "AuthenticationError",
    "AuthorizationError",
    "ResourceNotFoundError",
    "ExternalServiceError",
    "DatabaseError",
    "TimeoutError",
    "RateLimitError",
    "handle_errors",
    "handle_async_errors",
    "convert_exception_to_app_error",
    "format_error_for_response",
    "log_error",
    "create_error_response",
    "ErrorContext"
]
