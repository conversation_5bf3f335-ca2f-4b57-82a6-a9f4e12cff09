"""
Error Handling Package

This package provides a unified error handling system for the application.
It includes:
1. Base error classes and utilities
2. Component-specific error handling
3. Error context managers and decorators

These utilities ensure consistent error handling across the codebase.
"""

# Import base error classes and utilities
from .base import (
    ErrorSeverity,
    ErrorCategory,
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ExternalServiceError,
    DatabaseError,
    TimeoutError,
    RateLimitError,
    handle_errors,
    handle_async_errors,
    convert_exception_to_app_error,
    format_error_for_response,
    log_error,
    create_error_response,
    ErrorContext
)

# Import database error handling utilities
from .db import (
    handle_db_errors,
    handle_async_db_errors,
    DBErrorContext
)

# Import LLM error handling utilities
from .llm import (
    LLMError,
    LLMTimeoutError,
    LLMRateLimitError,
    handle_llm_errors,
    handle_async_llm_errors,
    LLMErrorContext
)

# Import API error handling utilities
from .api import (
    APIError,
    handle_api_errors,
    create_api_error_response
)

__all__ = [
    # Base error classes
    "ErrorSeverity",
    "ErrorCategory",
    "AppError",
    "ValidationError",
    "AuthenticationError",
    "AuthorizationError",
    "ResourceNotFoundError",
    "ExternalServiceError",
    "DatabaseError",
    "TimeoutError",
    "RateLimitError",
    
    # Base error utilities
    "handle_errors",
    "handle_async_errors",
    "convert_exception_to_app_error",
    "format_error_for_response",
    "log_error",
    "create_error_response",
    "ErrorContext",
    
    # Database error handling
    "handle_db_errors",
    "handle_async_db_errors",
    "DBErrorContext",
    
    # LLM error handling
    "LLMError",
    "LLMTimeoutError",
    "LLMRateLimitError",
    "handle_llm_errors",
    "handle_async_llm_errors",
    "LLMErrorContext",
    
    # API error handling
    "APIError",
    "handle_api_errors",
    "create_api_error_response"
]
