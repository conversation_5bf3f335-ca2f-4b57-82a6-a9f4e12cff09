"""
API Error Handling Utilities

This module provides error handling utilities specifically for API operations.
It builds on the core error handling utilities in app.core.errors.base.
"""
from typing import Dict, Any, Callable, TypeVar, cast, Optional, List, Union
import logging
import functools
import traceback
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse

from app.core.errors.base import (
    AppError, ValidationError, AuthenticationError, AuthorizationError,
    ResourceNotFoundError, ExternalServiceError, DatabaseError,
    TimeoutError, RateLimitError, ErrorCategory, ErrorSeverity,
    create_error_response
)

logger = logging.getLogger(__name__)

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])


class APIError(AppError):
    """Error raised when an API operation fails."""

    def __init__(
        self,
        message: str = "API operation failed",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an API error.

        Args:
            message: Human-readable error message
            status_code: HTTP status code
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        super().__init__(
            message=message,
            category=ErrorCategory.INTERNAL,
            severity=ErrorSeverity.ERROR,
            details=details,
            cause=cause,
            status_code=status_code
        )


def handle_api_errors(
    app_error_to_status_code: Optional[Dict[type, int]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling errors in FastAPI route handlers.

    Args:
        app_error_to_status_code: Mapping of AppError types to HTTP status codes

    Returns:
        Decorated function
    """
    app_error_to_status_code = app_error_to_status_code or {
        ValidationError: status.HTTP_400_BAD_REQUEST,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        AuthorizationError: status.HTTP_403_FORBIDDEN,
        ResourceNotFoundError: status.HTTP_404_NOT_FOUND,
        TimeoutError: status.HTTP_504_GATEWAY_TIMEOUT,
        RateLimitError: status.HTTP_429_TOO_MANY_REQUESTS,
        ExternalServiceError: status.HTTP_502_BAD_GATEWAY,
        DatabaseError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        APIError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        AppError: status.HTTP_500_INTERNAL_SERVER_ERROR
    }

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Get function details for better error reporting
                module = func.__module__
                function_name = func.__qualname__

                # Get the traceback
                tb = traceback.format_exc()

                # Create error context
                error_context = {
                    "function": f"{module}.{function_name}",
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": tb
                }

                # Convert to AppError if needed
                if not isinstance(e, AppError):
                    # Try to map common exceptions to appropriate AppError types
                    if isinstance(e, ValueError):
                        app_error = ValidationError(
                            message=str(e),
                            cause=e
                        )
                    elif isinstance(e, KeyError):
                        app_error = ResourceNotFoundError(
                            message=f"Resource not found: {str(e)}",
                            cause=e
                        )
                    elif isinstance(e, PermissionError):
                        app_error = AuthorizationError(
                            message=str(e),
                            cause=e
                        )
                    else:
                        # Default to a generic APIError
                        app_error = APIError(
                            message=f"API error: {str(e)}",
                            cause=e
                        )
                else:
                    app_error = e

                # Determine the status code
                status_code = None
                for error_type, code in app_error_to_status_code.items():
                    if isinstance(app_error, error_type):
                        status_code = code
                        break

                # Use the status code from the AppError if available and not overridden by app_error_to_status_code
                if app_error.status_code and status_code is None:
                    status_code = app_error.status_code

                # Default to 500 if no status code is found
                if status_code is None:
                    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

                # Log the error
                log_level = logging.ERROR
                if status_code < 500:
                    log_level = logging.WARNING

                logger.log(
                    log_level,
                    f"API error in {module}.{function_name}: {app_error.message}",
                    exc_info=True,
                    extra={"error_context": error_context, "app_error": app_error.to_dict()}
                )

                # Create the error response
                error_response = create_api_error_response(app_error)

                # Return the error response
                return JSONResponse(
                    status_code=status_code,
                    content=error_response
                )

        return cast(F, wrapper)

    return decorator


def create_api_error_response(
    error: Union[AppError, Exception],
    include_details: bool = True
) -> Dict[str, Any]:
    """
    Create a standardized error response for APIs.

    Args:
        error: The error to include in the response
        include_details: Whether to include detailed error information

    Returns:
        Error response dictionary
    """
    # Use the base create_error_response function
    response = create_error_response(error, include_details)

    # Add API-specific fields
    response["success"] = False

    return response
