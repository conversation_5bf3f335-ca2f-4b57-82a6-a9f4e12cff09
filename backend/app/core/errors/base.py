"""
Base Error Handling Utilities

This module provides centralized error handling utilities for the application.
It includes custom exception types, error handling decorators, and utility
functions for consistent error handling across the codebase.
"""
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, cast
import logging
import traceback
import functools
import inspect
import json
import asyncio
import builtins
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')
R = TypeVar('R')


class ErrorSeverity(str, Enum):
    """Error severity levels for categorizing errors."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Categories of errors for better organization and filtering."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    RESOURCE_NOT_FOUND = "resource_not_found"
    EXTERNAL_SERVICE = "external_service"
    DATABASE = "database"
    INTERNAL = "internal"
    INPUT = "input"
    OUTPUT = "output"
    TIMEOUT = "timeout"
    RATE_LIMIT = "rate_limit"
    UNKNOWN = "unknown"


class AppError(Exception):
    """
    Base exception class for application-specific errors.

    This class provides a consistent structure for all application errors,
    including metadata for better error handling and reporting.
    """

    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        status_code: Optional[int] = None
    ):
        """
        Initialize an application error.

        Args:
            message: Human-readable error message
            category: Category of the error
            severity: Severity level of the error
            details: Additional details about the error
            cause: Original exception that caused this error
            status_code: HTTP status code (for API errors)
        """
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.cause = cause
        self.status_code = status_code
        self.timestamp = datetime.now().isoformat()

        # Include the original exception message if available
        if cause:
            self.details["cause"] = str(cause)

        # Call the parent constructor
        super().__init__(message)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the error to a dictionary representation.

        Returns:
            Dictionary representation of the error
        """
        return {
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "details": self.details,
            "timestamp": self.timestamp,
            "status_code": self.status_code
        }

    def __str__(self) -> str:
        """
        Get a string representation of the error.

        Returns:
            String representation
        """
        return f"{self.category.value.upper()} ({self.severity.value}): {self.message}"


# Specific error types for common scenarios

class ValidationError(AppError):
    """Error raised when input validation fails."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize a validation error.

        Args:
            message: Human-readable error message
            field: Name of the field that failed validation
            value: Invalid value that was provided
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)

        super().__init__(
            message=message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=400
        )


class AuthenticationError(AppError):
    """Error raised when authentication fails."""

    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an authentication error.

        Args:
            message: Human-readable error message
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        super().__init__(
            message=message,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=401
        )


class AuthorizationError(AppError):
    """Error raised when authorization fails."""

    def __init__(
        self,
        message: str = "Not authorized to perform this action",
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an authorization error.

        Args:
            message: Human-readable error message
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        super().__init__(
            message=message,
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=403
        )


class ResourceNotFoundError(AppError):
    """Error raised when a requested resource is not found."""

    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize a resource not found error.

        Args:
            message: Human-readable error message
            resource_type: Type of resource that was not found
            resource_id: ID of the resource that was not found
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id:
            details["resource_id"] = resource_id

        super().__init__(
            message=message,
            category=ErrorCategory.RESOURCE_NOT_FOUND,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=404
        )


class ExternalServiceError(AppError):
    """Error raised when an external service call fails."""

    def __init__(
        self,
        message: str = "External service error",
        service_name: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an external service error.

        Args:
            message: Human-readable error message
            service_name: Name of the external service
            operation: Operation that was being performed
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if service_name:
            details["service_name"] = service_name
        if operation:
            details["operation"] = operation

        super().__init__(
            message=message,
            category=ErrorCategory.EXTERNAL_SERVICE,
            severity=ErrorSeverity.ERROR,
            details=details,
            cause=cause,
            status_code=502
        )


class DatabaseError(AppError):
    """Error raised when a database operation fails."""

    def __init__(
        self,
        message: str = "Database operation failed",
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize a database error.

        Args:
            message: Human-readable error message
            operation: Database operation that failed
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if operation:
            details["operation"] = operation

        super().__init__(
            message=message,
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.ERROR,
            details=details,
            cause=cause,
            status_code=500
        )


class TimeoutError(AppError):
    """Error raised when an operation times out."""

    def __init__(
        self,
        message: str = "Operation timed out",
        operation: Optional[str] = None,
        timeout_seconds: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize a timeout error.

        Args:
            message: Human-readable error message
            operation: Operation that timed out
            timeout_seconds: Timeout duration in seconds
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if operation:
            details["operation"] = operation
        if timeout_seconds is not None:
            details["timeout_seconds"] = timeout_seconds

        super().__init__(
            message=message,
            category=ErrorCategory.TIMEOUT,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=504
        )


class RateLimitError(AppError):
    """Error raised when a rate limit is exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        limit: Optional[int] = None,
        reset_time: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize a rate limit error.

        Args:
            message: Human-readable error message
            limit: Rate limit that was exceeded
            reset_time: Time when the rate limit will reset
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if limit is not None:
            details["limit"] = limit
        if reset_time:
            details["reset_time"] = reset_time

        super().__init__(
            message=message,
            category=ErrorCategory.RATE_LIMIT,
            severity=ErrorSeverity.WARNING,
            details=details,
            cause=cause,
            status_code=429
        )


# Error handling decorators

def handle_errors(
    fallback_return: Optional[Any] = None,
    error_handler: Optional[Callable[[Exception, Dict[str, Any]], Any]] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    expected_exceptions: Optional[List[Type[Exception]]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling errors in functions.

    Args:
        fallback_return: Value to return if an error occurs
        error_handler: Function to call when an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling
        expected_exceptions: List of exception types to handle specially

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get function details for better error reporting
                module = func.__module__
                function_name = func.__qualname__

                # Get the traceback
                tb = traceback.format_exc()

                # Create error context
                error_context = {
                    "function": f"{module}.{function_name}",
                    "args": str(args),
                    "kwargs": str(kwargs),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": tb
                }

                # Log the error
                logger.log(
                    log_level,
                    f"Error in {module}.{function_name}: {e}",
                    exc_info=True,
                    extra={"error_context": error_context}
                )

                # Handle expected exceptions differently if specified
                if expected_exceptions and any(isinstance(e, exc_type) for exc_type in expected_exceptions):
                    # Convert to AppError if it's not already
                    if not isinstance(e, AppError):
                        app_error = convert_exception_to_app_error(e)
                        error_context["app_error"] = app_error.to_dict()
                    else:
                        app_error = e
                        error_context["app_error"] = app_error.to_dict()

                    # Call the error handler if provided
                    if error_handler:
                        return error_handler(app_error, error_context)

                # Call the error handler if provided
                if error_handler:
                    return error_handler(e, error_context)

                # Reraise the exception if requested
                if reraise:
                    raise

                # Return the fallback value
                return fallback_return

        return cast(F, wrapper)

    return decorator


def handle_async_errors(
    fallback_return: Optional[Any] = None,
    error_handler: Optional[Callable[[Exception, Dict[str, Any]], Any]] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    expected_exceptions: Optional[List[Type[Exception]]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling errors in async functions.

    Args:
        fallback_return: Value to return if an error occurs
        error_handler: Function to call when an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling
        expected_exceptions: List of exception types to handle specially

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Get function details for better error reporting
                module = func.__module__
                function_name = func.__qualname__

                # Get the traceback
                tb = traceback.format_exc()

                # Create error context
                error_context = {
                    "function": f"{module}.{function_name}",
                    "args": str(args),
                    "kwargs": str(kwargs),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": tb
                }

                # Log the error
                logger.log(
                    log_level,
                    f"Error in {module}.{function_name}: {e}",
                    exc_info=True,
                    extra={"error_context": error_context}
                )

                # Handle expected exceptions differently if specified
                if expected_exceptions and any(isinstance(e, exc_type) for exc_type in expected_exceptions):
                    # Convert to AppError if it's not already
                    if not isinstance(e, AppError):
                        app_error = convert_exception_to_app_error(e)
                        error_context["app_error"] = app_error.to_dict()
                    else:
                        app_error = e
                        error_context["app_error"] = app_error.to_dict()

                    # Call the error handler if provided
                    if error_handler:
                        return await error_handler(app_error, error_context)

                # Call the error handler if provided
                if error_handler:
                    return await error_handler(e, error_context)

                # Reraise the exception if requested
                if reraise:
                    raise

                # Return the fallback value
                return fallback_return

        return cast(F, wrapper)

    return decorator


# Utility functions

def convert_exception_to_app_error(exception: Exception) -> AppError:
    """
    Convert a standard exception to an AppError.

    Args:
        exception: The exception to convert

    Returns:
        Converted AppError
    """
    # Map common exception types to appropriate AppError subclasses
    if isinstance(exception, ValueError):
        return ValidationError(
            message=str(exception),
            cause=exception
        )
    elif isinstance(exception, KeyError):
        return ResourceNotFoundError(
            message=f"Key not found: {str(exception)}",
            cause=exception
        )
    elif isinstance(exception, asyncio.TimeoutError) or (
        hasattr(builtins, 'TimeoutError') and isinstance(exception, builtins.TimeoutError)
    ):
        return TimeoutError(
            message=str(exception) or "Operation timed out",
            cause=exception
        )
    elif isinstance(exception, PermissionError):
        return AuthorizationError(
            message=str(exception),
            cause=exception
        )
    elif isinstance(exception, FileNotFoundError):
        return ResourceNotFoundError(
            message=str(exception),
            resource_type="file",
            cause=exception
        )
    else:
        # Default to a generic AppError
        return AppError(
            message=str(exception),
            category=ErrorCategory.INTERNAL,
            severity=ErrorSeverity.ERROR,
            cause=exception
        )


def format_error_for_response(error: Union[AppError, Exception]) -> Dict[str, Any]:
    """
    Format an error for inclusion in an API response.

    Args:
        error: The error to format

    Returns:
        Formatted error dictionary
    """
    if isinstance(error, AppError):
        return error.to_dict()
    else:
        # Convert to AppError first
        app_error = convert_exception_to_app_error(error)
        return app_error.to_dict()


def log_error(
    error: Union[AppError, Exception],
    context: Optional[Dict[str, Any]] = None,
    log_level: int = logging.ERROR
) -> None:
    """
    Log an error with consistent formatting.

    Args:
        error: The error to log
        context: Additional context for the error
        log_level: Logging level to use
    """
    # Convert to AppError if needed
    if not isinstance(error, AppError):
        app_error = convert_exception_to_app_error(error)
    else:
        app_error = error

    # Create the log message
    message = f"{app_error.category.value.upper()}: {app_error.message}"

    # Add context to the log
    extra = {
        "error_details": app_error.to_dict()
    }
    if context:
        extra["context"] = context

    # Log the error
    logger.log(log_level, message, exc_info=True, extra=extra)


def create_error_response(
    error: Union[AppError, Exception],
    include_details: bool = True
) -> Dict[str, Any]:
    """
    Create a standardized error response for APIs.

    Args:
        error: The error to include in the response
        include_details: Whether to include detailed error information

    Returns:
        Error response dictionary
    """
    # Convert to AppError if needed
    if not isinstance(error, AppError):
        app_error = convert_exception_to_app_error(error)
    else:
        app_error = error

    # Create the base response
    response = {
        "success": False,
        "error": {
            "message": app_error.message,
            "type": app_error.category.value,
            "code": app_error.status_code or 500
        }
    }

    # Add details if requested
    if include_details:
        response["error"]["details"] = app_error.details

    return response


# Context managers for error handling

class ErrorContext:
    """
    Context manager for handling errors with consistent formatting.

    Example:
        with ErrorContext("Processing file", reraise=False) as ctx:
            process_file(file_path)

        if ctx.error:
            # Handle the error
            print(f"Error: {ctx.error}")
    """

    def __init__(
        self,
        operation: str,
        reraise: bool = True,
        log_level: int = logging.ERROR,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the error context.

        Args:
            operation: Description of the operation being performed
            reraise: Whether to reraise the exception after handling
            log_level: Logging level for errors
            context: Additional context for error logging
        """
        self.operation = operation
        self.reraise = reraise
        self.log_level = log_level
        self.context = context or {}
        self.error = None
        self.app_error = None

    def __enter__(self) -> 'ErrorContext':
        """Enter the context manager."""
        return self

    def __exit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_val: Optional[BaseException],
        exc_tb: Optional[Any]  # Changed from traceback.TracebackType
    ) -> bool:
        """
        Exit the context manager.

        Args:
            exc_type: Type of the exception
            exc_val: Exception value
            exc_tb: Exception traceback

        Returns:
            Whether the exception was handled
        """
        if exc_val:
            # Store the original error
            self.error = exc_val

            # Convert to AppError if needed
            if not isinstance(exc_val, AppError):
                self.app_error = convert_exception_to_app_error(exc_val)
            else:
                self.app_error = exc_val

            # Add operation to context
            context = {**self.context, "operation": self.operation}

            # Log the error
            log_error(self.app_error, context, self.log_level)

            # Return True to suppress the exception if reraise is False
            return not self.reraise

        return False
