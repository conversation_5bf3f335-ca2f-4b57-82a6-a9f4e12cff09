"""
Database Error Handling Utilities

This module provides error handling utilities specifically for database operations.
It builds on the core error handling utilities in app.core.errors.base.
"""
from typing import Dict, Any, Callable, TypeVar, cast, Optional
import logging
import functools
import traceback

from app.core.errors.base import (
    handle_errors, handle_async_errors, AppError, DatabaseError,
    ValidationError, ResourceNotFoundError, ErrorContext
)

logger = logging.getLogger(__name__)

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')
R = TypeVar('R')


def handle_db_errors(
    fallback_return: Optional[Any] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False
) -> Callable[[F], F]:
    """
    Decorator for handling database errors in functions.

    Args:
        fallback_return: Value to return if an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling

    Returns:
        Decorated function
    """
    def error_mapper(exception: Exception, context: Dict[str, Any]) -> AppError:
        """Map database-specific exceptions to AppError types."""
        # SQLAlchemy exceptions
        if "sqlalchemy" in type(exception).__module__:
            # IntegrityError (e.g., unique constraint violation)
            if "IntegrityError" in type(exception).__name__:
                return ValidationError(
                    message="Database integrity error",
                    details={"db_error": str(exception)},
                    cause=exception
                )
            # NoResultFound
            elif "NoResultFound" in type(exception).__name__:
                return ResourceNotFoundError(
                    message="Resource not found in database",
                    details={"db_error": str(exception)},
                    cause=exception
                )
            # Other SQLAlchemy errors
            else:
                return DatabaseError(
                    message=f"Database error: {type(exception).__name__}",
                    operation=context.get("function", "unknown"),
                    details={"db_error": str(exception)},
                    cause=exception
                )

        # PostgreSQL exceptions
        elif "psycopg" in type(exception).__module__:
            return DatabaseError(
                message=f"PostgreSQL error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"db_error": str(exception)},
                cause=exception
            )

        # Generic database errors
        else:
            return DatabaseError(
                message=f"Database error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"db_error": str(exception)},
                cause=exception
            )

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get function details for better error reporting
                module = func.__module__
                function_name = func.__qualname__

                # Get the traceback
                tb = traceback.format_exc()

                # Create error context
                error_context = {
                    "function": f"{module}.{function_name}",
                    "args": str(args),
                    "kwargs": str(kwargs),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": tb
                }

                # Log the error
                logger.log(
                    log_level,
                    f"Database error in {module}.{function_name}: {e}",
                    exc_info=True,
                    extra={"error_context": error_context}
                )

                # Map the exception to an AppError
                app_error = error_mapper(e, error_context)

                # Reraise the exception if requested
                if reraise:
                    raise e

                # Return the fallback value
                return fallback_return

        return cast(F, wrapper)

    return decorator


def handle_async_db_errors(
    fallback_return: Optional[Any] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False
) -> Callable[[F], F]:
    """
    Decorator for handling database errors in async functions.

    Args:
        fallback_return: Value to return if an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling

    Returns:
        Decorated function
    """
    def error_mapper(exception: Exception, context: Dict[str, Any]) -> AppError:
        """Map database-specific exceptions to AppError types."""
        # SQLAlchemy exceptions
        if "sqlalchemy" in type(exception).__module__:
            # IntegrityError (e.g., unique constraint violation)
            if "IntegrityError" in type(exception).__name__:
                return ValidationError(
                    message="Database integrity error",
                    details={"db_error": str(exception)},
                    cause=exception
                )
            # NoResultFound
            elif "NoResultFound" in type(exception).__name__:
                return ResourceNotFoundError(
                    message="Resource not found in database",
                    details={"db_error": str(exception)},
                    cause=exception
                )
            # Other SQLAlchemy errors
            else:
                return DatabaseError(
                    message=f"Database error: {type(exception).__name__}",
                    operation=context.get("function", "unknown"),
                    details={"db_error": str(exception)},
                    cause=exception
                )

        # PostgreSQL exceptions
        elif "psycopg" in type(exception).__module__:
            return DatabaseError(
                message=f"PostgreSQL error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"db_error": str(exception)},
                cause=exception
            )

        # Generic database errors
        else:
            return DatabaseError(
                message=f"Database error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"db_error": str(exception)},
                cause=exception
            )

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Get function details for better error reporting
                module = func.__module__
                function_name = func.__qualname__

                # Get the traceback
                tb = traceback.format_exc()

                # Create error context
                error_context = {
                    "function": f"{module}.{function_name}",
                    "args": str(args),
                    "kwargs": str(kwargs),
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": tb
                }

                # Log the error
                logger.log(
                    log_level,
                    f"Database error in {module}.{function_name}: {e}",
                    exc_info=True,
                    extra={"error_context": error_context}
                )

                # Map the exception to an AppError
                app_error = error_mapper(e, error_context)

                # Reraise the exception if requested
                if reraise:
                    raise e

                # Return the fallback value
                return fallback_return

        return cast(F, wrapper)

    return decorator


class DBErrorContext(ErrorContext):
    """
    Context manager for handling database errors with consistent formatting.

    Example:
        with DBErrorContext("Querying user data", reraise=False) as ctx:
            user = session.query(User).filter_by(id=user_id).one()

        if ctx.error:
            # Handle the error
            print(f"Error: {ctx.error}")
    """

    def __exit__(
        self,
        exc_type,
        exc_val,
        exc_tb
    ) -> bool:
        """
        Exit the context manager.

        Args:
            exc_type: Type of the exception
            exc_val: Exception value
            exc_tb: Exception traceback

        Returns:
            Whether the exception was handled
        """
        if exc_val:
            # Store the original error
            self.error = exc_val

            # Map database-specific exceptions to AppError types
            if "sqlalchemy" in type(exc_val).__module__:
                # IntegrityError (e.g., unique constraint violation)
                if "IntegrityError" in type(exc_val).__name__:
                    self.app_error = ValidationError(
                        message="Database integrity error",
                        details={"db_error": str(exc_val), "operation": self.operation},
                        cause=exc_val
                    )
                # NoResultFound
                elif "NoResultFound" in type(exc_val).__name__:
                    self.app_error = ResourceNotFoundError(
                        message="Resource not found in database",
                        details={"db_error": str(exc_val), "operation": self.operation},
                        cause=exc_val
                    )
                # Other SQLAlchemy errors
                else:
                    self.app_error = DatabaseError(
                        message=f"Database error: {type(exc_val).__name__}",
                        operation=self.operation,
                        details={"db_error": str(exc_val)},
                        cause=exc_val
                    )
            # PostgreSQL exceptions
            elif "psycopg" in type(exc_val).__module__:
                self.app_error = DatabaseError(
                    message=f"PostgreSQL error: {type(exc_val).__name__}",
                    operation=self.operation,
                    details={"db_error": str(exc_val)},
                    cause=exc_val
                )
            # Generic database errors
            elif isinstance(exc_val, AppError):
                self.app_error = exc_val
            else:
                self.app_error = DatabaseError(
                    message=f"Database error: {type(exc_val).__name__}",
                    operation=self.operation,
                    details={"db_error": str(exc_val)},
                    cause=exc_val
                )

            # Add operation to context
            context = {**self.context, "operation": self.operation}

            # Log the error
            logger.log(
                self.log_level,
                f"Database error in {self.operation}: {self.app_error.message}",
                exc_info=True,
                extra={"error_details": self.app_error.to_dict(), "context": context}
            )

            # Return True to suppress the exception if reraise is False
            return not self.reraise

        return False
