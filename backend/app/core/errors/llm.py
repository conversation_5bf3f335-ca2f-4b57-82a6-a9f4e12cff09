"""
LLM Error Handling Utilities

This module provides error handling utilities specifically for LLM operations.
It builds on the core error handling utilities in app.core.errors.base.
"""
from typing import Dict, Any, Callable, TypeVar, cast, Optional, List
import logging
import time
import functools
import traceback
import asyncio

from app.core.errors.base import (
    handle_errors, handle_async_errors, AppError, ExternalServiceError,
    TimeoutError, RateLimitError, ErrorCategory, ErrorContext
)

logger = logging.getLogger(__name__)

# Type variables for function signatures
F = TypeVar('F', bound=Callable[..., Any])
T = TypeVar('T')
R = TypeVar('R')


class LLMError(ExternalServiceError):
    """Error raised when an LLM operation fails."""

    def __init__(
        self,
        message: str = "LLM operation failed",
        provider: Optional[str] = None,
        model: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an LLM error.

        Args:
            message: Human-readable error message
            provider: LLM provider (e.g., "openai", "anthropic")
            model: LLM model (e.g., "gpt-4", "claude-3")
            operation: Operation that was being performed (e.g., "chat", "embedding")
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if provider:
            details["provider"] = provider
        if model:
            details["model"] = model

        super().__init__(
            message=message,
            service_name=provider or "llm",
            operation=operation or "unknown",
            details=details,
            cause=cause
        )


class LLMTimeoutError(TimeoutError):
    """Error raised when an LLM operation times out."""

    def __init__(
        self,
        message: str = "LLM operation timed out",
        provider: Optional[str] = None,
        model: Optional[str] = None,
        operation: Optional[str] = None,
        timeout_seconds: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an LLM timeout error.

        Args:
            message: Human-readable error message
            provider: LLM provider (e.g., "openai", "anthropic")
            model: LLM model (e.g., "gpt-4", "claude-3")
            operation: Operation that was being performed (e.g., "chat", "embedding")
            timeout_seconds: Timeout duration in seconds
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if provider:
            details["provider"] = provider
        if model:
            details["model"] = model

        super().__init__(
            message=message,
            operation=operation or "unknown",
            timeout_seconds=timeout_seconds,
            details=details,
            cause=cause
        )


class LLMRateLimitError(RateLimitError):
    """Error raised when an LLM rate limit is exceeded."""

    def __init__(
        self,
        message: str = "LLM rate limit exceeded",
        provider: Optional[str] = None,
        model: Optional[str] = None,
        operation: Optional[str] = None,
        limit: Optional[int] = None,
        reset_time: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize an LLM rate limit error.

        Args:
            message: Human-readable error message
            provider: LLM provider (e.g., "openai", "anthropic")
            model: LLM model (e.g., "gpt-4", "claude-3")
            operation: Operation that was being performed (e.g., "chat", "embedding")
            limit: Rate limit that was exceeded
            reset_time: Time when the rate limit will reset
            details: Additional details about the error
            cause: Original exception that caused this error
        """
        details = details or {}
        if provider:
            details["provider"] = provider
        if model:
            details["model"] = model
        if operation:
            details["operation"] = operation

        super().__init__(
            message=message,
            limit=limit,
            reset_time=reset_time,
            details=details,
            cause=cause
        )


def handle_llm_errors(
    fallback_return: Optional[Any] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    retry_count: int = 0,
    retry_delay: float = 1.0,
    retry_backoff: float = 2.0,
    retry_on: Optional[List[type]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling LLM errors in functions.

    Args:
        fallback_return: Value to return if an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling
        retry_count: Number of times to retry the operation
        retry_delay: Initial delay between retries in seconds
        retry_backoff: Backoff factor for retry delay
        retry_on: List of exception types to retry on

    Returns:
        Decorated function
    """
    retry_on = retry_on or [TimeoutError, RateLimitError]

    def error_mapper(exception: Exception, context: Dict[str, Any]) -> AppError:
        """Map LLM-specific exceptions to AppError types."""
        # OpenAI exceptions
        if "openai" in type(exception).__module__:
            # Timeout
            if "Timeout" in type(exception).__name__:
                return LLMTimeoutError(
                    message="OpenAI API request timed out",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Rate limit
            elif "RateLimitError" in type(exception).__name__:
                return LLMRateLimitError(
                    message="OpenAI API rate limit exceeded",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Other OpenAI errors
            else:
                return LLMError(
                    message=f"OpenAI API error: {type(exception).__name__}",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    details={"api_error": str(exception)},
                    cause=exception
                )

        # Anthropic exceptions
        elif "anthropic" in type(exception).__module__:
            # Timeout
            if "Timeout" in type(exception).__name__:
                return LLMTimeoutError(
                    message="Anthropic API request timed out",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Rate limit
            elif "RateLimitError" in type(exception).__name__:
                return LLMRateLimitError(
                    message="Anthropic API rate limit exceeded",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Other Anthropic errors
            else:
                return LLMError(
                    message=f"Anthropic API error: {type(exception).__name__}",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    details={"api_error": str(exception)},
                    cause=exception
                )

        # Timeout errors
        elif isinstance(exception, (asyncio.TimeoutError, TimeoutError)):
            return LLMTimeoutError(
                message="LLM request timed out",
                operation=context.get("function", "unknown"),
                cause=exception
            )

        # Generic LLM errors
        else:
            return LLMError(
                message=f"LLM error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"error": str(exception)},
                cause=exception
            )

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            current_delay = retry_delay

            for attempt in range(retry_count + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    # Map the exception to an AppError
                    app_error = error_mapper(e, {"function": func.__qualname__})

                    # Log the error
                    logger.log(
                        log_level,
                        f"LLM error in {func.__qualname__} (attempt {attempt+1}/{retry_count+1}): {app_error.message}",
                        exc_info=True,
                        extra={"error_details": app_error.to_dict()}
                    )

                    # Check if we should retry
                    should_retry = (
                        attempt < retry_count and
                        any(isinstance(e, exc_type) for exc_type in retry_on)
                    )

                    if should_retry:
                        logger.info(f"Retrying in {current_delay:.2f} seconds...")
                        time.sleep(current_delay)
                        current_delay *= retry_backoff
                    else:
                        break

            # If we get here, all retries failed
            if reraise and last_exception:
                raise last_exception

            return fallback_return

        return cast(F, wrapper)

    return decorator


def handle_async_llm_errors(
    fallback_return: Optional[Any] = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    retry_count: int = 0,
    retry_delay: float = 1.0,
    retry_backoff: float = 2.0,
    retry_on: Optional[List[type]] = None
) -> Callable[[F], F]:
    """
    Decorator for handling LLM errors in async functions.

    Args:
        fallback_return: Value to return if an error occurs
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling
        retry_count: Number of times to retry the operation
        retry_delay: Initial delay between retries in seconds
        retry_backoff: Backoff factor for retry delay
        retry_on: List of exception types to retry on

    Returns:
        Decorated function
    """
    retry_on = retry_on or [TimeoutError, RateLimitError]

    def error_mapper(exception: Exception, context: Dict[str, Any]) -> AppError:
        """Map LLM-specific exceptions to AppError types."""
        # OpenAI exceptions
        if "openai" in type(exception).__module__:
            # Timeout
            if "Timeout" in type(exception).__name__:
                return LLMTimeoutError(
                    message="OpenAI API request timed out",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Rate limit
            elif "RateLimitError" in type(exception).__name__:
                return LLMRateLimitError(
                    message="OpenAI API rate limit exceeded",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Other OpenAI errors
            else:
                return LLMError(
                    message=f"OpenAI API error: {type(exception).__name__}",
                    provider="openai",
                    operation=context.get("function", "unknown"),
                    details={"api_error": str(exception)},
                    cause=exception
                )

        # Anthropic exceptions
        elif "anthropic" in type(exception).__module__:
            # Timeout
            if "Timeout" in type(exception).__name__:
                return LLMTimeoutError(
                    message="Anthropic API request timed out",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Rate limit
            elif "RateLimitError" in type(exception).__name__:
                return LLMRateLimitError(
                    message="Anthropic API rate limit exceeded",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    cause=exception
                )
            # Other Anthropic errors
            else:
                return LLMError(
                    message=f"Anthropic API error: {type(exception).__name__}",
                    provider="anthropic",
                    operation=context.get("function", "unknown"),
                    details={"api_error": str(exception)},
                    cause=exception
                )

        # Timeout errors
        elif isinstance(exception, (asyncio.TimeoutError, TimeoutError)):
            return LLMTimeoutError(
                message="LLM request timed out",
                operation=context.get("function", "unknown"),
                cause=exception
            )

        # Generic LLM errors
        else:
            return LLMError(
                message=f"LLM error: {type(exception).__name__}",
                operation=context.get("function", "unknown"),
                details={"error": str(exception)},
                cause=exception
            )

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            current_delay = retry_delay

            for attempt in range(retry_count + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    # Map the exception to an AppError
                    app_error = error_mapper(e, {"function": func.__qualname__})

                    # Log the error
                    logger.log(
                        log_level,
                        f"LLM error in {func.__qualname__} (attempt {attempt+1}/{retry_count+1}): {app_error.message}",
                        exc_info=True,
                        extra={"error_details": app_error.to_dict()}
                    )

                    # Check if we should retry
                    should_retry = (
                        attempt < retry_count and
                        any(isinstance(e, exc_type) for exc_type in retry_on)
                    )

                    if should_retry:
                        logger.info(f"Retrying in {current_delay:.2f} seconds...")
                        await asyncio.sleep(current_delay)
                        current_delay *= retry_backoff
                    else:
                        break

            # If we get here, all retries failed
            if reraise and last_exception:
                raise last_exception

            return fallback_return

        return cast(F, wrapper)

    return decorator


class LLMErrorContext(ErrorContext):
    """
    Context manager for handling LLM errors with consistent formatting.

    Example:
        with LLMErrorContext("Generating response", provider="openai", model="gpt-4") as ctx:
            response = await llm_adapter.chat(messages)

        if ctx.error:
            # Handle the error
            print(f"Error: {ctx.error}")
    """

    def __init__(
        self,
        operation: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        reraise: bool = True,
        log_level: int = logging.ERROR,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the LLM error context.

        Args:
            operation: Description of the operation being performed
            provider: LLM provider (e.g., "openai", "anthropic")
            model: LLM model (e.g., "gpt-4", "claude-3")
            reraise: Whether to reraise the exception after handling
            log_level: Logging level for errors
            context: Additional context for error logging
        """
        context = context or {}
        if provider:
            context["provider"] = provider
        if model:
            context["model"] = model

        super().__init__(
            operation=operation,
            reraise=reraise,
            log_level=log_level,
            context=context
        )

    def __exit__(
        self,
        exc_type,
        exc_val,
        exc_tb
    ) -> bool:
        """
        Exit the context manager.

        Args:
            exc_type: Type of the exception
            exc_val: Exception value
            exc_tb: Exception traceback

        Returns:
            Whether the exception was handled
        """
        if exc_val:
            # Store the original error
            self.error = exc_val

            provider = self.context.get("provider", "unknown")
            model = self.context.get("model", "unknown")

            # Map LLM-specific exceptions to AppError types
            if "openai" in type(exc_val).__module__:
                # Timeout
                if "Timeout" in type(exc_val).__name__:
                    self.app_error = LLMTimeoutError(
                        message="OpenAI API request timed out",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        cause=exc_val
                    )
                # Rate limit
                elif "RateLimitError" in type(exc_val).__name__:
                    self.app_error = LLMRateLimitError(
                        message="OpenAI API rate limit exceeded",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        cause=exc_val
                    )
                # Other OpenAI errors
                else:
                    self.app_error = LLMError(
                        message=f"OpenAI API error: {type(exc_val).__name__}",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        details={"api_error": str(exc_val)},
                        cause=exc_val
                    )

            # Anthropic exceptions
            elif "anthropic" in type(exc_val).__module__:
                # Timeout
                if "Timeout" in type(exc_val).__name__:
                    self.app_error = LLMTimeoutError(
                        message="Anthropic API request timed out",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        cause=exc_val
                    )
                # Rate limit
                elif "RateLimitError" in type(exc_val).__name__:
                    self.app_error = LLMRateLimitError(
                        message="Anthropic API rate limit exceeded",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        cause=exc_val
                    )
                # Other Anthropic errors
                else:
                    self.app_error = LLMError(
                        message=f"Anthropic API error: {type(exc_val).__name__}",
                        provider=provider,
                        model=model,
                        operation=self.operation,
                        details={"api_error": str(exc_val)},
                        cause=exc_val
                    )

            # Timeout errors
            elif isinstance(exc_val, (asyncio.TimeoutError, TimeoutError)):
                self.app_error = LLMTimeoutError(
                    message="LLM request timed out",
                    provider=provider,
                    model=model,
                    operation=self.operation,
                    cause=exc_val
                )

            # AppError
            elif isinstance(exc_val, AppError):
                self.app_error = exc_val

            # Generic LLM errors
            else:
                self.app_error = LLMError(
                    message=f"LLM error: {type(exc_val).__name__}",
                    provider=provider,
                    model=model,
                    operation=self.operation,
                    details={"error": str(exc_val)},
                    cause=exc_val
                )

            # Log the error
            logger.log(
                self.log_level,
                f"LLM error in {self.operation}: {self.app_error.message}",
                exc_info=True,
                extra={"error_details": self.app_error.to_dict(), "context": self.context}
            )

            # Return True to suppress the exception if reraise is False
            return not self.reraise

        return False
