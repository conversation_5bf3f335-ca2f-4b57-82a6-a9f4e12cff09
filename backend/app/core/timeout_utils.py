"""
Timeout and Retry Utilities (Compatibility Module)

This module provides backward compatibility with the old timeout utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.timeout instead.
"""

import warnings
import logging

# Show deprecation warning
warnings.warn(
    "The app.core.timeout_utils module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.timeout instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all symbols from the new modular structure
from app.core.timeout.base import (
    DEFAULT_TIMEOUTS,
    DEFAULT_RETRY_CONFIG,
    CircuitBreaker,
    ServiceState,
    with_timeout,
    with_retry,
    with_timeout_and_retry,
    TENACITY_AVAILABLE
)

# Set up logging
logger = logging.getLogger(__name__)

__all__ = [
    "DEFAULT_TIMEOUTS",
    "DEFAULT_RETRY_CONFIG",
    "CircuitBreaker",
    "ServiceState",
    "with_timeout",
    "with_retry",
    "with_timeout_and_retry"
]
