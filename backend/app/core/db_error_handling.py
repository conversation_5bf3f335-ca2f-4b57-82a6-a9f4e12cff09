"""
Database Error Handling Utilities (Compatibility Module)

This module provides backward compatibility with the old database error handling utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.errors.db instead.
"""

import warnings
import logging

# Show deprecation warning
warnings.warn(
    "The app.core.db_error_handling module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.errors.db instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all symbols from the new modular structure
from app.core.errors.db import (
    handle_db_errors,
    handle_async_db_errors,
    DBErrorContext
)

# Set up logging
logger = logging.getLogger(__name__)

__all__ = [
    "handle_db_errors",
    "handle_async_db_errors",
    "DBErrorContext"
]
