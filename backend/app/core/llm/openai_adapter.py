"""
OpenAI Adapter Implementation

This module implements the OpenAI adapter for the LLM interface.
"""

import asyncio
import logging
import os
from typing import AsyncIterator, Dict, List, Optional, Tuple, Union

from .base import ChatMessage, LLMAdapter, log_llm_metrics

logger = logging.getLogger(__name__)

# OpenAI dependencies:
try:
    import openai
    from openai import AsyncOpenAI
    # if imports work, "flag" is created to indicate that OPENAI is available (i.e., is True):
    OPENAI_AVAILABLE = True
except ImportError:
    # if imports fail, set variables to None and create "flag" to indicate that OPENAI is not available (i.e., is False):
    openai = None
    AsyncOpenAI = None
    OPENAI_AVAILABLE = False

# Tiktoken dependency (used for counting OpenAI tokens accurately):
try:
    import tiktoken  # OpenAI's official tokenizer library for models such as GPT-3.5 and GPT-4
    # If import succeeds, set a flag to indicate that tiktoken is available (i.e., is True)
    TIKTOKEN_AVAILABLE = True
except ImportError:
    # If import fails, set module to None and flag to False (i.e., is False)
    tiktoken = None
    TIKTOKEN_AVAILABLE = False

# Tenacity for retry logic:
try:
    # Try to import the Tenacity functions for retrying failed operations
    from tenacity import (retry,
                          retry_if_exception_type,
                          stop_after_attempt,
                          wait_exponential)
    # If imports work, set flag to True to indicate Tenacity is available
    TENACITY_AVAILABLE = True

except ImportError:
    # If Tenacity is not installed, define fallback dummy versions of the functions
    # These dummy functions do nothing, but prevent runtime errors in code that expects them

    # 'retry' becomes a decorator that returns the original function unmodified
    retry = lambda *_args, **_kwargs: lambda func: func  # noqa: E731, F841

    # 'retry_if_exception_type' becomes a placeholder that does nothing
    retry_if_exception_type = lambda *_args: None  # noqa: E731, F841

    # 'stop_after_attempt' becomes a placeholder that does nothing
    stop_after_attempt = lambda *_args: None  # noqa: E731, F841

    # 'wait_exponential' becomes a placeholder that does nothing
    wait_exponential = lambda *_args, **_kwargs: None  # noqa: E731, F841

    # Set flag to False to indicate Tenacity is not available
    TENACITY_AVAILABLE = False

    # Log a warning to flag that retry logic will be skipped
    logger.warning("Tenacity not installed. Retries will not be attempted.")


class OpenAIAdapter(LLMAdapter):
    """
    Adapter for OpenAI's GPT models.
    """

    def __init__(self, model: str = "gpt-4.1-2025-04-14", api_key: Optional[str] = None, **kwargs):
        """
        Initialize the OpenAI adapter.

        Args:
            model: The model to use (default: gpt-4)
            api_key: OpenAI API key (default: from environment)
            **kwargs: Additional configuration options including:
                - timeout: Request timeout in seconds (default: 30.0)
                - max_retries: Maximum number of retries (default: 3)
                - temperature: Sampling temperature (default: 0.7)
                - streaming: Whether to stream responses (default: False)
                - base_url: Base URL for API requests (for Azure/OpenAI proxies)
                - api_version: API version to use (for Azure/OpenAI proxies)
                - organization: OpenAI organization ID
        """
        if not OPENAI_AVAILABLE:
            raise ImportError(
                "OpenAI package is required. Install with 'pip install openai'."
            )

        self.model = model
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        # Additional configuration
        self.timeout = kwargs.get("timeout", 30.0)
        self.max_retries = kwargs.get("max_retries", 3)
        self.temperature = kwargs.get("temperature", 0.7)
        self.max_tokens = kwargs.get("max_tokens", None)
        self.streaming = kwargs.get("streaming", False)

        # Azure/OpenAI proxy configuration
        self.base_url = kwargs.get("base_url", None)
        self.api_version = kwargs.get("api_version", None)
        self.organization = kwargs.get("organization", None)

        # Initialize the client once
        client_kwargs = {"api_key": self.api_key, "timeout": self.timeout}

        # Add optional configuration if provided
        if self.base_url:
            client_kwargs["base_url"] = self.base_url
        if self.api_version:
            client_kwargs["api_version"] = self.api_version
        if self.organization:
            client_kwargs["organization"] = self.organization

        # Create the async client
        self._client = AsyncOpenAI(**client_kwargs)

        logger.info(f"OpenAI adapter initialized with model: {model}")

    @log_llm_metrics
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Send a chat request to OpenAI and get a response.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            **kwargs: Additional arguments to pass to the OpenAI API
                - temperature: Sampling temperature (default: self.temperature)
                - max_tokens: Maximum tokens to generate (default: None)
                - stream: Whether to stream the response (default: self.streaming)

        Returns:
            The model's response as a string, or an async iterator yielding chunks if streaming is enabled
        """
        if not OPENAI_AVAILABLE:
            raise ImportError(
                "OpenAI package is required. Install with 'pip install openai'."
            )

        # Merge instance config with call-specific kwargs
        temperature = kwargs.get("temperature", self.temperature)
        max_tokens = kwargs.get("max_tokens", self.max_tokens)
        stream = self.get_stream_flag(kwargs.get("stream", self.streaming))

        # Handle streaming separately
        if stream:
            # Create and return an async generator directly
            async def generate_stream():
                try:
                    # Prepare API call parameters
                    params = {
                        "model": self.model,
                        "messages": messages,
                        "temperature": temperature,
                        "stream": True,
                    }

                    if max_tokens:
                        params["max_tokens"] = max_tokens

                    # Make the streaming API call
                    stream = await self._client.chat.completions.create(**params)

                    # Yield content chunks as they arrive
                    async for chunk in stream:
                        if chunk.choices and chunk.choices[0].delta.content:
                            yield chunk.choices[0].delta.content
                except Exception as e:
                    logger.error(f"Error in streaming OpenAI API call: {e}")
                    raise

            return generate_stream()

        # Apply retry logic if tenacity is available for non-streaming requests
        if TENACITY_AVAILABLE:
            return await self._chat_with_retry(messages, temperature, max_tokens)
        else:
            return await self._chat_without_retry(messages, temperature, max_tokens)

    async def _chat_with_retry(self, messages, temperature, max_tokens):
        """Chat with retry logic using tenacity."""
        if TENACITY_AVAILABLE:
            # Define a callback to track retry attempts
            attempt_count = 1

            def before_retry_callback(_):
                nonlocal attempt_count
                attempt_count += 1
                logger.debug(
                    f"Tenacity retry {attempt_count}/{self.max_retries} for OpenAI call"
                )
                return None

            @retry(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential(multiplier=1, min=2, max=10),
                retry=retry_if_exception_type(
                    (
                        openai.APIError,
                        openai.APIConnectionError,
                        openai.RateLimitError,
                        openai.APITimeoutError,
                    )
                ),
                reraise=True,
            )
            async def _call_api():
                # Pass current attempt count for metrics
                return await self._chat_without_retry(
                    messages, temperature, max_tokens, attempt=attempt_count
                )

            return await _call_api()
        else:
            # Manual retry logic as fallback when tenacity is not available
            attempt = 0
            last_exception = None

            while attempt < self.max_retries:
                try:
                    # Pass attempt number for metrics
                    return await self._chat_without_retry(
                        messages, temperature, max_tokens, attempt=attempt + 1
                    )
                except (
                    openai.APIError,
                    openai.APIConnectionError,
                    openai.RateLimitError,
                    openai.APITimeoutError,
                ) as e:
                    attempt += 1
                    last_exception = e

                    if attempt >= self.max_retries:
                        break

                    # Check if this is a rate limit error
                    error_message = str(e).lower()
                    is_rate_limit = any(term in error_message for term in ["rate limit", "ratelimit", "too many requests", "429", "quota"])

                    # Exponential backoff
                    wait_time = min(10, 1 * (2**attempt))

                    # Add guidance for rate limit errors
                    if is_rate_limit:
                        logger.warning(
                            f"Rate limit hit for OpenAI API. Consider: "
                            f"1) Implementing request throttling, "
                            f"2) Checking your API quota in OpenAI dashboard, or "
                            f"3) Using a different model. "
                            f"Retry attempt {attempt}/{self.max_retries}. Waiting {wait_time}s..."
                        )
                    else:
                        logger.warning(
                            f"Retry attempt {attempt}/{self.max_retries} after error: {str(e)}. Waiting {wait_time}s..."
                        )
                    await asyncio.sleep(wait_time)
                except Exception as e:
                    # Don't retry other types of exceptions
                    raise

            # If we've exhausted all retries, log and raise the last exception
            if last_exception:
                logger.error(
                    f"Max retries ({self.max_retries}) reached for OpenAI call",
                    exc_info=last_exception,
                )
                raise last_exception

    @property
    def supports_streaming(self) -> bool:
        """
        Whether this adapter supports streaming responses.

        Returns:
            True if streaming is supported, False otherwise
        """
        return True

    async def _chat_without_retry(
        self, messages, temperature, max_tokens, attempt: int = 1
    ):
        """Chat without retry logic."""
        try:
            # Log attempt number for debugging
            if attempt > 1:
                logger.debug(
                    f"Attempt {attempt} for OpenAI API call with model {self.model}"
                )

            # Prepare API call parameters
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
            }

            if max_tokens:
                params["max_tokens"] = max_tokens

            # Make the API call using the async client
            response = await self._client.chat.completions.create(**params)

            # Return the response
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            raise

    async def get_token_count(self, messages: List[ChatMessage], verbose: bool = False) -> Tuple[int, int]:
        """
        Count the number of tokens in the messages.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            verbose: Whether to log detailed token information for each message

        Returns:
            A tuple of (prompt_tokens, estimated_response_tokens)
        """
        try:
            if not TIKTOKEN_AVAILABLE:
                raise ImportError("tiktoken not installed")

            # Get the encoding for the model
            try:
                encoding = tiktoken.encoding_for_model(self.model)
            except KeyError:
                # Fall back to cl100k_base for newer models not yet in tiktoken
                encoding = tiktoken.get_encoding("cl100k_base")

            # Count tokens
            prompt_tokens = 0
            message_tokens = []

            for message in messages:
                message_token_count = 4  # Base tokens for message format
                token_details = {"role": message.get("role", "unknown"), "content_preview": message.get("content", "")[:30] + "..."}

                # Add tokens for each key-value pair in the message
                for key, value in message.items():
                    key_tokens = len(encoding.encode(value))
                    token_details[f"{key}_tokens"] = key_tokens
                    message_token_count += key_tokens

                    # If there's a name, the role is omitted
                    if key == "name":
                        message_token_count -= 1  # Role is always required and always 1 token
                        token_details["role_tokens_deducted"] = 1

                token_details["total_tokens"] = message_token_count
                message_tokens.append(token_details)
                prompt_tokens += message_token_count

            # Every reply is primed with <im_start>assistant
            prompt_tokens += 2

            # Estimate response tokens (rough estimate)
            estimated_response_tokens = min(500, prompt_tokens)

            # Log detailed token information if verbose is enabled
            if verbose:
                logger.debug(
                    f"Token count details for {self.model}:",
                    extra={
                        "token_details": {
                            "model": self.model,
                            "messages": message_tokens,
                            "prompt_tokens": prompt_tokens,
                            "estimated_response_tokens": estimated_response_tokens,
                            "estimated_total_tokens": prompt_tokens + estimated_response_tokens
                        }
                    }
                )

            return prompt_tokens, estimated_response_tokens
        except ImportError:
            logger.warning("tiktoken not installed, using rough token estimation")
            # Rough estimation: ~4 chars per token
            prompt_chars = sum(len(m.get("content", "")) for m in messages)
            prompt_tokens = prompt_chars // 4
            estimated_response_tokens = min(500, prompt_tokens)
            return prompt_tokens, estimated_response_tokens
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0, 0
