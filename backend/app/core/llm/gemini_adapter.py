"""
Gemini Adapter Implementation

This module implements the Gemini adapter for the LLM interface.
"""

import asyncio
import logging
import os
from typing import AsyncIterator, Dict, List, Optional, Tuple, Union

from .base import ChatMessage, LLMAdapter, log_llm_metrics

logger = logging.getLogger(__name__)

# Flag to indicate if Gemini should be available in the adapter registry
# We'll check for actual availability when the adapter is instantiated
GEMINI_AVAILABLE = True

# Tenacity for retry logic:
try:
    # Try to import the Tenacity functions for retrying failed operations
    from tenacity import (retry,
                          retry_if_exception_type,
                          stop_after_attempt,
                          wait_exponential)
    # If imports work, set flag to True to indicate Tenacity is available
    TENACITY_AVAILABLE = True

except ImportError:
    # If Tenacity is not installed, define fallback dummy versions of the functions
    # These dummy functions do nothing, but prevent runtime errors in code that expects them

    # 'retry' becomes a decorator that returns the original function unmodified
    retry = lambda *_args, **_kwargs: lambda func: func  # noqa: E731, F841

    # 'retry_if_exception_type' becomes a placeholder that does nothing
    retry_if_exception_type = lambda *_args: None  # noqa: E731, F841

    # 'stop_after_attempt' becomes a placeholder that does nothing
    stop_after_attempt = lambda *_args: None  # noqa: E731, F841

    # 'wait_exponential' becomes a placeholder that does nothing
    wait_exponential = lambda *_args, **_kwargs: None  # noqa: E731, F841

    # Set flag to False to indicate Tenacity is not available
    TENACITY_AVAILABLE = False

    # Log a warning to flag that retry logic will be skipped
    logger.warning("Tenacity not installed. Retries will not be attempted.")


class GeminiAdapter(LLMAdapter):
    """
    Adapter for Google's Gemini models.
    """

    def __init__(
        self,
        model: str = "gemini-2.0-flash",
        api_key: Optional[str] = None,
        **kwargs,
    ):
        """
        Initialize the Gemini adapter.

        Args:
            model: The model to use (default: gemini-2.0-flash)
                  Available models include:
                  - gemini-2.0-flash: Fast and efficient for most tasks
                  - gemini-2.5-pro-exp-03-25: Advanced model for complex tasks
            api_key: Google API key (default: from environment)
            **kwargs: Additional configuration options including:
                - timeout: Request timeout in seconds (default: 30.0)
                - max_retries: Maximum number of retries (default: 3)
                - temperature: Sampling temperature (default: 0.7)
                - max_tokens: Maximum tokens to generate (default: 1000)
        """
        # Import dependencies here (lazy import)
        try:
            import google.generativeai as genai  # type: ignore
            self._genai = genai
        except ImportError:
            raise ImportError(
                "Google Generative AI package is required. Install with 'pip install google-generativeai'."
            )

        self.model = model
        # Try different environment variable names for the API key
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("Google API key is required")

        # Additional configuration
        self.timeout = kwargs.get("timeout", 30.0)
        self.max_retries = kwargs.get("max_retries", 3)
        self.temperature = kwargs.get("temperature", 0.7)
        self.max_tokens = kwargs.get("max_tokens", 1000)

        # Initialize the client
        self._genai.configure(api_key=self.api_key)
        self._model = self._genai.GenerativeModel(model_name=self.model)

        logger.info(f"Gemini adapter initialized with model: {model}")

    @log_llm_metrics
    async def chat(
        self, messages: List[ChatMessage], **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Send a chat request to Gemini and get a response.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            **kwargs: Additional arguments to pass to the Gemini API
                - temperature: Sampling temperature (default: self.temperature)
                - max_tokens: Maximum tokens to generate (default: self.max_tokens)
                - stream: Whether to stream the response (default: False)

        Returns:
            The model's response as a string, or an async iterator yielding chunks if streaming is enabled
        """
        # We'll check for the package availability in __init__ with lazy imports

        # Merge instance config with call-specific kwargs
        temperature = kwargs.get("temperature", self.temperature)
        max_tokens = kwargs.get("max_tokens", self.max_tokens)
        stream = self.get_stream_flag(kwargs.get("stream", False))

        # Convert messages to Gemini format
        gemini_messages = self._convert_to_gemini_format(messages)

        # Handle streaming separately
        if stream:
            # Create and return an async generator directly
            async def generate_stream():
                try:
                    # Create generation config
                    generation_config = {
                        "temperature": temperature,
                        "max_output_tokens": max_tokens,
                    }

                    # Start a chat session
                    chat = self._model.start_chat(history=gemini_messages[:-1])

                    # Send the last message and stream the response
                    stream_response = chat.send_message_streaming(
                        gemini_messages[-1]["parts"][0]["text"],
                        generation_config=generation_config
                    )

                    # Yield content chunks as they arrive
                    async for chunk in self._async_iterator(stream_response):
                        if chunk.text:
                            yield chunk.text
                except Exception as e:
                    logger.error(f"Error in streaming Gemini API call: {e}")
                    raise

            return generate_stream()

        # Apply retry logic if tenacity is available for non-streaming requests
        if TENACITY_AVAILABLE:
            return await self._chat_with_retry(messages, temperature, max_tokens)
        else:
            return await self._chat_without_retry(messages, temperature, max_tokens)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return True

    async def _chat_with_retry(self, messages, temperature, max_tokens):
        """Chat with retry logic using tenacity."""
        if TENACITY_AVAILABLE:
            # Define a callback to track retry attempts
            attempt_count = 1

            def before_retry_callback(_):
                nonlocal attempt_count
                attempt_count += 1
                logger.debug(
                    f"Tenacity retry {attempt_count}/{self.max_retries} for Gemini call"
                )
                return None

            @retry(
                stop=stop_after_attempt(self.max_retries),
                wait=wait_exponential(multiplier=1, min=2, max=10),
                retry=retry_if_exception_type(Exception),  # Retry on any exception
                reraise=True,
            )
            async def _call_api():
                # Pass current attempt count for metrics
                return await self._chat_without_retry(
                    messages, temperature, max_tokens, attempt=attempt_count
                )

            return await _call_api()
        else:
            # Manual retry logic as fallback when tenacity is not available
            attempt = 0
            last_exception = None

            while attempt < self.max_retries:
                try:
                    # Pass attempt number for metrics
                    return await self._chat_without_retry(
                        messages, temperature, max_tokens, attempt=attempt + 1
                    )
                except Exception as e:
                    attempt += 1
                    last_exception = e

                    if attempt >= self.max_retries:
                        break

                    # Check if this is a rate limit error
                    error_message = str(e).lower()
                    is_rate_limit = any(term in error_message for term in ["rate limit", "ratelimit", "too many requests", "429"])

                    # Exponential backoff
                    wait_time = min(10, 1 * (2**attempt))

                    # Add guidance for rate limit errors
                    if is_rate_limit:
                        logger.warning(
                            f"Rate limit hit for Gemini API. Consider: "
                            f"1) Implementing request throttling, "
                            f"2) Checking your API quota in Google Cloud Console, or "
                            f"3) Using a different model. "
                            f"Retry attempt {attempt}/{self.max_retries}. Waiting {wait_time}s..."
                        )
                    else:
                        logger.warning(
                            f"Retry attempt {attempt}/{self.max_retries} after error: {str(e)}. Waiting {wait_time}s..."
                        )
                    await asyncio.sleep(wait_time)

            # If we've exhausted all retries, log and raise the last exception
            if last_exception:
                logger.error(
                    f"Max retries ({self.max_retries}) reached for Gemini call",
                    exc_info=last_exception,
                )
                raise last_exception

    @property
    def supports_streaming(self) -> bool:
        """
        Whether this adapter supports streaming responses.

        Returns:
            True if streaming is supported, False otherwise
        """
        return True

    async def _chat_without_retry(
        self, messages, temperature, max_tokens, attempt: int = 1
    ):
        """Chat without retry logic."""
        try:
            # Log attempt number for debugging
            if attempt > 1:
                logger.debug(
                    f"Attempt {attempt} for Gemini API call with model {self.model}"
                )

            # Convert messages to Gemini format
            gemini_messages = self._convert_to_gemini_format(messages)

            # Create generation config
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
            }

            # Start a chat session
            chat = self._model.start_chat(history=gemini_messages[:-1])

            # Send the last message and get the response
            response = await self._async_call(
                chat.send_message,
                gemini_messages[-1]["parts"][0]["text"],
                generation_config=generation_config
            )

            # Return the response
            return response.text

        except Exception as e:
            logger.error(f"Error calling Gemini API: {e}")
            raise

    def _convert_to_gemini_format(self, messages: List[ChatMessage]) -> List[Dict]:
        """
        Convert standard chat messages to Gemini format.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys

        Returns:
            List of messages in Gemini format
        """
        gemini_messages = []
        system_content = None

        # First pass: extract system message if present
        for message in messages:
            if message["role"] == "system":
                system_content = message["content"]
                break

        # Second pass: convert messages to Gemini format
        for message in messages:
            role = message["role"]
            content = message["content"]

            # Skip system messages (we'll handle them separately)
            if role == "system":
                continue

            # Map standard roles to Gemini roles
            if role == "assistant":
                gemini_role = "model"
            elif role == "user":
                gemini_role = "user"
                # If this is a user message and we have system content, prepend it
                if system_content and len(gemini_messages) == 0:
                    # Only add system content to the first user message
                    content = f"[System Instructions: {system_content}]\n\nUser query: {content}"
            else:
                # For any other roles, map to user
                gemini_role = "user"

            gemini_messages.append({
                "role": gemini_role,
                "parts": [{"text": content}]
            })

        # If we only have a system message, create a user message with the system content
        if len(gemini_messages) == 0 and system_content:
            gemini_messages.append({
                "role": "user",
                "parts": [{"text": f"[System Instructions: {system_content}]"}]
            })

        return gemini_messages

    async def _async_call(self, func, *args, **kwargs):
        """
        Run a synchronous function asynchronously.

        Args:
            func: The function to call
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            The result of the function call
        """
        return await asyncio.to_thread(func, *args, **kwargs)

    async def _async_iterator(self, iterator):
        """
        Convert a synchronous iterator to an asynchronous iterator.

        Args:
            iterator: The synchronous iterator

        Yields:
            Items from the iterator
        """
        for item in iterator:
            yield item
            # Small sleep to allow other tasks to run
            await asyncio.sleep(0)

    async def get_token_count(self, messages: List[ChatMessage], verbose: bool = False) -> Tuple[int, int]:
        """
        Count the number of tokens in the messages.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            verbose: Whether to log detailed token information for each message

        Returns:
            A tuple of (prompt_tokens, estimated_response_tokens)
        """
        try:
            # Gemini doesn't have a built-in token counter, so we use a character-based heuristic
            # Gemini uses roughly 1 token per 4 characters

            # Convert messages to Gemini format for accurate role mapping
            gemini_messages = self._convert_to_gemini_format(messages)

            # Prepare message details for verbose logging
            message_tokens = []
            total_chars = 0

            for i, (orig_msg, gemini_msg) in enumerate(zip(messages, gemini_messages)):
                content = orig_msg.get("content", "")
                char_count = len(content)
                total_chars += char_count
                token_count = char_count // 4

                # Add tokens for message format (roughly 5 tokens per message)
                token_count += 5

                if verbose:
                    message_tokens.append({
                        "index": i,
                        "role": orig_msg.get("role", "unknown"),
                        "gemini_role": gemini_msg.get("role", "unknown"),
                        "content_preview": content[:30] + "..." if len(content) > 30 else content,
                        "char_count": char_count,
                        "estimated_tokens": token_count
                    })

            # Calculate total tokens
            prompt_tokens = total_chars // 4

            # Add tokens for message format (roughly 5 tokens per message)
            prompt_tokens += len(messages) * 5

            # Estimate response tokens (rough estimate)
            estimated_response_tokens = min(500, prompt_tokens)

            # Log detailed token information if verbose is enabled
            if verbose:
                logger.debug(
                    f"Token count details for {self.model}:",
                    extra={
                        "token_details": {
                            "model": self.model,
                            "token_source": "character_heuristic",
                            "messages": message_tokens,
                            "prompt_tokens": prompt_tokens,
                            "estimated_response_tokens": estimated_response_tokens,
                            "estimated_total_tokens": prompt_tokens + estimated_response_tokens,
                            "note": "Gemini token counts are estimates based on character count"
                        }
                    }
                )

            return prompt_tokens, estimated_response_tokens
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0, 0
