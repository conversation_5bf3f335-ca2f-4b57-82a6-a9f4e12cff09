"""
Mock Adapter Implementation

This module implements a mock adapter for testing purposes.
"""

import asyncio
import logging
import random
from typing import AsyncIterator, Dict, List, Optional, Tuple, Union

from .base import ChatMessage, LLMAdapter, log_llm_metrics

logger = logging.getLogger(__name__)


class MockAdapter(LLMAdapter):
    """
    Mock adapter for testing purposes.
    """

    def __init__(self, responses: Optional[Dict[str, str]] = None, **kwargs):
        """
        Initialize the mock adapter.

        Args:
            responses: Dictionary mapping query patterns to responses
            **kwargs: Additional configuration options (ignored in mock)
        """
        self.responses = responses or {
            "default": "I'm the Co-CEO of BusinessLM. I can help you with various business questions and coordinate with our department heads when needed. How can I assist you today?",
            "finance": "Based on our Q2 financial data, our revenue was €2.7M, which is a 6% increase year-over-year. Our operating expenses were €1.2M, exceeding our forecast of €1.05M by 14.3%. Our burn rate is €350k/month, which we've reduced through IT vendor renegotiation. Our EBITDA is €510k with a gross margin of 68%. Our cash runway is currently 9.5 months, and we're aiming to extend it to 16 months through Series B funding and cost containment measures.",
            "revenue": "Our Q2 revenue was €2.7M, representing a 6% increase compared to the same period last year. This growth was achieved despite increased competition in our market.",
            "marketing": "Our marketing performance in H1 2025 showed mixed results. The GreenEdge Campaign (Feb-Apr) achieved an 18% increase in brand awareness with an ROI of 4.8x, with LinkedIn carousels and SEO blog series being our best-performing assets. The TechWorld Summit in May generated 12 qualified leads with a spend of €65k. Our TikTok Pilot in June underperformed with 2.1% engagement versus our 4.5% target, leading us to pivot toward YouTube Shorts and LinkedIn video. Our LinkedIn Ads achieved a 5.3% CTR, making them our strongest B2B conversion channel.",
            "strategic": "Our strategic vision for Q3 and Q4 2025 focuses on scaling AI automation across HR, Customer Support, and Finance departments. We're forging partnerships with 3 key EU sustainability accelerators and launching our cross-functional Innovation Task Force on August 15. We're also working on Series B due diligence, which should close by September 30. Our shared goals for 2025-2026 include building for scale without bloating teams, making sustainability a competitive advantage, and retaining top talent through visibility, equity, and learning pathways.",
            "help": "I can help you with information about our company's financial performance, marketing campaigns, and strategic vision. Just ask me specific questions about our Q2 financial results, our marketing performance in H1 2025, or our strategic plans for Q3 and Q4.",
            "query": "I've analyzed your question and consulted with our department experts. Our finance department reports a Q2 revenue of €2.7M (+6% YoY) with an EBITDA of €510k. Our marketing department achieved an 18% increase in brand awareness through the GreenEdge Campaign with an ROI of 4.8x. Our strategic vision focuses on scaling AI automation and forging partnerships with EU sustainability accelerators. Would you like more specific details from any particular department?",
        }

        # Add more specific query-response pairs for better testing
        self.query_specific_responses = {
            "financial performance": {
                "finance": """From the Finance department:

Our financial performance in Q2 2025 has been strong despite market challenges:

- Revenue: €2.7M (+6% YoY)
- EBITDA: €510k (18.9% margin)
- Gross Margin: 68% (improved from 65% in Q1)
- Operating Expenses: €1.2M (14.3% over forecast)
- Burn Rate: €350k/month (reduced by 12% from Q1)
- Cash Runway: 9.5 months (target: extend to 16 months)

Key financial initiatives for Q3-Q4:
1. Series B fundraising (target close: September 30)
2. Cost containment program (target: 8% OpEx reduction)
3. Vendor contract renegotiations (target: €120k annual savings)
4. Implementation of AI-powered financial forecasting tools

Would you like more specific details on any of these areas?""",
                "marketing": """From the Marketing department:

While our financial metrics are handled by Finance, I can share our marketing ROI and budget performance:

- Marketing Budget: €450k for H1 2025 (€90k over forecast)
- Campaign ROI: 4.8x for GreenEdge Campaign
- CAC: €1,250 (improved by 12% from 2024)
- LTV:CAC Ratio: 3.2:1 (target: 4:1 by EOY)
- Conversion Rate: 2.8% (up from 2.1% in Q1)

Our TechWorld Summit investment (€65k) has already generated 12 qualified leads, with 3 in late-stage pipeline discussions worth approximately €180k in potential ARR.

We're optimizing our marketing spend for Q3-Q4 to align with the company's financial goals."""
            },
            "marketing strategy": {
                "marketing": """From the Marketing department:

Our marketing strategy for 2025 focuses on four key pillars:

1. Thought Leadership & Brand Authority
   - "Founders Unfiltered" podcast launch (September)
   - Executive speaking engagements at 6 industry conferences
   - Monthly industry research reports

2. Demand Generation
   - Account-based marketing for enterprise targets
   - LinkedIn-focused B2B content strategy
   - Partner co-marketing initiatives

3. Customer Advocacy
   - Customer success story video series
   - Quarterly user community events
   - Referral program enhancement

4. Digital Optimization
   - AI-powered content personalization
   - Conversion rate optimization program
   - Marketing automation enhancement

For H2 2025, we're prioritizing the enterprise segment with targeted ABM campaigns while scaling back our TikTok experiments in favor of LinkedIn and YouTube Shorts.""",
                "finance": """From the Finance department:

While marketing strategy is primarily handled by our Marketing department, I can share the financial aspects:

- Marketing Budget Allocation: €850k for 2025 (12% of revenue)
- ROI Targets: 3.5x minimum for all campaigns
- Investment Split: 60% digital, 25% events, 15% content
- Performance Metrics: Weekly financial reviews of CAC, LTV, and ROI

We've implemented a new marketing finance approval process requiring ROI projections for all campaigns over €10k, with post-campaign analysis to refine our forecasting models."""
            },
            "both marketing and finance": {
                "finance": """From the Finance department:

Our Q2 financial performance shows revenue of €2.7M (+6% YoY) with an EBITDA of €510k (18.9% margin). Our operating expenses were €1.2M, which exceeded our forecast by 14.3% primarily due to marketing overspend and new hires in R&D.

Key financial metrics:
- Gross Margin: 68%
- Burn Rate: €350k/month
- Cash Runway: 9.5 months
- CAC Payback Period: 11 months

We're working closely with Marketing to optimize spend efficiency while maintaining growth momentum.""",
                "marketing": """From the Marketing department:

Our marketing performance in H1 2025 shows strong results from our GreenEdge Campaign with an 18% increase in brand awareness and 4.8x ROI. Our channel performance varies:

- LinkedIn: 5.3% CTR (our strongest B2B channel)
- Content Marketing: 32% newsletter open rate
- Events: TechWorld Summit generated 12 qualified leads
- Social Media: Pivoting from TikTok (2.1% engagement) to YouTube Shorts

We're collaborating with Finance on a more efficient Q3-Q4 budget allocation, focusing on high-ROI channels while reducing experimental investments."""
            }
        }

        # Add JSON analysis responses for different query types
        self.analysis_responses = {
            "finance": """```json
{
  "query_type": "financial",
  "relevant_departments": ["finance"],
  "priority": "normal",
  "topics": ["financial performance", "revenue", "expenses"],
  "explanation": "This query is specifically about financial information, so it should be routed to the Finance department."
}```""",
            "marketing": """```json
{
  "query_type": "marketing",
  "relevant_departments": ["marketing"],
  "priority": "normal",
  "topics": ["marketing strategy", "campaigns", "performance"],
  "explanation": "This query is specifically about marketing information, so it should be routed to the Marketing department."
}```""",
            "both": """```json
{
  "query_type": "cross-functional",
  "relevant_departments": ["finance", "marketing"],
  "priority": "normal",
  "topics": ["financial performance", "marketing strategy", "business overview"],
  "explanation": "This query requires information from both Finance and Marketing departments to provide a comprehensive answer."
}```""",
            "strategic": """```json
{
  "query_type": "strategic",
  "relevant_departments": ["co_ceo"],
  "priority": "high",
  "topics": ["strategy", "vision", "planning"],
  "explanation": "This is a high-level strategic question that should be handled by the Co-CEO."
}```""",
            "default": """```json
{
  "query_type": "general",
  "relevant_departments": ["co_ceo"],
  "priority": "normal",
  "topics": ["general information"],
  "explanation": "This appears to be a general query that can be handled by the Co-CEO."
}```"""
        }

        # Add some latency for realistic testing
        self.latency = kwargs.get("latency", 0.1)
        self.model = kwargs.get("model", "mock-model")
        logger.info("Mock adapter initialized")

    @log_llm_metrics
    async def chat(
        self,
        messages: List[ChatMessage],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Return a mock response based on the last message content.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            temperature: Controls randomness (ignored in mock)
            max_tokens: Maximum tokens to generate (ignored in mock)
            stream: Whether to stream the response
            **kwargs: Additional arguments (ignored in mock)

        Returns:
            A mock response or an async iterator of response chunks if streaming
        """
        # Get the appropriate stream flag based on adapter capabilities
        stream = self.get_stream_flag(stream)

        # Log the kwargs for debugging purposes but don't use them
        if kwargs:
            logger.debug(f"MockAdapter received kwargs: {kwargs}")

        # Check if we're in a department-specific context
        department = kwargs.get("department", None)

        # Get the query from the last message if available
        query = ""
        if messages:
            query = messages[-1]["content"].lower()

        # Check if this is a query analysis request
        is_analysis_request = False
        for message in messages:
            if message.get("role") == "user" and "analyze the following query" in message.get("content", "").lower():
                is_analysis_request = True
                break

        if is_analysis_request:
            # This is a query analysis request, return a structured JSON response
            if "financial" in query and "marketing" in query:
                response = self.analysis_responses["both"]
            elif "financial" in query or "finance" in query or "revenue" in query or "expenses" in query:
                response = self.analysis_responses["finance"]
            elif "marketing" in query or "campaign" in query or "brand" in query:
                response = self.analysis_responses["marketing"]
            elif "strategic" in query or "vision" in query or "strategy" in query:
                response = self.analysis_responses["strategic"]
            else:
                # Default analysis response
                response = self.analysis_responses["default"]

            logger.info(f"Returning mock analysis response for query: {query}")

        elif department:
            # Generate department-specific responses based on the query
            # First check if we have a query-specific response for this department
            for query_pattern, dept_responses in self.query_specific_responses.items():
                if query_pattern.lower() in query.lower() and department in dept_responses:
                    response = dept_responses[department]
                    logger.info(f"Using query-specific response for '{query_pattern}' and department '{department}'")
                    break
            else:
                # If no query-specific response was found, use the generic department responses
                if department == "finance":
                    if "financial performance" in query or "q2" in query or "revenue" in query:
                        response = """From the Finance department:

Based on our Q2 financial review, here are the key financial performance metrics:

- Revenue: €2.7M, which represents a 6% increase year-over-year
- Operating Expenses: €1.2M, which exceeded our forecast of €1.05M by 14.3%
- EBITDA: €510k with a gross margin of 68%
- Burn rate: €350k/month, which we've reduced through IT vendor renegotiation
- Cash runway: Currently 9.5 months, with a goal to extend to 16 months

The budget variance was primarily due to:
- Marketing overage of €90k for the TechWorld Summit and an emergency campaign
- Payroll increase of 8.5% due to 6 new hires across R&D and Data
- SaaS tools costs increased by 18% (Notion, Snowflake, Supermetrics)

Overall, while our revenue growth is positive, we need to focus on cost containment and improving operational efficiency in the second half of the year."""
                    elif "series b" in query or "fundraising" in query:
                        response = """From the Finance department:

For the Series B fundraising materials, Finance is contributing:
1. Detailed financial projections showing our path to profitability
2. Cash flow analysis demonstrating efficient capital utilization
3. Unit economics breakdown highlighting our improving margins
4. Comprehensive risk assessment and mitigation strategies
5. Historical financial performance with clear growth trends

We've prepared a detailed financial data room with audited statements, tax compliance documentation, and detailed forecasts that show our capital efficiency and growth trajectory.

The Series B due diligence is scheduled to close by September 30, led by our CFO with oversight from Legal and the Co-CEO."""
                    else:
                        response = """From the Finance department:

Our current strategic priorities include:
- Extending our cash runway from 9.5 months to 16 months through Series B funding and cost containment
- Updating forecasts from all departments (due July 20)
- Conducting vendor audits in Q3, with owners justifying all renewals over €10k/year
- Developing Power BI & dbt dashboards in collaboration with the Data team
- Implementing an invoice OCR tagging pilot using AI models (current recall: 94%)

Our CFO will present our comprehensive financial strategy on July 28, and we require all-hands approval for any capital reallocation exceeding €50k."""
                elif department == "marketing":
                    if "campaign" in query or "performance" in query or "marketing" in query:
                        response = """From the Marketing department:

Our key campaign performance in H1 2025 includes:

1. GreenEdge Campaign (Feb-Apr):
   - 18% increase in brand awareness with an ROI of 4.8x
   - Best-performing assets were LinkedIn carousels and our SEO blog series

2. TechWorld Summit (May):
   - Generated 12 qualified leads from a €65k spend
   - Follow-up has resulted in 3 scheduled demos with lead nurturing via email automation

3. TikTok Pilot (June):
   - Below-target engagement at 2.1% vs our goal of 4.5%
   - We're pausing TikTok and exploring YouTube Shorts and LinkedIn video instead

Our channel performance metrics show:
- LinkedIn Ads: 5.3% CTR with the strongest B2B conversion
- Podcast Sponsorships: 2,300 listens average per episode
- Newsletter Open Rate: 32%, with a revamp planned for Q3"""
                    elif "series b" in query or "fundraising" in query:
                        response = """From the Marketing department:

Marketing is playing a crucial role in the Series B fundraising materials by:
1. Creating compelling investor-facing pitch materials that showcase our market position
2. Developing case studies that demonstrate our product-market fit and customer success stories
3. Providing competitive analysis showing our unique value proposition
4. Documenting our go-to-market strategy and customer acquisition channels
5. Preparing market sizing analysis and growth opportunity assessment

We've also created a comprehensive brand narrative that positions us as an industry innovator, supported by testimonials from key customers and industry analysts. Our marketing metrics show improving CAC:LTV ratios and increasing market penetration in our target segments."""
                    else:
                        response = """From the Marketing department:

Our upcoming plans for H2 2025 include:
- Launching the "Founders Unfiltered" podcast in September
- Adding AI-generated A/B testing for email subject lines and ads
- Supporting Series B storytelling via content assets and metrics

We've set the following OKRs:
- Increase inbound B2B leads by 20%
- Grow our monthly newsletter subscribers by 20%
- Increase organic blog traffic by 15%

We've also made operational improvements with new hires (1 campaign manager and 1 content strategist) and implemented Monday.com for sprint planning."""
                elif department == "co_ceo":
                    if "strategic" in query or "vision" in query or "strategy" in query:
                        response = """From the Co-CEO:

Our strategic focus areas for Q3 & Q4 2025 include:
- Scaling AI automation in HR (candidate ranking), Customer Support (intent-based routing), and Finance (smart reconciliation)
- Forging partnerships with 3 key EU sustainability accelerators: EIT Climate-KIC, Circular Valley, and StartupAmsterdam
- Officially launching the cross-functional Innovation Task Force on August 15
- Completing Series B due diligence by September 30

Our shared goals for 2025-2026 are:
1. Building for scale without bloating teams
2. Making sustainability a competitive advantage
3. Retaining top talent through visibility, equity, and learning pathways"""
                    else:
                        response = """From the Co-CEO:

I'm focused on several key initiatives for organizational development:
- Improving operational efficiency by 15% YoY via updated SOPs, automation, and role re-alignment
- Implementing standardized quarterly OKRs across departments, with the first review on October 10
- Launching the "Leaders of Tomorrow" mid-manager leadership initiative on September 5
- Conducting monthly town halls and bi-weekly "Ask Leadership" AMAs to foster transparency and trust

On the technology front, we're launching Exec Dashboard v2 in Q4 with live OKRs, MRR, churn, NPS, and hiring stats, and enhancing internal collaboration via an AI-powered assistant pilot that integrates Slack, Notion, and Monday.com."""
                else:
                    # Default response for other departments
                    response = self.responses.get(department, self.responses["default"])
        elif not messages:
            response = self.responses["default"]
        else:
            last_message = messages[-1]["content"].lower()

            # Check for query-specific responses first
            for query_pattern, dept_responses in self.query_specific_responses.items():
                if query_pattern.lower() in last_message:
                    # If we have responses for multiple departments, combine them
                    if len(dept_responses) > 1:
                        combined_response = "I've consulted with multiple departments:\n\n"
                        for dept, resp in dept_responses.items():
                            combined_response += f"{resp}\n\n"
                        response = combined_response
                        logger.info(f"Using combined query-specific response for '{query_pattern}'")
                        break
                    # If we only have one department response, use it
                    elif len(dept_responses) == 1:
                        dept, resp = next(iter(dept_responses.items()))
                        response = resp
                        logger.info(f"Using query-specific response for '{query_pattern}' from department '{dept}'")
                        break
            else:
                # If no query-specific response was found, use the generic responses
                response = self.responses["default"]
                for key, resp in self.responses.items():
                    if key.lower() in last_message:
                        response = resp
                        break

        if stream:
            return self._stream_response(response)
        else:
            # Simulate API latency
            if self.latency > 0:
                await asyncio.sleep(self.latency)
            return response

    async def _stream_response(self, response: str) -> AsyncIterator[str]:
        """
        Stream a response in chunks.

        Args:
            response: The full response to stream

        Returns:
            An async iterator of response chunks
        """
        # Split the response into words
        words = response.split()

        # Stream 1-3 words at a time
        buffer = []
        for word in words:
            buffer.append(word)

            # Randomly decide if we should yield the buffer (or if it's full)
            if len(buffer) >= 3 or random.random() < 0.3:
                # Simulate API latency
                if self.latency > 0:
                    await asyncio.sleep(self.latency / 5)  # Shorter delay for streaming

                yield " ".join(buffer) + " "
                buffer = []

        # Yield any remaining words
        if buffer:
            if self.latency > 0:
                await asyncio.sleep(self.latency / 5)
            yield " ".join(buffer)

    @property
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses."""
        return True

    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """
        Mock token counting.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys

        Returns:
            A tuple of (prompt_tokens, estimated_response_tokens)
        """
        # Simple mock implementation
        prompt_chars = sum(len(m.get("content", "")) for m in messages)
        prompt_tokens = prompt_chars // 4
        return prompt_tokens, 50  # Fixed response tokens for mock
