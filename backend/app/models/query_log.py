"""
Query Log Model

This module defines the SQLAlchemy ORM model for query logs.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import text

from app.config import get_settings
from app.core.db.database import Base
from app.core.db.pgvector import Vector


class QueryLog(Base):
    """Model for storing query logs with vector embeddings."""

    __tablename__ = "query_logs"

    id = Column(PGUUID, primary_key=True, server_default=text("gen_random_uuid()"))
    query_text = Column(Text, nullable=False)
    user_id = Column(PGUUID, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    thread_id = Column(String, nullable=True)
    session_id = Column(String, nullable=True)
    timestamp = Column(DateTime(timezone=True), server_default=text("now()"), nullable=False)
    department = Column(String, nullable=True)
    top_results = Column(JSONB, nullable=True)
    execution_time_ms = Column(Integer, nullable=True)
    embedding = Column(Vector(get_settings().VECTOR_DIMENSION), nullable=True)
    meta_info = Column(JSONB, server_default=text("'{}'::jsonb"), nullable=False)

    # Relationships
    user = relationship("User", back_populates="query_logs")

    def to_dict(self) -> Dict[str, Any]:
        """Convert the query log to a dictionary.

        Returns:
            Dictionary representation of the query log
        """
        return {
            "id": str(self.id),
            "query_text": self.query_text,
            "user_id": str(self.user_id) if self.user_id else None,
            "thread_id": self.thread_id,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "department": self.department,
            "top_results": self.top_results,
            "execution_time_ms": self.execution_time_ms,
            "metadata": self.meta_info,
        }
