"""
PostgreSQL Knowledge Base Service

This module implements a knowledge base service that works with PostgreSQL and pgvector
instead of Firebase. It provides a compatible interface for the HybridRetriever.
"""

import logging
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple

from app.rag.reranker import Reranker, get_reranker
from app.core.db.database import get_db_context
from app.core.db.models import Document

logger = logging.getLogger(__name__)


class PgVectorKnowledgeBaseService:
    """
    Knowledge base service that works with PostgreSQL and pgvector.

    This service provides a compatible interface for the HybridRetriever,
    allowing it to work with our PostgreSQL + pgvector setup instead of Firebase.
    """

    def __init__(
        self,
        vector_store,
        embedding_model=None,
        reranker=None,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        use_reranking: bool = False,
        reranking_candidates: int = 20
    ):
        """
        Initialize the knowledge base service.

        Args:
            vector_store: The pgvector store for embedding storage and retrieval
            embedding_model: The embedding model for vector conversion
            reranker: Optional reranker for improving search result relevance
            vector_weight: Weight for vector search results in hybrid search (0.0-1.0)
            keyword_weight: Weight for keyword search results in hybrid search (0.0-1.0)
            use_reranking: Whether to use reranking for search results
            reranking_candidates: Number of candidates to consider for reranking
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.reranker = reranker
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        self.use_reranking = use_reranking
        self.reranking_candidates = reranking_candidates

        # Initialize reranker if not provided but reranking is enabled
        if self.use_reranking and not self.reranker:
            try:
                self.reranker = get_reranker("cross-encoder")
                logger.info("Initialized default cross-encoder reranker")
            except Exception as e:
                logger.warning(f"Failed to initialize reranker: {e}. Reranking will be disabled.")
                self.use_reranking = False

        logger.info(
            f"PgVector knowledge base service initialized with "
            f"vector_store={vector_store is not None}, "
            f"embedding_model={embedding_model is not None}, "
            f"reranker={reranker is not None}, "
            f"use_reranking={use_reranking}"
        )

    async def search(
        self,
        query: str,
        collection: str = None,  # Ignored, kept for compatibility
        limit: int = 5,
        search_type: str = "hybrid",
        filters: Optional[Dict[str, Any]] = None,
        vector_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for documents in the knowledge base.

        Args:
            query: The search query
            collection: Ignored, kept for compatibility with Firebase interface
            limit: Maximum number of results to return
            search_type: Type of search to perform ("vector", "keyword", "hybrid")
            filters: Metadata filters to apply
            vector_weight: Optional override for vector search weight
            keyword_weight: Optional override for keyword search weight

        Returns:
            List of matching documents with relevance scores
        """
        # Use provided weights or defaults
        v_weight = vector_weight if vector_weight is not None else self.vector_weight
        k_weight = keyword_weight if keyword_weight is not None else self.keyword_weight

        # Normalize weights
        total_weight = v_weight + k_weight
        v_weight = v_weight / total_weight
        k_weight = k_weight / total_weight

        try:
            if search_type == "vector" and self.embedding_model and self.vector_store:
                # Perform vector search
                results = await self._vector_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

                # Apply reranking if enabled
                if self.use_reranking and self.reranker and results:
                    try:
                        reranked_results = await self.reranker.rerank(
                            query=query,
                            results=results[:self.reranking_candidates],
                            top_n=limit
                        )
                        if reranked_results:
                            results = reranked_results
                    except Exception as e:
                        logger.error(f"Error during vector search reranking: {e}")
                        # Continue with original results

            elif search_type == "keyword":
                # Perform keyword search using PostgreSQL text search
                results = await self._keyword_search(
                    query, limit, filters,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

            elif search_type == "hybrid" and self.embedding_model and self.vector_store:
                # Perform hybrid search combining vector and keyword results
                results = await self._hybrid_search(
                    query, limit, filters, v_weight, k_weight,
                    user_id=user_id, thread_id=thread_id,
                    session_id=session_id, department=department
                )

            else:
                # Fall back to vector search if hybrid is not available
                logger.warning(
                    f"Falling back to vector search because search_type={search_type} "
                    f"and vector_store={self.vector_store is not None}, "
                    f"embedding_model={self.embedding_model is not None}"
                )
                results = await self._vector_search(query, limit, filters)

        except Exception as e:
            logger.error(f"Error during search: {e}")
            # Return empty results on error
            results = []

        return results

    async def _vector_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform vector search using pgvector.

        Args:
            query: The search query
            limit: Maximum number of results to return
            filters: Metadata filters to apply
            user_id: User ID for query logging
            thread_id: Thread ID for query logging
            session_id: Session ID for query logging
            department: Department for query logging

        Returns:
            List of matching documents with similarity scores
        """
        # Embed the query
        query_embedding = await self.embedding_model.embed_query(query)

        # Search the vector store
        raw_results = await self.vector_store.search(
            query_embedding,
            limit=limit,
            filters=filters,
            query_text=query,
            user_id=user_id,
            thread_id=thread_id,
            session_id=session_id,
            department=department
        )

        # Format results to match the expected interface
        results = []
        for result in raw_results:
            search_result = {
                "id": result["id"],
                "text": result["text"],
                "score": result["score"],
                "metadata": result["metadata"],
                "document": None  # Will be populated later if needed
            }
            results.append(search_result)

        return results

    async def _keyword_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword search using PostgreSQL text search.

        Args:
            query: The search query
            limit: Maximum number of results to return
            filters: Metadata filters to apply

        Returns:
            List of matching documents with relevance scores
        """
        # For now, we'll use the vector store's search method with a special flag
        # In a real implementation, this would use PostgreSQL's full-text search capabilities
        if hasattr(self.vector_store, "keyword_search"):
            raw_results = await self.vector_store.keyword_search(
                query,
                limit=limit,
                filters=filters
            )

            # Format results
            results = []
            for result in raw_results:
                search_result = {
                    "id": result["id"],
                    "text": result["text"],
                    "score": result["score"],
                    "metadata": result["metadata"],
                    "document": None
                }
                results.append(search_result)

            return results
        else:
            # Fall back to vector search if keyword search is not implemented
            logger.warning("Keyword search not implemented, falling back to vector search")
            return await self._vector_search(query, limit, filters)

    async def _hybrid_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]] = None,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining vector and keyword search.

        Args:
            query: The search query
            limit: Maximum number of results to return
            filters: Metadata filters to apply
            vector_weight: Weight for vector search results
            keyword_weight: Weight for keyword search results

        Returns:
            List of matching documents with combined scores
        """
        # Get more candidates for each search method
        candidates = max(limit * 2, self.reranking_candidates if self.use_reranking else limit)

        # Perform both searches
        vector_results = await self._vector_search(
            query, candidates, filters,
            user_id=user_id, thread_id=thread_id,
            session_id=session_id, department=department
        )
        keyword_results = await self._keyword_search(
            query, candidates, filters,
            user_id=user_id, thread_id=thread_id,
            session_id=session_id, department=department
        )

        # Combine results using Reciprocal Rank Fusion
        combined_results = self._combine_results(
            vector_results, keyword_results, vector_weight, keyword_weight
        )

        # Apply reranking if enabled
        if self.use_reranking and self.reranker:
            try:
                rerank_candidates = combined_results[:self.reranking_candidates]
                reranked_results = await self.reranker.rerank(
                    query=query,
                    results=rerank_candidates,
                    top_n=limit
                )
                if reranked_results:
                    return reranked_results
            except Exception as e:
                logger.error(f"Error during reranking: {e}")
                # Fall back to original results

        # Return top results
        return combined_results[:limit]

    def _combine_results(
        self,
        vector_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        k: int = 60  # RRF constant
    ) -> List[Dict[str, Any]]:
        """
        Combine vector and keyword search results using weighted RRF.

        Args:
            vector_results: Results from vector search
            keyword_results: Results from keyword search
            vector_weight: Weight for vector search results
            keyword_weight: Weight for keyword search results
            k: RRF constant

        Returns:
            Combined and sorted results
        """
        # Create maps of document IDs to their ranks
        vector_ranks = {result["id"]: i + 1 for i, result in enumerate(vector_results)}
        keyword_ranks = {result["id"]: i + 1 for i, result in enumerate(keyword_results)}

        # Collect all unique document IDs
        all_ids = set(vector_ranks.keys()) | set(keyword_ranks.keys())

        # Calculate RRF scores with weights
        combined_scores = {}
        for doc_id in all_ids:
            v_rank = vector_ranks.get(doc_id, len(vector_results) + 1)
            k_rank = keyword_ranks.get(doc_id, len(keyword_results) + 1)

            v_score = vector_weight * (1.0 / (k + v_rank))
            k_score = keyword_weight * (1.0 / (k + k_rank))

            combined_scores[doc_id] = v_score + k_score

        # Sort by combined score
        sorted_ids = sorted(all_ids, key=lambda doc_id: combined_scores[doc_id], reverse=True)

        # Reconstruct the results
        combined_results = []
        for doc_id in sorted_ids:
            # Find the original result to preserve metadata
            result = None

            # First check vector results
            for r in vector_results:
                if r["id"] == doc_id:
                    result = r
                    break

            # If not found in vector results, check keyword results
            if not result:
                for r in keyword_results:
                    if r["id"] == doc_id:
                        result = r
                        break

            if result:
                # Add the combined score
                result_copy = dict(result)
                result_copy["score"] = combined_scores[doc_id]
                combined_results.append(result_copy)

        return combined_results

    async def retrieve(
        self,
        query: str,
        collection: str = None,  # Ignored, kept for compatibility
        limit: int = 5,
        top_k: int = None,  # Added for compatibility with department agents
        filters: Optional[Dict[str, Any]] = None,
        use_hybrid_search: bool = True,  # Added for compatibility with department agents
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None,
        **kwargs  # Added to catch any other parameters
    ) -> List[Dict[str, Any]]:
        """
        Retrieve documents from the knowledge base.
        This is an alias for search() to maintain compatibility with the department agents.

        Args:
            query: The search query
            collection: Ignored, kept for compatibility
            limit: Maximum number of results to return
            top_k: Alternative way to specify limit, takes precedence if provided
            filters: Metadata filters to apply
            use_hybrid_search: Whether to use hybrid search
            **kwargs: Additional parameters to catch any other parameters

        Returns:
            List of matching documents with relevance scores
        """
        # Use top_k if provided, otherwise use limit
        result_limit = top_k if top_k is not None else limit

        # Determine search type based on use_hybrid_search
        search_type = "hybrid" if use_hybrid_search else "vector"

        return await self.search(
            query, collection, result_limit,
            search_type=search_type,
            filters=filters,
            user_id=user_id,
            thread_id=thread_id,
            session_id=session_id,
            department=department
        )

    async def add_document(
        self,
        document: Dict[str, Any],
        collection: str = None  # Ignored, kept for compatibility
    ) -> str:
        """
        Add a document to the knowledge base.

        Args:
            document: The document to add
            collection: Ignored, kept for compatibility with Firebase interface

        Returns:
            str: The document ID
        """
        try:
            if not self.embedding_model or not self.vector_store:
                raise ValueError("Embedding model and vector store are required to add documents")

            # Extract document content and metadata
            content = document.get("content", "")
            if not content:
                content = document.get("text", "")

            if not content:
                raise ValueError("Document must have 'content' or 'text' field")

            title = document.get("title", "Untitled Document")

            # Create metadata from all other fields
            metadata = {k: v for k, v in document.items() if k not in ["content", "text", "title"]}

            # Generate a document ID
            doc_id = str(uuid.uuid4())

            # Add the document to the documents table
            with get_db_context() as db:
                # Create a new Document
                new_doc = Document(
                    id=uuid.UUID(doc_id),
                    title=title,
                    content=content,
                    meta_info=metadata
                )
                db.add(new_doc)
                db.commit()
                logger.info(f"Added document to database with ID: {doc_id}")

            # Generate embeddings for the document
            # For simplicity, we'll treat the entire document as one chunk
            # In a real implementation, you would chunk the document first
            embeddings = await self.embedding_model.embed_documents([content])

            if not embeddings or len(embeddings) == 0:
                raise ValueError("Failed to generate embeddings for document")

            # Add the embedding to the vector store
            ids = await self.vector_store.add_embeddings(
                embeddings=embeddings,
                texts=[content],
                metadatas=[{"title": title, **metadata}],
                ids=[doc_id]  # Use the same ID for both document and embedding
            )

            if not ids or len(ids) == 0:
                raise ValueError("Failed to add document to vector store")

            # Return the document ID
            return doc_id

        except Exception as e:
            logger.error(f"Error adding document: {e}")
            raise
