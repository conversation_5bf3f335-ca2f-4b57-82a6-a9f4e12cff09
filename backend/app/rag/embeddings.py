"""
Embedding Models for Retrieval-Augmented Generation (RAG)

WHAT ARE EMBEDDINGS?
-------------------
Embeddings are numerical representations of text that capture meaning. Think of them as
converting words into points in space where similar meanings are closer together.

For example, in a good embedding space:
- "dog" and "puppy" would be close together
- "dog" and "cat" would be somewhat close (both are pets)
- "dog" and "refrigerator" would be far apart

These embeddings are created by AI models that have been trained to understand language.
The result is a list of numbers (a vector) for each piece of text.

WHY DO WE NEED EMBEDDINGS?
-------------------------
Computers can't directly understand text, but they can work with numbers. Embeddings let us:
1. Find similar documents (by finding vectors that are close together)
2. Organize information by meaning
3. Enable AI systems to retrieve relevant information

WHAT THIS MODULE DOES:
---------------------
This module converts text into these numerical embeddings using different AI models.
It's like having different translators that can convert text to numbers, each with
their own strengths and weaknesses.

Key Components (Think of these as different tools in our toolbox):
-----------------------------------------------------------------
1. EmbeddingModel: A blueprint that defines what any embedding tool must be able to do
   (like a template or interface)

2. HuggingFaceEmbedding: A tool that runs locally on your computer
   - Pros: Free to use, works offline, no data sent to external services
   - Cons: Uses more computer resources, might be slower

3. OpenAIEmbedding: A tool that uses OpenAI's online service
   - Pros: Fast, high quality, less computer resources
   - Cons: Costs money, requires internet, sends data to OpenAI

4. EmbeddingFactory: A smart tool selector that can switch between tools if one fails
   - Like having a backup plan if your first choice doesn't work

How To Use This Module (Simple Examples):
---------------------------------------
1. Basic usage with HuggingFace (local model):
   ```python
   # Create a local embedding tool
   embedding_model = HuggingFaceEmbedding(model_name="intfloat/e5-base-v2")

   # Convert a question to numbers
   query_embedding = await embedding_model.embed_query("What is RAG?")
   # Result: a list of 768 numbers that represent the meaning of "What is RAG?"
   ```

2. Using OpenAI (online service):
   ```python
   # Create an online embedding tool with smaller vectors
   embedding_model = OpenAIEmbedding(
       model_name="text-embedding-3-small",
       dimensions=512  # Get 512 numbers instead of the default 1024
   )

   # Convert a question to numbers
   query_embedding = await embedding_model.embed_query("What is RAG?")
   # Result: a list of 512 numbers
   ```

3. Using the smart tool selector with backup plan:
   ```python
   # Try to use HuggingFace, but switch to OpenAI if it doesn't work
   embedding_model = EmbeddingFactory.create(
       provider="huggingface",  # First choice
       fallback=True,           # Allow backup plan
       model_name="intfloat/e5-base-v2"  # Specific model to use
   )
   ```

Important Design Choices Explained:
---------------------------------
- Async Methods: All functions use "async/await" which means they can wait for slow
  operations without freezing your entire program. This is like being able to do other
  tasks while waiting for water to boil.

- Multiple Providers: We support both local (HuggingFace) and online (OpenAI) options
  so you can choose what works best for your situation (like having both a gas stove
  and a microwave).

- Fallback System: If your first choice fails, we automatically try another option
  (like having a backup generator if the power goes out).

- Consistent Output: No matter which tool you use, you always get the same type of result
  (a list of numbers), making it easy to switch between tools.

- Dimension Information: Each model tells you how many numbers it produces, which is
  important when storing and searching these embeddings later.

Choosing Between Options:
-----------------------
- HuggingFace models are like cooking at home:
  * Free but requires your own ingredients and equipment
  * Good for learning, development, or when you have powerful computers
  * Complete control and privacy

- OpenAI models are like ordering takeout:
  * Costs money but convenient and requires minimal setup
  * Good for production systems or when you need high quality
  * Less control but often better results

- The embedding dimension (number of values) is like image resolution:
  * Higher dimensions = more detail but larger file size
  * Lower dimensions = less storage but might miss subtle meanings
"""

# Standard library imports
import os  # For interacting with the operating system (e.g., environment variables, file paths)
import logging  # For application-level logging (debugging, info, warnings, errors)
import asyncio  # Enables asynchronous programming (e.g., coroutines, event loop)

# Abstract base classes
from abc import ABC, abstractmethod  # For creating abstract base classes and enforcing method implementations

# Type hinting utilities from Python's standard typing module
from typing import (
    List,         # Generic type for a sequence of items of a single type; e.g., List[str] for a list of strings
    Optional,     # Shorthand for Union[T, None]; used when a variable can either have a value or be None
    Dict,         # Generic type for a key-value mapping; e.g., Dict[str, int] means keys are strings, values are integers
    Any,          # A wildcard type that disables type checking; used when the data type is dynamic or not yet defined
    Literal,      # Used to constrain a variable to a fixed set of constant values; e.g., Literal['start', 'stop'] ensures only those strings are valid
    Union         # Represents a variable that could be one of multiple specified types; e.g., Union[int, str] allows either int or str
)

from app.config import get_settings

# Configure logging
logger = logging.getLogger(__name__)


class EmbeddingModel(ABC):
    """
    Blueprint for all embedding models - defines what any embedding tool must do.

    WHAT IS THIS?
    ------------
    This is what we call an "abstract base class" - think of it like a blueprint or template
    that defines what features any embedding model MUST have to work in our system.

    It's like saying: "If you want to be an embedding model in our system, you MUST be able
    to do these specific things."

    WHY DO WE NEED THIS?
    ------------------
    1. Consistency: All embedding models will work the same way from the outside
    2. Interchangeability: We can swap different models without changing other code
    3. Type Safety: Python will help catch errors if we try to use an incomplete model

    WHAT MUST ALL EMBEDDING MODELS DO?
    -------------------------------
    Any embedding model must implement these three capabilities:

    1. embed_query: Convert a single question/query into numbers
       - Optimized for search queries
       - Example: "What is RAG?" → [0.1, 0.2, 0.3, ...]

    2. embed_documents: Convert multiple documents into numbers efficiently
       - Handles batches of text for better performance
       - Example: ["Doc1", "Doc2"] → [[0.1, 0.2, ...], [0.3, 0.4, ...]]

    3. dimension: Tell us how many numbers are in each embedding
       - This is a "property" (accessed like an attribute, not a method)
       - Example: model.dimension → 768

    WHY ARE THE METHODS ASYNC?
    -----------------------
    All methods use "async" which means they can pause without blocking other code.
    This is important because:

    1. API calls (like OpenAI) might take time to respond
    2. Local models might need to process large batches of text
    3. Your application can do other things while waiting for embeddings

    It's like being able to check your phone while waiting for coffee to brew,
    instead of just standing there doing nothing.

    HOW TO USE THIS CLASS?
    -------------------
    You don't use this class directly! It's just a template.
    Instead, you use concrete implementations like:

    - HuggingFaceEmbedding (local processing)
    - OpenAIEmbedding (API-based)

    TECHNICAL NOTE:
    ------------
    The ABC (Abstract Base Class) and @abstractmethod decorators enforce that
    any class inheriting from EmbeddingModel MUST implement all these methods,
    or Python will prevent that class from being instantiated.
    """

    @abstractmethod
    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a question or search query into a list of numbers.

        WHAT DOES THIS DO?
        ----------------
        This method takes a text string (like a question) and turns it into a list
        of numbers (an embedding) that captures the meaning of the text.

        Think of it like translating English to "number-language" that computers
        can understand and compare.

        WHY IS THIS USEFUL?
        -----------------
        When you want to find information related to a question, you can:
        1. Convert the question to numbers using this method
        2. Compare those numbers to the numbers of stored documents
        3. Find documents with the most similar numbers
        4. Return those documents as relevant information

        PARAMETERS:
        ----------
        text (str): The question or search query to convert to numbers
                    Example: "What is machine learning?"

        RETURNS:
        -------
        List[float]: A list of decimal numbers representing the meaning
                     Example: [0.123, -0.456, 0.789, ...]

                     These numbers typically have hundreds of dimensions
                     (768 for HuggingFace e5-base, 1024 for OpenAI embedding-3-small)

        IMPORTANT NOTES:
        --------------
        1. This is an "async" method, so you must use "await" when calling it:
           embedding = await model.embed_query("my question")

        2. Different models may produce different numbers for the same text

        3. This method is specifically optimized for questions/queries, while
           embed_documents() is optimized for document content
        """
        # This is just a placeholder - actual implementation will be provided by subclasses
        # Each subclass (like HuggingFaceEmbedding or OpenAIEmbedding) will implement
        # its own version of this method using different AI models
        pass

    @abstractmethod
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert multiple documents into lists of numbers all at once.

        WHAT DOES THIS DO?
        ----------------
        This method takes multiple text documents and converts each one into a list
        of numbers (embeddings) that capture the meaning of each document.

        It's like batch processing or translating multiple documents at once from
        human language to "number-language" that computers can understand.

        WHY IS THIS USEFUL?
        -----------------
        When you have many documents to process, this is much more efficient than
        converting them one by one. It's like:

        1. Cooking multiple meals in an oven at once instead of one at a time
        2. Doing a single load of laundry instead of washing each item separately

        Most embedding models can process batches more efficiently, saving time
        and computational resources.

        PARAMETERS:
        ----------
        texts (List[str]): A list of document texts to convert to numbers
                          Example: ["Document about cats", "Document about dogs"]

        RETURNS:
        -------
        List[List[float]]: A list of embeddings, one for each input document
                          Example: [
                              [0.1, 0.2, 0.3, ...],  # embedding for first document
                              [0.4, 0.5, 0.6, ...],  # embedding for second document
                          ]

        IMPORTANT NOTES:
        --------------
        1. This is an "async" method, so you must use "await" when calling it:
           embeddings = await model.embed_documents(["doc1", "doc2"])

        2. Some models (like Instructor models) use different prompting for
           documents vs. queries to get better results

        3. There may be limits on how many documents you can process at once
           (based on memory or API limitations)

        4. The order of the output embeddings matches the order of the input texts
        """
        # This is just a placeholder - actual implementation will be provided by subclasses
        pass

    @property
    @abstractmethod
    def dimension(self) -> int:
        """
        Tell us how many numbers are in each embedding vector.

        WHAT IS THIS?
        -----------
        This is a "property" (not a method) that tells you how many numbers are in
        each embedding vector produced by this model.

        Think of it like asking "how many pixels wide is this image?" - it's a
        fundamental characteristic of the embeddings this model creates.

        WHY IS THIS IMPORTANT?
        -------------------
        1. Vector stores need to know the dimension to store embeddings properly
        2. Similarity calculations need vectors of the same dimension
        3. Different models produce different sized embeddings:
           - HuggingFace e5-base: 768 dimensions
           - OpenAI text-embedding-3-small: 1024 dimensions (default)
           - OpenAI text-embedding-3-large: 1536 dimensions

        HOW TO USE IT:
        ------------
        Since this is a property (not a method), you access it like this:

        ```python
        model = HuggingFaceEmbedding()
        dim = model.dimension  # Notice: no parentheses!
        print(f"This model produces embeddings with {dim} dimensions")
        ```

        RETURNS:
        -------
        int: The number of dimensions (numbers) in each embedding vector
             Example: 768

        TECHNICAL NOTE:
        -------------
        This uses Python's @property decorator along with @abstractmethod to create
        an abstract property that subclasses must implement.
        """
        # This is just a placeholder - actual implementation will be provided by subclasses
        pass


class HuggingFaceEmbedding(EmbeddingModel):
    """
    Local embedding model that runs on your own computer using HuggingFace models.

    WHAT IS THIS?
    -----------
    This is a concrete implementation of the EmbeddingModel that uses HuggingFace's
    sentence-transformers library to generate embeddings locally on your machine.

    Think of it like having a translation tool installed directly on your computer
    instead of using an online translation service.

    ADVANTAGES:
    ---------
    1. Free to use - no API costs
    2. Works offline - no internet connection needed
    3. Privacy - your data never leaves your computer
    4. Customizable - many different models to choose from

    DISADVANTAGES:
    -----------
    1. Requires more computer resources (CPU/RAM/GPU)
    2. Initial download of models can be large (1-2GB)
    3. May be slower than cloud-based alternatives
    4. Requires installing additional Python packages

    POPULAR MODELS:
    ------------
    - "intfloat/e5-base-v2" (default): Good balance of quality and speed (768 dimensions)
    - "intfloat/e5-large-v2": Higher quality but slower and larger (1024 dimensions)
    - "hkunlp/instructor-xl": Instruction-tuned model that follows specific prompts

    HOW IT WORKS:
    ----------
    1. Loads a pre-trained model from HuggingFace into memory
    2. Uses that model to convert text into embedding vectors
    3. For instructor models, adds special prompts to guide the embedding process
    4. Uses asyncio.to_thread to run the model in a separate thread (non-blocking)

    REQUIREMENTS:
    ----------
    You must install the sentence-transformers package:
    ```
    pip install sentence-transformers
    ```
    """

    def __init__(
        self,
        model_name: str = "intfloat/e5-base-v2",
        device: str = "cpu",
        normalize_embeddings: bool = True,
        **kwargs  # This allows for additional options to be passed to the model
    ):
        """
        Set up a new HuggingFace embedding model.

        WHAT DOES THIS DO?
        ----------------
        This method prepares a HuggingFace embedding model for use by:
        1. Loading the specified model from HuggingFace's model hub
        2. Setting up configuration options like device and normalization
        3. Detecting if it's an "instructor" model that needs special prompting
        4. Determining the embedding dimension for the selected model

        PARAMETERS:
        ----------
        model_name (str): Which pre-trained model to use
            - Default: "intfloat/e5-base-v2" (good balance of quality and speed)
            - Other options: "intfloat/e5-large-v2", "hkunlp/instructor-xl"

        device (str): Which hardware to run the model on
            - "cpu": Use regular processor (slower but always works)
            - "cuda": Use NVIDIA GPU (much faster if available)
            - "mps": Use Apple M1/M2 GPU (faster on Mac)
            - Default: "cpu"

        normalize_embeddings (bool): Whether to make all vectors unit length
            - True: All vectors will have length 1.0 (better for similarity)
            - False: Keep raw vector values (may have varying magnitudes)
            - Default: True (recommended for most cases)

        **kwargs: Any additional options to pass to the SentenceTransformer model
            - These are passed directly to the underlying model
            - Example: cache_folder="./my_cache" to specify where to store models

        RAISES:
        ------
        ImportError: If the sentence-transformers package is not installed
            - Solution: Run "pip install sentence-transformers"

        TECHNICAL DETAILS:
        ---------------
        - The model is loaded when this method is called (not when embedding)
        - For instructor models, we detect "instructor" in the name and use special prompts
        - The dimension is determined by asking the model for its embedding size
        - We use logging to record which model was initialized
        """
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(model_name, device=device)
            self.normalize = normalize_embeddings
            self._dimension = self.model.get_sentence_embedding_dimension()
            self.model_name = model_name
            self.is_instructor = "instructor" in model_name.lower()
            logger.info(f"Initialized HuggingFaceEmbedding with model {model_name}")
        except ImportError:
            raise ImportError(
                "sentence-transformers package is required. "
                "Install with 'pip install sentence-transformers'"
            )

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        # Format text for instructor models
        if self.is_instructor:
            text = f"Represent this sentence for retrieval: {text}"

        # Use asyncio to run the embedding in a thread pool
        embedding = await asyncio.to_thread(
            self.model.encode,
            text,
            normalize_embeddings=self.normalize
        )

        return embedding.tolist()

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        # Format texts for instructor models
        if self.is_instructor:
            texts = [f"Represent this document for retrieval: {text}" for text in texts]

        # Use asyncio to run the embedding in a thread pool
        embeddings = await asyncio.to_thread(
            self.model.encode,
            texts,
            normalize_embeddings=self.normalize
        )

        return embeddings.tolist()

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors produced by this model.

        Returns:
            The dimension (number of elements) in each embedding vector
        """
        return self._dimension

    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        This method is provided for compatibility with the main.py startup code.

        Returns:
            The dimension of the embedding vectors
        """
        return self._dimension


class OpenAIEmbedding(EmbeddingModel):
    """
    Cloud-based embedding model that uses OpenAI's API service.

    WHAT IS THIS?
    -----------
    This is a concrete implementation of the EmbeddingModel that connects to
    OpenAI's API to generate embeddings in the cloud.

    Think of it like using Google Translate online instead of installing a
    translation program on your computer - the work happens on remote servers.

    ADVANTAGES:
    ---------
    1. No local computation - saves your computer's resources
    2. High-quality embeddings - often better than local models
    3. Simple setup - no large model files to download
    4. Consistent performance - not affected by your hardware

    DISADVANTAGES:
    -----------
    1. Costs money - pay per token processed
    2. Requires internet connection - won't work offline
    3. Privacy concerns - your data is sent to OpenAI
    4. Rate limits - can only process so much at once

    AVAILABLE MODELS:
    --------------
    - "text-embedding-3-small" (default): Good balance of quality and cost (1024 dimensions)
    - "text-embedding-3-large": Higher quality but more expensive (1536 dimensions)
    - "text-embedding-ada-002": Legacy model (1536 dimensions)

    HOW IT WORKS:
    ----------
    1. Connects to OpenAI's API using your API key
    2. Sends text to OpenAI's servers for processing
    3. Receives embedding vectors back from the API
    4. Handles authentication, error checking, and response parsing

    REQUIREMENTS:
    ----------
    1. An OpenAI API key (get one at https://platform.openai.com)
    2. The openai Python package:
       ```
       pip install openai
       ```

    PRICING (as of 2023):
    -----------------
    - text-embedding-3-small: $0.00002 per 1K tokens
    - text-embedding-3-large: $0.00013 per 1K tokens
    """

    def __init__(
        self,
        model_name: str = "text-embedding-3-small",
        api_key: Optional[str] = None,
        dimensions: Optional[int] = None,
        **kwargs  # This allows for additional options to be passed to the API
    ):
        """
        Set up a new OpenAI embedding model connected to their API.

        WHAT DOES THIS DO?
        ----------------
        This method prepares an OpenAI embedding model for use by:
        1. Setting up the API connection with your API key
        2. Configuring which model to use and its parameters
        3. Determining the embedding dimension for the selected model

        PARAMETERS:
        ----------
        model_name (str): Which OpenAI embedding model to use
            - Default: "text-embedding-3-small" (good balance of quality and cost)
            - Other options: "text-embedding-3-large", "text-embedding-ada-002"

        api_key (Optional[str]): Your OpenAI API key
            - If None (default): Will look for key in OPENAI_API_KEY environment variable
            - Otherwise: Will use the provided key directly
            - You can get a key at https://platform.openai.com

        dimensions (Optional[int]): Custom output dimension for embeddings
            - If None (default): Uses the model's default dimension
              (1024 for small, 1536 for large)
            - Otherwise: Requests a specific dimension (must be <= model's max)
            - Smaller dimensions save storage space but may lose information

        **kwargs: Any additional options to pass to the OpenAI API
            - These are passed directly to the API call
            - Example: organization="org-123" to specify your organization

        RAISES:
        ------
        ValueError: If no API key is provided or found in environment
        ImportError: If the openai package is not installed
            - Solution: Run "pip install openai"

        TECHNICAL DETAILS:
        ---------------
        - Creates an AsyncOpenAI client for non-blocking API calls
        - Sets default dimensions based on the model if not specified
        - Uses logging to record which model was initialized
        - Stores configuration for later use in embed_query and embed_documents
        """
        try:
            from openai import AsyncOpenAI
            self.api_key = api_key or os.getenv("OPENAI_API_KEY")
            if not self.api_key:
                raise ValueError("OpenAI API key is required")

            self.client = AsyncOpenAI(api_key=self.api_key)
            self.model_name = model_name
            self.dimensions = dimensions

            # Default dimensions based on model
            self._dimension = dimensions or (
                1536 if "text-embedding-ada-002" in model_name else
                1536 if "text-embedding-3-large" in model_name else
                1024 if "text-embedding-3-small" in model_name else
                1536  # Default fallback
            )
            logger.info(f"Initialized OpenAIEmbedding with model {model_name}")
        except ImportError:
            raise ImportError(
                "openai package is required. "
                "Install with 'pip install openai'"
            )

    async def embed_query(self, text: str) -> List[float]:
        """
        Convert a query text to a vector representation.

        Args:
            text: The query text to embed

        Returns:
            A list of floats representing the embedding vector
        """
        params = {"model": self.model_name, "input": text}
        if self.dimensions:
            params["dimensions"] = self.dimensions

        response = await self.client.embeddings.create(**params)

        return response.data[0].embedding

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Convert a list of document texts to vector representations.

        Args:
            texts: List of document texts to embed

        Returns:
            A list of embedding vectors, one for each input text
        """
        params = {"model": self.model_name, "input": texts}
        if self.dimensions:
            params["dimensions"] = self.dimensions

        response = await self.client.embeddings.create(**params)

        # Sort by index to ensure order matches input
        sorted_data = sorted(response.data, key=lambda x: x.index)
        return [item.embedding for item in sorted_data]

    @property
    def dimension(self) -> int:
        """
        Get the dimension of the embedding vectors produced by this model.

        Returns:
            The dimension (number of elements) in each embedding vector
        """
        return self._dimension

    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        This method is provided for compatibility with the main.py startup code.

        Returns:
            The dimension of the embedding vectors
        """
        return self._dimension


def get_embedding_model() -> EmbeddingModel:
    """
    Get an embedding model based on the application configuration.

    This function reads the configuration from the application settings
    and creates an appropriate embedding model.

    Returns:
        An initialized embedding model ready for use
    """
    settings = get_settings()

    # Get embedding model type from settings
    model_type = settings.DEFAULT_EMBEDDING_MODEL
    model_name = settings.EMBEDDING_MODEL

    logger.info(f"Creating embedding model of type {model_type} with model {model_name}")

    # Create the embedding model using the factory
    return EmbeddingFactory.create(
        provider=model_type,
        model_name=model_name,
        fallback=True
    )


class EmbeddingFactory:
    """
    Smart factory that creates embedding models with automatic fallback options.

    WHAT IS THIS?
    -----------
    This is a "factory" class that helps you create embedding models without
    worrying about which specific implementation to use or what happens if
    your first choice fails.

    Think of it like a smart assistant that knows how to set up different
    embedding tools and can switch to Plan B if Plan A doesn't work.

    WHY USE THIS INSTEAD OF DIRECT CREATION?
    -------------------------------------
    1. Automatic Fallback: If your first choice fails (e.g., missing dependencies),
       it automatically tries another option

    2. Simplified Creation: One consistent way to create any type of embedding model

    3. Future-Proof: New embedding providers can be added without changing how
       you create models

    4. Error Handling: Better error messages and graceful degradation

    HOW IT WORKS:
    ----------
    1. You specify your preferred embedding provider (huggingface or openai)
    2. The factory tries to create that type of embedding model
    3. If it fails and fallback is enabled, it tries an alternative provider
    4. If all options fail, it raises an informative error

    USAGE EXAMPLE:
    ------------
    ```python
    # Try HuggingFace first, fall back to OpenAI if needed
    model = EmbeddingFactory.create(
        provider="huggingface",  # First choice
        fallback=True,           # Enable automatic fallback
        model_name="intfloat/e5-base-v2"  # Model-specific parameter
    )

    # Use the model without worrying about which type it actually is
    embedding = await model.embed_query("What is RAG?")
    ```
    """

    @staticmethod
    def create(
        provider: Literal["huggingface", "openai"] = "huggingface",
        fallback: bool = True,
        **kwargs  # These are passed to the specific embedding model constructor
    ) -> EmbeddingModel:
        """
        Create an embedding model with automatic fallback options.

        WHAT DOES THIS DO?
        ----------------
        This method creates an embedding model of your preferred type, with
        the option to automatically try a different type if your first choice
        fails (for example, if required packages aren't installed).

        It's like ordering a meal at a restaurant - if they're out of your
        first choice, they can suggest an alternative instead of just saying "no".

        PARAMETERS:
        ----------
        provider (Literal["huggingface", "openai"]): Which type of embedding model to create
            - "huggingface": Local model using sentence-transformers (default)
            - "openai": Cloud-based model using OpenAI's API

        fallback (bool): Whether to try alternatives if first choice fails
            - True: Try other providers if the requested one fails (default)
            - False: Raise an error if the requested provider fails

        **kwargs: Options to pass to the specific embedding model
            - These are forwarded to the constructor of the chosen model
            - Common options:
              * model_name: Name of the specific model to use
              * device: For HuggingFace, which hardware to use (cpu/cuda/mps)
              * api_key: For OpenAI, your API key
              * dimensions: For OpenAI, custom embedding dimensions

        RETURNS:
        -------
        EmbeddingModel: A ready-to-use embedding model
            - This will be an instance of a specific implementation
              (HuggingFaceEmbedding or OpenAIEmbedding)
            - You can use it without knowing which specific type it is

        RAISES:
        ------
        ValueError: If the provider is unknown and fallback=False
            - This happens if you specify a provider that doesn't exist

        ImportError: If required dependencies are missing and fallback=False
            - For HuggingFace: If sentence-transformers isn't installed
            - For OpenAI: If openai package isn't installed
            - For both: If neither package is installed

        EXAMPLES:
        --------
        1. Create a HuggingFace model (default):
           ```python
           model = EmbeddingFactory.create(model_name="intfloat/e5-base-v2")
           ```

        2. Create an OpenAI model with specific dimensions:
           ```python
           model = EmbeddingFactory.create(
               provider="openai",
               model_name="text-embedding-3-small",
               dimensions=512
           )
           ```

        3. Create a model with fallback disabled:
           ```python
           model = EmbeddingFactory.create(
               provider="huggingface",
               fallback=False  # Will raise error if HuggingFace fails
           )
           ```
        """
        try:
            if provider == "huggingface":
                return HuggingFaceEmbedding(**kwargs)
            elif provider == "openai":
                return OpenAIEmbedding(**kwargs)
            else:
                if fallback:
                    logger.warning(
                        f"Unknown provider '{provider}', falling back to huggingface"
                    )
                    return HuggingFaceEmbedding(**kwargs)
                else:
                    raise ValueError(f"Unknown embedding provider: {provider}")
        except ImportError as e:
            if not fallback:
                raise

            logger.warning(
                f"Provider '{provider}' unavailable: {str(e)}. "
                "Trying fallback providers."
            )

            # Try OpenAI if HuggingFace fails
            if provider == "huggingface":
                try:
                    return OpenAIEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )

            # Try HuggingFace if OpenAI fails
            elif provider == "openai":
                try:
                    return HuggingFaceEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )
