"""
Retriever Component for RAG System

This module implements the retriever component of the RAG system, which is responsible for:
1. Processing and potentially rewriting user queries
2. Finding relevant documents using multiple search strategies
3. Combining results from different search methods
4. Ensuring retrieved context fits within the LLM's context window

The retriever coordinates between the query analyzer and knowledge base service
to provide enhanced retrieval capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

# Import optional dependencies
try:
    HAS_TIKTOKEN = True
    import tiktoken
except ImportError:
    HAS_TIKTOKEN = False

# Import timeout utilities
from app.core.timeout import with_timeout_and_retry
from app.rag.timeout import (
    with_embedding_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

# Set up logging
logger = logging.getLogger(__name__)


class Retriever(ABC):
    """
    Base class for document retrieval.

    This abstract class defines the interface for all retrievers.
    Concrete implementations should override the retrieve method.
    """

    @abstractmethod
    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query.

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        raise NotImplementedError("Subclasses must implement retrieve")


class HybridRetriever(Retriever):
    """
    Retriever that combines vector similarity and keyword search.

    This class coordinates between the query analyzer and knowledge base service
    to provide enhanced retrieval results using multiple search methods.
    """

    def __init__(
        self,
        query_analyzer,
        knowledge_base_service,
        default_vector_weight: float = 0.7,
        default_keyword_weight: float = 0.3,
        **kwargs
    ):
        """
        Initialize the hybrid retriever.

        Args:
            query_analyzer: The query analyzer for determining retrieval strategies
            knowledge_base_service: The knowledge base service for search operations
            default_vector_weight: Default weight for vector similarity results (0.0 to 1.0)
            default_keyword_weight: Default weight for keyword search results (0.0 to 1.0)
            **kwargs: Additional arguments
        """
        self.query_analyzer = query_analyzer
        self.knowledge_base_service = knowledge_base_service

        # Ensure weights sum to 1.0
        total_weight = default_vector_weight + default_keyword_weight
        self.default_vector_weight = default_vector_weight / total_weight
        self.default_keyword_weight = default_keyword_weight / total_weight

        # Initialize optional components
        self.context_window_manager = kwargs.get("context_window_manager")
        self.query_rewriter = kwargs.get("query_rewriter")

    @with_hybrid_search_timeout_decorator(
        timeout_seconds=12,  # 12 seconds timeout for hybrid search
        max_attempts=2,      # 2 retry attempts
        fallback_func=None   # No fallback function, will return empty list on failure
    )
    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents using hybrid search.

        This method:
        1. Analyzes the query if no retrieval strategy is provided
        2. Applies the retrieval strategy to guide the search
        3. Performs search using the knowledge base service
        4. Enhances results with additional processing if needed

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        try:
            # Rewrite query if query rewriter is available
            processed_query = query
            if self.query_rewriter:
                try:
                    # Create context for query rewriting
                    rewrite_context = {}

                    # Add retrieval strategy to context if available
                    if retrieval_strategy:
                        rewrite_context["retrieval_strategy"] = retrieval_strategy

                        # Extract department from filters if available
                        if "filters" in retrieval_strategy and "department" in retrieval_strategy["filters"]:
                            rewrite_context["department"] = retrieval_strategy["filters"]["department"]

                    # Call the enhanced query rewriter with context
                    processed_query = await with_timeout_and_retry(
                        self.query_rewriter.rewrite_query,
                        query,
                        rewrite_context,
                        timeout_seconds=5,  # 5 seconds timeout for query rewriting
                        operation_name="query_rewriter.rewrite_query",
                        max_attempts=2      # Try twice before giving up
                    )
                    logger.debug(f"Rewrote query: '{query}' -> '{processed_query}'")
                except Exception as e:
                    logger.warning(f"Query rewriting failed: {e}. Using original query.")
                    processed_query = query

            # Get retrieval strategy if not provided
            if retrieval_strategy is None:
                try:
                    analysis = await with_timeout_and_retry(
                        self.query_analyzer.analyze,
                        processed_query,
                        timeout_seconds=5,  # 5 seconds timeout for query analysis
                        operation_name="query_analyzer.analyze",
                        max_attempts=1      # Only try once, fall back to default strategy
                    )
                    retrieval_strategy = analysis.get("retrieval_strategy", {})
                    logger.debug(f"Query analysis: {analysis}")
                except Exception as e:
                    logger.warning(f"Query analysis failed: {e}. Using default strategy.")
                    retrieval_strategy = {}

            # Extract search parameters from strategy
            search_type = retrieval_strategy.get("search_type", "hybrid")
            strategy_limit = retrieval_strategy.get("limit", limit)
            strategy_filters = retrieval_strategy.get("filters", {})
            vector_weight = retrieval_strategy.get("weights", {}).get("vector", self.default_vector_weight)
            keyword_weight = retrieval_strategy.get("weights", {}).get("keyword", self.default_keyword_weight)

            # Merge filters - prioritize department_id from strategy_filters
            merged_filters = {}

            # Start with user-provided filters
            if filters:
                merged_filters.update(filters)

            # Add strategy filters, but handle department_id specially
            has_department_filter = False
            for key, value in strategy_filters.items():
                if key == "department_id" and isinstance(value, dict) and "$in" in value:
                    # Convert department_id filter to metadata.department filter for PostgreSQL
                    merged_filters["metadata.department"] = value
                    has_department_filter = True
                elif key == "department_id" and isinstance(value, dict) and "$eq" in value:
                    # Convert department_id filter to metadata.department filter for PostgreSQL
                    merged_filters["metadata.department"] = {"$eq": value["$eq"]}
                    has_department_filter = True
                elif key == "department":
                    # Convert department filter to metadata.department filter for PostgreSQL
                    if isinstance(value, dict) and "$in" in value:
                        merged_filters["metadata.department"] = value
                    else:
                        merged_filters["metadata.department"] = {"$eq": value}
                    has_department_filter = True
                else:
                    merged_filters[key] = value

            # Remove any conflicting department filters
            if has_department_filter and "department_id" in merged_filters:
                del merged_filters["department_id"]
            if has_department_filter and "department" in merged_filters:
                del merged_filters["department"]

            logger.debug(f"Merged filters: {merged_filters}")

            # Perform search using knowledge base service with timeout
            results = await with_timeout_and_retry(
                self.knowledge_base_service.search,
                query=processed_query,
                collection="knowledge",
                limit=strategy_limit,
                search_type=search_type,
                filters=merged_filters,
                vector_weight=vector_weight,
                keyword_weight=keyword_weight,
                timeout_seconds=10,  # 10 seconds timeout for search
                operation_name=f"knowledge_base_service.search.{search_type}",
                max_attempts=2       # Try twice before giving up
            )

            # Fit to context window if manager is available
            if self.context_window_manager and results:
                try:
                    results = await with_timeout_and_retry(
                        self.context_window_manager.fit_to_context_window,
                        query=processed_query,
                        documents=results,
                        timeout_seconds=3,  # 3 seconds timeout for context window fitting
                        operation_name="context_window_manager.fit_to_context_window",
                        max_attempts=1      # Only try once, fall back to original results
                    )
                except Exception as e:
                    logger.warning(f"Context window fitting failed: {e}. Using original results.")

            return results

        except Exception as e:
            logger.error(f"Retrieval failed: {e}", exc_info=True)
            # Return empty results on failure
            return []


class ContextWindowManager:
    """
    Manages context window size for LLM input.

    This class ensures that retrieved documents fit within the LLM's
    context window by truncating or summarizing as needed.
    """

    def __init__(
        self,
        llm_adapter,
        max_tokens: int = 4000,
        token_buffer: int = 1000
    ):
        """
        Initialize the context window manager.

        Args:
            llm_adapter: The LLM adapter to use for token counting and summarization
            max_tokens: Maximum tokens allowed in the context window
            token_buffer: Buffer tokens to reserve for the query and response
        """
        self.llm_adapter = llm_adapter
        self.max_tokens = max_tokens
        self.token_buffer = token_buffer

        # Import tiktoken for token counting if available
        self.has_tiktoken = HAS_TIKTOKEN
        if self.has_tiktoken:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")

    async def fit_to_context_window(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Ensure documents fit within the context window.

        Args:
            query: The query text
            documents: The retrieved documents

        Returns:
            Documents that fit within the context window
        """
        # Estimate query tokens
        query_tokens = await self._count_tokens(query)

        # Calculate available tokens for documents
        available_tokens = self.max_tokens - query_tokens - self.token_buffer

        # Estimate tokens for each document
        documents_with_tokens = []
        for doc in documents:
            doc_tokens = await self._count_tokens(doc.get("text", ""))
            documents_with_tokens.append({
                **doc,
                "tokens": doc_tokens
            })

        # Sort by score (descending)
        documents_with_tokens.sort(key=lambda x: x.get("score", 0), reverse=True)

        # Fit documents to context window
        fitted_documents = []
        total_tokens = 0

        for doc in documents_with_tokens:
            doc_tokens = doc["tokens"]

            if total_tokens + doc_tokens <= available_tokens:
                # Document fits entirely
                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_documents.append(fitted_doc)
                total_tokens += doc_tokens
            elif total_tokens < available_tokens:
                # Document needs truncation
                truncated_text = await self._truncate_text(
                    doc.get("text", ""),
                    available_tokens - total_tokens
                )

                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_doc["text"] = truncated_text
                fitted_doc["truncated"] = True
                fitted_documents.append(fitted_doc)

                # Update total tokens
                truncated_tokens = await self._count_tokens(truncated_text)
                total_tokens += truncated_tokens
            else:
                # No more space available
                break

        return fitted_documents

    async def _count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.

        Args:
            text: The text to count tokens for

        Returns:
            Number of tokens
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate token counting
            return len(self.tokenizer.encode(text))
        else:
            # Fall back to LLM adapter for token counting with timeout
            try:
                tokens_result = await with_timeout_and_retry(
                    self.llm_adapter.get_token_count,
                    [{"role": "user", "content": text}],
                    timeout_seconds=2,  # 2 seconds timeout for token counting
                    operation_name="llm_adapter.get_token_count",
                    max_attempts=1      # Only try once, fall back to character estimation
                )
                tokens, _ = tokens_result
                return tokens
            except Exception as e:
                logger.warning(f"Token counting with LLM adapter failed: {e}. Using character estimation.")
                # Last resort: estimate based on characters
                return len(text) // 4

    async def _truncate_text(self, text: str, max_tokens: int) -> str:
        """
        Truncate text to fit within max_tokens.

        Args:
            text: The text to truncate
            max_tokens: Maximum tokens allowed

        Returns:
            Truncated text
        """
        if self.has_tiktoken:
            # Use tiktoken for accurate truncation
            tokens = self.tokenizer.encode(text)
            if len(tokens) <= max_tokens:
                return text

            truncated_tokens = tokens[:max_tokens]
            return self.tokenizer.decode(truncated_tokens)
        else:
            # Estimate based on characters
            chars_per_token = 4
            max_chars = max_tokens * chars_per_token

            if len(text) <= max_chars:
                return text

            return text[:max_chars] + "..."


class QueryRewriter:
    """
    Rewrites queries to improve retrieval performance.

    This class uses the LLM to rewrite queries for better retrieval results.
    It implements several strategies for query enhancement:
    1. Key term extraction and expansion
    2. Context-aware query reformulation
    3. Fallback mechanisms for when rewriting fails
    """

    def __init__(self, llm_adapter, embedding_model=None, knowledge_base_service=None):
        """
        Initialize the query rewriter.

        Args:
            llm_adapter: The LLM adapter to use for query rewriting
            embedding_model: Optional embedding model for semantic analysis
            knowledge_base_service: Optional knowledge base service for context-aware rewriting
        """
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.knowledge_base_service = knowledge_base_service

        # Common terms to avoid expanding (stopwords, common business terms)
        self.common_terms = {
            "the", "and", "or", "a", "an", "in", "on", "at", "to", "for", "with", "by",
            "business", "company", "team", "project", "report", "meeting", "email",
            "plan", "strategy", "goal", "objective", "target", "result", "outcome"
        }

        # Common acronyms and their expansions
        self.acronyms = {
            "roi": "return on investment",
            "kpi": "key performance indicator",
            "ctr": "click-through rate",
            "cac": "customer acquisition cost",
            "ltv": "lifetime value",
            "arpu": "average revenue per user",
            "mrr": "monthly recurring revenue",
            "arr": "annual recurring revenue",
            "cro": "conversion rate optimization",
            "seo": "search engine optimization",
            "sem": "search engine marketing",
            "ppc": "pay per click",
            "cpa": "cost per acquisition",
            "cpc": "cost per click",
            "cpm": "cost per mille",
            "b2b": "business to business",
            "b2c": "business to consumer",
            "d2c": "direct to consumer",
            "saas": "software as a service",
            "q1": "first quarter",
            "q2": "second quarter",
            "q3": "third quarter",
            "q4": "fourth quarter",
            "fy": "fiscal year",
            "yoy": "year over year",
            "mom": "month over month",
            "qoq": "quarter over quarter",
            "ebitda": "earnings before interest taxes depreciation and amortization",
            "cogs": "cost of goods sold",
            "p&l": "profit and loss",
            "r&d": "research and development",
            "hr": "human resources",
            "pr": "public relations",
            "ceo": "chief executive officer",
            "cfo": "chief financial officer",
            "cmo": "chief marketing officer",
            "cto": "chief technology officer",
            "coo": "chief operating officer"
        }

    @with_embedding_timeout_decorator(timeout_seconds=5, max_attempts=2)
    async def rewrite_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """
        Rewrite a query to improve retrieval performance.

        This method uses a multi-strategy approach:
        1. First tries simple rule-based enhancements (acronym expansion, etc.)
        2. Then attempts LLM-based rewriting if available
        3. Falls back to the original query with minimal enhancements if needed

        Args:
            query: The original query
            context: Optional context to guide rewriting (e.g., user info, conversation history)

        Returns:
            Rewritten query
        """
        # Start with the original query
        enhanced_query = query.strip()

        try:
            # Step 1: Apply rule-based enhancements
            rule_based_query = await self._apply_rule_based_enhancements(enhanced_query)

            # If rule-based enhancements made significant changes, use that
            if self._is_significantly_different(enhanced_query, rule_based_query):
                enhanced_query = rule_based_query
                logger.debug(f"Rule-based query enhancement: '{query}' -> '{enhanced_query}'")

            # Step 2: Try LLM-based rewriting
            llm_rewritten_query = await self._llm_rewrite_query(enhanced_query, context)

            # If LLM rewriting was successful and made significant changes, use that
            if llm_rewritten_query and self._is_significantly_different(enhanced_query, llm_rewritten_query):
                enhanced_query = llm_rewritten_query
                logger.debug(f"LLM-based query rewriting: '{query}' -> '{enhanced_query}'")

            # Step 3: Try context-aware rewriting if knowledge base service is available
            if self.knowledge_base_service and context:
                context_aware_query = await self._context_aware_rewrite(enhanced_query, context)

                # If context-aware rewriting made significant changes, use that
                if context_aware_query and self._is_significantly_different(enhanced_query, context_aware_query):
                    enhanced_query = context_aware_query
                    logger.debug(f"Context-aware query rewriting: '{query}' -> '{enhanced_query}'")

            return enhanced_query

        except Exception as e:
            logger.error(f"Error rewriting query: {e}", exc_info=True)
            # Fall back to original query with basic enhancements
            return await self._fallback_rewrite(query)

    async def _apply_rule_based_enhancements(self, query: str) -> str:
        """
        Apply rule-based enhancements to the query.

        Args:
            query: The original query

        Returns:
            Enhanced query
        """
        # Convert to lowercase for processing
        query_lower = query.lower()

        # Check for acronyms directly in the query
        words = query_lower.split()
        has_expanded_acronyms = False
        expanded_acronyms = []

        for word in words:
            # Clean the word from punctuation
            clean_word = word.strip(".,?!:;()")
            if clean_word in self.acronyms:
                expanded_acronyms.append(f"{clean_word} ({self.acronyms[clean_word]})")
                has_expanded_acronyms = True
            else:
                expanded_acronyms.append(word)

        # If we expanded any acronyms, use the expanded version
        if has_expanded_acronyms:
            enhanced_query = f"{query} ({' '.join(self.acronyms[w.strip('.,?!:;()')] for w in words if w.strip('.,?!:;()') in self.acronyms)})"
        else:
            # Extract terms from the query
            terms = self._extract_terms(query_lower)

            # Expand acronyms in extracted terms
            expanded_terms = []
            for term in terms:
                if term.lower() in self.acronyms:
                    expanded_terms.append(self.acronyms[term.lower()])
                else:
                    expanded_terms.append(term)

            # Combine original query with expanded terms
            if len(expanded_terms) > len(terms):
                enhanced_query = f"{query} ({' '.join(expanded_terms)})"
            else:
                enhanced_query = query

        return enhanced_query

    async def _llm_rewrite_query(self, query: str, context: Dict[str, Any] = None) -> Optional[str]:
        """
        Use the LLM to rewrite the query.

        Args:
            query: The query to rewrite
            context: Optional context to guide rewriting

        Returns:
            Rewritten query or None if rewriting failed
        """
        # Construct context-aware prompt if context is provided
        context_str = ""
        if context:
            if "user_info" in context:
                context_str += f"\nUser Information: {context['user_info']}"
            if "conversation_history" in context:
                context_str += f"\nConversation History: {context['conversation_history']}"
            if "department" in context:
                context_str += f"\nDepartment Focus: {context['department']}"

        prompt = [
            {
                "role": "system",
                "content": (
                    "You are a query rewriting assistant. Your task is to rewrite search queries "
                    "to improve retrieval performance. Focus on:"
                    "\n1. Expanding acronyms"
                    "\n2. Adding synonyms for important terms"
                    "\n3. Making implicit information explicit"
                    "\n4. Removing unnecessary words"
                    "\n5. Focusing on key concepts"
                    "\n6. Extracting specific entities, metrics, or time periods"
                    "\n7. Adding domain-specific terminology"
                    f"{context_str}"
                    "\nProvide ONLY the rewritten query without explanation."
                )
            },
            {
                "role": "user",
                "content": f"Original query: {query}\n\nRewritten query:"
            }
        ]

        try:
            # Use timeout and retry for LLM call
            rewritten_query = await with_timeout_and_retry(
                self.llm_adapter.chat,
                prompt,
                timeout_seconds=5,  # 5 seconds timeout for LLM call
                operation_name="llm_adapter.chat_for_query_rewrite",
                max_attempts=2      # Try twice before giving up
            )
            return rewritten_query.strip()
        except Exception as e:
            logger.warning(f"LLM-based query rewriting failed: {e}")
            return None

    async def _context_aware_rewrite(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Rewrite the query based on available context.

        Args:
            query: The query to rewrite
            context: Context to guide rewriting

        Returns:
            Context-aware rewritten query or None if rewriting failed
        """
        if not self.knowledge_base_service:
            return None

        try:
            # Extract department if available
            department = context.get("department")

            # Perform a preliminary search to get relevant terms
            filters = {"department": department} if department else {}
            preliminary_results = await with_timeout_and_retry(
                self.knowledge_base_service.search,
                query=query,
                collection="knowledge",
                limit=3,
                search_type="keyword",
                filters=filters,
                timeout_seconds=3,
                operation_name="knowledge_base_service.search_for_query_rewrite",
                max_attempts=1
            )

            if not preliminary_results:
                return None

            # Extract key terms from the preliminary results
            key_terms = set()
            for result in preliminary_results:
                # Extract terms from the document title and content
                if "metadata" in result and "title" in result["metadata"]:
                    title_terms = self._extract_terms(result["metadata"]["title"])
                    key_terms.update([t for t in title_terms if t.lower() not in self.common_terms])

                if "text" in result:
                    # Only use the first 200 characters to extract key terms
                    content_sample = result["text"][:200]
                    content_terms = self._extract_terms(content_sample)
                    key_terms.update([t for t in content_terms if t.lower() not in self.common_terms])

            # Filter out terms already in the query
            query_terms = set(self._extract_terms(query.lower()))
            new_terms = [term for term in key_terms if term.lower() not in query_terms]

            # If we found new terms, enhance the query
            if new_terms:
                # Take at most 3 new terms to avoid query explosion
                selected_terms = new_terms[:3]
                enhanced_query = f"{query} {' '.join(selected_terms)}"
                return enhanced_query

            return None

        except Exception as e:
            logger.warning(f"Context-aware query rewriting failed: {e}")
            return None

    async def _fallback_rewrite(self, query: str) -> str:
        """
        Apply minimal enhancements when other methods fail.

        Args:
            query: The original query

        Returns:
            Minimally enhanced query
        """
        # Just expand acronyms as a last resort
        query_lower = query.lower()
        words = query_lower.split()

        # Check for acronyms
        for i, word in enumerate(words):
            if word in self.acronyms:
                words[i] = self.acronyms[word]

        # If changes were made, return the enhanced query
        enhanced_query = " ".join(words)
        if enhanced_query != query_lower:
            return f"{query} ({enhanced_query})"

        # Otherwise, return the original query
        return query

    def _extract_terms(self, text: str) -> List[str]:
        """
        Extract meaningful terms from text.

        Args:
            text: The text to extract terms from

        Returns:
            List of extracted terms
        """
        # Simple whitespace tokenization
        words = text.split()

        # Clean words from punctuation
        cleaned_words = [word.strip(".,?!:;()") for word in words]

        # Filter out common terms and very short terms
        filtered_words = [word for word in cleaned_words if word.lower() not in self.common_terms and len(word) > 2]

        # Extract multi-word phrases (simple approach)
        phrases = []
        for i in range(len(cleaned_words) - 1):
            word1 = cleaned_words[i].lower()
            word2 = cleaned_words[i+1].lower()

            if word1 not in self.common_terms and word2 not in self.common_terms and len(word1) > 2 and len(word2) > 2:
                phrases.append(f"{cleaned_words[i]} {cleaned_words[i+1]}")

        # Extract three-word phrases for better coverage
        for i in range(len(cleaned_words) - 2):
            word1 = cleaned_words[i].lower()
            word2 = cleaned_words[i+1].lower()
            word3 = cleaned_words[i+2].lower()

            if (word1 not in self.common_terms and
                word3 not in self.common_terms and
                len(word1) > 2 and len(word3) > 2):
                phrases.append(f"{cleaned_words[i]} {cleaned_words[i+1]} {cleaned_words[i+2]}")

        # Combine single words and phrases
        all_terms = filtered_words + phrases

        # Remove duplicates and limit the number of terms
        unique_terms = list(dict.fromkeys(all_terms))
        return unique_terms[:15]  # Limit to 15 terms

    def _is_significantly_different(self, original: str, rewritten: str) -> bool:
        """
        Check if the rewritten query is significantly different from the original.

        Args:
            original: The original query
            rewritten: The rewritten query

        Returns:
            True if significantly different, False otherwise
        """
        if not rewritten or rewritten == original:
            return False

        # Check if length difference is significant
        if len(rewritten) < len(original) * 0.8 or len(rewritten) > len(original) * 2.0:
            return True

        # Check if term difference is significant
        original_terms = set(self._extract_terms(original.lower()))
        rewritten_terms = set(self._extract_terms(rewritten.lower()))

        # Calculate Jaccard similarity
        intersection = len(original_terms.intersection(rewritten_terms))
        union = len(original_terms.union(rewritten_terms))

        if union == 0:
            return False

        similarity = intersection / union

        # If similarity is low, queries are significantly different
        return similarity < 0.7