"""
Cross-encoder reranker for improving search result relevance.

This module provides a reranker that uses a cross-encoder model to rerank
search results based on their relevance to the query.
"""

import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple

import numpy as np
from abc import ABC, abstractmethod

from app.core.timeout import with_timeout_and_retry

# Configure logging
logger = logging.getLogger(__name__)


class Reranker(ABC):
    """
    Abstract base class for rerankers.

    This class defines the interface that all reranker implementations must follow.
    """

    @abstractmethod
    async def rerank(
        self,
        query: str,
        results: List[Dict[str, Any]],
        top_n: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Rerank search results based on their relevance to the query.

        Args:
            query: The search query
            results: The search results to rerank
            top_n: Optional number of results to return after reranking

        Returns:
            Reranked search results
        """
        pass


class CrossEncoderReranker(Reranker):
    """
    Reranker that uses a cross-encoder model to rerank search results.

    This class uses a cross-encoder model to compute relevance scores
    between the query and each search result, then reranks the results
    based on these scores.
    """

    def __init__(
        self,
        model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2",
        device: str = "cpu",
        batch_size: int = 16,
        **kwargs
    ):
        """
        Initialize the cross-encoder reranker.

        Args:
            model_name: The name of the cross-encoder model to use
            device: The device to run the model on ("cpu" or "cuda")
            batch_size: The batch size for inference
            **kwargs: Additional arguments for the cross-encoder model
        """
        self.model_name = model_name
        self.device = device
        self.batch_size = batch_size
        self.kwargs = kwargs
        self.model = None
        self._initialize_model()

    def _initialize_model(self):
        """
        Initialize the cross-encoder model.
        """
        try:
            from sentence_transformers import CrossEncoder
            self.model = CrossEncoder(
                self.model_name,
                device=self.device,
                **self.kwargs
            )
            logger.info(f"Initialized cross-encoder model: {self.model_name}")
        except ImportError:
            logger.error(
                "Failed to import sentence_transformers. "
                "Please install it with: pip install sentence-transformers"
            )
            raise
        except Exception as e:
            logger.error(f"Failed to initialize cross-encoder model: {e}")
            raise

    async def rerank(
        self,
        query: str,
        results: List[Dict[str, Any]],
        top_n: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Rerank search results based on their relevance to the query.

        Args:
            query: The search query
            results: The search results to rerank
            top_n: Optional number of results to return after reranking

        Returns:
            Reranked search results
        """
        if not results:
            return []

        if not self.model:
            logger.warning("Cross-encoder model not initialized. Returning original results.")
            return results

        # Default to returning all results if top_n is not specified
        if top_n is None:
            top_n = len(results)

        try:
            # Prepare query-document pairs for the cross-encoder
            pairs = [(query, result["text"]) for result in results]

            # Compute relevance scores with timeout and retry
            scores = await with_timeout_and_retry(
                self._compute_scores,
                pairs,
                timeout_seconds=10,
                operation_name="cross_encoder_rerank",
                max_attempts=2
            )

            # Add cross-encoder scores to results
            for i, score in enumerate(scores):
                results[i]["cross_encoder_score"] = float(score)
                # Combine with original score for a weighted final score
                # The weight can be adjusted based on the relative importance
                # of the original score vs. the cross-encoder score
                original_score = results[i]["score"]
                results[i]["final_score"] = 0.3 * original_score + 0.7 * float(score)

            # Sort by the final score
            reranked_results = sorted(
                results,
                key=lambda x: x["final_score"],
                reverse=True
            )

            # Return the top N results
            return reranked_results[:top_n]

        except Exception as e:
            logger.error(f"Error during reranking: {e}")
            # Fall back to the original results
            return results[:top_n]

    async def _compute_scores(self, pairs: List[Tuple[str, str]]) -> List[float]:
        """
        Compute relevance scores for query-document pairs.

        Args:
            pairs: List of (query, document) pairs

        Returns:
            List of relevance scores
        """
        # This is a synchronous operation, but we're wrapping it in an async
        # function for API consistency and to allow for timeout and retry
        scores = self.model.predict(
            pairs,
            batch_size=self.batch_size,
            show_progress_bar=False
        )
        return scores


class DummyReranker(Reranker):
    """
    Dummy reranker that doesn't actually rerank results.

    This class is useful for testing or when reranking is not needed.
    """

    async def rerank(
        self,
        query: str,
        results: List[Dict[str, Any]],
        top_n: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Return the original results without reranking.

        Args:
            query: The search query
            results: The search results
            top_n: Optional number of results to return

        Returns:
            The original search results, optionally limited to top_n
        """
        if top_n is None:
            return results
        return results[:top_n]


def get_reranker(
    reranker_type: str = "cross-encoder",
    **kwargs
) -> Reranker:
    """
    Factory function to create a reranker.

    Args:
        reranker_type: The type of reranker to create
        **kwargs: Additional arguments for the reranker

    Returns:
        A reranker instance

    Raises:
        ValueError: If the specified reranker type is not supported
    """
    if reranker_type.lower() == "cross-encoder":
        return CrossEncoderReranker(**kwargs)
    elif reranker_type.lower() == "dummy":
        return DummyReranker()
    else:
        raise ValueError(f"Unsupported reranker type: {reranker_type}")


__all__ = [
    "Reranker",
    "CrossEncoderReranker",
    "DummyReranker",
    "get_reranker",
]
