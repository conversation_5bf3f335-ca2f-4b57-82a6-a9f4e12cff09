"""
Generator Component for RAG System

This module implements the generator component of the RAG system, which is responsible for:
1. Formatting retrieved documents for inclusion in prompts
2. Constructing prompts for the LLM
3. Managing the interaction with the LLM
4. Generating responses with proper citations

The generator is the final piece of the RAG pipeline, combining retrieved context
with LLM generation to produce contextually informed responses.
"""

import logging
import re
import tiktoken
from typing import Dict, List, Any, Union, AsyncIterator, Tuple, Optional, Callable
from abc import ABC, abstractmethod
from functools import lru_cache

# Set up logging
logger = logging.getLogger(__name__)


@lru_cache(maxsize=20)
def get_tokenizer(model_name: str = "gpt-3.5-turbo"):
    """
    Get a tokenizer for the specified model.

    Args:
        model_name: The name of the model to get a tokenizer for

    Returns:
        A tokenizer for the specified model
    """
    try:
        # Try to get the encoding for the specific model
        return tiktoken.encoding_for_model(model_name)
    except KeyError:
        # Map model families to appropriate encodings
        if "gpt-4" in model_name:
            return tiktoken.encoding_for_model("gpt-4")
        elif "gpt-3.5" in model_name:
            return tiktoken.encoding_for_model("gpt-3.5-turbo")
        elif "claude" in model_name.lower():
            # Claude models use cl100k_base
            return tiktoken.get_encoding("cl100k_base")
        else:
            # Fall back to cl100k_base for newer models
            return tiktoken.get_encoding("cl100k_base")


# Cache for frequently used text token counts
token_count_cache = {}
MAX_CACHE_SIZE = 1000
MAX_CACHE_TEXT_LENGTH = 5000  # Only cache texts shorter than this


def count_tokens(text: str, model_name: str = "gpt-3.5-turbo") -> int:
    """
    Count the number of tokens in a text string.

    Uses a cache for frequently counted texts to improve performance.

    Args:
        text: The text to count tokens for
        model_name: The name of the model to count tokens for

    Returns:
        The number of tokens in the text
    """
    # For very long texts, don't use the cache
    if len(text) > MAX_CACHE_TEXT_LENGTH:
        tokenizer = get_tokenizer(model_name)
        return len(tokenizer.encode(text))

    # Use the cache for shorter texts
    cache_key = f"{model_name}:{hash(text)}"
    if cache_key in token_count_cache:
        return token_count_cache[cache_key]

    # Not in cache, compute and store
    tokenizer = get_tokenizer(model_name)
    count = len(tokenizer.encode(text))

    # Manage cache size
    if len(token_count_cache) >= MAX_CACHE_SIZE:
        # Remove a random item to avoid lock contention
        # In a production system, you might use LRU eviction instead
        try:
            token_count_cache.pop(next(iter(token_count_cache)))
        except (StopIteration, KeyError):
            pass

    token_count_cache[cache_key] = count
    return count


def truncate_to_token_limit(text: str, max_tokens: int, model_name: str = "gpt-3.5-turbo") -> Tuple[str, bool]:
    """
    Truncate text to a maximum number of tokens.

    Uses an optimized binary search approach for large texts to avoid
    encoding/decoding the entire text multiple times.

    Args:
        text: The text to truncate
        max_tokens: The maximum number of tokens
        model_name: The name of the model to count tokens for

    Returns:
        Tuple of (truncated text, whether truncation occurred)
    """
    # Quick check if truncation is needed
    tokenizer = get_tokenizer(model_name)
    tokens = tokenizer.encode(text)

    if len(tokens) <= max_tokens:
        return text, False

    # For very large texts, use binary search to find truncation point
    if len(tokens) > 10000:
        # Binary search for truncation point
        left, right = 0, len(text)
        best_length = 0

        while left <= right:
            mid = (left + right) // 2
            mid_text = text[:mid]
            mid_tokens = len(tokenizer.encode(mid_text))

            if mid_tokens <= max_tokens:
                best_length = mid
                left = mid + 1
            else:
                right = mid - 1

        # Fine-tune the truncation point
        truncated_text = text[:best_length]

        # Try to end at a sentence boundary
        last_period = truncated_text.rfind('.')
        if last_period > 0.8 * best_length:  # Only if period is near the end
            truncated_text = truncated_text[:last_period + 1]
    else:
        # For smaller texts, use direct truncation
        truncated_tokens = tokens[:max_tokens]
        truncated_text = tokenizer.decode(truncated_tokens)

    return truncated_text, True


def estimate_tokens_from_chars(text_or_chars: Union[str, int]) -> int:
    """
    Quickly estimate token count from character count.

    This is a fast approximation that can be used for initial filtering
    before more accurate token counting.

    Args:
        text_or_chars: The text to estimate tokens for, or a character count

    Returns:
        Estimated token count
    """
    # Most languages average 4-5 characters per token
    # This is a rough estimate that can be used for quick filtering
    if isinstance(text_or_chars, str):
        char_count = len(text_or_chars)
    else:
        char_count = text_or_chars

    return char_count // 4


class Generator(ABC):
    """
    Base class for context-enhanced response generation.

    This abstract class defines the interface for all generators.
    Concrete implementations should override the generate method.
    """

    @abstractmethod
    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        **kwargs
    ) -> str:
        """
        Generate a response based on the query and retrieved context.

        Args:
            query: The query text
            context: The retrieved documents
            **kwargs: Additional arguments

        Returns:
            Generated response
        """
        raise NotImplementedError("Subclasses must implement generate")


class RAGGenerator(Generator):
    """
    Generator that combines retrieved context with LLM generation.

    This class formats retrieved documents, constructs prompts, and
    manages the interaction with the LLM to produce contextually
    informed responses with proper citations and context management.
    """

    def __init__(
        self,
        llm_adapter,
        include_citations: bool = True,
        citation_format: str = "inline",
        query_rewriter = None,
        max_context_tokens: int = 4000,
        token_buffer: int = 1000,
        structured_output: bool = True,
        fallback_to_summarization: bool = True
    ):
        """
        Initialize the RAG generator.

        Args:
            llm_adapter: The LLM adapter to use for generation
            include_citations: Whether to include citations in the response
            citation_format: Format for citations (inline, footnote, endnote, or academic)
            query_rewriter: Optional QueryRewriter instance for query enhancement
            max_context_tokens: Maximum number of tokens for context (excluding prompt)
            token_buffer: Buffer tokens to reserve for the response
            structured_output: Whether to format responses with structured sections
            fallback_to_summarization: Whether to summarize context when it's too large
        """
        self.llm_adapter = llm_adapter
        self.include_citations = include_citations
        self.citation_format = citation_format
        self.query_rewriter = query_rewriter
        self.max_context_tokens = max_context_tokens
        self.token_buffer = token_buffer
        self.structured_output = structured_output
        self.fallback_to_summarization = fallback_to_summarization

        # Validate citation format
        valid_formats = ["inline", "footnote", "endnote", "academic", "none"]
        if citation_format not in valid_formats:
            logger.warning(
                f"Invalid citation format: {citation_format}. Using 'inline' instead."
            )
            self.citation_format = "inline"

    async def generate(
        self,
        query: str,
        context: List[Dict[str, Any]],
        stream: bool = False,
        model_name: str = None,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Generate a response based on the query and retrieved context.

        Args:
            query: The query text
            context: The retrieved documents
            stream: Whether to stream the response
            model_name: The name of the model to use for token counting
            **kwargs: Additional arguments to pass to the LLM

        Returns:
            Generated response or async iterator for streaming
        """
        try:
            # Get model name for token counting
            model_name = model_name or kwargs.get("model", "gpt-3.5-turbo")

            # Track if we had to apply any fallbacks
            applied_fallbacks = []

            # Enhance query with query rewriter if available
            enhanced_query = query
            if self.query_rewriter:
                try:
                    # Extract metadata from context for query rewriting
                    rewrite_context = {}

                    # Extract department information if available
                    departments = set()
                    for doc in context:
                        if "metadata" in doc and "department" in doc["metadata"]:
                            departments.add(doc["metadata"]["department"])

                    if departments:
                        # If multiple departments, use the first one
                        rewrite_context["department"] = list(departments)[0]

                    # Add any additional context from kwargs
                    if "user_info" in kwargs:
                        rewrite_context["user_info"] = kwargs["user_info"]
                    if "conversation_history" in kwargs:
                        rewrite_context["conversation_history"] = kwargs["conversation_history"]

                    # Rewrite the query
                    from app.core.timeout import with_timeout_and_retry
                    enhanced_query = await with_timeout_and_retry(
                        self.query_rewriter.rewrite_query,
                        query,
                        rewrite_context,
                        timeout_seconds=3,
                        operation_name="query_rewriter.rewrite_query_for_generator",
                        max_attempts=1
                    )
                    logger.debug(f"Enhanced query for generation: '{query}' -> '{enhanced_query}'")
                except Exception as e:
                    logger.warning(f"Query enhancement for generation failed: {e}. Using original query.")
                    enhanced_query = query
                    applied_fallbacks.append("query_enhancement")

            # Sort context by relevance score if available
            if context:
                context = sorted(
                    context,
                    key=lambda x: x.get("score", 0) if isinstance(x.get("score"), (int, float)) else 0,
                    reverse=True
                )

            # Apply context window management
            managed_context, context_info = self._manage_context_window(
                context,
                model_name=model_name
            )

            if context_info.get("truncated", False):
                applied_fallbacks.append("context_truncation")
                logger.info(
                    f"Context was truncated from {context_info.get('original_docs', 0)} to "
                    f"{context_info.get('used_docs', 0)} documents due to token limit"
                )

            if context_info.get("summarized", False):
                applied_fallbacks.append("context_summarization")
                logger.info(f"Context was summarized due to token limit")

            # Format context for the prompt
            formatted_context = self._format_context(managed_context)

            # Construct the prompt with the enhanced query
            prompt = self._construct_prompt(
                enhanced_query,
                formatted_context,
                original_query=query,
                context_info=context_info
            )

            # Add structured output instructions if enabled
            if self.structured_output:
                prompt = self._add_structured_output_instructions(prompt)

            # Generate response
            if stream:
                logger.debug(f"Generating streaming response for query: {enhanced_query}")
                # Pass department to the LLM adapter if provided
                department = kwargs.pop("department", None)
                return await self.llm_adapter.chat(prompt, stream=True, department=department, **kwargs)
            else:
                logger.debug(f"Generating response for query: {enhanced_query}")
                # Pass department to the LLM adapter if provided
                department = kwargs.pop("department", None) if "department" in kwargs else None
                response = await self.llm_adapter.chat(prompt, department=department, **kwargs)

                # Post-process response to ensure proper citation formatting
                if self.include_citations:
                    response = self._ensure_citations(response, managed_context)

                # Add fallback notices if any were applied
                if applied_fallbacks and not stream:
                    response = self._add_fallback_notices(response, applied_fallbacks, context_info)

                return response
        except Exception as e:
            logger.error(f"Error generating response: {e}", exc_info=True)
            return f"I encountered an error while generating a response: {str(e)}"

    def _manage_context_window(
        self,
        context: List[Dict[str, Any]],
        model_name: str = "gpt-3.5-turbo"
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Manage the context window to ensure it fits within token limits.

        Uses optimized token counting and smart document selection strategies
        to maximize the relevance of included context while staying within
        token limits.

        Args:
            context: The retrieved documents
            model_name: The name of the model to use for token counting

        Returns:
            Tuple of (managed context, context info dictionary)
        """
        if not context:
            return [], {"original_docs": 0, "used_docs": 0, "truncated": False, "summarized": False}

        # Quick estimate of tokens using character count
        # This avoids expensive token counting for documents that will clearly fit
        estimated_total_chars = sum(len(doc.get("text", "")) for doc in context)
        estimated_total_tokens = estimate_tokens_from_chars(estimated_total_chars)

        # If estimated tokens are well below the limit, do accurate counting
        # Otherwise, we'll count tokens for each document individually
        if estimated_total_tokens <= self.max_context_tokens * 0.7:
            # Calculate tokens for each document (accurate but more expensive)
            for doc in context:
                doc["token_count"] = count_tokens(doc.get("text", ""), model_name)

            # Calculate total tokens in context
            total_tokens = sum(doc["token_count"] for doc in context)

            # Initialize context info
            context_info = {
                "original_docs": len(context),
                "original_tokens": total_tokens,
                "used_docs": len(context),
                "used_tokens": total_tokens,
                "truncated": False,
                "summarized": False
            }

            # If context is within limits, return as is
            if total_tokens <= self.max_context_tokens:
                return context, context_info
        else:
            # Initialize context info with estimates
            context_info = {
                "original_docs": len(context),
                "original_tokens": estimated_total_tokens,  # Estimated
                "used_docs": 0,
                "used_tokens": 0,
                "truncated": True,  # We know we'll need to truncate
                "summarized": False
            }

        # Context exceeds token limit or is estimated to exceed it
        logger.info(
            f"Context likely exceeds token limit (est. {estimated_total_tokens} > {self.max_context_tokens}), "
            f"applying management strategies"
        )

        # Strategy 1: Smart document selection
        # First, ensure all documents have token counts
        for doc in context:
            if "token_count" not in doc:
                doc["token_count"] = count_tokens(doc.get("text", ""), model_name)

        # Sort documents by a combination of relevance and information density
        # This prioritizes highly relevant but concise documents
        for doc in context:
            # Calculate information density (tokens per relevance point)
            # Higher score means more relevant and/or more concise
            relevance = doc.get("score", 0.5)  # Default to middle relevance if not scored
            if relevance <= 0:  # Avoid division by zero
                relevance = 0.01

            # Normalize relevance to 0-1 range if it's outside that
            if relevance > 1.0:
                relevance = relevance / 10.0 if relevance < 10 else 0.9

            # Calculate density score (higher is better)
            doc["density_score"] = relevance / (doc["token_count"] / 100)

        # Sort by density score (highest first)
        sorted_context = sorted(context, key=lambda x: x.get("density_score", 0), reverse=True)

        # Strategy 2: Allocate tokens to documents based on priority
        managed_context = []
        current_tokens = 0
        remaining_tokens = self.max_context_tokens

        # First pass: include highest priority documents that fit completely
        for doc in sorted_context:
            if doc["token_count"] <= remaining_tokens:
                managed_context.append(doc)
                current_tokens += doc["token_count"]
                remaining_tokens -= doc["token_count"]
            else:
                # Can't fit this document completely, will handle in second pass
                pass

        # Second pass: if we have very few documents or lots of remaining tokens,
        # include partial content from the next highest priority document
        if (len(managed_context) < min(3, len(context)) or
                remaining_tokens > self.max_context_tokens * 0.2) and sorted_context:

            # Find the next document that wasn't included
            next_docs = [doc for doc in sorted_context if doc not in managed_context]
            if next_docs:
                next_doc = next_docs[0]

                # Truncate the document to fit the remaining tokens
                truncated_text, _ = truncate_to_token_limit(
                    next_doc.get("text", ""),
                    remaining_tokens,
                    model_name
                )

                # Only include if we got a meaningful amount of text
                if len(truncated_text) > 100:  # Arbitrary threshold
                    truncated_doc = next_doc.copy()
                    truncated_doc["text"] = truncated_text
                    truncated_doc["truncated"] = True
                    truncated_doc["token_count"] = count_tokens(truncated_text, model_name)
                    managed_context.append(truncated_doc)
                    current_tokens += truncated_doc["token_count"]
                    remaining_tokens -= truncated_doc["token_count"]

        # Update context info
        context_info["used_docs"] = len(managed_context)
        context_info["used_tokens"] = current_tokens
        context_info["truncated"] = len(managed_context) < len(context)

        # Strategy 3: If enabled and still too few documents, summarize
        if (self.fallback_to_summarization and
                len(managed_context) < min(3, len(context)) and
                len(context) > 3):

            # This would be implemented with a call to summarize the context
            # For now, we'll just mark it as something we would do
            context_info["summarized"] = True
            logger.info(f"Would summarize context here if implemented")

        return managed_context, context_info

    def _add_structured_output_instructions(self, prompt: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Add instructions for structured output to the prompt.

        Args:
            prompt: The prompt to add instructions to

        Returns:
            Updated prompt with structured output instructions
        """
        # Get the system message
        system_message = prompt[0]["content"]

        # Add structured output instructions
        structured_instructions = (
            "\n\n6. Structure your response with clear sections:\n"
            "   - Start with a concise summary or direct answer\n"
            "   - Use headings for different aspects of the answer\n"
            "   - Include a brief conclusion\n"
            "   - List all sources in a References section at the end\n"
        )

        # Update the system message
        prompt[0]["content"] = system_message + structured_instructions

        return prompt

    def _ensure_citations(self, response: str, context: List[Dict[str, Any]]) -> str:
        """
        Ensure that the response includes proper citations.

        Args:
            response: The generated response
            context: The context documents used

        Returns:
            Response with proper citations
        """
        # If no context or citations not required, return as is
        if not context or not self.include_citations or self.citation_format == "none":
            return response

        # Check if response already has citations
        citation_patterns = {
            "inline": r'\[\d+\]',
            "footnote": r'\[\^?\d+\]',
            "endnote": r'\[\d+\]',
            "academic": r'\([A-Za-z]+ et al\., \d{4}\)'
        }

        pattern = citation_patterns.get(self.citation_format, r'\[\d+\]')
        has_citations = bool(re.search(pattern, response))

        # If response already has citations, return as is
        if has_citations:
            return response

        # Add missing citations section
        if "References" not in response and "Sources" not in response:
            references = "\n\n## References\n"

            for i, doc in enumerate(context, 1):
                metadata = doc.get("metadata", {})
                source = metadata.get("source", "Unknown")
                title = metadata.get("title", "Untitled")
                section = metadata.get("section", "")

                if self.citation_format == "academic":
                    author = metadata.get("author", "Unknown Author")
                    year = metadata.get("year", "n.d.")
                    references += f"{i}. {author} ({year}). {title}. {source}"
                else:
                    references += f"{i}. {title}"
                    if section:
                        references += f", {section}"
                    references += f" (Source: {source})"

                references += "\n"

            response += references

        return response

    def _add_fallback_notices(
        self,
        response: str,
        applied_fallbacks: List[str],
        context_info: Dict[str, Any]
    ) -> str:
        """
        Add notices about any fallbacks that were applied.

        Args:
            response: The generated response
            applied_fallbacks: List of fallbacks that were applied
            context_info: Information about the context processing

        Returns:
            Response with fallback notices
        """
        if not applied_fallbacks:
            return response

        notices = []

        if "context_truncation" in applied_fallbacks:
            original = context_info.get("original_docs", 0)
            used = context_info.get("used_docs", 0)
            notices.append(
                f"Note: Only {used} of {original} relevant documents were used due to context limitations."
            )

        if "context_summarization" in applied_fallbacks:
            notices.append(
                "Note: The source documents were summarized due to their length."
            )

        if "query_enhancement" in applied_fallbacks:
            notices.append(
                "Note: Query enhancement was attempted but failed. Using original query."
            )

        if notices:
            notice_text = "\n\n---\n" + "\n".join(notices)

            # Add notices before References section if it exists
            if "## References" in response:
                response = response.replace("## References", f"{notice_text}\n\n## References")
            elif "Sources:" in response:
                response = response.replace("Sources:", f"{notice_text}\n\nSources:")
            else:
                response += notice_text

        return response

    def _format_context(self, context: List[Dict[str, Any]]) -> str:
        """
        Format retrieved documents for inclusion in the prompt.

        Args:
            context: The retrieved documents

        Returns:
            Formatted context string
        """
        if not context:
            return "No relevant information found."

        formatted_chunks = []

        for i, doc in enumerate(context):
            try:
                # Extract document information
                text = doc.get("text", "")
                metadata = doc.get("metadata", {})

                # Extract citation information
                source = metadata.get("source", "Unknown")
                title = metadata.get("title", "Untitled")
                section = metadata.get("section", "")
                page = metadata.get("page", "")
                author = metadata.get("author", "")
                date = metadata.get("date", "")
                year = metadata.get("year", "")
                url = metadata.get("url", "")

                # Format citation based on format
                if self.citation_format == "academic":
                    # Academic format: Author (Year). Title. Source.
                    citation_author = author or "Unknown Author"
                    citation_year = year or date.split("-")[0] if date else "n.d."
                    citation = f"[{i+1}] {citation_author}"
                    if citation_year:
                        citation += f" ({citation_year})"
                    citation += f". {title}"
                    if source:
                        citation += f". {source}"
                elif self.citation_format == "endnote":
                    # Endnote format: [1] Title, Section, Source (Year)
                    citation = f"[{i+1}] {title}"
                    if section:
                        citation += f", {section}"
                    if source:
                        citation += f", {source}"
                    if year or date:
                        year_str = year or date.split("-")[0] if date else ""
                        if year_str:
                            citation += f" ({year_str})"
                elif self.citation_format == "footnote":
                    # Footnote format: Title, Section, p.Page (Source)
                    citation = f"[^{i+1}] {title}"
                    if section:
                        citation += f", {section}"
                    if page:
                        citation += f", p.{page}"
                    if source:
                        citation += f" (Source: {source})"
                else:
                    # Default inline format: [1] Title, Section, p.Page (Source)
                    citation = f"[{i+1}] {title}"
                    if section:
                        citation += f", {section}"
                    if page:
                        citation += f", p.{page}"
                    if source:
                        citation += f" (Source: {source})"

                # Add URL if available and not already in source
                if url and "http" not in citation:
                    citation += f" URL: {url}"

                # Check if document was truncated
                truncated = doc.get("truncated", False)
                truncation_note = " [truncated]" if truncated else ""

                # Add relevance score if available
                score_note = ""
                if "score" in doc and isinstance(doc["score"], (int, float)):
                    score = doc["score"]
                    if score <= 1.0:  # Normalized score
                        score_percentage = f"{score:.0%}"
                    else:  # Raw score
                        score_percentage = f"{score:.2f}"
                    score_note = f" [relevance: {score_percentage}]"

                # Format chunk with citation
                formatted_chunk = f"Document {i+1}:{truncation_note}{score_note}\n{text}\nCitation: {citation}\n"
                formatted_chunks.append(formatted_chunk)
            except Exception as e:
                logger.warning(f"Error formatting document {i}: {e}")
                # Add a placeholder for the problematic document
                formatted_chunks.append(f"Document {i+1}: [Error formatting document]\n")

        return "\n".join(formatted_chunks)

    def _construct_prompt(
        self,
        query: str,
        formatted_context: str,
        original_query: str = None,
        context_info: Dict[str, Any] = None
    ) -> List[Dict[str, str]]:
        """
        Construct a prompt for the LLM.

        Args:
            query: The query text (possibly enhanced)
            formatted_context: The formatted context
            original_query: The original query before enhancement (if different)
            context_info: Information about the context processing

        Returns:
            Prompt for the LLM
        """
        system_message = (
            "You are a helpful assistant that provides accurate, informative responses based on the "
            "provided context. Follow these guidelines:\n"
            "1. Base your response primarily on the information in the provided documents.\n"
            "2. Look carefully for relevant information in the documents, even if it's not immediately obvious.\n"
            "3. Extract specific details, numbers, and facts from the documents when available.\n"
            "4. If after thorough examination you're certain the documents don't contain any relevant information, say so clearly.\n"
            "5. Don't make up information that isn't supported by the documents.\n"
            "6. Use a clear, concise, and helpful tone.\n"
        )

        if self.include_citations:
            system_message += (
                "7. Include citations to reference specific documents.\n"
            )

            if self.citation_format == "inline":
                system_message += (
                    "   - Use inline citations like [1], [2], etc.\n"
                    "   - You can cite multiple documents for a single statement if needed.\n"
                    "   - Include a References section at the end with the full citations.\n"
                )
            elif self.citation_format == "footnote":
                system_message += (
                    "   - Use footnote citations like [^1], [^2], etc.\n"
                    "   - Include a References section at the end with the full citations.\n"
                )
            elif self.citation_format == "endnote":
                system_message += (
                    "   - Use endnote citations like [1], [2], etc.\n"
                    "   - Include a numbered References section at the end.\n"
                )
            elif self.citation_format == "academic":
                system_message += (
                    "   - Use academic citations like (Author, Year) or (Author et al., Year).\n"
                    "   - Include a References section at the end in academic format.\n"
                )

        # Include both original and enhanced queries if they differ
        question_section = ""
        if original_query and original_query != query:
            question_section = (
                f"Original Question: {original_query}\n"
                f"Enhanced Question: {query}\n"
            )
        else:
            question_section = f"Question: {query}\n"

        # Add context information if available
        context_section = ""
        if context_info:
            # If context was truncated, add a note
            if context_info.get("truncated", False):
                original_docs = context_info.get("original_docs", 0)
                used_docs = context_info.get("used_docs", 0)
                if original_docs > used_docs:
                    context_section += (
                        f"Note: Only {used_docs} of {original_docs} relevant documents "
                        f"could be included due to context limitations.\n"
                    )

            # If context was summarized, add a note
            if context_info.get("summarized", False):
                context_section += (
                    "Note: Some documents were summarized due to their length.\n"
                )

        # Construct the user message
        user_message = f"{question_section}\n"

        if context_section:
            user_message += f"{context_section}\n"

        user_message += (
            f"Context:\n{formatted_context}\n\n"
            f"Please provide a comprehensive answer based on the above context. "
            f"Look carefully for specific information related to the query, including numbers, dates, and specific details. "
            f"Pay special attention to any mentions of cash runway, Series B funding, or department contributions in the documents."
        )

        # Add specific instructions based on citation format
        if self.include_citations:
            if self.citation_format == "inline":
                user_message += (
                    " Make sure to include inline citations [1], [2], etc. for each piece of information."
                )
            elif self.citation_format == "footnote":
                user_message += (
                    " Make sure to include footnote citations [^1], [^2], etc. for each piece of information."
                )
            elif self.citation_format == "endnote":
                user_message += (
                    " Make sure to include endnote citations [1], [2], etc. for each piece of information."
                )
            elif self.citation_format == "academic":
                user_message += (
                    " Make sure to include academic citations (Author, Year) for each piece of information."
                )

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]


# Note: The following classes are planned for future implementation
# and are not part of the proof-of-concept.

# class SelfCritiqueGenerator(RAGGenerator):
#     """
#     Generator with self-critique capabilities.
#
#     This class extends RAGGenerator with the ability to critique and
#     refine its own responses for improved accuracy and quality.
#     """
#     ...

# class StreamingRAGGenerator(RAGGenerator):
#     """
#     Generator with enhanced streaming capabilities.
#
#     This class extends RAGGenerator with improved streaming support,
#     including progress indicators during context processing.
#     """
#     ...
