"""
Vector Utilities Module

This module provides utilities for vector operations, including dimension reduction,
similarity calculation, and clustering.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.cluster import KMeans, DBSCAN

# Import UMAP if available
try:
    import umap
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False
    logging.warning("UMAP not available. Install with 'pip install umap-learn' for better visualization.")

logger = logging.getLogger(__name__)


def reduce_dimensions(
    vectors: List[List[float]],
    method: str = "pca",
    n_components: int = 2,
    random_state: int = 42,
    **kwargs
) -> np.ndarray:
    """Reduce the dimensionality of vectors for visualization.

    Args:
        vectors: List of vectors to reduce
        method: Dimension reduction method ('pca', 'tsne', 'umap')
        n_components: Number of components to reduce to
        random_state: Random state for reproducibility
        **kwargs: Additional arguments for the reduction method

    Returns:
        Reduced vectors as a numpy array
    """
    # Convert to numpy array
    vectors_array = np.array(vectors)

    # Apply dimension reduction
    if method.lower() == "pca":
        reducer = PCA(n_components=n_components, random_state=random_state, **kwargs)
        reduced_vectors = reducer.fit_transform(vectors_array)
    elif method.lower() == "tsne":
        reducer = TSNE(n_components=n_components, random_state=random_state, **kwargs)
        reduced_vectors = reducer.fit_transform(vectors_array)
    elif method.lower() == "umap":
        if not UMAP_AVAILABLE:
            logger.warning("UMAP not available. Falling back to PCA.")
            reducer = PCA(n_components=n_components, random_state=random_state)
            reduced_vectors = reducer.fit_transform(vectors_array)
        else:
            reducer = umap.UMAP(n_components=n_components, random_state=random_state, **kwargs)
            reduced_vectors = reducer.fit_transform(vectors_array)
    else:
        raise ValueError(f"Unknown dimension reduction method: {method}")

    return reduced_vectors


def calculate_similarity(
    vector1: List[float],
    vector2: List[float],
    method: str = "cosine"
) -> float:
    """Calculate similarity between two vectors.

    Args:
        vector1: First vector
        vector2: Second vector
        method: Similarity method ('cosine', 'euclidean', 'dot')

    Returns:
        Similarity score (0-1)
    """
    # Convert to numpy arrays
    v1 = np.array(vector1)
    v2 = np.array(vector2)

    # Calculate similarity
    if method.lower() == "cosine":
        # Cosine similarity
        dot_product = np.dot(v1, v2)
        norm_v1 = np.linalg.norm(v1)
        norm_v2 = np.linalg.norm(v2)
        
        # Avoid division by zero
        if norm_v1 == 0 or norm_v2 == 0:
            return 0.0
            
        similarity = dot_product / (norm_v1 * norm_v2)
        
        # Ensure similarity is between 0 and 1
        return max(0.0, min(1.0, (similarity + 1) / 2))
    
    elif method.lower() == "euclidean":
        # Euclidean distance (converted to similarity)
        distance = np.linalg.norm(v1 - v2)
        # Convert distance to similarity (0-1)
        max_distance = np.sqrt(len(v1)) * 2  # Maximum possible distance
        similarity = 1 - (distance / max_distance)
        return max(0.0, similarity)
    
    elif method.lower() == "dot":
        # Dot product (normalized to 0-1)
        dot_product = np.dot(v1, v2)
        # Normalize to 0-1
        max_dot = np.linalg.norm(v1) * np.linalg.norm(v2)
        if max_dot == 0:
            return 0.0
        similarity = (dot_product / max_dot + 1) / 2
        return max(0.0, min(1.0, similarity))
    
    else:
        raise ValueError(f"Unknown similarity method: {method}")


def cluster_vectors(
    vectors: List[List[float]],
    method: str = "kmeans",
    n_clusters: int = 5,
    random_state: int = 42,
    **kwargs
) -> List[int]:
    """Cluster vectors into groups.

    Args:
        vectors: List of vectors to cluster
        method: Clustering method ('kmeans', 'dbscan')
        n_clusters: Number of clusters (for kmeans)
        random_state: Random state for reproducibility
        **kwargs: Additional arguments for the clustering method

    Returns:
        List of cluster labels for each vector
    """
    # Convert to numpy array
    vectors_array = np.array(vectors)

    # Apply clustering
    if method.lower() == "kmeans":
        clusterer = KMeans(n_clusters=n_clusters, random_state=random_state, **kwargs)
        labels = clusterer.fit_predict(vectors_array)
    elif method.lower() == "dbscan":
        clusterer = DBSCAN(**kwargs)
        labels = clusterer.fit_predict(vectors_array)
    else:
        raise ValueError(f"Unknown clustering method: {method}")

    return labels.tolist()


def find_nearest_neighbors(
    query_vector: List[float],
    vectors: List[List[float]],
    k: int = 5,
    method: str = "cosine"
) -> List[Tuple[int, float]]:
    """Find the nearest neighbors of a query vector.

    Args:
        query_vector: Query vector
        vectors: List of vectors to search
        k: Number of neighbors to return
        method: Similarity method ('cosine', 'euclidean', 'dot')

    Returns:
        List of (index, similarity) tuples for the nearest neighbors
    """
    # Calculate similarities
    similarities = [
        (i, calculate_similarity(query_vector, vector, method=method))
        for i, vector in enumerate(vectors)
    ]

    # Sort by similarity (descending)
    similarities.sort(key=lambda x: x[1], reverse=True)

    # Return top k
    return similarities[:k]
