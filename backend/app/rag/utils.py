"""
RAG Utility Functions

This module provides utility functions for the RAG system, including
initialization of embedding models and knowledge base services.
"""

import logging
import random
import os
from typing import List, Optional

from app.config import get_settings
from app.rag.embeddings import EmbeddingFactory, EmbeddingModel
from app.rag.vector_store import get_vector_store
from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService

logger = logging.getLogger(__name__)


class EnhancedMockEmbeddingModel(EmbeddingModel):
    """Enhanced mock embedding model that simulates semantic relationships."""

    def __init__(self, dimension: int = 768):
        self._dimension = dimension
        logger.info(f"Initialized EnhancedMockEmbeddingModel with dimension {dimension}")

        # Define some keyword-based embedding patterns to simulate semantic relationships
        self.keyword_patterns = {
            "finance": [0.8, 0.2, 0.1, 0.0, 0.0],
            "budget": [0.7, 0.3, 0.1, 0.0, 0.0],
            "money": [0.6, 0.3, 0.2, 0.0, 0.0],
            "marketing": [0.1, 0.0, 0.0, 0.8, 0.7],
            "brand": [0.2, 0.0, 0.0, 0.7, 0.8],
            "customer": [0.1, 0.0, 0.0, 0.6, 0.7],
            "product": [0.3, 0.1, 0.0, 0.5, 0.6],
            "sales": [0.4, 0.2, 0.0, 0.6, 0.5],
            "report": [0.5, 0.4, 0.3, 0.5, 0.4],
            "analysis": [0.5, 0.5, 0.4, 0.4, 0.3],
            "data": [0.4, 0.4, 0.4, 0.4, 0.4],
        }

        # Pad the patterns to the full dimension
        for keyword in self.keyword_patterns:
            pattern = self.keyword_patterns[keyword]
            # Extend with random but deterministic values
            random.seed(keyword)
            pattern.extend([random.uniform(-0.1, 0.1) for _ in range(dimension - len(pattern))])
            self.keyword_patterns[keyword] = pattern

    async def embed_query(self, text: str) -> List[float]:
        """Generate a deterministic mock embedding based on the text content."""
        # Initialize with random but deterministic values
        random.seed(text)
        embedding = [random.uniform(-0.1, 0.1) for _ in range(self._dimension)]

        # Modify the embedding based on keywords present in the text
        text_lower = text.lower()
        for keyword, pattern in self.keyword_patterns.items():
            if keyword in text_lower:
                # Add the keyword pattern with some weight
                weight = 0.5 + 0.5 * text_lower.count(keyword) / len(text_lower.split())
                for i in range(self._dimension):
                    embedding[i] += pattern[i] * weight

        # Normalize the embedding
        magnitude = sum(x**2 for x in embedding) ** 0.5
        if magnitude > 0:
            embedding = [x / magnitude for x in embedding]

        return embedding

    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Generate mock embeddings for a list of texts."""
        return [await self.embed_query(text) for text in texts]

    @property
    def dimension(self) -> int:
        """Get the dimension of the embeddings."""
        return self._dimension


async def initialize_embedding_model(
    provider: Optional[str] = None,
    model_name: Optional[str] = None,
    fallback: bool = True
) -> EmbeddingModel:
    """
    Initialize the embedding model using the EmbeddingFactory.

    This function creates an embedding model using the EmbeddingFactory with fallback enabled.
    If both real embedding models fail, it falls back to a mock model.

    Args:
        provider: Optional provider override (huggingface or openai)
        model_name: Optional model name override
        fallback: Whether to enable fallback to other providers

    Returns:
        EmbeddingModel: The initialized embedding model
    """
    settings = get_settings()
    embedding_provider = provider or settings.DEFAULT_EMBEDDING_MODEL

    try:
        # Create the embedding model using the factory with fallback enabled
        logger.info(f"Creating embedding model with provider: {embedding_provider}")

        # Default to HuggingFace if not specified
        if embedding_provider not in ["huggingface", "openai"]:
            logger.warning(f"Unknown embedding provider '{embedding_provider}', defaulting to huggingface")
            embedding_provider = "huggingface"

        # For HuggingFace, use e5-base-v2 model which is a good balance of quality and speed
        if embedding_provider == "huggingface":
            hf_model_name = model_name or "intfloat/e5-base-v2"
            embedding_model = EmbeddingFactory.create(
                provider="huggingface",
                fallback=fallback,
                model_name=hf_model_name,  # Good balance of quality and speed
                device="cpu",  # Use CPU for compatibility
                normalize_embeddings=True  # Better for similarity search
            )
            logger.info(f"Successfully initialized HuggingFace embedding model ({hf_model_name})")
        else:
            # For OpenAI, use the API key from settings
            openai_model_name = model_name or "text-embedding-3-small"
            embedding_model = EmbeddingFactory.create(
                provider="openai",
                fallback=fallback,
                model_name=openai_model_name,  # Good balance of quality and cost
                api_key=settings.OPENAI_API_KEY
            )
            logger.info(f"Successfully initialized OpenAI embedding model ({openai_model_name})")

        return embedding_model

    except Exception as e:
        # If both real embedding models fail, fall back to a mock model
        logger.warning(f"Failed to initialize real embedding model: {e}. Falling back to mock model.")

        # Use the enhanced mock embedding model as a last resort
        mock_model = EnhancedMockEmbeddingModel(dimension=768)
        logger.info("Using enhanced mock embedding model as fallback")
        return mock_model


async def initialize_knowledge_base_service(
    embedding_model: Optional[EmbeddingModel] = None,
    vector_store_type: Optional[str] = None,
    use_mock_documents: Optional[bool] = None
):
    """
    Initialize the knowledge base service with proper embedding model.

    This function creates an embedding model using the EmbeddingFactory,
    initializes a vector store, and creates a PgVectorKnowledgeBaseService with
    all components. If any step fails, it falls back to a basic
    PgVectorKnowledgeBaseService initialization.

    Args:
        embedding_model: Optional embedding model to use
        vector_store_type: Optional vector store type to use
        use_mock_documents: Override to force using mock documents (True) or real service (False)

    Returns:
        A knowledge base service (either PgVectorKnowledgeBaseService or a mock service)
    """
    settings = get_settings()

    # Check if we should use mock documents
    if use_mock_documents is None:
        # Check environment variable
        use_mock_documents = os.getenv("USE_MOCK_DOCUMENTS", "false").lower() == "true"

        # Also use mock documents if we're in CLI mode and mock documents exist
        if os.path.exists(os.path.expanduser("~/.businesslm/mock_documents.json")):
            # Check if we're being called from a CLI script
            import inspect
            caller_frame = inspect.currentframe().f_back
            caller_filename = caller_frame.f_code.co_filename
            if "scripts" in caller_filename or "cli.py" in caller_filename:
                use_mock_documents = True

    # Use mock document adapter if requested
    if use_mock_documents:
        logger.info("Using mock document adapter for knowledge base service")
        from app.rag.mock_document_adapter import get_mock_document_adapter

        # Create embedding model if not provided
        if embedding_model is None:
            embedding_model = await initialize_embedding_model()

        # Get or create mock document adapter
        mock_adapter = get_mock_document_adapter(embedding_model)

        # Create a simple wrapper that mimics the knowledge base service interface
        class MockKnowledgeBaseService:
            def __init__(self):
                self.embedding_model = embedding_model

            async def search(self, *args, **kwargs):
                return await mock_adapter.search(*args, **kwargs)

            async def retrieve(self, query, *args, **kwargs):
                # This is a simplified version of retrieve that just calls search
                return await self.search(query, *args, **kwargs)

        return MockKnowledgeBaseService()

    try:
        # Create embedding model if not provided
        if embedding_model is None:
            embedding_model = await initialize_embedding_model()

        # Initialize vector store
        store_type = vector_store_type or settings.VECTOR_STORE_TYPE
        vector_store = get_vector_store(
            store_type=store_type,
            dimension=embedding_model.dimension
        )

        # Initialize knowledge base service with all components
        return PgVectorKnowledgeBaseService(
            vector_store=vector_store,
            embedding_model=embedding_model
        )
    except Exception as e:
        logger.warning(f"Failed to initialize knowledge base service: {e}. Using basic PgVectorKnowledgeBaseService.")
        # Fall back to basic initialization if embedding model fails
        return PgVectorKnowledgeBaseService()
