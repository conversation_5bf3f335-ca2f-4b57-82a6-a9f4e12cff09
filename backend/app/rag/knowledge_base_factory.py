"""
Knowledge Base Factory

This module provides a factory function to create the appropriate knowledge base service
based on the application configuration.
"""

import logging
from typing import Optional

from app.config import get_settings
from app.rag.embedding_utils import get_embedding_model
from app.rag.vector_store import get_vector_store
from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService

logger = logging.getLogger(__name__)


async def get_knowledge_base_service() -> PgVectorKnowledgeBaseService:
    """
    Get a knowledge base service based on the application configuration.

    This function creates a knowledge base service using the configuration from the application settings.
    It initializes the vector store and embedding model, and creates a knowledge base service
    that works with our PostgreSQL + pgvector setup.

    Returns:
        PgVectorKnowledgeBaseService: A knowledge base service ready to use
    """
    settings = get_settings()

    # Get the vector store
    vector_store = get_vector_store()

    # Get the embedding model
    embedding_model = get_embedding_model()

    # Create the knowledge base service
    knowledge_base_service = PgVectorKnowledgeBaseService(
        vector_store=vector_store,
        embedding_model=embedding_model,
        vector_weight=0.7,
        keyword_weight=0.3,
        use_reranking=False  # Disable reranking for now
    )

    logger.info("Created PgVectorKnowledgeBaseService")

    return knowledge_base_service
