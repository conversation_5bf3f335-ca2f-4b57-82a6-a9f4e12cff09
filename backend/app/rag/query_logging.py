"""
Query Logging Module

This module provides functions for logging queries to the database and retrieving them.
It supports logging queries with vector embeddings for similarity search.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import desc, select, func, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.config import get_settings
from app.core.db.database import get_db_context
from app.models.query_log import QueryLog


async def log_query(
    query_text: str,
    embedding: Optional[List[float]] = None,
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    department: Optional[str] = None,
    top_results: Optional[List[Dict[str, Any]]] = None,
    execution_time_ms: Optional[int] = None,
    metadata: Optional[Dict[str, Any]] = None,
) -> UUID:
    """Log a query to the database.

    Args:
        query_text: The text of the query
        embedding: Vector embedding of the query
        user_id: ID of the user who made the query
        thread_id: ID of the conversation thread
        session_id: ID of the session
        department: Department the query was routed to
        top_results: Top results returned for the query
        execution_time_ms: Execution time in milliseconds
        metadata: Additional metadata

    Returns:
        ID of the created query log
    """
    with get_db_context() as db:
        # Convert user_id to UUID if it's a string
        if user_id and isinstance(user_id, str):
            try:
                user_id = UUID(user_id)
            except ValueError:
                # If it's not a valid UUID, set it to None
                user_id = None

        # Create query log
        query_log = QueryLog(
            query_text=query_text,
            embedding=embedding,
            user_id=user_id,
            thread_id=thread_id,
            session_id=session_id,
            department=department,
            top_results=top_results,
            execution_time_ms=execution_time_ms,
            meta_info=metadata or {},
        )

        # Add to database
        db.add(query_log)
        db.commit()
        db.refresh(query_log)

        return query_log.id


def get_query_logs(
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    department: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """Get query logs from the database.

    Args:
        user_id: Filter by user ID
        thread_id: Filter by thread ID
        session_id: Filter by session ID
        department: Filter by department
        start_time: Filter by start time
        end_time: Filter by end time
        limit: Maximum number of logs to return
        offset: Offset for pagination

    Returns:
        List of query logs
    """
    with get_db_context() as db:
        # Build query
        query = db.query(QueryLog).order_by(desc(QueryLog.timestamp))

        # Apply filters
        if user_id:
            if isinstance(user_id, str):
                try:
                    user_id = UUID(user_id)
                except ValueError:
                    pass
            query = query.filter(QueryLog.user_id == user_id)

        if thread_id:
            query = query.filter(QueryLog.thread_id == thread_id)

        if session_id:
            query = query.filter(QueryLog.session_id == session_id)

        if department:
            query = query.filter(QueryLog.department == department)

        if start_time:
            query = query.filter(QueryLog.timestamp >= start_time)

        if end_time:
            query = query.filter(QueryLog.timestamp <= end_time)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute query
        query_logs = query.all()

        # Convert to dictionaries
        return [log.to_dict() for log in query_logs]


def get_similar_queries(
    query_text: str,
    embedding: List[float],
    limit: int = 10,
    threshold: float = 0.7,
) -> List[Dict[str, Any]]:
    """Get similar queries from the database.

    Args:
        query_text: The query text to find similar queries for
        embedding: The query embedding
        limit: Maximum number of similar queries to return
        threshold: Similarity threshold (0-1)

    Returns:
        List of similar queries with similarity scores
    """
    with get_db_context() as db:
        # Use pgvector's cosine similarity operator
        # The <=> operator is the cosine distance operator
        # 1 - (embedding <=> QueryLog.embedding) gives us the cosine similarity
        similarity_expr = func.text("1 - (embedding <=> :embedding)")

        # Build query
        query = (
            db.query(
                QueryLog,
                similarity_expr.label("similarity"),
            )
            .filter(QueryLog.embedding.is_not(None))
            .filter(similarity_expr >= threshold)
            .order_by(desc("similarity"))
            .params(embedding=embedding)
            .limit(limit)
        )

        # Execute query
        results = query.all()

        # Convert to dictionaries
        return [
            {
                **log.to_dict(),
                "similarity": similarity,
            }
            for log, similarity in results
        ]


def delete_query_logs(
    user_id: Optional[Union[str, UUID]] = None,
    thread_id: Optional[str] = None,
    session_id: Optional[str] = None,
    older_than: Optional[datetime] = None,
) -> int:
    """Delete query logs from the database.

    Args:
        user_id: Delete logs for this user ID
        thread_id: Delete logs for this thread ID
        session_id: Delete logs for this session ID
        older_than: Delete logs older than this time

    Returns:
        Number of logs deleted
    """
    with get_db_context() as db:
        # Build query
        query = db.query(QueryLog)

        # Apply filters
        if user_id:
            if isinstance(user_id, str):
                try:
                    user_id = UUID(user_id)
                except ValueError:
                    pass
            query = query.filter(QueryLog.user_id == user_id)

        if thread_id:
            query = query.filter(QueryLog.thread_id == thread_id)

        if session_id:
            query = query.filter(QueryLog.session_id == session_id)

        if older_than:
            query = query.filter(QueryLog.timestamp < older_than)

        # Get count of logs to delete
        count = query.count()

        # Delete logs
        query.delete()
        db.commit()

        return count
