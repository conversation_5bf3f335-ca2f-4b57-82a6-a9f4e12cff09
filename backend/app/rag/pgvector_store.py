"""
PostgreSQL Vector Store

This module provides a vector store implementation using PostgreSQL with pgvector extension.
"""

import uuid
import logging
import json
import time
from typing import List, Dict, Optional, Any, Union, Tuple

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
# from sqlalchemy.sql import text  # Using sa.text instead
from sqlalchemy.orm import Session

from app.config import get_settings
from app.core.db.database import get_db_context
from app.rag.vector_store import VectorStore, SearchResult
from app.rag.query_logging import log_query as log_query_func

logger = logging.getLogger(__name__)


class PGVectorStore(VectorStore):
    """
    Vector store implementation using PostgreSQL with pgvector extension.

    This class provides a persistent vector store with PostgreSQL and pgvector as the backend.
    It supports metadata filtering and various distance metrics.
    """

    def __init__(
        self,
        dimension: int = None,  # Will be loaded from settings if None
        table_name: str = "embeddings",
        distance_metric: str = "cosine",  # "cosine", "l2", "ip" (inner product)
        index_type: str = "hnsw",  # "hnsw", "ivfflat", None
        **kwargs
    ):
        """
        Initialize the PostgreSQL vector store.

        Args:
            dimension: Dimension of the embedding vectors
            table_name: Name of the table to store embeddings
            distance_metric: Distance metric for similarity search
            index_type: Type of index to use (hnsw, ivfflat, or None for no index)
            **kwargs: Additional arguments for specific index types
        """
        # Get dimension from settings if not provided
        if dimension is None:
            self.dimension = get_settings().VECTOR_DIMENSION
        else:
            self.dimension = dimension

        self.table_name = table_name
        self.distance_metric = distance_metric
        self.index_type = index_type

        logger.info(f"Initializing PGVectorStore with dimension {self.dimension}")

        # Initialize the database table if it doesn't exist
        self._initialize_table(**kwargs)

    def _initialize_table(self, **kwargs):
        """
        Initialize the database table for storing embeddings.

        Args:
            **kwargs: Additional arguments for specific index types
        """
        with get_db_context() as db:
            # Check if pgvector extension is installed
            try:
                db.execute(sa.text("CREATE EXTENSION IF NOT EXISTS vector"))
                db.commit()
                logger.info("pgvector extension initialized")
            except Exception as e:
                logger.error(f"Error initializing pgvector extension: {e}")
                logger.warning("Will attempt to continue without pgvector extension. Vector operations may not work correctly.")
                # Don't raise the exception - we'll try to continue without pgvector
                # and fall back to other methods if possible

            # Create the embeddings table if it doesn't exist
            try:
                # Check if table exists
                result = db.execute(
                    sa.text(
                        f"SELECT EXISTS (SELECT FROM information_schema.tables "
                        f"WHERE table_name = '{self.table_name}')"
                    )
                ).scalar()

                if not result:
                    # Create the table
                    db.execute(
                        sa.text(
                            f"CREATE TABLE {self.table_name} ("
                            f"id UUID PRIMARY KEY DEFAULT gen_random_uuid(), "
                            f"embedding vector({self.dimension}), "
                            f"text TEXT NOT NULL, "
                            f"meta_info JSONB DEFAULT '{{}}',"  # Renamed from metadata to avoid SQLAlchemy reserved keyword
                            f"created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()"
                            f")"
                        )
                    )
                    db.commit()  # Commit after table creation to ensure it exists

                    # Create text search index
                    db.execute(
                        sa.text(
                            f"ALTER TABLE {self.table_name} ADD COLUMN text_search tsvector "
                            f"GENERATED ALWAYS AS (to_tsvector('english', text)) STORED"
                        )
                    )
                    db.commit()  # Commit after adding the column

                    db.execute(
                        sa.text(
                            f"CREATE INDEX idx_{self.table_name}_text_search "
                            f"ON {self.table_name} USING GIN(text_search)"
                        )
                    )
                    db.commit()  # Commit after creating the index

                    # Create vector index based on index_type
                    if self.index_type:
                        self._create_vector_index(db, **kwargs)
                        db.commit()  # Commit after creating the vector index

                    logger.info(f"Created embeddings table: {self.table_name}")

                    # Verify the table exists after creation
                    result = db.execute(
                        sa.text(
                            f"SELECT EXISTS (SELECT FROM information_schema.tables "
                            f"WHERE table_name = '{self.table_name}')"
                        )
                    ).scalar()

                    if not result:
                        logger.error(f"Table {self.table_name} was not created successfully")
                        raise Exception(f"Table {self.table_name} was not created successfully")
                else:
                    logger.info(f"Embeddings table already exists: {self.table_name}")
            except Exception as e:
                logger.error(f"Error creating embeddings table: {e}")
                db.rollback()
                raise

    def get_operator(self):
        """
        Get the operator for the distance metric.

        Returns:
            Operator string for the distance metric
        """
        if self.distance_metric == "cosine":
            return "<=>"  # Cosine distance
        elif self.distance_metric == "l2":
            return "<->"  # L2 distance
        elif self.distance_metric == "ip":
            return "<#>"  # Negative inner product
        else:
            logger.warning(f"Unknown distance metric: {self.distance_metric}, using cosine")
            return "<=>"  # Default to cosine

    def _create_vector_index(self, db: Session, **kwargs):
        """
        Create a vector index on the embeddings table.

        Args:
            db: Database session
            **kwargs: Additional arguments for specific index types
        """
        try:
            # Get the appropriate index method and operator based on distance metric
            if self.index_type == "ivfflat":
                index_method = "ivfflat"
                lists = kwargs.get("lists", 100)  # Default to 100 lists for ivfflat
            elif self.index_type == "hnsw":
                index_method = "hnsw"
                m = kwargs.get("m", 16)  # Default to M=16 for HNSW
                ef_construction = kwargs.get("ef_construction", 64)  # Default ef_construction
            else:
                # Default to basic index
                index_method = None

            # Try to create the appropriate index based on the distance metric and index type
            if index_method == "ivfflat":
                try:
                    if self.distance_metric == "l2":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING ivfflat (embedding vector_l2_ops) "
                                f"WITH (lists = {lists})"
                            )
                        )
                    elif self.distance_metric == "cosine":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING ivfflat (embedding vector_cosine_ops) "
                                f"WITH (lists = {lists})"
                            )
                        )
                    elif self.distance_metric == "ip":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING ivfflat (embedding vector_ip_ops) "
                                f"WITH (lists = {lists})"
                            )
                        )
                    db.commit()
                    logger.info(f"Created IVFFlat index on {self.table_name} for {self.distance_metric} distance")
                    return
                except Exception as e:
                    logger.warning(f"Failed to create IVFFlat index: {e}")
                    db.rollback()
                    # Fall through to try other index types

            elif index_method == "hnsw":
                try:
                    if self.distance_metric == "l2":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING hnsw (embedding vector_l2_ops) "
                                f"WITH (m = {m}, ef_construction = {ef_construction})"
                            )
                        )
                    elif self.distance_metric == "cosine":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING hnsw (embedding vector_cosine_ops) "
                                f"WITH (m = {m}, ef_construction = {ef_construction})"
                            )
                        )
                    elif self.distance_metric == "ip":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING hnsw (embedding vector_ip_ops) "
                                f"WITH (m = {m}, ef_construction = {ef_construction})"
                            )
                        )
                    db.commit()
                    logger.info(f"Created HNSW index on {self.table_name} for {self.distance_metric} distance")
                    return
                except Exception as e:
                    logger.warning(f"Failed to create HNSW index: {e}")
                    db.rollback()
                    # Fall through to try other index types

            # If specialized indexes fail or aren't requested, try a basic index
            try:
                # For newer pgvector versions (0.5.0+), we need to use the vector_* operators
                # in a different syntax
                if self.distance_metric == "l2":
                    db.execute(
                        sa.text(
                            f"CREATE INDEX idx_{self.table_name}_embedding "
                            f"ON {self.table_name} USING btree (embedding vector_l2_ops)"
                        )
                    )
                elif self.distance_metric == "cosine":
                    db.execute(
                        sa.text(
                            f"CREATE INDEX idx_{self.table_name}_embedding "
                            f"ON {self.table_name} USING btree (embedding vector_cosine_ops)"
                        )
                    )
                elif self.distance_metric == "ip":
                    db.execute(
                        sa.text(
                            f"CREATE INDEX idx_{self.table_name}_embedding "
                            f"ON {self.table_name} USING btree (embedding vector_ip_ops)"
                        )
                    )
                db.commit()
                logger.info(f"Created btree index on {self.table_name} for {self.distance_metric} distance")
                return
            except Exception as e:
                logger.warning(f"Failed to create btree index with vector ops: {e}")
                db.rollback()

                # Try a GiST index which can handle larger vectors
                try:
                    if self.distance_metric == "l2":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING gist (embedding vector_l2_ops)"
                            )
                        )
                    elif self.distance_metric == "cosine":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING gist (embedding vector_cosine_ops)"
                            )
                        )
                    elif self.distance_metric == "ip":
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} USING gist (embedding vector_ip_ops)"
                            )
                        )
                    db.commit()
                    logger.info(f"Created GiST index on {self.table_name} for {self.distance_metric} distance")
                    return
                except Exception as e3:
                    logger.warning(f"Failed to create GiST index: {e3}")
                    db.rollback()

                    # As a last resort, try a basic index without operator class
                    try:
                        db.execute(
                            sa.text(
                                f"CREATE INDEX idx_{self.table_name}_embedding "
                                f"ON {self.table_name} (embedding)"
                            )
                        )
                        db.commit()
                        logger.info(f"Created basic index on {self.table_name}")
                        return
                    except Exception as e4:
                        logger.error(f"Failed to create basic index: {e4}")
                        db.rollback()

                        # If all index creation attempts fail, log a warning but don't fail
                        logger.warning("Could not create any vector index. Queries may be slower.")
        except Exception as e:
            logger.error(f"Error creating vector index: {e}")
            db.rollback()  # Rollback the transaction to avoid transaction errors

    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add embeddings to the vector store.

        Args:
            embeddings: List of embedding vectors
            texts: List of original texts
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs for the embeddings

        Returns:
            List of IDs for the added embeddings
        """
        # Validate inputs
        if len(embeddings) != len(texts):
            raise ValueError("Number of embeddings must match number of texts")

        if metadatas is not None and len(metadatas) != len(embeddings):
            raise ValueError("Number of metadata items must match number of embeddings")

        # Generate IDs if not provided
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(embeddings))]
        elif len(ids) != len(embeddings):
            raise ValueError("Number of IDs must match number of embeddings")

        # Create metadata if not provided
        if metadatas is None:
            metadatas = [{} for _ in range(len(embeddings))]

        with get_db_context() as db:
            try:
                # Insert embeddings into the database
                for i, (doc_id, embedding, text, metadata) in enumerate(zip(ids, embeddings, texts, metadatas)):
                    # Convert embedding to PostgreSQL vector format
                    vector_str = f"[{','.join(str(x) for x in embedding)}]"

                    # Insert the embedding
                    # Use direct string interpolation for all parameters to avoid PostgreSQL type issues
                    meta_info_json = json.dumps(metadata).replace("'", "''")  # Escape single quotes for SQL
                    text_escaped = text.replace("'", "''")  # Escape single quotes for SQL

                    db.execute(
                        sa.text(
                            f"INSERT INTO {self.table_name} (id, embedding, text, meta_info) "
                            f"VALUES ('{doc_id}', '{vector_str}'::vector, '{text_escaped}', '{meta_info_json}'::jsonb)"
                        )
                    )

                db.commit()
                logger.debug(f"Added {len(embeddings)} embeddings to {self.table_name}")
                return ids
            except Exception as e:
                db.rollback()
                logger.error(f"Error adding embeddings: {e}")
                raise

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None,
        query_text: Optional[str] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None,
        department: Optional[str] = None,
        log_query: bool = True
    ) -> List[SearchResult]:
        """
        Search for similar vectors in the store.

        Args:
            query_embedding: The query embedding vector
            limit: Maximum number of results to return
            filters: Optional metadata filters
            query_text: Original query text (for logging)
            user_id: User ID (for logging)
            thread_id: Thread ID (for logging)
            session_id: Session ID (for logging)
            department: Department (for logging)
            log_query: Whether to log the query

        Returns:
            List of SearchResult dictionaries
        """
        start_time = time.time()

        with get_db_context() as db:
            try:
                # Convert query embedding to PostgreSQL vector format
                vector_str = f"[{','.join(str(x) for x in query_embedding)}]"

                # Determine the operator based on distance_metric
                operator = self.get_operator()

                # Build the query with direct string interpolation for the vector
                # This avoids issues with PostgreSQL parameter binding for the vector type
                # Handle different column names based on table name
                if self.table_name == "document_chunks":
                    # document_chunks table uses 'content' instead of 'text'
                    query = f"SELECT id, content as text, meta_info, document_id, embedding {operator} '{vector_str}'::vector AS score FROM {self.table_name}"
                else:
                    # Default for embeddings table
                    query = f"SELECT id, text, meta_info, embedding {operator} '{vector_str}'::vector AS score FROM {self.table_name}"

                # Add filters if provided
                where_clauses = []
                params = {}

                if filters:
                    for i, (key, value) in enumerate(filters.items()):
                        # Handle different filter types in a PostgreSQL-native way
                        if isinstance(value, dict) and "$in" in value:
                            # Handle $in operator (array membership)
                            in_values = value["$in"]
                            if key.startswith("metadata."):
                                # Handle nested metadata filters with IN clause
                                metadata_key = key.replace("metadata.", "")
                                # For PostgreSQL, we need to use a different approach for IN clauses
                                # with parameter binding
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>{metadata_key!r} IN ({', '.join(placeholders)})")
                            elif key == "department_id":
                                # Handle department_id filter (stored in meta_info)
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>'department' IN ({', '.join(placeholders)})")
                            else:
                                # Handle regular column with IN clause
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"{key} IN ({', '.join(placeholders)})")
                        elif isinstance(value, dict) and "$eq" in value:
                            # Handle $eq operator (equality)
                            eq_value = value["$eq"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :eq_value_{i}")
                                params[f"eq_value_{i}"] = str(eq_value)
                            elif key == "department_id":
                                # Handle department_id filter (stored in meta_info)
                                where_clauses.append(f"meta_info->>'department' = :eq_value_{i}")
                                params[f"eq_value_{i}"] = eq_value
                            else:
                                where_clauses.append(f"{key} = :eq_value_{i}")
                                params[f"eq_value_{i}"] = eq_value
                        elif isinstance(value, dict) and "$ne" in value:
                            # Handle $ne operator (not equal)
                            ne_value = value["$ne"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                where_clauses.append(f"meta_info->>{metadata_key!r} != :ne_value_{i}")
                                params[f"ne_value_{i}"] = str(ne_value)
                            else:
                                where_clauses.append(f"{key} != :ne_value_{i}")
                                params[f"ne_value_{i}"] = ne_value
                        elif isinstance(value, dict) and "$exists" in value:
                            # Handle $exists operator
                            exists_value = value["$exists"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                if exists_value:
                                    where_clauses.append(f"meta_info ? {metadata_key!r}")
                                else:
                                    where_clauses.append(f"NOT (meta_info ? {metadata_key!r})")
                            else:
                                # For regular columns, we can't easily check existence
                                # This is a simplified approach
                                if exists_value:
                                    where_clauses.append(f"{key} IS NOT NULL")
                                else:
                                    where_clauses.append(f"{key} IS NULL")
                        elif key.startswith("metadata."):
                            # Handle regular metadata filters
                            metadata_key = key.replace("metadata.", "")
                            if isinstance(value, (int, float, bool)):
                                # For numeric or boolean values, convert to string for JSONB comparison
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :meta_value_{i}")
                                params[f"meta_value_{i}"] = str(value)
                            else:
                                # For string values
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :meta_value_{i}")
                                params[f"meta_value_{i}"] = value
                        elif key == "department":
                            # Handle department filter (stored in meta_info)
                            if isinstance(value, dict) and "$in" in value:
                                # Handle $in operator for department
                                in_values = value["$in"]
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>'department' IN ({', '.join(placeholders)})")
                            else:
                                # Handle simple equality for department
                                where_clauses.append(f"meta_info->>'department' = :value_{i}")
                                params[f"value_{i}"] = value
                        else:
                            # Handle regular column filters
                            where_clauses.append(f"{key} = :value_{i}")
                            params[f"value_{i}"] = value

                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)

                # Add order by and limit
                query += f" ORDER BY score LIMIT {limit}"

                # Execute the query
                result = db.execute(sa.text(query), params)

                # Process results
                results = []
                for row in result:
                    # Get document title from document_id if available
                    document_title = None
                    document_id = None

                    # Extract metadata
                    meta_info = row.meta_info or {}

                    # If this is a document chunk, get the document title
                    if self.table_name == "document_chunks" and hasattr(row, 'document_id'):
                        document_id = str(row.document_id)
                        # Try to get the document title from the documents table
                        try:
                            doc_result = db.execute(
                                sa.text("SELECT title FROM documents WHERE id = :id"),
                                {"id": document_id}
                            ).fetchone()
                            if doc_result:
                                document_title = doc_result.title
                        except Exception as e:
                            logger.warning(f"Error getting document title: {e}")

                    # Prepare metadata with document information
                    metadata = dict(meta_info)
                    if document_title:
                        metadata["title"] = document_title
                    if document_id:
                        metadata["document_id"] = document_id

                    # Add department if available
                    if "department" in meta_info:
                        metadata["department"] = meta_info["department"]

                    results.append({
                        "id": str(row.id),
                        "text": row.text,
                        "metadata": metadata,
                        "score": float(row.score)
                    })

                # Log the query if requested
                if log_query and query_text:
                    execution_time_ms = int((time.time() - start_time) * 1000)
                    try:
                        # Extract top results for logging
                        top_results = [
                            {
                                "id": r["id"],
                                "text": r["text"][:100] + "..." if len(r["text"]) > 100 else r["text"],
                                "score": r["score"],
                                "metadata": {k: v for k, v in r["metadata"].items() if k in ["title", "department", "source"]}
                            }
                            for r in results[:min(3, len(results))]
                        ]

                        # Log the query asynchronously
                        await log_query_func(
                            query_text=query_text,
                            embedding=query_embedding,
                            user_id=user_id,
                            thread_id=thread_id,
                            session_id=session_id,
                            department=department,
                            top_results=top_results,
                            execution_time_ms=execution_time_ms,
                            metadata={"filter_count": len(where_clauses) if filters else 0}
                        )
                    except Exception as e:
                        logger.error(f"Error logging query: {e}")
                        # Don't fail the search if logging fails

                return results
            except Exception as e:
                logger.error(f"Error during vector search: {e}")

                # Check if this is a pgvector-related error
                if "vector" in str(e).lower() or "operator" in str(e).lower():
                    logger.warning("Vector search failed, falling back to keyword search")

                    # Try to extract keywords from the embedding for a fallback search
                    # This is a very basic fallback - in a real implementation, you might
                    # want to use the original query text if available
                    try:
                        # Convert the embedding to a simple keyword by taking the top dimensions
                        # This is just a heuristic fallback when we can't do proper vector search
                        top_indices = sorted(range(len(query_embedding)),
                                            key=lambda i: abs(query_embedding[i]),
                                            reverse=True)[:5]
                        fallback_query = f"embedding_{','.join(str(i) for i in top_indices)}"

                        # Use the keyword search as fallback
                        return await self.keyword_search(fallback_query, limit, filters)
                    except Exception as fallback_error:
                        logger.error(f"Fallback keyword search also failed: {fallback_error}")
                        # Return empty results as last resort
                        return []
                else:
                    # Re-raise non-pgvector errors
                    raise

    async def keyword_search(
        self,
        query: str,
        limit: int = 5,
        k: int = None,  # For backward compatibility
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for documents using PostgreSQL full-text search.

        Args:
            query: The search query text
            limit: Maximum number of results to return
            k: Deprecated, use limit instead
            filters: Optional metadata filters

        Returns:
            List of SearchResult dictionaries
        """
        # For backward compatibility
        if k is not None and limit == 5:  # If limit is still the default but k is provided
            limit = k
        with get_db_context() as db:
            try:
                # Prepare the query for full-text search
                # Use parameterized query for security
                # Handle different column names based on table name
                if self.table_name == "document_chunks":
                    # document_chunks table uses 'content' instead of 'text'
                    # and might not have text_search column, so we use to_tsvector on content
                    sql_query = f"""
                    SELECT id, content as text, meta_info, document_id,
                           ts_rank(to_tsvector('english', content), plainto_tsquery('english', :query_text)) AS score
                    FROM {self.table_name}
                    WHERE to_tsvector('english', content) @@ plainto_tsquery('english', :query_text)
                    """
                else:
                    # Default for embeddings table with text_search column
                    sql_query = f"""
                    SELECT id, text, meta_info, ts_rank(text_search, plainto_tsquery('english', :query_text)) AS score
                    FROM {self.table_name}
                    WHERE text_search @@ plainto_tsquery('english', :query_text)
                    """

                # Initialize parameters with the query text
                params = {"query_text": query}

                # Add filters if provided
                where_clauses = []

                if filters:
                    for i, (key, value) in enumerate(filters.items()):
                        # Handle different filter types in a PostgreSQL-native way
                        if isinstance(value, dict) and "$in" in value:
                            # Handle $in operator (array membership)
                            in_values = value["$in"]
                            if key.startswith("metadata."):
                                # Handle nested metadata filters with IN clause
                                metadata_key = key.replace("metadata.", "")
                                # For PostgreSQL, we need to use a different approach for IN clauses
                                # with parameter binding
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>{metadata_key!r} IN ({', '.join(placeholders)})")
                            elif key == "department_id":
                                # Handle department_id filter (stored in meta_info)
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>'department' IN ({', '.join(placeholders)})")
                            else:
                                # Handle regular column with IN clause
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"{key} IN ({', '.join(placeholders)})")
                        elif isinstance(value, dict) and "$eq" in value:
                            # Handle $eq operator (equality)
                            eq_value = value["$eq"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :eq_value_{i}")
                                params[f"eq_value_{i}"] = str(eq_value)
                            elif key == "department_id":
                                # Handle department_id filter (stored in meta_info)
                                where_clauses.append(f"meta_info->>'department' = :eq_value_{i}")
                                params[f"eq_value_{i}"] = eq_value
                            else:
                                where_clauses.append(f"{key} = :eq_value_{i}")
                                params[f"eq_value_{i}"] = eq_value
                        elif isinstance(value, dict) and "$ne" in value:
                            # Handle $ne operator (not equal)
                            ne_value = value["$ne"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                where_clauses.append(f"meta_info->>{metadata_key!r} != :ne_value_{i}")
                                params[f"ne_value_{i}"] = str(ne_value)
                            else:
                                where_clauses.append(f"{key} != :ne_value_{i}")
                                params[f"ne_value_{i}"] = ne_value
                        elif isinstance(value, dict) and "$exists" in value:
                            # Handle $exists operator
                            exists_value = value["$exists"]
                            if key.startswith("metadata."):
                                metadata_key = key.replace("metadata.", "")
                                if exists_value:
                                    where_clauses.append(f"meta_info ? {metadata_key!r}")
                                else:
                                    where_clauses.append(f"NOT (meta_info ? {metadata_key!r})")
                            else:
                                # For regular columns, we can't easily check existence
                                # This is a simplified approach
                                if exists_value:
                                    where_clauses.append(f"{key} IS NOT NULL")
                                else:
                                    where_clauses.append(f"{key} IS NULL")
                        elif key.startswith("metadata."):
                            # Handle regular metadata filters
                            metadata_key = key.replace("metadata.", "")
                            if isinstance(value, (int, float, bool)):
                                # For numeric or boolean values, convert to string for JSONB comparison
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :meta_value_{i}")
                                params[f"meta_value_{i}"] = str(value)
                            else:
                                # For string values
                                where_clauses.append(f"meta_info->>{metadata_key!r} = :meta_value_{i}")
                                params[f"meta_value_{i}"] = value
                        elif key == "department":
                            # Handle department filter (stored in meta_info)
                            if isinstance(value, dict) and "$in" in value:
                                # Handle $in operator for department
                                in_values = value["$in"]
                                placeholders = []
                                for j, in_value in enumerate(in_values):
                                    param_name = f"in_value_{i}_{j}"
                                    params[param_name] = in_value
                                    placeholders.append(f":{param_name}")

                                where_clauses.append(f"meta_info->>'department' IN ({', '.join(placeholders)})")
                            else:
                                # Handle simple equality for department
                                where_clauses.append(f"meta_info->>'department' = :value_{i}")
                                params[f"value_{i}"] = value
                        else:
                            # Handle regular column filters
                            where_clauses.append(f"{key} = :value_{i}")
                            params[f"value_{i}"] = value

                # Add the where clauses to the query
                if where_clauses:
                    sql_query += " AND " + " AND ".join(where_clauses)

                # Add order by and limit
                sql_query += f" ORDER BY score DESC LIMIT {limit}"

                # Execute the query
                result = db.execute(sa.text(sql_query), params)

                # Process results
                results = []
                for row in result:
                    # Get document title from document_id if available
                    document_title = None
                    document_id = None

                    # Extract metadata
                    meta_info = row.meta_info or {}

                    # If this is a document chunk, get the document title
                    if self.table_name == "document_chunks" and hasattr(row, 'document_id'):
                        document_id = str(row.document_id)
                        # Try to get the document title from the documents table
                        try:
                            doc_result = db.execute(
                                sa.text("SELECT title FROM documents WHERE id = :id"),
                                {"id": document_id}
                            ).fetchone()
                            if doc_result:
                                document_title = doc_result.title
                        except Exception as e:
                            logger.warning(f"Error getting document title: {e}")

                    # Prepare metadata with document information
                    metadata = dict(meta_info)
                    if document_title:
                        metadata["title"] = document_title
                    if document_id:
                        metadata["document_id"] = document_id

                    # Add department if available
                    if "department" in meta_info:
                        metadata["department"] = meta_info["department"]

                    results.append({
                        "id": str(row.id),
                        "text": row.text,
                        "metadata": metadata,
                        "score": float(row.score)
                    })

                return results
            except Exception as e:
                logger.error(f"Error performing keyword search: {e}")
                raise

    async def delete(self, ids: List[str]) -> None:
        """
        Delete embeddings from the store.

        Args:
            ids: List of embedding IDs to delete
        """
        with get_db_context() as db:
            try:
                # Delete embeddings from the database
                for doc_id in ids:
                    db.execute(
                        sa.text(f"DELETE FROM {self.table_name} WHERE id = :id"),
                        {"id": doc_id}
                    )

                db.commit()
                logger.debug(f"Deleted {len(ids)} embeddings from {self.table_name}")
            except Exception as e:
                db.rollback()
                logger.error(f"Error deleting embeddings: {e}")
                raise

    async def get(self, ids: List[str]) -> List[Dict[str, Any]]:
        """
        Get embeddings by ID.

        Args:
            ids: List of embedding IDs to retrieve

        Returns:
            List of DocumentChunk dictionaries
        """
        with get_db_context() as db:
            try:
                results = []
                for doc_id in ids:
                    # Get the embedding
                    if self.table_name == "document_chunks":
                        # document_chunks table uses 'content' instead of 'text'
                        result = db.execute(
                            sa.text(f"SELECT id, embedding, content as text, meta_info, document_id FROM {self.table_name} WHERE id = :id"),
                            {"id": doc_id}
                        ).fetchone()
                    else:
                        # Default for embeddings table
                        result = db.execute(
                            sa.text(f"SELECT id, embedding, text, meta_info FROM {self.table_name} WHERE id = :id"),
                            {"id": doc_id}
                        ).fetchone()

                    if result:
                        # Get document title from document_id if available
                        document_title = None
                        document_id = None

                        # Extract metadata
                        meta_info = result.meta_info or {}

                        # If this is a document chunk, get the document title
                        if self.table_name == "document_chunks" and hasattr(result, 'document_id'):
                            document_id = str(result.document_id)
                            # Try to get the document title from the documents table
                            try:
                                doc_result = db.execute(
                                    sa.text("SELECT title FROM documents WHERE id = :id"),
                                    {"id": document_id}
                                ).fetchone()
                                if doc_result:
                                    document_title = doc_result.title
                            except Exception as e:
                                logger.warning(f"Error getting document title: {e}")

                        # Prepare metadata with document information
                        metadata = dict(meta_info)
                        if document_title:
                            metadata["title"] = document_title
                        if document_id:
                            metadata["document_id"] = document_id

                        # Add department if available
                        if "department" in meta_info:
                            metadata["department"] = meta_info["department"]

                        results.append({
                            "id": str(result.id),
                            "embedding": list(result.embedding),
                            "text": result.text,
                            "metadata": metadata
                        })

                return results
            except Exception as e:
                logger.error(f"Error getting embeddings: {e}")
                raise

    async def clear(self) -> None:
        """
        Clear all embeddings from the store.
        """
        with get_db_context() as db:
            try:
                # Delete all embeddings from the database
                db.execute(sa.text(f"DELETE FROM {self.table_name}"))
                db.commit()
                logger.debug(f"Cleared all embeddings from {self.table_name}")
            except Exception as e:
                db.rollback()
                logger.error(f"Error clearing embeddings: {e}")
                raise

    async def save(self, file_path: str) -> None:
        """
        Save the vector store to disk.

        For PostgreSQL, this is a no-op as the data is already persisted.

        Args:
            file_path: Path to save the vector store
        """
        logger.info("PostgreSQL vector store is already persisted, no need to save")

    async def load(self, file_path: str) -> None:
        """
        Load the vector store from disk.

        For PostgreSQL, this is a no-op as the data is already persisted.

        Args:
            file_path: Path to load the vector store from
        """
        logger.info("PostgreSQL vector store is already persisted, no need to load")

    @property
    def count(self) -> int:
        """
        Get the number of embeddings in the store.

        Returns:
            Number of embeddings
        """
        with get_db_context() as db:
            try:
                # Count embeddings in the database
                result = db.execute(sa.text(f"SELECT COUNT(*) FROM {self.table_name}")).scalar()
                return result
            except Exception as e:
                logger.error(f"Error counting embeddings: {e}")
                return 0
