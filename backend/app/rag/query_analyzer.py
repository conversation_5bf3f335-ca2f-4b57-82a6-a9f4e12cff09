"""
Query Analyzer

This module implements the query analyzer for determining query intent
and relevant departments.
"""
from typing import Dict, List, Any
import logging
import re
import traceback
import numpy as np
import string
from collections import Counter

logger = logging.getLogger(__name__)

class QueryAnalyzer:
    """
    Analyzer for user queries with embedding-based similarity and hybrid detection.

    This class analyzes user queries to determine their intent and relevant departments.
    It uses a combination of embedding-based similarity, keyword matching, and LLM-based
    analysis for robust department detection.
    """

    def __init__(self, llm_adapter, embedding_model=None):
        """
        Initialize the query analyzer.

        Args:
            llm_adapter: The LLM adapter to use for analysis
            embedding_model: The embedding model for vector similarity (optional)
        """
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model

        # Simple keyword-based department mapping
        self.department_keywords = {
            "co_ceo": [
                "strategy", "leadership", "executive", "ceo", "direction", "vision",
                "company", "organization", "business", "overview", "coordination",
                "cross-department", "fundraising", "funding", "investment",
                "investor", "due diligence", "oversight", "collaboration",
                "company-wide", "strategic", "initiative", "planning", "roadmap",
            ],
            "finance": [
                "budget", "financial", "money", "cost", "expense", "revenue",
                "profit", "loss", "investment", "funding", "price", "pricing",
                "forecast", "projection", "fiscal", "quarter", "annual", "tax",
                "cash", "runway", "containment", "capital", "expenditure", "roi",
                "return", "investment", "valuation", "financial", "model",
            ],
            "marketing": [
                "marketing", "campaign", "advertisement", "promotion", "brand",
                "customer", "audience", "market", "social media", "content",
                "engagement", "conversion", "lead", "funnel", "acquisition",
                "retention", "seo", "analytics", "metrics", "performance",
                "storytelling", "content", "assets", "messaging", "positioning",
            ],
        }

        # Department descriptions for embedding-based similarity
        self.department_descriptions = {
            "co_ceo": "The Co-CEO department handles company strategy, leadership, executive decisions, business direction, vision, cross-departmental coordination, fundraising, investor relations, due diligence, and oversight of major company initiatives.",
            "finance": "The finance department handles budgeting, financial reporting, financial analysis, revenue forecasting, expense management, investment decisions, funding allocation, pricing strategies, profit optimization, tax planning, financial risk management, cash runway analysis, and cost containment strategies.",
            "marketing": "The marketing department handles campaigns, brand management, customer acquisition, content creation, social media, advertising, market research, audience targeting, lead generation, conversion optimization, customer engagement, SEO, analytics, performance tracking, storytelling, and content assets for various initiatives."
        }

        # Section descriptions for section detection
        self.section_descriptions = {
            "executive_summary": "High-level overview of key points, findings, and recommendations.",
            "financial_data": "Financial information including budgets, costs, revenue, and forecasts.",
            "marketing_strategy": "Marketing plans, campaigns, target audiences, and promotional activities.",
            "product_details": "Product specifications, features, capabilities, and technical information.",
            "market_analysis": "Analysis of market trends, competitors, and industry landscape.",
            "performance_metrics": "KPIs, statistics, and performance measurements.",
            "goals_objectives": "Company goals, objectives, targets, and strategic priorities."
        }

        # Initialize department embeddings (to be populated later)
        self.department_embeddings = {}

        # Initialize section embeddings (to be populated later)
        self.section_embeddings = {}

        # Method weights for hybrid approach (higher = more reliable)
        self.embedding_weight = 0.6  # Embedding-based detection weight
        self.keyword_weight = 0.3    # Keyword-based detection weight
        self.llm_weight = 0.4        # LLM-based detection weight

        # Confidence thresholds
        self.similarity_threshold = 0.65  # Minimum similarity for embedding-based detection
        self.confidence_threshold = 0.5   # Minimum confidence for final results

        # Text preprocessing
        self.stopwords = {
            "a", "an", "the", "and", "or", "but", "if", "because", "as", "what",
            "which", "this", "that", "these", "those", "then", "just", "so", "than",
            "such", "when", "who", "how", "where", "why", "is", "are", "was", "were",
            "be", "been", "being", "have", "has", "had", "having", "do", "does", "did",
            "doing", "would", "should", "could", "ought", "i'm", "you're", "he's",
            "she's", "it's", "we're", "they're", "i've", "you've", "we've", "they've",
            "i'd", "you'd", "he'd", "she'd", "we'd", "they'd", "i'll", "you'll",
            "he'll", "she'll", "we'll", "they'll", "isn't", "aren't", "wasn't",
            "weren't", "hasn't", "haven't", "hadn't", "doesn't", "don't", "didn't",
            "can't", "couldn't", "shouldn't", "wouldn't", "for", "of", "with", "in",
            "on", "at", "by", "about", "against", "between", "into", "through",
            "during", "before", "after", "above", "below", "to", "from", "up", "down",
            "can", "will", "may", "must", "our", "my", "your", "their", "his", "her",
            "its", "us", "me", "him", "them", "we", "i", "you", "it", "he", "she",
            "they", "there", "here", "some", "any", "all", "many", "few", "more",
            "most", "other", "another", "not", "only", "very", "also"
        }

        logger.info("Query analyzer initialized")

        # Initialize embeddings if model is provided
        if self.embedding_model:
            import asyncio
            asyncio.create_task(self._initialize_embeddings())

    async def _initialize_embeddings(self):
        """
        Initialize embeddings for departments and sections.
        """
        try:
            logger.info("Initializing embeddings")

            # Generate embeddings for each department description
            for dept_id, description in self.department_descriptions.items():
                try:
                    # Generate embedding
                    embedding = await self.embedding_model.embed_query(description)

                    # Store embedding with metadata
                    self.department_embeddings[dept_id] = embedding

                    logger.debug(f"Generated embedding for department: {dept_id}")
                except Exception as e:
                    logger.error(f"Error generating embedding for department {dept_id}: {e}")
                    logger.debug(f"Error details: {traceback.format_exc()}")

            logger.info(f"Generated embeddings for {len(self.department_embeddings)} departments")

            # Generate embeddings for each section description
            for section_id, description in self.section_descriptions.items():
                try:
                    # Generate embedding
                    embedding = await self.embedding_model.embed_query(description)

                    # Store embedding with metadata
                    self.section_embeddings[section_id] = {
                        "embedding": embedding,
                        "description": description
                    }

                    logger.debug(f"Generated embedding for section: {section_id}")
                except Exception as e:
                    logger.error(f"Error generating embedding for section {section_id}: {e}")
                    logger.debug(f"Error details: {traceback.format_exc()}")

            logger.info(f"Generated embeddings for {len(self.section_embeddings)} sections")
        except Exception as e:
            logger.error(f"Error initializing embeddings: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")

    async def _embedding_detect_departments(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using embedding similarity.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        if not self.embedding_model or not self.department_embeddings:
            logger.warning("Embedding model or department embeddings not available")
            return []

        try:
            # Generate query embedding
            query_embedding = await self.embedding_model.embed_query(query)

            # Calculate similarity with each department
            results = []
            for dept_id, dept_embedding in self.department_embeddings.items():
                # Calculate cosine similarity
                similarity = self._calculate_similarity(query_embedding, dept_embedding)

                # Add to results if above threshold
                if similarity > self.similarity_threshold:
                    results.append({
                        "id": dept_id,
                        "confidence": similarity,
                        "method": "embedding"
                    })

            # Sort by confidence
            results.sort(key=lambda x: x["confidence"], reverse=True)

            logger.debug(f"Embedding-based department detection results: {results}")
            return results
        except Exception as e:
            logger.error(f"Error in embedding-based department detection: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            return []

    async def detect_sections(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant document sections using embedding similarity.

        Args:
            query: The user query

        Returns:
            List of section objects with confidence scores
        """
        if not self.embedding_model or not self.section_embeddings:
            logger.warning("Embedding model or section embeddings not available")
            return []

        try:
            # Generate query embedding
            query_embedding = await self.embedding_model.embed_query(query)

            # Calculate similarity with each section
            results = []
            for section_id, section_data in self.section_embeddings.items():
                # Calculate cosine similarity
                similarity = self._calculate_similarity(query_embedding, section_data["embedding"])

                # Add to results if above threshold
                if similarity > self.similarity_threshold:
                    results.append({
                        "id": section_id,
                        "confidence": similarity,
                        "description": section_data["description"]
                    })

            # Sort by confidence
            results.sort(key=lambda x: x["confidence"], reverse=True)

            # Limit to top 3 sections
            results = results[:3]

            logger.debug(f"Section detection results: {results}")
            return results
        except Exception as e:
            logger.error(f"Error in section detection: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            return []

    def _calculate_similarity(self, embedding1, embedding2):
        """
        Calculate cosine similarity between two embeddings.

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            Cosine similarity score (0-1)
        """
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)

            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)

            # Ensure result is in range [0, 1]
            return max(0.0, min(1.0, similarity))
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

    async def detect_departments(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments using a hybrid approach.

        This method combines embedding-based, keyword-based, and LLM-based detection
        to provide robust department detection with confidence scores.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        logger.info(f"Detecting departments for query: {query}")

        # Track method successes for fallback logic
        embedding_success = False
        keyword_success = False

        # Get results from each method with fallbacks
        try:
            # Try embedding-based detection first if available
            embedding_results = []
            if self.embedding_model and self.department_embeddings:
                embedding_results = await self._embedding_detect_departments(query)
                embedding_success = len(embedding_results) > 0
        except Exception as e:
            logger.error(f"Embedding-based detection failed: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            embedding_results = []

        # Always try keyword-based detection as it's fast and reliable
        keyword_results = self._keyword_detect_departments(query)
        keyword_success = len(keyword_results) > 0

        # Only use LLM if other methods didn't produce confident results
        llm_results = []
        if not embedding_success and not keyword_success:
            try:
                llm_results = await self._llm_detect_departments(query)
            except Exception as e:
                logger.error(f"LLM-based detection failed: {e}")
                logger.debug(f"Error details: {traceback.format_exc()}")
                llm_results = []

        # Combine results
        combined_results = {}

        # Process embedding results
        for result in embedding_results:
            dept_id = result["id"]
            combined_results[dept_id] = combined_results.get(dept_id, {})
            combined_results[dept_id]["embedding_score"] = result["confidence"]

        # Process keyword results
        for result in keyword_results:
            dept_id = result["id"]
            combined_results[dept_id] = combined_results.get(dept_id, {})
            combined_results[dept_id]["keyword_score"] = result["confidence"]
            if "matched_keywords" in result:
                combined_results[dept_id]["matched_keywords"] = result["matched_keywords"]

        # Process LLM results
        for result in llm_results:
            dept_id = result["id"]
            combined_results[dept_id] = combined_results.get(dept_id, {})
            combined_results[dept_id]["llm_score"] = result["confidence"]

        # Calculate final confidence scores
        final_results = []
        for dept_id, scores in combined_results.items():
            # Get scores with defaults
            embedding_score = scores.get("embedding_score", 0.0)
            keyword_score = scores.get("keyword_score", 0.0)
            llm_score = scores.get("llm_score", 0.0)

            # Calculate weighted average
            total_weight = 0
            weighted_sum = 0

            if embedding_score > 0:
                weighted_sum += embedding_score * self.embedding_weight
                total_weight += self.embedding_weight

            if keyword_score > 0:
                weighted_sum += keyword_score * self.keyword_weight
                total_weight += self.keyword_weight

            if llm_score > 0:
                weighted_sum += llm_score * self.llm_weight
                total_weight += self.llm_weight

            # Calculate confidence (avoid division by zero)
            confidence = weighted_sum / total_weight if total_weight > 0 else 0.0

            # Add to results if above threshold
            if confidence > self.confidence_threshold:
                result = {
                    "id": dept_id,
                    "confidence": confidence,
                    "methods": []
                }

                # Add method details
                if embedding_score > 0:
                    result["methods"].append("embedding")
                if keyword_score > 0:
                    result["methods"].append("keyword")
                    if "matched_keywords" in scores:
                        result["matched_keywords"] = scores["matched_keywords"]
                if llm_score > 0:
                    result["methods"].append("llm")

                final_results.append(result)

        # Sort by confidence
        final_results.sort(key=lambda x: x["confidence"], reverse=True)

        # Ensure at least one department if results are empty but we have some data
        if not final_results and combined_results:
            # Get department with highest combined score
            best_dept = max(combined_results.items(),
                            key=lambda x: sum([
                                x[1].get("embedding_score", 0) * self.embedding_weight,
                                x[1].get("keyword_score", 0) * self.keyword_weight,
                                x[1].get("llm_score", 0) * self.llm_weight
                            ]))

            # Calculate confidence
            scores = best_dept[1]
            methods = []
            if "embedding_score" in scores:
                methods.append("embedding")
            if "keyword_score" in scores:
                methods.append("keyword")
            if "llm_score" in scores:
                methods.append("llm")

            final_results.append({
                "id": best_dept[0],
                "confidence": 0.4,  # Lower confidence for fallback
                "methods": methods,
                "fallback": True
            })

        logger.info(f"Department detection results: {final_results}")
        return final_results

    async def analyze(self, query: str) -> Dict[str, Any]:
        """
        Analyze a query to determine its intent, relevant departments, and sections.

        This method performs comprehensive analysis of the query, including:
        - Query type detection
        - Intent recognition
        - Department detection
        - Section detection
        - Retrieval strategy determination
        - Text preprocessing

        Args:
            query: The user query

        Returns:
            Dict containing comprehensive analysis results
        """
        logger.info(f"Analyzing query: {query}")

        # Initialize embeddings if needed
        if self.embedding_model and not self.department_embeddings:
            await self._initialize_embeddings()

        # Preprocess the query
        preprocessed = self.preprocess_text(query)

        # Determine query type
        query_type = self._detect_query_type(query)

        # Detect intent
        intent = self._detect_intent(query)

        # Detect departments using hybrid approach
        departments = await self.detect_departments(query)

        # Detect relevant sections
        sections = []
        if self.embedding_model and self.section_embeddings:
            sections = await self.detect_sections(query)

        # Determine retrieval strategy
        retrieval_strategy = self.determine_retrieval_strategy(query, intent, departments)

        # Compile comprehensive analysis results
        return {
            "query": query,
            "preprocessed": preprocessed,
            "query_type": query_type,
            "intent": intent,
            "relevant_departments": departments,
            "relevant_sections": sections,
            "retrieval_strategy": retrieval_strategy
        }

    def _keyword_detect_departments(self, query: str) -> List[Dict[str, Any]]:
        """
        Detect relevant departments based on keywords.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        try:
            query_lower = query.lower()
            dept_matches = {}

            # Check each department's keywords
            for dept_id, keywords in self.department_keywords.items():
                match_count = 0
                matched_keywords = []

                for keyword in keywords:
                    if keyword.lower() in query_lower:
                        match_count += 1
                        matched_keywords.append(keyword)

                if match_count > 0:
                    # Calculate confidence based on number of matches
                    # More matches = higher confidence
                    confidence = min(0.95, 0.5 + (match_count / len(keywords)) * 0.5)

                    dept_matches[dept_id] = {
                        "id": dept_id,
                        "confidence": confidence,
                        "method": "keyword",
                        "matched_keywords": matched_keywords
                    }

            # Convert to list and sort by confidence
            results = list(dept_matches.values())
            results.sort(key=lambda x: x["confidence"], reverse=True)

            logger.debug(f"Keyword-based department detection results: {results}")
            return results
        except Exception as e:
            logger.error(f"Error in keyword-based department detection: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            return []

    def _detect_query_type(self, query: str) -> str:
        """
        Detect the type of query.

        Args:
            query: The user query

        Returns:
            Query type (general, specific, etc.)
        """
        # Simple heuristics for query type detection
        query_lower = query.lower()

        if re.search(r"(what|how|explain|describe|tell me about)", query_lower):
            return "informational"
        elif re.search(r"(can you|could you|would you|will you)", query_lower):
            return "request"
        elif re.search(r"(why|reason|cause|effect)", query_lower):
            return "analytical"
        elif re.search(r"(compare|difference|versus|vs)", query_lower):
            return "comparative"
        else:
            return "general"

    def _detect_intent(self, query: str) -> Dict[str, Any]:
        """
        Detect the user's intent beyond simple query type.

        This method analyzes the query to determine the user's underlying intent,
        such as seeking specific information, comparing options, or requesting
        recommendations.

        Args:
            query: The user query

        Returns:
            Dict containing intent information with primary intent, confidence, and secondary intents
        """
        query_lower = query.lower()

        # Initialize intent data
        intent = {
            "primary": "information_seeking",  # Default intent
            "confidence": 0.7,
            "secondary": [],
        }

        # Check for comparison intent
        if re.search(r'(compare|comparison|versus|vs\.?|difference|better|best|worse|worst|between)', query_lower):
            intent["primary"] = "comparison"
            intent["confidence"] = 0.85

        # Check for recommendation intent
        elif re.search(r'(recommend|suggestion|advise|should I|best way|how (can|should) I)', query_lower):
            intent["primary"] = "recommendation"
            intent["confidence"] = 0.85

        # Check for explanation intent
        elif re.search(r'(explain|clarify|elaborate|what does .+ mean|why is|how does)', query_lower):
            intent["primary"] = "explanation"
            intent["confidence"] = 0.8

        # Check for procedural intent
        elif re.search(r'(how to|steps|process|procedure|instructions|guide me)', query_lower):
            intent["primary"] = "procedural"
            intent["confidence"] = 0.85

        # Check for quantitative intent
        elif re.search(r'(how much|how many|calculate|compute|total|sum|average|mean|median)', query_lower):
            intent["primary"] = "quantitative"
            intent["confidence"] = 0.9

        # Check for temporal intent
        elif re.search(r'(when|time|schedule|deadline|due date|timeline|duration)', query_lower):
            intent["primary"] = "temporal"
            intent["confidence"] = 0.8

        # Check for secondary intents
        secondary_intents = []

        if "recent" in query_lower or "latest" in query_lower or "update" in query_lower:
            secondary_intents.append("recency_focused")

        if "example" in query_lower or "instance" in query_lower or "case" in query_lower:
            secondary_intents.append("example_seeking")

        if "summary" in query_lower or "overview" in query_lower or "brief" in query_lower:
            secondary_intents.append("summary_seeking")

        intent["secondary"] = secondary_intents

        return intent

    def determine_retrieval_strategy(self, query: str, intent: Dict[str, Any], departments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Determine the optimal retrieval strategy based on query analysis.

        This method uses the query, detected intent, and relevant departments
        to determine the best retrieval strategy for the knowledge base.

        Args:
            query: The user query
            intent: The detected intent information
            departments: The detected relevant departments

        Returns:
            Dict containing retrieval strategy parameters
        """
        # Initialize default strategy
        strategy = {
            "search_type": "hybrid",  # Default to hybrid search
            "limit": 5,               # Default number of results
            "filters": {},            # Default filters
            "reranking": "default",   # Default reranking method
            "weights": {              # Default weights for hybrid search
                "vector": 0.7,
                "keyword": 0.3
            }
        }

        # Apply department filters if available
        if departments:
            dept_ids = [dept["id"] for dept in departments]
            strategy["filters"]["department_id"] = {"$in": dept_ids}

            # If high confidence in a single department, increase its weight
            if len(departments) == 1 and departments[0]["confidence"] > 0.8:
                strategy["filters"]["department_id"] = {"$eq": departments[0]["id"]}

        # Adjust strategy based on intent
        primary_intent = intent.get("primary", "information_seeking")
        secondary_intents = intent.get("secondary", [])

        # Adjust based on primary intent
        if primary_intent == "comparison":
            # For comparison, we need more diverse results
            strategy["limit"] = 8
            strategy["weights"]["vector"] = 0.6
            strategy["weights"]["keyword"] = 0.4
            strategy["reranking"] = "diversity"

        elif primary_intent == "recommendation":
            # For recommendations, semantic search is more important
            strategy["weights"]["vector"] = 0.85
            strategy["weights"]["keyword"] = 0.15
            strategy["reranking"] = "relevance"

        elif primary_intent == "explanation":
            # For explanations, we want comprehensive results
            strategy["limit"] = 7
            strategy["reranking"] = "comprehensive"

        elif primary_intent == "procedural":
            # For procedural queries, we want step-by-step information
            strategy["weights"]["vector"] = 0.75
            strategy["weights"]["keyword"] = 0.25
            strategy["reranking"] = "sequential"

        elif primary_intent == "quantitative":
            # For quantitative queries, keyword search is more important
            strategy["weights"]["vector"] = 0.5
            strategy["weights"]["keyword"] = 0.5
            strategy["reranking"] = "factual"

        # Adjust based on secondary intents
        if "recency_focused" in secondary_intents:
            # Add recency filter or boost
            strategy["filters"]["timestamp"] = {"$gt": "recent"}  # Placeholder for actual timestamp logic

        if "example_seeking" in secondary_intents:
            # Boost documents with examples
            strategy["filters"]["contains_examples"] = True  # Placeholder for actual metadata

        if "summary_seeking" in secondary_intents:
            # Prefer summary documents
            strategy["filters"]["document_type"] = {"$in": ["summary", "overview"]}  # Placeholder for actual metadata

        # Adjust search type based on query length
        if len(query.split()) < 3:
            # Short queries often work better with keyword search
            strategy["weights"]["vector"] = 0.5
            strategy["weights"]["keyword"] = 0.5
        elif len(query.split()) > 15:
            # Long queries often work better with semantic search
            strategy["weights"]["vector"] = 0.85
            strategy["weights"]["keyword"] = 0.15

        return strategy

    def preprocess_text(self, text: str, remove_stopwords: bool = True) -> str:
        """
        Preprocess text for improved matching.

        This method applies various text preprocessing techniques to improve
        the quality of text matching, including lowercasing, punctuation removal,
        stopword removal, and normalization.

        Args:
            text: The text to preprocess
            remove_stopwords: Whether to remove stopwords

        Returns:
            Preprocessed text
        """
        # Convert to lowercase
        text = text.lower()

        # Remove punctuation
        text = text.translate(str.maketrans("", "", string.punctuation))

        # Remove extra whitespace
        text = " ".join(text.split())

        # Remove stopwords if requested
        if remove_stopwords:
            words = text.split()
            words = [word for word in words if word not in self.stopwords]
            text = " ".join(words)

        # Extract key phrases (n-grams)
        words = text.split()
        ngrams = []

        # Add unigrams
        ngrams.extend(words)

        # Add bigrams
        if len(words) >= 2:
            bigrams = [f"{words[i]} {words[i+1]}" for i in range(len(words)-1)]
            ngrams.extend(bigrams)

        # Add trigrams
        if len(words) >= 3:
            trigrams = [f"{words[i]} {words[i+1]} {words[i+2]}" for i in range(len(words)-2)]
            ngrams.extend(trigrams)

        # Count term frequencies
        term_freq = Counter(ngrams)

        # Get most frequent terms (up to 10)
        important_terms = [term for term, _ in term_freq.most_common(10)]

        # Return both preprocessed text and important terms
        return {
            "processed_text": text,
            "important_terms": important_terms
        }

    async def _llm_detect_departments(self, query: str) -> List[Dict[str, Any]]:
        """
        Use LLM to detect relevant departments with confidence scores.

        Args:
            query: The user query

        Returns:
            List of department objects with confidence scores
        """
        prompt = [
            {
                "role": "system",
                "content": (
                    "You are a query analyzer that determines which departments a query is relevant to. "
                    "The available departments are: co_ceo, finance, marketing. "
                    "For each department, provide a relevance score between 0 and 1, where 1 means highly relevant "
                    "and 0 means not relevant at all. Respond in JSON format with department IDs as keys and "
                    "confidence scores as values."
                )
            },
            {
                "role": "user",
                "content": f"Query: {query}\n\nAnalyze which departments this query is relevant to:"
            }
        ]

        try:
            response = await self.llm_adapter.chat(prompt)

            # Parse JSON response
            try:
                # Extract JSON from response (handle cases where LLM adds extra text)
                import json
                import re

                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    dept_scores = json.loads(json_str)
                else:
                    # Fallback parsing for non-JSON responses
                    dept_scores = {}
                    for dept_id in self.department_descriptions.keys():
                        match = re.search(rf'{dept_id}[^\d]*(\d+(\.\d+)?)', response, re.IGNORECASE)
                        if match:
                            try:
                                score = float(match.group(1))
                                # Normalize score to 0-1 range if needed
                                if score > 1:
                                    score = score / 10 if score <= 10 else 1.0
                                dept_scores[dept_id] = score
                            except ValueError:
                                continue

                # Convert to list format
                results = []
                for dept_id, confidence in dept_scores.items():
                    if dept_id in self.department_descriptions and confidence > 0.2:  # Minimum threshold
                        results.append({
                            "id": dept_id,
                            "confidence": confidence,
                            "method": "llm"
                        })

                # Sort by confidence
                results.sort(key=lambda x: x["confidence"], reverse=True)

                logger.debug(f"LLM-based department detection results: {results}")

                return results
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse LLM response as JSON: {response}")

                # Fallback to simple parsing
                if "none" in response.lower():
                    return []

                # Simple parsing for comma-separated list
                departments = []
                for dept in response.split(","):
                    dept_id = dept.strip().lower()
                    if dept_id in ["co_ceo", "finance", "marketing"]:
                        departments.append({
                            "id": dept_id,
                            "confidence": 0.7,  # Default confidence for simple parsing
                            "method": "llm"
                        })

                return departments

        except Exception as e:
            logger.error(f"Error in LLM-based department detection: {e}")
            logger.debug(f"Error details: {traceback.format_exc()}")
            return []
