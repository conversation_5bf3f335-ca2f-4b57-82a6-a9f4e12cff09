"""
Embedding Utilities

This module provides utility functions for working with embedding models.
"""

import logging
from typing import Optional

from app.config import get_settings
from app.rag.embeddings import EmbeddingModel, EmbeddingFactory

logger = logging.getLogger(__name__)


def get_embedding_model() -> EmbeddingModel:
    """
    Get an embedding model based on the application settings.
    
    This function creates an embedding model using the configuration from the application settings.
    It uses the EmbeddingFactory to create the appropriate model based on the EMBEDDING_MODEL setting.
    
    Returns:
        EmbeddingModel: An embedding model ready to use
        
    Raises:
        ImportError: If the required dependencies are not installed
        ValueError: If the embedding model provider is unknown
    """
    settings = get_settings()
    model_name = settings.EMBEDDING_MODEL
    
    # Determine the provider based on the model name
    if "openai" in model_name.lower():
        provider = "openai"
    else:
        provider = "huggingface"
    
    logger.info(f"Creating embedding model with provider '{provider}' and model '{model_name}'")
    
    return EmbeddingFactory.create(
        provider=provider,
        fallback=True,
        model_name=model_name
    )
