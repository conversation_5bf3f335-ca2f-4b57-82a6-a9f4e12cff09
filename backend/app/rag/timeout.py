"""
Timeout Handling for RAG Components (Compatibility Module)

This module provides backward compatibility with the old RAG timeout utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.timeout.rag instead.
"""

import warnings
import logging

# Show deprecation warning
warnings.warn(
    "The app.rag.timeout module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.timeout.rag instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export all symbols from the new modular structure
from app.core.timeout.rag import (
    RAG_TIMEOUTS,
    with_embedding_timeout,
    with_vector_search_timeout,
    with_rag_timeout,
    with_embedding_timeout_decorator,
    with_vector_search_timeout_decorator,
    with_keyword_search_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

# Set up logging
logger = logging.getLogger(__name__)


__all__ = [
    "RAG_TIMEOUTS",
    "with_embedding_timeout",
    "with_vector_search_timeout",
    "with_rag_timeout",
    "with_embedding_timeout_decorator",
    "with_vector_search_timeout_decorator",
    "with_keyword_search_timeout_decorator",
    "with_hybrid_search_timeout_decorator"
]
