"""
Marketing Department Agent Implementation

This module implements the Marketing department agent, which is responsible for:
1. Processing marketing-related queries
2. Using marketing-specific tools
3. Generating marketing-related responses
"""
import logging

from app.agents.department_agent import DepartmentAgent

logger = logging.getLogger(__name__)

class MarketingAgent(DepartmentAgent):
    """
    Marketing Department Agent that handles marketing-related queries.

    This agent is responsible for processing marketing-related queries,
    using marketing-specific tools, and generating responses.
    """

    def __init__(self, llm_adapter, knowledge_base_service, tools=None):
        """
        Initialize the Marketing agent.

        Args:
            llm_adapter: The LLM adapter to use for generating responses
            knowledge_base_service: The knowledge base service for retrieving information
            tools: Marketing-specific tools to use
        """
        super().__init__(llm_adapter, knowledge_base_service, department="marketing", tools=tools)

    # All common functionality is now inherited from DepartmentAgent
