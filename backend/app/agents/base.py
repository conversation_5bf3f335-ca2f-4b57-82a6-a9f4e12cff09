"""
Base Classes and Utilities for Agents

This module provides base classes and utilities for implementing agents in the system.
It defines common interfaces and functionality that all agents should implement.

The base classes are designed to:
1. Provide a consistent interface for all agents
2. Enforce proper typing and validation
3. Support async execution
4. Enable proper error handling and logging
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Base class for all agents in the system.

    This abstract class defines the interface that all agents must implement.
    It provides common functionality and enforces a consistent API.
    """

    def __init__(self, llm_adapter, knowledge_base_service, tools=None):
        """
        Initialize the base agent.

        Args:
            llm_adapter: The LLM adapter to use for generating responses
            knowledge_base_service: The knowledge base service for retrieving information
            tools: Optional tools for the agent to use
        """
        self.llm_adapter = llm_adapter
        self.knowledge_base_service = knowledge_base_service
        self.tools = tools or {}
        self.name = self.__class__.__name__
        logger.info(f"{self.name} initialized")

    @abstractmethod
    async def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a query and generate a response.

        Args:
            query: The user query
            context: Additional context for the query

        Returns:
            Dict containing the response and metadata
        """
        pass

    async def use_tool(self, tool_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use a tool.

        Args:
            tool_id: The ID of the tool to use
            params: Parameters for the tool

        Returns:
            Dict containing the tool results
        """
        if tool_id not in self.tools:
            raise ValueError(f"Tool {tool_id} not found in {self.name}")

        logger.info(f"{self.name} using tool: {tool_id}")

        tool = self.tools[tool_id]
        return await tool.run(**params)

    def __str__(self) -> str:
        """
        Get a string representation of the agent.

        Returns:
            String representation
        """
        return f"{self.name}(tools={list(self.tools.keys())})"

    def __repr__(self) -> str:
        """
        Get a detailed string representation of the agent.

        Returns:
            Detailed string representation
        """
        return f"{self.name}(llm_adapter={self.llm_adapter}, tools={list(self.tools.keys())})"