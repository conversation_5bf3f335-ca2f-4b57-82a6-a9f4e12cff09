"""
Templates Package

This package contains the prompt templates for the various agents in the system.
"""

import os
import logging
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)

# Base path for templates
TEMPLATES_DIR = os.path.dirname(os.path.abspath(__file__))


def load_template(agent_type: str, template_name: str = "system_prompt.txt") -> str:
    """
    Load a template from the templates directory.

    Args:
        agent_type: The type of agent (co_ceo, finance, marketing)
        template_name: The name of the template file

    Returns:
        The template content as a string
    """
    template_path = os.path.join(TEMPLATES_DIR, agent_type, template_name)
    
    try:
        with open(template_path, "r") as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"Template not found: {template_path}")
        # Return a basic template as fallback
        return f"You are a {agent_type.replace('_', ' ').title()} agent for BusinessLM."
    except Exception as e:
        logger.error(f"Error loading template {template_path}: {e}")
        return f"You are a {agent_type.replace('_', ' ').title()} agent for BusinessLM."


def render_template(template: str, **kwargs: Any) -> str:
    """
    Render a template with the given parameters.

    Args:
        template: The template string
        **kwargs: The parameters to substitute in the template

    Returns:
        The rendered template
    """
    return template.format(**kwargs)


def get_agent_prompt(agent_type: str, **kwargs: Any) -> str:
    """
    Get a fully rendered prompt for an agent.

    Args:
        agent_type: The type of agent (co_ceo, finance, marketing)
        **kwargs: The parameters to substitute in the template

    Returns:
        The rendered prompt
    """
    template = load_template(agent_type)
    
    # If no parameters, return the template as is
    if not kwargs:
        return template
    
    # Otherwise, render the template with the parameters
    return render_template(template, **kwargs)
