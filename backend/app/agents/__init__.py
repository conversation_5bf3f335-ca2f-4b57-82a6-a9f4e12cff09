"""
Agents Package

This package contains the implementation of the various agents in the system.
It includes base classes, agent implementations, state management, and orchestration.
"""

# Base classes and utilities
from .base import BaseAgent
from .department_agent import DepartmentAgent

# Agent implementations
from .co_ceo import CoCEOAgent
from .finance import FinanceAgent
from .marketing import MarketingAgent

# State management (imported from langgraph package)
from app.langgraph import (
    AgentState, RagContext, append_unique, create_initial_state, STATE_MODEL_VERSION,
    validate_state, transition_add_rag_documents, transition_add_message,
    transition_add_department, transition_update_analysis, transition_set_response,
    transition_update_metadata, create_transition
)

# Orchestration (imported from langgraph package)
from app.langgraph.graph import build_co_ceo_graph
from app.langgraph.checkpointing import CheckpointerInterface, SQLiteCheckpointer, get_checkpointer

# Registry
from .registry import AgentRegistry, agent_registry, get_registry

__all__ = [
    # Base classes
    "BaseAgent",
    "DepartmentAgent",

    # Agent implementations
    "CoCEOAgent",
    "FinanceAgent",
    "MarketingAgent",

    # State management
    "AgentState",
    "RagContext",
    "append_unique",
    "create_initial_state",
    "STATE_MODEL_VERSION",
    "validate_state",
    "transition_add_rag_documents",
    "transition_add_message",
    "transition_add_department",
    "transition_update_analysis",
    "transition_set_response",
    "transition_update_metadata",
    "create_transition",

    # Orchestration
    "build_co_ceo_graph",
    "CheckpointerInterface",
    "SQLiteCheckpointer",
    "get_checkpointer",

    # Registry
    "AgentRegistry",
    "agent_registry",
    "get_registry",

    # Initialization
    "initialize_agents"
]


def initialize_agents(llm_adapter, knowledge_base_service):
    """
    Initialize and register all agents.

    Args:
        llm_adapter: The LLM adapter to use for generating responses
        knowledge_base_service: The knowledge base service for retrieving information

    Returns:
        Dictionary of initialized agents
    """
    # Import here to avoid circular imports
    from app.core.tools import get_tools_by_department

    # Create agent instances
    co_ceo_agent = CoCEOAgent(llm_adapter, knowledge_base_service)
    finance_agent = FinanceAgent(llm_adapter, knowledge_base_service)
    marketing_agent = MarketingAgent(llm_adapter, knowledge_base_service)

    # Get tools for each department
    finance_tools = get_tools_by_department("finance")
    marketing_tools = get_tools_by_department("marketing")

    # Create tool dictionaries for each agent
    # The key format is "{department}.{tool_name}" to match the tool_id format in use_tool
    finance_tools_dict = {f"finance.{tool.name}": tool for tool in finance_tools}
    marketing_tools_dict = {f"marketing.{tool.name}": tool for tool in marketing_tools}

    # Assign tools to agents
    finance_agent.tools = finance_tools_dict
    marketing_agent.tools = marketing_tools_dict

    # Register agents
    agent_registry.register("co_ceo", co_ceo_agent)
    agent_registry.register("finance", finance_agent)
    agent_registry.register("marketing", marketing_agent)

    # Register tools in the agent registry
    agent_registry.register_toolset("finance", *finance_tools)
    agent_registry.register_toolset("marketing", *marketing_tools)

    return {
        "co_ceo": co_ceo_agent,
        "finance": finance_agent,
        "marketing": marketing_agent,
    }
