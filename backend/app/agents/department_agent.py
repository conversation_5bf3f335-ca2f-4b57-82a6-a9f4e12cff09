"""
Department Agent Base Class

This module provides a base class for department-specific agents (Finance, Marketing, etc.)
to reduce code duplication and standardize department agent implementations.
It includes enhanced knowledge retrieval and response generation capabilities.
"""
from typing import Dict, List, Any, Optional, Tuple, Callable, Union, Literal
import logging
from datetime import datetime
import json
import re
from enum import Enum

from app.agents.base import BaseAgent
from app.agents.templates import get_agent_prompt, render_template
from app.core.errors.base import (
    handle_async_errors, AppError, ValidationError, ResourceNotFoundError,
    ExternalServiceError, TimeoutError, ErrorCategory, ErrorContext
)

logger = logging.getLogger(__name__)

# Response style options
class ResponseStyle(str, Enum):
    """Response style options for department agents."""
    CONCISE = "concise"
    DETAILED = "detailed"
    ANALYTICAL = "analytical"
    CONVERSATIONAL = "conversational"

# Citation format options
class CitationFormat(str, Enum):
    """Citation format options for department agents."""
    INLINE = "inline"
    FOOTNOTE = "footnote"
    ENDNOTE = "endnote"
    NONE = "none"


class DepartmentAgent(BaseAgent):
    """
    Base class for department-specific agents.

    This class provides common functionality for department agents,
    reducing code duplication between Finance, Marketing, and other
    department implementations. It includes enhanced knowledge retrieval
    and response generation capabilities.
    """

    def __init__(
        self,
        llm_adapter,
        knowledge_base_service,
        department: str,
        tools=None,
        default_response_style: ResponseStyle = ResponseStyle.DETAILED,
        default_citation_format: CitationFormat = CitationFormat.INLINE,
        default_retrieval_params: Dict[str, Any] = None
    ):
        """
        Initialize a department agent.

        Args:
            llm_adapter: The LLM adapter to use for generating responses
            knowledge_base_service: The knowledge base service for retrieving information
            department: The department name (e.g., "finance", "marketing")
            tools: Department-specific tools to use
            default_response_style: Default style for responses (concise, detailed, etc.)
            default_citation_format: Default format for citations in responses
            default_retrieval_params: Default parameters for knowledge retrieval
        """
        super().__init__(llm_adapter, knowledge_base_service, tools)
        self.department = department
        self.default_response_style = default_response_style
        self.default_citation_format = default_citation_format
        self.default_retrieval_params = default_retrieval_params or {
            "top_k": 5,
            "similarity_threshold": 0.7,
            "use_hybrid_search": True,
            "rerank_results": True
        }
        logger.info(f"{self.department.title()} Agent initialized with {default_response_style.value} response style")

    async def process_query(
        self,
        query: str,
        context: Dict[str, Any] = None,
        retrieval_params: Dict[str, Any] = None,
        response_style: ResponseStyle = None,
        citation_format: CitationFormat = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a department-specific query and generate a response with enhanced capabilities.

        Args:
            query: The user query
            context: Additional context for the query
            retrieval_params: Optional parameters to customize retrieval behavior
            response_style: Style to use for the response (overrides default)
            citation_format: Format to use for citations (overrides default)

        Returns:
            Dict containing the response and metadata
        """
        try:
            # Validate input
            if not query or not query.strip():
                raise ValidationError(
                    message="Query cannot be empty",
                    field="query",
                    value=query
                )

            logger.info(f"Processing {self.department} query: {query}")
            context = context or {}

            # Add response customization options to context if provided
            if response_style:
                context["response_style"] = response_style
            if citation_format:
                context["citation_format"] = citation_format

            start_time = datetime.now()

            # Use ErrorContext for structured error handling
            with ErrorContext(
                operation=f"Processing {self.department} query",
                reraise=True,
                context={"query": query, "department": self.department}
            ):
                # Step 1: Retrieve relevant knowledge with enhanced capabilities
                knowledge_results = await self._retrieve_department_knowledge(
                    query,
                    retrieval_params,
                    user_id=user_id,
                    thread_id=thread_id,
                    session_id=session_id
                )

                # Step 2: Generate response using the LLM with enhanced capabilities
                response = await self._generate_department_response(query, knowledge_results, context)

                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()

                # Get response style and citation format used (from context or defaults)
                used_response_style = context.get("response_style", self.default_response_style)
                used_citation_format = context.get("citation_format", self.default_citation_format)

                return {
                    "response": response,
                    "query": query,
                    "tools_used": [],
                    "knowledge_sources": [doc.get("source", "Unknown") for doc in knowledge_results],
                    "knowledge_count": len(knowledge_results),
                    "timestamp": datetime.now().isoformat(),
                    "department": self.department,
                    "processing_time": processing_time,
                    "response_style": used_response_style.value,
                    "citation_format": used_citation_format.value,
                    "metadata": {
                        "expanded_queries": await self._expand_query(query),
                        "retrieval_params": retrieval_params or self.default_retrieval_params
                    }
                }
        except Exception as e:
            # Log the error
            logger.error(f"Error processing {self.department} query: {str(e)}", exc_info=True)

            # Return an error response
            error_message = str(e)

            # Get response style and citation format values
            response_style_value = self.default_response_style.value
            if response_style:
                response_style_value = response_style.value if hasattr(response_style, "value") else str(response_style)

            citation_format_value = self.default_citation_format.value
            if citation_format:
                citation_format_value = citation_format.value if hasattr(citation_format, "value") else str(citation_format)

            return {
                "response": f"I encountered an error while processing your query: {error_message}",
                "query": query,
                "error": error_message,
                "timestamp": datetime.now().isoformat(),
                "department": self.department,
                "tools_used": [],
                "knowledge_sources": [],
                "knowledge_count": 0,
                "processing_time": 0,
                "response_style": response_style_value,
                "citation_format": citation_format_value,
                "metadata": {}
            }

    async def _expand_query(self, query: str) -> List[str]:
        """
        Expand the query with related terms to improve retrieval.

        Args:
            query: The original user query

        Returns:
            List of expanded query terms
        """
        # Simple keyword expansion based on department
        department_keywords = {
            "finance": ["budget", "financial", "revenue", "expense", "profit", "cost", "investment"],
            "marketing": ["campaign", "advertising", "brand", "customer", "market", "promotion", "social media"],
            # Add more departments as needed
        }

        # Get keywords for the current department
        keywords = department_keywords.get(self.department, [])

        # Create expanded queries
        expanded_queries = [query]  # Always include the original query

        # Add department-specific expansions if available
        if keywords:
            for keyword in keywords:
                if keyword.lower() in query.lower():
                    # If the keyword is already in the query, add a more specific expansion
                    expanded_queries.append(f"{query} {self.department} analysis")
                    break
            else:
                # If no keywords matched, add a general department expansion
                expanded_queries.append(f"{query} {self.department}")

        logger.debug(f"Expanded query '{query}' to: {expanded_queries}")
        return expanded_queries

    @handle_async_errors(
        fallback_return=[],
        log_level=logging.ERROR,
        expected_exceptions=[ExternalServiceError, TimeoutError]
    )
    async def _retrieve_department_knowledge(
        self,
        query: str,
        retrieval_params: Dict[str, Any] = None,
        user_id: Optional[str] = None,
        thread_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve department-specific knowledge for the query with enhanced capabilities.

        Args:
            query: The user query
            retrieval_params: Optional parameters to customize retrieval behavior

        Returns:
            List of relevant documents
        """
        logger.info(f"Retrieving {self.department} knowledge for: {query}")

        # Merge default params with any provided params
        params = {**self.default_retrieval_params}
        if retrieval_params:
            params.update(retrieval_params)

        # Extract parameters
        top_k = params.get("top_k", 5)
        similarity_threshold = params.get("similarity_threshold", 0.7)
        use_hybrid_search = params.get("use_hybrid_search", True)
        rerank_results = params.get("rerank_results", True)

        # Create metadata filter
        metadata_filter = {"department": self.department}

        # Add any additional filters from params
        additional_filters = params.get("additional_filters", {})
        if additional_filters:
            metadata_filter.update(additional_filters)

        # Use ErrorContext for structured error handling
        with ErrorContext(
            operation=f"Retrieving {self.department} knowledge",
            reraise=True,
            context={
                "query": query,
                "department": self.department,
                "top_k": top_k,
                "similarity_threshold": similarity_threshold
            }
        ):
            # Expand the query for better retrieval
            expanded_queries = await self._expand_query(query)
            all_results = []

            # Retrieve documents for each expanded query
            for expanded_query in expanded_queries:
                try:
                    # Call the knowledge base service with the expanded query
                    results = await self.knowledge_base_service.retrieve(
                        query=expanded_query,
                        top_k=top_k,
                        metadata_filter=metadata_filter,
                        similarity_threshold=similarity_threshold,
                        use_hybrid_search=use_hybrid_search,
                        user_id=user_id,
                        thread_id=thread_id,
                        session_id=session_id,
                        department=self.department
                    )
                    all_results.extend(results)
                except Exception as e:
                    # Convert to ExternalServiceError for better error handling
                    raise ExternalServiceError(
                        message=f"Error retrieving knowledge for query: {expanded_query}",
                        service_name="knowledge_base_service",
                        operation="retrieve",
                        cause=e
                    )

            # Remove duplicates based on document ID
            unique_results = []
            seen_ids = set()
            for doc in all_results:
                doc_id = doc.get("id")
                if doc_id and doc_id not in seen_ids:
                    seen_ids.add(doc_id)
                    unique_results.append(doc)

            # Rerank results if enabled
            if rerank_results and len(unique_results) > 1:
                # Simple reranking by relevance score
                unique_results.sort(key=lambda x: x.get("score", 0), reverse=True)

                # Limit to top_k after reranking
                unique_results = unique_results[:top_k]

            logger.info(f"Retrieved {len(unique_results)} {self.department} documents after expansion and filtering")
            return unique_results

    def _format_citations(
        self,
        knowledge_results: List[Dict[str, Any]],
        citation_format: CitationFormat
    ) -> Tuple[str, str]:
        """
        Format citations based on the specified format.

        Args:
            knowledge_results: Retrieved knowledge documents
            citation_format: Format to use for citations

        Returns:
            Tuple of (formatted knowledge context, citations section)
        """
        knowledge_context = ""
        citations_section = ""

        if citation_format == CitationFormat.NONE:
            # No citations, just include the content
            for i, doc in enumerate(knowledge_results, 1):
                content = doc.get("content", doc.get("text", ""))
                knowledge_context += f"\n[Document {i}] {content}\n"
            return knowledge_context, ""

        elif citation_format == CitationFormat.INLINE:
            # Inline citations [1], [2], etc.
            for i, doc in enumerate(knowledge_results, 1):
                content = doc.get("content", doc.get("text", ""))

                # Extract metadata from the document
                metadata = doc.get("metadata", {})
                if not isinstance(metadata, dict):
                    metadata = {}

                # Get document title and source
                title = metadata.get("title", doc.get("title", f"{self.department.title()} Document"))
                source = metadata.get("source", doc.get("source", "Unknown"))

                knowledge_context += f"\n[Document {i}] {content} [Source {i}]\n"
                citations_section += f"[Source {i}]: {title} (Source: {source})\n"

        elif citation_format == CitationFormat.FOOTNOTE:
            # Footnote citations with superscript numbers
            for i, doc in enumerate(knowledge_results, 1):
                content = doc.get("content", doc.get("text", ""))

                # Extract metadata from the document
                metadata = doc.get("metadata", {})
                if not isinstance(metadata, dict):
                    metadata = {}

                # Get document title and source
                title = metadata.get("title", doc.get("title", f"{self.department.title()} Document"))
                source = metadata.get("source", doc.get("source", "Unknown"))

                knowledge_context += f"\n[Document {i}] {content} ^{i}\n"
                citations_section += f"^{i}: {title} (Source: {source})\n"

        elif citation_format == CitationFormat.ENDNOTE:
            # Endnote citations with numbers in brackets
            for i, doc in enumerate(knowledge_results, 1):
                content = doc.get("content", doc.get("text", ""))

                # Extract metadata from the document
                metadata = doc.get("metadata", {})
                if not isinstance(metadata, dict):
                    metadata = {}

                # Get document title and source
                title = metadata.get("title", doc.get("title", f"{self.department.title()} Document"))
                source = metadata.get("source", doc.get("source", "Unknown"))

                knowledge_context += f"\n[Document {i}] {content}\n"
                citations_section += f"[{i}] {title} (Source: {source})\n"

        return knowledge_context, citations_section

    def _get_response_instructions(self, response_style: ResponseStyle) -> str:
        """
        Get response instructions based on the specified style.

        Args:
            response_style: Style to use for the response

        Returns:
            Instructions for generating the response
        """
        base_instructions = f"""
Please provide a response that:
1. Directly addresses the {self.department} query with specific details from the documents
2. Uses the provided knowledge context extensively
3. Cites sources where appropriate
4. Uses appropriate {self.department} terminology
5. Includes specific numbers, dates, and other concrete details from the documents
6. Avoids vague statements like "the documents do not provide detailed information" when specific information is available
7. Extracts and presents all relevant specific details from the knowledge context
"""

        if response_style == ResponseStyle.CONCISE:
            return base_instructions + """
8. Is brief and to the point (2-3 paragraphs maximum)
9. Focuses only on the most important information
10. Uses bullet points for key details
11. Avoids unnecessary background information
"""

        elif response_style == ResponseStyle.DETAILED:
            return base_instructions + """
8. Is comprehensive and thorough
9. Includes relevant {self.department} metrics and data
10. Provides detailed analysis and insights
11. Uses headings and subheadings for organization
12. Acknowledges any limitations in the available information
"""

        elif response_style == ResponseStyle.ANALYTICAL:
            return base_instructions + """
8. Focuses on data analysis and insights
9. Includes quantitative metrics where available
10. Provides comparative analysis (e.g., trends, benchmarks)
11. Evaluates implications and potential outcomes
12. Presents a balanced assessment of the information
13. Uses tables or structured formats for data presentation
"""

        elif response_style == ResponseStyle.CONVERSATIONAL:
            return base_instructions + """
8. Uses a friendly, conversational tone
9. Explains concepts in simple, accessible language
10. Avoids excessive jargon or technical terminology
11. Includes relatable examples or analogies
12. Maintains a helpful and supportive tone
"""

        else:
            # Default to detailed if style not recognized
            return base_instructions

    @handle_async_errors(
        fallback_return="I'm sorry, but I encountered an error while generating a response. Please try again or rephrase your question.",
        log_level=logging.ERROR,
        expected_exceptions=[ExternalServiceError, TimeoutError]
    )
    async def _generate_department_response(
        self,
        query: str,
        knowledge_results: List[Dict[str, Any]],
        context: Dict[str, Any] = None
    ) -> str:
        """
        Generate a department-specific response using the LLM with enhanced capabilities.

        Args:
            query: The user query
            knowledge_results: Retrieved knowledge documents
            context: Additional context including response customization options

        Returns:
            Generated response
        """
        context = context or {}

        # Get response customization options from context or use defaults
        response_style = context.get("response_style", self.default_response_style)
        citation_format = context.get("citation_format", self.default_citation_format)

        # Use ErrorContext for structured error handling
        with ErrorContext(
            operation=f"Generating {self.department} response",
            reraise=True,
            context={
                "query": query,
                "department": self.department,
                "response_style": response_style.value,
                "citation_format": citation_format.value,
                "knowledge_count": len(knowledge_results)
            }
        ):
            # Check if we can use the RAG Generator
            try:
                from app.rag.generator import RAGGenerator

                # Create a RAG Generator instance
                rag_generator = RAGGenerator(
                    llm_adapter=self.llm_adapter,
                    include_citations=citation_format != CitationFormat.NONE,
                    citation_format=citation_format.value,
                    structured_output=True
                )

                # Prepare additional kwargs for the generator
                generator_kwargs = {
                    "model_name": context.get("model", "gpt-3.5-turbo"),
                    "department": self.department,
                    "response_style": response_style.value
                }

                # Add any additional context
                if "user_info" in context:
                    generator_kwargs["user_info"] = context["user_info"]
                if "conversation_history" in context:
                    generator_kwargs["conversation_history"] = context["conversation_history"]

                # Add response style instructions
                response_instructions = self._get_response_instructions(response_style)
                generator_kwargs["additional_instructions"] = response_instructions

                # Generate response using the RAG Generator
                logger.info(f"Using RAG Generator for {self.department} response")

                # Convert knowledge_results to the format expected by the RAG Generator
                rag_context = []
                for doc in knowledge_results:
                    # Log the document to debug
                    logger.debug(f"Processing document for RAG: {doc}")

                    # Check if we have content or text
                    content = doc.get("content", doc.get("text", ""))

                    # Extract metadata from the document
                    metadata = doc.get("metadata", {})

                    # If metadata is not a dictionary, initialize it as an empty dict
                    if not isinstance(metadata, dict):
                        metadata = {}

                    # Get document title from metadata or document itself
                    title = metadata.get("title", doc.get("title", f"{self.department.title()} Document"))

                    # Get source from metadata or document itself
                    source = metadata.get("source", doc.get("source", "Unknown"))

                    # Get section from metadata or document itself
                    section = metadata.get("section", doc.get("section", ""))

                    # Get document ID from metadata or document itself
                    document_id = metadata.get("document_id", doc.get("document_id", doc.get("id", "")))

                    rag_doc = {
                        "text": content,  # Use content or text field
                        "metadata": {
                            "source": source,
                            "title": title,
                            "section": section,
                            "department": self.department,
                            "document_id": document_id
                        }
                    }

                    # Add score if available
                    if "score" in doc:
                        rag_doc["score"] = doc["score"]

                    # Log the converted document
                    logger.debug(f"Converted document for RAG: {rag_doc}")

                    rag_context.append(rag_doc)

                # Generate the response - pass department to the LLM adapter
                # Make sure we don't pass department twice
                if "department" not in generator_kwargs:
                    generator_kwargs["department"] = self.department

                response = await rag_generator.generate(
                    query=query,
                    context=rag_context,
                    **generator_kwargs
                )

                return response

            except (ImportError, Exception) as e:
                # Fall back to the original implementation if RAG Generator is not available
                # or if there's an error using it
                logger.warning(f"Falling back to original response generation: {str(e)}")

                # Get the department-specific agent prompt
                system_prompt = get_agent_prompt(self.department)

                # Format the knowledge context and citations based on the specified format
                knowledge_context, citations_section = self._format_citations(knowledge_results, citation_format)

                # Get response instructions based on the specified style
                response_instructions = self._get_response_instructions(response_style)

                # Create the user prompt
                citation_part = ""
                if citations_section:
                    citation_part = "Citations:\n" + citations_section

                knowledge_part = knowledge_context
                if not knowledge_context:
                    knowledge_part = f"No specific {self.department} documents found for this query."

                additional_instructions = context.get("additional_instructions", "")

                user_prompt = f"""
Please respond to the following {self.department}-related query using the provided knowledge context.

Query: "{query}"

Knowledge Context:
{knowledge_part}

Additional Context:
- Current date: {datetime.now().strftime("%Y-%m-%d")}
- Response style: {response_style.value}
{additional_instructions}

{response_instructions}

{citation_part}
"""

                # Call the LLM
                try:
                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]

                    # Pass department to the LLM adapter
                    response = await self.llm_adapter.chat(
                        messages,
                        department=self.department  # Pass department to the LLM adapter
                    )
                except Exception as e:
                    # Convert to ExternalServiceError for better error handling
                    raise ExternalServiceError(
                        message="Error generating response from LLM",
                        service_name="llm_adapter",
                        operation="chat",
                        cause=e
                    )

                # Post-process the response if needed
                if citation_format != CitationFormat.NONE and citations_section:
                    # Ensure citations are included in the response
                    if citations_section not in response:
                        response += f"\n\nSources:\n{citations_section}"

                return response
