"""
Co-CEO Agent Implementation

This module implements the Co-CEO agent, which is responsible for:
1. Analyzing user queries
2. Routing to appropriate department agents
3. Coordinating responses
"""
from typing import Dict, List, Any, Optional, Tuple
import logging
import json
from datetime import datetime
import numpy as np

from app.agents.base import BaseAgent
from app.agents.templates import get_agent_prompt
from app.core.utils import cosine_similarity

logger = logging.getLogger(__name__)

# Available departments
DEPARTMENTS = ["co_ceo", "finance", "marketing"]

# Department descriptions for embedding-based similarity
DEPARTMENT_DESCRIPTIONS = {
    "co_ceo": "Company strategy, leadership, executive decisions, business direction, vision, cross-departmental coordination, fundraising, investor relations, due diligence, oversight of major company initiatives",
    "finance": "Financial analysis, budgeting, forecasting, expense management, financial reporting, cash flow, investments, financial metrics, profit and loss, balance sheet, financial planning, cash runway analysis, cost containment strategies",
    "marketing": "Marketing campaigns, market research, brand strategy, content planning, digital marketing, social media, email marketing, SEO, advertising, customer acquisition, marketing metrics, marketing ROI, storytelling, content assets for various initiatives"
}

class CoCEOAgent(BaseAgent):
    """
    Co-CEO Agent that orchestrates the multi-agent system.

    This agent is responsible for analyzing queries, routing to appropriate
    department agents, and coordinating responses.
    """

    def __init__(self, llm_adapter, knowledge_base_service, tools=None):
        """
        Initialize the Co-CEO agent.

        Args:
            llm_adapter: The LLM adapter to use for generating responses
            knowledge_base_service: The knowledge base service for retrieving information
            tools: Optional tools for the agent to use
        """
        super().__init__(llm_adapter, knowledge_base_service, tools)
        logger.info("Co-CEO Agent initialized")

    async def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a user query and generate a response.

        Args:
            query: The user query
            context: Additional context for the query

        Returns:
            Dict containing the response and metadata
        """
        logger.info(f"Processing query: {query}")
        context = context or {}

        try:
            # Step 1: Analyze the query
            analysis = await self.analyze_query(query)
            logger.info(f"Query analysis: {analysis}")

            # Step 2: Route to departments
            department_responses = await self.route_to_departments(query, analysis)
            departments_consulted = [resp.get("department") for resp in department_responses]
            logger.info(f"Departments consulted: {departments_consulted}")

            # Step 3: Generate response
            response = await self.generate_response(query, analysis, department_responses)

            return {
                "response": response,
                "query": query,
                "departments_consulted": departments_consulted,
                "analysis": analysis,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)
            return {
                "response": f"I encountered an error while processing your query. Please try again or rephrase your question.",
                "query": query,
                "departments_consulted": [],
                "error": str(e)
            }

    async def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze a query to determine its intent and relevant departments.

        This function uses a hybrid approach:
        1. Embedding-based similarity to calculate relevance scores
        2. LLM-based classification for more nuanced understanding

        Args:
            query: The user query

        Returns:
            Dict containing analysis results
        """
        logger.info(f"Analyzing query: {query}")

        # Step 1: Calculate embedding-based similarity scores
        embedding_scores = await self._calculate_department_similarity(query)
        logger.info(f"Embedding similarity scores: {embedding_scores}")

        # Step 2: Use LLM to analyze the query
        llm_analysis = await self._llm_analyze_query(query)
        logger.info(f"LLM analysis: {llm_analysis}")

        # Step 3: Combine the results
        combined_analysis = self._combine_analysis_results(embedding_scores, llm_analysis)

        return combined_analysis

    async def _calculate_department_similarity(self, query: str) -> Dict[str, float]:
        """
        Calculate similarity scores between the query and department descriptions.

        Args:
            query: The user query

        Returns:
            Dictionary mapping departments to similarity scores
        """
        # Get embeddings for the query and department descriptions
        query_embedding = await self.knowledge_base_service.embedding_model.embed_query(query)

        department_embeddings = {}
        for dept, desc in DEPARTMENT_DESCRIPTIONS.items():
            dept_embedding = await self.knowledge_base_service.embedding_model.embed_query(desc)
            department_embeddings[dept] = dept_embedding

        # Calculate similarity scores
        similarity_scores = {}
        for dept, embedding in department_embeddings.items():
            similarity = cosine_similarity(query_embedding, embedding)
            similarity_scores[dept] = float(similarity)  # Convert to float for JSON serialization

        return similarity_scores

    async def _llm_analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Use the LLM to analyze the query for intent and relevant departments.

        Args:
            query: The user query

        Returns:
            Dictionary with analysis results
        """
        # Create the prompt for query analysis
        system_prompt = get_agent_prompt("co_ceo")

        user_prompt = f"""
Please analyze the following query and determine:
1. The query type (informational, analytical, strategic, etc.)
2. The relevant departments that should be consulted
3. The priority level of the query (low, normal, high)

Query: "{query}"

Return your analysis as a JSON object with the following structure:
{{
  "query_type": "string",
  "relevant_departments": ["department1", "department2"],
  "priority": "string",
  "topics": ["topic1", "topic2"],
  "explanation": "string"
}}

Available departments: {", ".join(DEPARTMENTS)}
"""

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await self.llm_adapter.chat(messages)

        # Extract the JSON from the response
        try:
            # Find JSON in the response (it might be wrapped in markdown code blocks)
            import re

            # First, try to find JSON in code blocks
            json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                logger.debug(f"Found JSON in code block: {json_str}")
            else:
                # Try to find JSON without code blocks - look for the most complete JSON object
                # This regex looks for balanced JSON objects with nested structures
                json_matches = re.findall(r'({(?:[^{}]|(?:{[^{}]*}))*})', response, re.DOTALL)
                if json_matches:
                    # Use the longest match as it's likely the most complete
                    json_str = max(json_matches, key=len)
                    logger.debug(f"Found JSON without code block: {json_str}")
                else:
                    # If no JSON-like structure is found, create a basic structure from the response
                    logger.warning(f"No JSON structure found in response: {response}")

                    # Try to extract relevant departments from the text if JSON parsing fails
                    departments = []
                    for dept in DEPARTMENTS:
                        if dept.lower() in response.lower():
                            departments.append(dept)

                    # Create a simple JSON structure from the response
                    json_str = json.dumps({
                        "query_type": "general",
                        "relevant_departments": departments,  # Use extracted departments
                        "priority": "normal",
                        "topics": [],
                        "explanation": "Generated from non-JSON response"
                    })

            # Clean the JSON string - remove any leading/trailing whitespace and quotes
            json_str = json_str.strip()
            if json_str.startswith("'") and json_str.endswith("'"):
                json_str = json_str[1:-1]

            # Try to parse the JSON
            try:
                analysis = json.loads(json_str)
            except json.JSONDecodeError as json_err:
                logger.warning(f"Initial JSON parsing failed: {json_err}. Attempting to fix JSON.")
                # Try to fix common JSON issues
                fixed_json_str = json_str.replace("'", '"')  # Replace single quotes with double quotes
                fixed_json_str = re.sub(r'(\w+)(?=:)', r'"\1"', fixed_json_str)  # Add quotes to keys

                try:
                    analysis = json.loads(fixed_json_str)
                except json.JSONDecodeError:
                    # If still failing, try a more aggressive approach
                    logger.warning("Advanced JSON fixing attempt")
                    # Remove any non-JSON characters that might be causing issues
                    clean_str = re.sub(r'[^\x20-\x7E]', '', fixed_json_str)
                    # Try to extract a valid JSON object
                    json_obj_match = re.search(r'({.*})', clean_str, re.DOTALL)
                    if json_obj_match:
                        try:
                            analysis = json.loads(json_obj_match.group(1))
                        except json.JSONDecodeError:
                            # If all attempts fail, use a default structure
                            analysis = {
                                "query_type": "general",
                                "relevant_departments": [],
                                "priority": "normal",
                                "topics": [],
                                "explanation": "Generated after multiple JSON parsing failures"
                            }
                    else:
                        # Default structure if no valid JSON object found
                        analysis = {
                            "query_type": "general",
                            "relevant_departments": [],
                            "priority": "normal",
                            "topics": [],
                            "explanation": "Generated after multiple JSON parsing failures"
                        }

            # Ensure the analysis has the expected fields
            analysis.setdefault("query_type", "general")
            analysis.setdefault("relevant_departments", [])
            analysis.setdefault("priority", "normal")
            analysis.setdefault("topics", [])
            analysis.setdefault("explanation", "")

            # Filter departments to only include valid ones
            analysis["relevant_departments"] = [
                dept for dept in analysis["relevant_departments"]
                if dept in DEPARTMENTS
            ]

            return analysis
        except Exception as e:
            logger.error(f"Error parsing LLM analysis: {e}", exc_info=True)
            logger.error(f"Response that caused the error: {response}")
            # Return a default analysis
            return {
                "query_type": "general",
                "relevant_departments": [],
                "priority": "normal",
                "topics": [],
                "explanation": f"Error parsing LLM analysis: {str(e)}"
            }

    def _combine_analysis_results(
        self,
        embedding_scores: Dict[str, float],
        llm_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Combine embedding-based similarity scores with LLM analysis.

        Args:
            embedding_scores: Dictionary mapping departments to similarity scores
            llm_analysis: Dictionary with LLM analysis results

        Returns:
            Combined analysis results
        """
        # Start with the LLM analysis
        combined = llm_analysis.copy()

        # Add embedding scores
        combined["embedding_scores"] = embedding_scores

        # Determine relevant departments based on both methods
        llm_departments = set(llm_analysis.get("relevant_departments", []))

        # Add departments with high embedding scores
        embedding_departments = {
            dept for dept, score in embedding_scores.items()
            if score > 0.35  # Threshold for relevance
        }

        # Combine departments from both methods
        all_departments = llm_departments.union(embedding_departments)

        # If no departments were selected, pick the one with the highest embedding score
        if not all_departments and embedding_scores:
            top_dept = max(embedding_scores.items(), key=lambda x: x[1])[0]
            all_departments = {top_dept}

        combined["relevant_departments"] = list(all_departments)

        return combined

    async def route_to_departments(self, query: str, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Route a query to relevant department agents.

        Args:
            query: The user query
            analysis: The query analysis results

        Returns:
            List of department responses
        """
        logger.info(f"Routing query to departments: {analysis['relevant_departments']}")

        # Get the agent registry
        from app.agents.registry import agent_registry

        department_responses = []
        for dept in analysis["relevant_departments"]:
            try:
                # Get the department agent from the registry
                agent = agent_registry.get_agent(dept)

                if agent:
                    # Create context with analysis results
                    context = {
                        "analysis": analysis,
                        "additional_instructions": f"This query was routed to you by the Co-CEO agent because it was identified as relevant to the {dept} department."
                    }

                    # Process the query with the department agent
                    response = await agent.process_query(query, context)
                    department_responses.append(response)
                else:
                    logger.warning(f"Department agent not found: {dept}")
                    # Add a placeholder response if the agent is not found
                    department_responses.append({
                        "department": dept,
                        "response": f"I couldn't find information from the {dept} department at this time.",
                        "confidence": 0.0,
                        "timestamp": datetime.now().isoformat()
                    })
            except Exception as e:
                logger.error(f"Error routing to {dept} department: {e}", exc_info=True)
                # Add an error response
                department_responses.append({
                    "department": dept,
                    "response": f"I encountered an error while consulting the {dept} department.",
                    "error": str(e),
                    "confidence": 0.0,
                    "timestamp": datetime.now().isoformat()
                })

        return department_responses

    async def generate_response(
        self,
        query: str,
        analysis: Dict[str, Any],
        department_responses: List[Dict[str, Any]]
    ) -> str:
        """
        Generate a comprehensive response based on department inputs.

        Args:
            query: The original user query
            analysis: The query analysis results
            department_responses: Responses from department agents

        Returns:
            Synthesized response
        """
        # If no department responses, generate a direct response
        if not department_responses:
            return await self._generate_direct_response(query, analysis)

        # Create the prompt for response synthesis
        system_prompt = get_agent_prompt("co_ceo")

        # Format department responses
        dept_responses_text = ""
        for resp in department_responses:
            dept_responses_text += f"\n## {resp['department'].title()} Department Response:\n{resp['response']}\n"

        user_prompt = f"""
I need you to synthesize a comprehensive response to the user's query based on the information provided by different departments.

User Query: "{query}"

Query Analysis:
- Type: {analysis.get('query_type', 'general')}
- Topics: {', '.join(analysis.get('topics', []))}
- Priority: {analysis.get('priority', 'normal')}

Department Responses:
{dept_responses_text}

Please synthesize a coherent, comprehensive response that:
1. Addresses the user's query directly with specific details from the documents
2. Integrates information from all departments
3. Resolves any contradictions or inconsistencies
4. Provides clear, actionable insights
5. Uses a professional, helpful tone
6. Includes specific numbers, dates, and other concrete details from the documents
7. Avoids vague statements like "the documents do not provide detailed information" when specific information is available
8. Clearly cites the source of information (which department provided it)

Your response should be well-structured with appropriate headings and formatting. Be sure to extract and present all relevant specific details from the department responses.
"""

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await self.llm_adapter.chat(messages)
        return response

    async def _generate_direct_response(self, query: str, analysis: Dict[str, Any]) -> str:
        """
        Generate a direct response when no departments are consulted.

        Args:
            query: The user query
            analysis: The query analysis results

        Returns:
            Direct response
        """
        system_prompt = get_agent_prompt("co_ceo")

        user_prompt = f"""
Please respond directly to the following user query:

"{query}"

Since this query doesn't require specific department expertise, provide a helpful, general response based on your knowledge as the Co-CEO Agent.
"""

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await self.llm_adapter.chat(messages)
        return response
