"""
Agent and Tool Registry

This module implements the registry for managing agents and their associated tools.
It provides a central location for registering, retrieving, and managing agents and tools.

The registry is designed to:
1. Provide a single point of access for agents and tools
2. Support dynamic registration and retrieval
3. Enable iteration over registered agents
4. Support tool management per agent
"""

from typing import Dict, List, Any, Iterator, Tuple
import logging

logger = logging.getLogger(__name__)


class AgentRegistry:
    """
    Registry for managing agents and their associated tools.
    """

    def __init__(self):
        """Initialize the registry."""
        self.agents = {}
        self.tools = {}
        logger.info("Agent registry initialized")

    def register(self, name: str, agent_func, tools=None):
        """
        Register an agent with optional tools.

        Args:
            name: The name of the agent
            agent_func: The agent function or instance
            tools: Optional list of tools for the agent
        """
        self.agents[name] = agent_func

        if tools:
            # Store the tools in the registry
            self.tools[name] = tools

            # Create a dictionary of tools with the format "{department}.{tool_name}" as keys
            tools_dict = {}
            for tool in tools:
                tool_id = f"{tool.department}.{tool.name}"
                tools_dict[tool_id] = tool

            # Update the agent's tools attribute
            agent_func.tools.update(tools_dict)

        logger.info(f"Registered agent: {name}")

    def register_toolset(self, name: str, *tool_objs):
        """
        Register a set of tools for an agent.

        Args:
            name: The name of the agent
            tool_objs: The tool objects to register
        """
        if name not in self.agents:
            logger.warning(f"Registering tools for unknown agent: {name}")
        else:
            # Get the agent instance
            agent = self.agents[name]

            # Create a dictionary of tools with the format "{department}.{tool_name}" as keys
            tools_dict = {}
            for tool in tool_objs:
                tool_id = f"{tool.department}.{tool.name}"
                tools_dict[tool_id] = tool

            # Update the agent's tools attribute
            agent.tools.update(tools_dict)
            logger.info(f"Updated tools for agent: {name}")

        # Store the tools in the registry
        self.tools[name] = list(tool_objs)
        logger.info(f"Registered {len(tool_objs)} tools for agent: {name}")

    def get_agent(self, name: str):
        """
        Get an agent by name.

        Args:
            name: The name of the agent

        Returns:
            The agent function or None if not found
        """
        agent = self.agents.get(name)
        if agent is None:
            logger.warning(f"Agent not found: {name}")
        return agent

    def get_tools(self, name: str) -> List[Any]:
        """
        Get tools for an agent by name.

        Args:
            name: The name of the agent

        Returns:
            List of tools or empty list if none found
        """
        return self.tools.get(name, [])

    def list_agents(self) -> List[str]:
        """
        Get a list of all registered agent names.

        Returns:
            List of agent names
        """
        return list(self.agents.keys())

    def list_tools(self, name: str = None) -> Dict[str, List[str]]:
        """
        Get a dictionary of tool names by agent.

        Args:
            name: Optional agent name to filter by

        Returns:
            Dictionary of tool names by agent
        """
        if name:
            tools = self.get_tools(name)
            return {name: [tool.__class__.__name__ for tool in tools]}

        return {
            agent_name: [tool.__class__.__name__ for tool in tools]
            for agent_name, tools in self.tools.items()
        }

    def __iter__(self) -> Iterator[Tuple[str, Any]]:
        """
        Enable iteration over the registry.

        Returns:
            Iterator of (name, agent) pairs
        """
        return iter(self.agents.items())

    def __len__(self) -> int:
        """
        Get the number of registered agents.

        Returns:
            Number of agents in the registry
        """
        return len(self.agents)


# Create a singleton instance
agent_registry = AgentRegistry()


def get_registry() -> AgentRegistry:
    """
    Get the singleton registry instance.

    Returns:
        The registry instance
    """
    return agent_registry
