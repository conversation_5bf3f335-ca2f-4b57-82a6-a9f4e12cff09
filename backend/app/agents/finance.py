"""
Finance Department Agent Implementation

This module implements the Finance department agent, which is responsible for:
1. Processing finance-related queries
2. Using finance-specific tools
3. Generating finance-related responses
"""
import logging

from app.agents.department_agent import DepartmentAgent

logger = logging.getLogger(__name__)

class FinanceAgent(DepartmentAgent):
    """
    Finance Department Agent that handles finance-related queries.

    This agent is responsible for processing finance-related queries,
    using finance-specific tools, and generating responses.
    """

    def __init__(self, llm_adapter, knowledge_base_service, tools=None):
        """
        Initialize the Finance agent.

        Args:
            llm_adapter: The LLM adapter to use for generating responses
            knowledge_base_service: The knowledge base service for retrieving information
            tools: Finance-specific tools to use
        """
        super().__init__(llm_adapter, knowledge_base_service, department="finance", tools=tools)

    # All common functionality is now inherited from DepartmentAgent
