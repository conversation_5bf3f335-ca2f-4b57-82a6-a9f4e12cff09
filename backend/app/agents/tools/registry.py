"""
Tool Registry Integration

This module integrates the agent tools with the core tool registry.
It provides a centralized way to register all department-specific tools
with the core tool registry.
"""

import logging
from app.core.tools import register_tool
from .finance import BudgetAnalysisTool, ForecastTool
from .marketing import CampaignAnalysisTool, MarketResearchTool

logger = logging.getLogger(__name__)

# Initialize tool instances
budget_analysis_tool = BudgetAnalysisTool()
forecast_tool = ForecastTool()
campaign_analysis_tool = CampaignAnalysisTool()
market_research_tool = MarketResearchTool()

# List of all tool instances for easy iteration
ALL_TOOLS = [
    # Finance tools
    budget_analysis_tool,
    forecast_tool,

    # Marketing tools
    campaign_analysis_tool,
    market_research_tool,
]

def register_tools(override: bool = False):
    """
    Register all agent tools in the core tool registry.

    Args:
        override: Whether to override existing tools with the same ID

    Returns:
        Number of tools registered
    """
    count = 0
    for tool in ALL_TOOLS:
        try:
            register_tool(tool, override=override)
            count += 1
        except ValueError as e:
            logger.warning(f"Failed to register tool {tool.department}.{tool.name}: {e}")

    logger.info(f"Registered {count} tools")
    return count

# Register tools on import
register_tools()
