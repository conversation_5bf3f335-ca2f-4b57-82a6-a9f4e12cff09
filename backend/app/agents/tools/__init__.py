"""
Agent Tools Package

This package contains the tools used by the various agents in the system.
It provides specialized tools for each department agent and integrates them
with the core tool registry.
"""

# Import department-specific tools
from .finance import BudgetAnalysisTool, ForecastTool
from .marketing import CampaignAnalysisTool, MarketResearchTool

# Import registry integration
from .registry import register_tools

__all__ = [
    # Finance tools
    "BudgetAnalysisTool",
    "ForecastTool",

    # Marketing tools
    "CampaignAnalysisTool",
    "MarketResearchTool",

    # Registry
    "register_tools"
]
