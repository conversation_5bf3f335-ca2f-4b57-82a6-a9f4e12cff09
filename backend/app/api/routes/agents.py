"""
Agents API Routes

This module defines the API routes for agent interactions.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging
import uuid
from datetime import datetime

from app.agents import initialize_agents
from app.core import get_llm_adapter
from app.rag import initialize_knowledge_base_service
from app.config import get_settings

logger = logging.getLogger(__name__)

def get_provider_from_model_id(model_id: Optional[str]) -> str:
    """
    Determine the LLM provider from the model ID.

    Args:
        model_id: The model ID to check

    Returns:
        The provider name ('openai', 'anthropic', 'gemini', or 'mock')
    """
    if not model_id:
        return "openai"  # Default provider

    model_id = model_id.lower()

    if model_id.startswith(("gpt", "text-davinci")):
        return "openai"
    elif model_id.startswith("claude"):
        return "anthropic"
    elif model_id.startswith("gemini"):
        return "gemini"
    elif model_id.startswith("mock"):
        return "mock"
    else:
        logger.warning(f"Unknown model ID format: {model_id}, defaulting to OpenAI")
        return "openai"  # Default fallback

# Create router
router = APIRouter()

# Models
class QueryRequest(BaseModel):
    """Request model for agent queries."""
    query: str = Field(..., description="The user query")
    user_id: Optional[str] = Field(None, description="User ID for context")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversation context")
    model_id: Optional[str] = Field(None, description="Model ID to use for this query")

class QueryResponse(BaseModel):
    """Response model for agent queries."""
    response: str = Field(..., description="The agent response")
    thread_id: str = Field(..., description="Thread ID for the conversation")
    departments_consulted: List[str] = Field(default_factory=list, description="Departments consulted")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

# Routes
@router.post("/query", response_model=QueryResponse)
async def query_agent(
    request: QueryRequest
):
    """
    Query the Co-CEO agent.

    This endpoint processes a user query through the Co-CEO agent, which may
    consult with department agents as needed.
    """
    # Use the user ID from the request
    user_id = request.user_id or "cli-user"
    try:
        # Get dependencies
        # If model_id is provided, use it to determine the provider
        if request.model_id:
            provider = get_provider_from_model_id(request.model_id)
            # Get API key from settings
            settings = get_settings()
            api_key = None
            if provider == "openai":
                api_key = settings.OPENAI_API_KEY
            elif provider == "anthropic":
                api_key = settings.ANTHROPIC_API_KEY
            elif provider == "gemini":
                api_key = settings.GOOGLE_API_KEY

            llm_adapter = get_llm_adapter(
                provider=provider,
                model=request.model_id,
                api_key=api_key
            )
            logger.info(f"Using model {request.model_id} with provider {provider}")
        else:
            # Get default provider and API key from settings
            settings = get_settings()
            provider = settings.DEFAULT_LLM_PROVIDER
            api_key = None
            if provider == "openai":
                api_key = settings.OPENAI_API_KEY
            elif provider == "anthropic":
                api_key = settings.ANTHROPIC_API_KEY
            elif provider == "gemini":
                api_key = settings.GOOGLE_API_KEY

            llm_adapter = get_llm_adapter(
                provider=provider,
                api_key=api_key
            )

        # Initialize Firebase client
        # Firebase client initialization removed

        # Initialize the knowledge base service with the Firebase client
        knowledge_base_service = await initialize_knowledge_base_service()

        # Initialize and register agents
        agents = initialize_agents(llm_adapter, knowledge_base_service)
        co_ceo_agent = agents["co_ceo"]
        finance_agent = agents["finance"]
        marketing_agent = agents["marketing"]

        # Process query using LangGraph
        from app.langgraph.graph import process_query

        thread_id = request.thread_id or str(uuid.uuid4())
        result = await process_query(
            query=request.query,
            co_ceo_agent=co_ceo_agent,
            finance_agent=finance_agent,
            marketing_agent=marketing_agent,
            knowledge_base_service=knowledge_base_service,
            user_id=user_id,
            thread_id=thread_id,
            timeout_seconds=30
        )

        # Extract departments consulted from the result
        departments_consulted = []
        if result.get("metadata") and result["metadata"].get("departments"):
            departments_consulted = result["metadata"]["departments"]

        # Return response
        return QueryResponse(
            response=result.get("response", "I'm sorry, I couldn't process your request."),
            thread_id=thread_id,
            departments_consulted=departments_consulted,
            metadata={
                "timestamp": datetime.now().isoformat(),
                "query": request.query,
                "model_id": request.model_id,
                "routing_decision": result.get("metadata", {}).get("routing_decision", {})
            }
        )
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@router.get("/departments", response_model=List[str])
async def list_departments():
    """
    List available departments.

    This endpoint returns a list of available department agents.
    """
    return ["co_ceo", "finance", "marketing"]
