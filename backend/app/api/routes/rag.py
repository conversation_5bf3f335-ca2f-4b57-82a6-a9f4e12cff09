"""
RAG API Routes

This module defines the API routes for RAG (Retrieval-Augmented Generation) interactions.
"""

from fastapi import APIRouter, HTTPException, File, UploadFile
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import logging

from app.rag import initialize_knowledge_base_service

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Models
class SearchRequest(BaseModel):
    """Request model for knowledge base search."""
    query: str = Field(..., description="The search query")
    collection: Optional[str] = Field("knowledge", description="The collection to search")
    limit: Optional[int] = Field(5, description="Maximum number of results to return")
    search_type: Optional[str] = Field("hybrid", description="Search type: vector, keyword, or hybrid")

class SearchResponse(BaseModel):
    """Response model for knowledge base search."""
    results: List[Dict[str, Any]] = Field(..., description="Search results")
    query: str = Field(..., description="The original query")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class DocumentRequest(BaseModel):
    """Request model for document operations."""
    content: str = Field(..., description="Document content")
    title: str = Field(..., description="Document title")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    collection: Optional[str] = Field("knowledge", description="Collection to store the document in")

class DocumentResponse(BaseModel):
    """Response model for document operations."""
    id: str = Field(..., description="Document ID")
    title: str = Field(..., description="Document title")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")

# Routes
@router.post("/search", response_model=SearchResponse)
async def search_knowledge_base(request: SearchRequest):
    """
    Search the knowledge base.

    This endpoint searches the knowledge base for relevant documents.
    """
    try:
        # Initialize knowledge base service with proper embedding model
        knowledge_base_service = await initialize_knowledge_base_service()

        # Search knowledge base
        results = await knowledge_base_service.search(
            request.query,
            collection=request.collection,
            limit=request.limit,
            search_type=request.search_type
        )

        # Return response
        return SearchResponse(
            results=results,
            query=request.query,
            metadata={
                "count": len(results),
                "search_type": request.search_type
            }
        )
    except Exception as e:
        logger.error(f"Error searching knowledge base: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching knowledge base: {str(e)}")

@router.post("/documents", response_model=DocumentResponse)
async def add_document(request: DocumentRequest):
    """
    Add a document to the knowledge base.

    This endpoint adds a document to the knowledge base.
    """
    try:
        # Initialize knowledge base service with proper embedding model
        knowledge_base_service = await initialize_knowledge_base_service()

        # Create document
        document = {
            "title": request.title,
            "content": request.content,
            **request.metadata
        }

        # Add document to knowledge base
        doc_id = await knowledge_base_service.add_document(
            document,
            collection=request.collection
        )

        # Return response
        return DocumentResponse(
            id=doc_id,
            title=request.title,
            metadata=request.metadata
        )
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise HTTPException(status_code=500, detail=f"Error adding document: {str(e)}")

@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    title: str,
    collection: Optional[str] = "knowledge",
    file: UploadFile = File(...)
):
    """
    Upload a document to the knowledge base.

    This endpoint uploads a file and adds it to the knowledge base.
    """
    try:
        # Read file content
        content = await file.read()
        content_str = content.decode("utf-8")

        # Initialize knowledge base service with proper embedding model
        knowledge_base_service = await initialize_knowledge_base_service()

        # Create document
        document = {
            "title": title,
            "content": content_str,
            "filename": file.filename,
            "content_type": file.content_type
        }

        # Add document to knowledge base
        doc_id = await knowledge_base_service.add_document(
            document,
            collection=collection
        )

        # Return response
        return DocumentResponse(
            id=doc_id,
            title=title,
            metadata={
                "filename": file.filename,
                "content_type": file.content_type
            }
        )
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@router.get("/collections", response_model=List[str])
async def list_collections():
    """
    List available collections.

    This endpoint returns a list of available collections in the knowledge base.
    """
    try:
        # Initialize knowledge base service with proper embedding model
        knowledge_base_service = await initialize_knowledge_base_service()

        # Get collections
        collections = await knowledge_base_service.list_collections()

        # Return collections
        return collections
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing collections: {str(e)}")
