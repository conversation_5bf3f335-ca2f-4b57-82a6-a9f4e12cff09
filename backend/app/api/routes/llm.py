"""
LLM API Routes

This module provides API routes for interacting with LLM providers.
"""

from typing import Dict, Any, List, Optional
import logging
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
import os
import httpx
from app.core.llm import get_llm_adapter
from app.api.middleware.auth import get_optional_user
from app.config import get_settings

router = APIRouter()
logger = logging.getLogger(__name__)

class AnthropicMessage(BaseModel):
    role: str
    content: str

class AnthropicRequest(BaseModel):
    model: str
    messages: List[AnthropicMessage]
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7
    stream: Optional[bool] = False
    system: Optional[str] = None

class AnthropicResponse(BaseModel):
    id: str
    type: str
    role: str
    content: List[Dict[str, Any]]
    model: str
    stop_reason: Optional[str] = None
    stop_sequence: Optional[str] = None
    usage: Dict[str, int]

@router.post("/proxy/anthropic", response_model=AnthropicResponse)
async def proxy_anthropic_api(request_data: AnthropicRequest, user_info: Optional[Dict[str, Any]] = Depends(get_optional_user())):
    """
    Proxy requests to the Anthropic API.
    
    This endpoint forwards requests to the Anthropic API, using the server's API key.
    It's a direct replacement for the Firebase function that was previously used.
    """
    try:
        # Get the Anthropic API key from environment variables
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            logger.error("ANTHROPIC_API_KEY environment variable is not set")
            raise HTTPException(status_code=500, detail="Server configuration error")
        
        # Forward the request to Anthropic API
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://api.anthropic.com/v1/messages",
                json=request_data.model_dump(exclude_none=True),
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": api_key,
                    "anthropic-version": "2023-06-01"
                }
            )
            
            # Check for errors
            if response.status_code != 200:
                logger.error(f"Error from Anthropic API: {response.status_code} {response.text}")
                return HTTPException(
                    status_code=response.status_code,
                    detail=response.json() if response.headers.get("content-type") == "application/json" else response.text
                )
            
            # Return the response from Anthropic
            return response.json()
    except httpx.RequestError as e:
        logger.error(f"Error making request to Anthropic API: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to proxy request to Anthropic API: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in Anthropic API proxy: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

class ModelInfoResponse(BaseModel):
    provider: str
    model_id: str
    capabilities: List[str]
    max_tokens: int
    pricing: Dict[str, float]
    description: str

@router.get("/models", response_model=List[ModelInfoResponse])
async def list_available_models(user_info: Optional[Dict[str, Any]] = Depends(get_optional_user())):
    """
    List available LLM models.
    
    This endpoint returns information about the available LLM models,
    including their capabilities and pricing.
    """
    # Get available API keys from environment
    settings = get_settings()
    available_providers = []
    
    if settings.OPENAI_API_KEY:
        available_providers.append("openai")
    if settings.ANTHROPIC_API_KEY:
        available_providers.append("anthropic")
    if settings.GOOGLE_API_KEY:
        available_providers.append("gemini")
    
    # Define model information
    models = []
    
    if "openai" in available_providers:
        models.extend([
            ModelInfoResponse(
                provider="openai",
                model_id="gpt-4.1-2025-04-14",
                capabilities=["chat", "function_calling", "streaming"],
                max_tokens=128000,
                pricing={"input": 0.01, "output": 0.03},
                description="GPT-4.1 Turbo - OpenAI's most advanced model"
            ),
            ModelInfoResponse(
                provider="openai",
                model_id="gpt-4o-2024-05-13",
                capabilities=["chat", "function_calling", "streaming"],
                max_tokens=128000,
                pricing={"input": 0.005, "output": 0.015},
                description="GPT-4o - OpenAI's most capable multimodal model"
            )
        ])
    
    if "anthropic" in available_providers:
        models.extend([
            ModelInfoResponse(
                provider="anthropic",
                model_id="claude-3-7-sonnet-20250219",
                capabilities=["chat", "streaming"],
                max_tokens=200000,
                pricing={"input": 0.003, "output": 0.015},
                description="Claude 3.7 Sonnet - Anthropic's most advanced model"
            ),
            ModelInfoResponse(
                provider="anthropic",
                model_id="claude-3-opus-20240229",
                capabilities=["chat", "streaming"],
                max_tokens=200000,
                pricing={"input": 0.015, "output": 0.075},
                description="Claude 3 Opus - Anthropic's most capable model"
            )
        ])
    
    if "gemini" in available_providers:
        models.extend([
            ModelInfoResponse(
                provider="gemini",
                model_id="gemini-2.0-flash",
                capabilities=["chat", "streaming"],
                max_tokens=32768,
                pricing={"input": 0.00035, "output": 0.00035},
                description="Gemini 2.0 Flash - Google's fastest model"
            ),
            ModelInfoResponse(
                provider="gemini",
                model_id="gemini-2.5-pro-exp-03-25",
                capabilities=["chat", "streaming"],
                max_tokens=1048576,
                pricing={"input": 0.0005, "output": 0.0005},
                description="Gemini 2.5 Pro - Google's most capable model"
            )
        ])
    
    return models
