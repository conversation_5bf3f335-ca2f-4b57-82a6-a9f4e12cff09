"""
API Error Handlers

This module provides error handlers for the API routes.
It uses the error handling utilities from app.core.errors.
"""
from typing import Dict, Any, Callable
import logging
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException

from app.core.errors.base import (
    AppError, ValidationError, AuthenticationError, AuthorizationError,
    ResourceNotFoundError, ExternalServiceError, DatabaseError,
    TimeoutError, RateLimitError, ErrorCategory, format_error_for_response,
    log_error, create_error_response
)

logger = logging.getLogger(__name__)


async def app_error_handler(request: Request, exc: AppError) -> JSONResponse:
    """
    Handle AppError exceptions in FastAPI routes.

    Args:
        request: The FastAPI request
        exc: The AppError exception

    Returns:
        JSONResponse with error details
    """
    # Log the error
    log_error(
        exc,
        context={
            "request_url": str(request.url),
            "request_method": request.method,
            "client_host": request.client.host if request.client else None
        }
    )

    # Create the error response
    response = create_error_response(exc)

    # Determine the status code
    status_code = exc.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR

    return JSONResponse(
        status_code=status_code,
        content=response
    )


async def validation_error_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle FastAPI RequestValidationError exceptions.

    Args:
        request: The FastAPI request
        exc: The RequestValidationError exception

    Returns:
        JSONResponse with error details
    """
    # Convert to our ValidationError
    errors = exc.errors()
    error_details = {}

    for error in errors:
        loc = ".".join([str(l) for l in error.get("loc", [])])
        error_details[loc] = error.get("msg", "")

    app_error = ValidationError(
        message="Request validation failed",
        details={"validation_errors": error_details}
    )

    # Log the error
    log_error(
        app_error,
        context={
            "request_url": str(request.url),
            "request_method": request.method,
            "client_host": request.client.host if request.client else None,
            "original_errors": errors
        }
    )

    # Create the error response
    response = create_error_response(app_error)

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=response
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle FastAPI HTTPException exceptions.

    Args:
        request: The FastAPI request
        exc: The HTTPException exception

    Returns:
        JSONResponse with error details
    """
    # Map status code to appropriate AppError
    if exc.status_code == status.HTTP_401_UNAUTHORIZED:
        app_error = AuthenticationError(message=exc.detail)
    elif exc.status_code == status.HTTP_403_FORBIDDEN:
        app_error = AuthorizationError(message=exc.detail)
    elif exc.status_code == status.HTTP_404_NOT_FOUND:
        app_error = ResourceNotFoundError(message=exc.detail)
    elif exc.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
        app_error = RateLimitError(message=exc.detail)
    else:
        app_error = AppError(
            message=exc.detail,
            status_code=exc.status_code
        )

    # Log the error
    log_error(
        app_error,
        context={
            "request_url": str(request.url),
            "request_method": request.method,
            "client_host": request.client.host if request.client else None,
            "status_code": exc.status_code
        }
    )

    # Create the error response
    response = create_error_response(app_error)

    return JSONResponse(
        status_code=exc.status_code,
        content=response
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle general exceptions in FastAPI routes.

    Args:
        request: The FastAPI request
        exc: The exception

    Returns:
        JSONResponse with error details
    """
    # Convert to AppError
    app_error = AppError(
        message="An unexpected error occurred",
        category=ErrorCategory.INTERNAL,
        details={"error_type": type(exc).__name__},
        cause=exc
    )

    # Log the error
    log_error(
        app_error,
        context={
            "request_url": str(request.url),
            "request_method": request.method,
            "client_host": request.client.host if request.client else None,
            "exception_type": type(exc).__name__
        },
        log_level=logging.ERROR
    )

    # Create the error response
    response = create_error_response(app_error)

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response
    )


def register_error_handlers(app) -> None:
    """
    Register all error handlers with the FastAPI app.

    Args:
        app: The FastAPI app
    """
    app.add_exception_handler(AppError, app_error_handler)
    app.add_exception_handler(RequestValidationError, validation_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
