"""
BusinessLM CLI - Command Line Interface for the BusinessLM Python Backend

This module provides a command-line interface for interacting with the BusinessLM
Python backend without requiring the frontend.
"""

import os
import sys
import json
import asyncio
import logging
from typing import Optional, List, Dict, Any

import typer
from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint
from dotenv import load_dotenv

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize Typer app
app = typer.Typer(
    name="businesslm",
    help="Command Line Interface for the BusinessLM Python Backend",
    add_completion=False,
)

# Initialize Rich console
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("businesslm-cli")

# Load environment variables
load_dotenv()


@app.callback()
def callback():
    """
    BusinessLM CLI - Command Line Interface for the BusinessLM Python Backend
    """
    # This function will be called before any command
    pass


@app.command()
def query(
    message: str = typer.Argument(..., help="The message to send to the agent"),
    agent: str = typer.Option("co_ceo", help="The agent to query (co_ceo, finance, marketing)"),
    model: str = typer.Option(None, help="The model to use for the query"),
    thread_id: str = typer.Option(None, help="Thread ID for continuing a conversation"),
    user_id: str = typer.Option("cli-user", help="User ID for the query"),
):
    """
    Send a query to an agent and get a response.
    """
    from app.agents import initialize_agents
    from app.core import get_llm_adapter
    # Firebase import removed
    from app.rag import initialize_knowledge_base_service

    async def run_query():
        try:
            # Initialize dependencies
            console.print(f"Initializing LLM adapter...", style="yellow")
            llm_adapter = get_llm_adapter(model_id=model)

            console.print(f"Initializing Firebase client...", style="yellow")
            # Firebase client initialization removed

            console.print(f"Initializing knowledge base service...", style="yellow")
            knowledge_base_service = await initialize_knowledge_base_service()

            console.print(f"Initializing agents...", style="yellow")
            agents = initialize_agents(llm_adapter, knowledge_base_service)

            # Get the requested agent
            if agent not in agents:
                console.print(f"Error: Agent '{agent}' not found. Available agents: {', '.join(agents.keys())}", style="red")
                return

            selected_agent = agents[agent]

            # Process query
            console.print(f"\n[bold cyan]Sending query to {agent} agent:[/bold cyan] {message}")

            # Use LangGraph to process the query
            from app.langgraph.graph import process_query

            # Generate a thread ID if not provided
            nonlocal thread_id
            if not thread_id:
                import uuid
                thread_id = str(uuid.uuid4())
                console.print(f"Generated new thread ID: {thread_id}", style="yellow")

            # Process the query
            result = await process_query(
                query=message,
                thread_id=thread_id,
                user_id=user_id,
                agents=agents,
                llm_adapter=llm_adapter,
                knowledge_base_service=knowledge_base_service,
            )

            # Display the response
            console.print("\n[bold green]Response:[/bold green]")
            console.print(Panel(Markdown(result.get("response", "No response received.")), title=f"{agent.upper()} Agent"))

            # Display metadata
            if "metadata" in result and result["metadata"]:
                metadata_table = Table(title="Response Metadata")
                metadata_table.add_column("Key", style="cyan")
                metadata_table.add_column("Value", style="green")

                for key, value in result["metadata"].items():
                    if isinstance(value, dict):
                        metadata_table.add_row(key, json.dumps(value, indent=2))
                    else:
                        metadata_table.add_row(key, str(value))

                console.print(metadata_table)

            # Display departments consulted
            if "departments_consulted" in result and result["departments_consulted"]:
                console.print(f"\n[bold yellow]Departments consulted:[/bold yellow] {', '.join(result['departments_consulted'])}")

            console.print(f"\n[bold blue]Thread ID for continuing this conversation:[/bold blue] {thread_id}")

        except Exception as e:
            console.print(f"Error: {str(e)}", style="red")
            logger.exception("Error in query command")

    # Run the async function
    asyncio.run(run_query())


@app.command()
def list_agents():
    """
    List all available agents.
    """
    from app.agents import initialize_agents
    from app.core import get_llm_adapter
    # Firebase import removed
    from app.rag import initialize_knowledge_base_service

    async def run_list_agents():
        try:
            # Initialize dependencies
            llm_adapter = get_llm_adapter()
            # Firebase client initialization removed
            knowledge_base_service = await initialize_knowledge_base_service()

            # Initialize agents
            agents = initialize_agents(llm_adapter, knowledge_base_service)

            # Display agents
            agents_table = Table(title="Available Agents")
            agents_table.add_column("Agent ID", style="cyan")
            agents_table.add_column("Description", style="green")

            agent_descriptions = {
                "co_ceo": "Strategic oversight and cross-department coordination",
                "finance": "Financial planning, budgeting, and analysis",
                "marketing": "Brand strategy, digital marketing, and content",
                # Add more as needed
            }

            for agent_id in agents.keys():
                description = agent_descriptions.get(agent_id, "No description available")
                agents_table.add_row(agent_id, description)

            console.print(agents_table)

        except Exception as e:
            console.print(f"Error: {str(e)}", style="red")
            logger.exception("Error in list_agents command")

    # Run the async function
    asyncio.run(run_list_agents())


@app.command()
def health():
    """
    Check the health of the BusinessLM backend.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Make a request to the health endpoint
        response = requests.get(f"{backend_url}/health")

        if response.status_code == 200:
            data = response.json()
            console.print(f"[bold green]Backend is healthy![/bold green]")
            console.print(f"Version: {data.get('version', 'unknown')}")
            console.print(f"Status: {data.get('status', 'unknown')}")
        else:
            console.print(f"[bold red]Backend health check failed with status code {response.status_code}[/bold red]")
            console.print(response.text)

    except Exception as e:
        console.print(f"[bold red]Error connecting to backend: {str(e)}[/bold red]")
        logger.exception("Error in health command")


@app.command()
def test_anthropic():
    """
    Test the Anthropic API proxy.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Prepare request data
        data = {
            "model": "claude-3-7-sonnet-20250219",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, Claude! What's the capital of France?"
                }
            ],
            "max_tokens": 100,
            "temperature": 0.7,
        }

        # Make a request to the Anthropic API proxy endpoint
        console.print("[bold yellow]Testing Anthropic API proxy...[/bold yellow]")
        response = requests.post(
            f"{backend_url}/api/llm/proxy/anthropic",
            json=data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            result = response.json()

            # Display the response
            console.print("\n[bold green]Anthropic API Response:[/bold green]")

            # Extract content from the response
            content = result.get("content", [])
            if content and isinstance(content, list) and len(content) > 0:
                text = content[0].get("text", "No response text")
                console.print(Markdown(text))
            else:
                console.print("No content in response")

            # Display metadata
            console.print("\n[bold blue]Response Metadata:[/bold blue]")
            metadata_table = Table()
            metadata_table.add_column("Key", style="cyan")
            metadata_table.add_column("Value", style="green")

            metadata_table.add_row("Model", result.get("model", "unknown"))
            metadata_table.add_row("ID", result.get("id", "unknown"))
            metadata_table.add_row("Stop Reason", result.get("stop_reason", "unknown"))

            if "usage" in result:
                usage = result["usage"]
                metadata_table.add_row("Input Tokens", str(usage.get("input_tokens", 0)))
                metadata_table.add_row("Output Tokens", str(usage.get("output_tokens", 0)))

            console.print(metadata_table)
        else:
            console.print(f"[bold red]API request failed with status code {response.status_code}[/bold red]")
            console.print(response.text)

    except Exception as e:
        console.print(f"[bold red]Error testing Anthropic API proxy: {str(e)}[/bold red]")
        logger.exception("Error in test_anthropic command")


@app.command()
def test_openai():
    """
    Test the OpenAI API integration.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Check if we're in development mode
        dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
        if dev_mode:
            console.print("[bold yellow]Running in development mode with mock OpenAI adapter[/bold yellow]")

        # Prepare a simple test query
        data = {
            "query": "What's the capital of France?",
            "model_id": "gpt-4o-2024-05-13",
            "department": None,
        }

        # Make the request
        console.print("[bold yellow]Testing OpenAI integration via agent query...[/bold yellow]")
        response = requests.post(
            f"{backend_url}/api/agents/query",
            json=data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            result = response.json()

            # Display the response
            console.print("\n[bold green]OpenAI Response:[/bold green]")
            console.print(Markdown(result.get("response", "No response received.")))

            # Display metadata
            if "metadata" in result:
                metadata_table = Table(title="Response Metadata")
                metadata_table.add_column("Key", style="cyan")
                metadata_table.add_column("Value", style="green")

                for key, value in result["metadata"].items():
                    if isinstance(value, dict):
                        metadata_table.add_row(key, json.dumps(value, indent=2))
                    else:
                        metadata_table.add_row(key, str(value))

                console.print(metadata_table)

            console.print("\n[bold green]✓ OpenAI integration test successful[/bold green]")
        else:
            console.print(f"[bold red]API request failed with status code {response.status_code}[/bold red]")
            console.print(response.text)

    except Exception as e:
        console.print(f"[bold red]Error testing OpenAI integration: {str(e)}[/bold red]")
        logger.exception("Error in test_openai command")


@app.command()
def test_gemini():
    """
    Test the Google Gemini API integration.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Check if we're in development mode
        dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
        if dev_mode:
            console.print("[bold yellow]Running in development mode with mock Gemini adapter[/bold yellow]")

        # Prepare a simple test query
        data = {
            "query": "What's the capital of France?",
            "model_id": "gemini-2.0-flash",
            "department": None,
        }

        # Make the request
        console.print("[bold yellow]Testing Gemini integration via agent query...[/bold yellow]")
        response = requests.post(
            f"{backend_url}/api/agents/query",
            json=data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            result = response.json()

            # Display the response
            console.print("\n[bold green]Gemini Response:[/bold green]")
            console.print(Markdown(result.get("response", "No response received.")))

            # Display metadata
            if "metadata" in result:
                metadata_table = Table(title="Response Metadata")
                metadata_table.add_column("Key", style="cyan")
                metadata_table.add_column("Value", style="green")

                for key, value in result["metadata"].items():
                    if isinstance(value, dict):
                        metadata_table.add_row(key, json.dumps(value, indent=2))
                    else:
                        metadata_table.add_row(key, str(value))

                console.print(metadata_table)

            console.print("\n[bold green]✓ Gemini integration test successful[/bold green]")
        else:
            console.print(f"[bold red]API request failed with status code {response.status_code}[/bold red]")
            console.print(response.text)

    except Exception as e:
        console.print(f"[bold red]Error testing Gemini integration: {str(e)}[/bold red]")
        logger.exception("Error in test_gemini command")


@app.command()
def test_all_llms():
    """
    Test all available LLM integrations.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Get available models
        console.print("[bold yellow]Fetching available LLM models...[/bold yellow]")
        response = requests.get(f"{backend_url}/api/llm/models")

        if response.status_code != 200:
            console.print(f"[bold red]Failed to fetch available models: {response.status_code}[/bold red]")
            return

        models = response.json()

        # Group models by provider
        providers = {}
        for model in models:
            provider = model["provider"]
            if provider not in providers:
                providers[provider] = []
            providers[provider].append(model)

        # Display available providers
        console.print(f"[bold blue]Found {len(providers)} LLM providers:[/bold blue] {', '.join(providers.keys())}")

        # Test each provider with its first model
        results_table = Table(title="LLM Integration Test Results")
        results_table.add_column("Provider", style="cyan")
        results_table.add_column("Model", style="blue")
        results_table.add_column("Status", style="green")
        results_table.add_column("Response Time", style="yellow")

        for provider, provider_models in providers.items():
            model = provider_models[0]  # Use the first model for each provider

            console.print(f"\n[bold yellow]Testing {provider} with model {model['model_id']}...[/bold yellow]")

            # Prepare a simple test query
            data = {
                "query": "What's the capital of France?",
                "model_id": model["model_id"],
                "department": None,
            }

            # Time the request
            import time
            start_time = time.time()

            # Make the request
            response = requests.post(
                f"{backend_url}/api/agents/query",
                json=data,
                headers={"Content-Type": "application/json"}
            )

            # Calculate response time
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()

                # Display a snippet of the response
                response_text = result.get("response", "No response received.")
                snippet = response_text[:100] + "..." if len(response_text) > 100 else response_text
                console.print(f"[green]Response snippet:[/green] {snippet}")

                # Add to results table
                results_table.add_row(
                    provider,
                    model["model_id"],
                    "[bold green]✓ Success[/bold green]",
                    f"{response_time:.2f}s"
                )
            else:
                console.print(f"[bold red]Failed: {response.status_code}[/bold red]")

                # Add to results table
                results_table.add_row(
                    provider,
                    model["model_id"],
                    f"[bold red]✗ Failed ({response.status_code})[/bold red]",
                    f"{response_time:.2f}s"
                )

        # Display results table
        console.print("\n")
        console.print(results_table)

    except Exception as e:
        console.print(f"[bold red]Error testing LLM integrations: {str(e)}[/bold red]")
        logger.exception("Error in test_all_llms command")


@app.command()
def test_jwt_auth():
    """
    Test JWT authentication.
    """
    import requests

    try:
        # Get the backend URL from environment or use default
        backend_url = os.getenv("BACKEND_URL", "http://localhost:8000")

        # Check if we're in development mode
        dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
        if dev_mode:
            console.print("[bold yellow]Running in development mode with mock JWT authentication[/bold yellow]")

        # Test the protected endpoint
        console.print("[bold yellow]Testing JWT authentication...[/bold yellow]")
        response = requests.get(f"{backend_url}/api/auth/user")

        if response.status_code == 200:
            user_info = response.json()

            # Display user info
            console.print("\n[bold green]Authentication successful![/bold green]")

            user_table = Table(title="User Information")
            user_table.add_column("Field", style="cyan")
            user_table.add_column("Value", style="green")

            # Add user info to table
            for key, value in user_info.items():
                if isinstance(value, dict):
                    user_table.add_row(key, json.dumps(value, indent=2))
                elif isinstance(value, list):
                    user_table.add_row(key, ", ".join(str(item) for item in value))
                else:
                    user_table.add_row(key, str(value))

            console.print(user_table)

            # Check if we're using a mock user in development mode
            if dev_mode and user_info.get("id") == "dev-user-123":
                console.print("[bold blue]Using mock user in development mode[/bold blue]")

            console.print("\n[bold green]✓ JWT authentication test successful[/bold green]")
        else:
            console.print(f"[bold red]Authentication failed with status code {response.status_code}[/bold red]")
            console.print(response.text)

    except Exception as e:
        console.print(f"[bold red]Error testing JWT authentication: {str(e)}[/bold red]")
        logger.exception("Error in test_jwt_auth command")


@app.command()
def test_database():
    """
    Test PostgreSQL database operations.
    """
    from app.core.db.database import get_db_context
    from sqlalchemy import text
    import time

    async def run_test():
        try:
            # Check if we're in development mode
            dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
            if dev_mode:
                console.print("[bold yellow]Running in development mode[/bold yellow]")

            # Initialize database connection
            console.print("[bold yellow]Initializing database connection...[/bold yellow]")

            # Create a test table
            test_table = f"cli_test_{int(time.time())}"
            console.print(f"[bold yellow]Creating test table '{test_table}'...[/bold yellow]")

            with get_db_context() as db:
                # Create test table
                db.execute(text(
                    f"CREATE TABLE {test_table} ("
                    f"id SERIAL PRIMARY KEY, "
                    f"name TEXT NOT NULL, "
                    f"created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), "
                    f"test_value INTEGER, "
                    f"tags TEXT[])"
                ))
                db.commit()
                console.print("[green]Test table created successfully[/green]")

                # Insert test data
                test_data = {
                    "name": "Test Document",
                    "test_value": 42,
                    "tags": "{test,cli,database}"  # PostgreSQL array syntax
                }

                # Insert data
                console.print(f"[bold yellow]Inserting test data into '{test_table}'...[/bold yellow]")
                result = db.execute(text(
                    f"INSERT INTO {test_table} (name, test_value, tags) "
                    f"VALUES (:name, :test_value, :tags) RETURNING id"
                ), test_data)
                db.commit()

                # Get the inserted ID
                row_id = result.scalar()
                console.print(f"[green]Data inserted with ID: {row_id}[/green]")

                # Query the data
                console.print(f"[bold yellow]Querying data with ID {row_id}...[/bold yellow]")
                result = db.execute(text(
                    f"SELECT * FROM {test_table} WHERE id = :id"
                ), {"id": row_id}).fetchone()

                if result:
                    console.print("[green]Data retrieved successfully[/green]")

                    # Display data
                    data_table = Table(title=f"Row {row_id}")
                    data_table.add_column("Column", style="cyan")
                    data_table.add_column("Value", style="green")

                    for column, value in result._mapping.items():
                        data_table.add_row(str(column), str(value))

                    console.print(data_table)
                else:
                    console.print("[bold red]Failed to retrieve data[/bold red]")
                    return

                # Update data
                update_data = {"test_value": 99, "id": row_id}
                console.print(f"[bold yellow]Updating data with ID {row_id}...[/bold yellow]")
                db.execute(text(
                    f"UPDATE {test_table} SET test_value = :test_value WHERE id = :id"
                ), update_data)
                db.commit()
                console.print("[green]Data updated successfully[/green]")

                # Query updated data
                console.print(f"[bold yellow]Querying updated data with ID {row_id}...[/bold yellow]")
                result = db.execute(text(
                    f"SELECT * FROM {test_table} WHERE id = :id"
                ), {"id": row_id}).fetchone()

                if result:
                    console.print("[green]Updated data retrieved successfully[/green]")

                    # Display updated data
                    updated_table = Table(title=f"Updated Row {row_id}")
                    updated_table.add_column("Column", style="cyan")
                    updated_table.add_column("Value", style="green")

                    for column, value in result._mapping.items():
                        updated_table.add_row(str(column), str(value))

                    console.print(updated_table)

                    # Verify update
                    if result.test_value == 99:
                        console.print("[bold green]✓ Update verification successful[/bold green]")
                    else:
                        console.print("[bold red]Update verification failed[/bold red]")
                else:
                    console.print("[bold red]Failed to retrieve updated data[/bold red]")

                # Delete data
                console.print(f"[bold yellow]Deleting data with ID {row_id}...[/bold yellow]")
                db.execute(text(
                    f"DELETE FROM {test_table} WHERE id = :id"
                ), {"id": row_id})
                db.commit()
                console.print("[green]Data deleted successfully[/green]")

                # Verify deletion
                result = db.execute(text(
                    f"SELECT * FROM {test_table} WHERE id = :id"
                ), {"id": row_id}).fetchone()

                if result is None:
                    console.print("[bold green]✓ Deletion verification successful[/bold green]")
                else:
                    console.print("[bold red]Deletion verification failed[/bold red]")

                # Drop test table
                console.print(f"[bold yellow]Dropping test table '{test_table}'...[/bold yellow]")
                db.execute(text(f"DROP TABLE {test_table}"))
                db.commit()
                console.print("[green]Test table dropped successfully[/green]")

            console.print("\n[bold green]✓ Database test completed successfully[/bold green]")

        except Exception as e:
            console.print(f"[bold red]Error testing database: {str(e)}[/bold red]")
            logger.exception("Error in test_database command")

    # Run the async function
    asyncio.run(run_test())


@app.command()
def test_rag():
    """
    Test the RAG (Retrieval Augmented Generation) pipeline.
    """
    from app.core import get_llm_adapter
    # Firebase import removed
    from app.rag import initialize_knowledge_base_service

    async def run_test():
        try:
            # Check if we're in development mode
            dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
            if dev_mode:
                console.print("[bold yellow]Running in development mode with mock implementations[/bold yellow]")

            # Initialize dependencies
            console.print("[bold yellow]Initializing LLM adapter...[/bold yellow]")
            llm_adapter = get_llm_adapter()

            console.print("[bold yellow]Initializing Firebase client...[/bold yellow]")
            # Firebase client initialization removed

            console.print("[bold yellow]Initializing knowledge base service...[/bold yellow]")
            knowledge_base_service = await initialize_knowledge_base_service()

            # Test query
            test_query = "What are our company's marketing goals?"
            console.print(f"[bold yellow]Testing RAG pipeline with query: [/bold yellow]{test_query}")

            # Get relevant knowledge
            console.print("[bold yellow]Retrieving relevant knowledge...[/bold yellow]")
            from app.rag.retriever import retrieve_relevant_knowledge

            knowledge_results = await retrieve_relevant_knowledge(
                query=test_query,
                knowledge_base_service=knowledge_base_service,
                department_id="marketing"
            )

            if knowledge_results:
                console.print("[green]Knowledge retrieval successful[/green]")

                # Display retrieved knowledge
                knowledge_table = Table(title="Retrieved Knowledge")
                knowledge_table.add_column("Section", style="cyan")
                knowledge_table.add_column("Content", style="green")
                knowledge_table.add_column("Score", style="yellow")

                for item in knowledge_results:
                    section = item.get("section", "unknown")
                    content = item.get("content", "No content")
                    score = item.get("score", 0)

                    # Truncate content if too long
                    if len(content) > 100:
                        content = content[:97] + "..."

                    knowledge_table.add_row(section, content, f"{score:.4f}")

                console.print(knowledge_table)
            else:
                console.print("[yellow]No knowledge retrieved[/yellow]")

            # Generate response with RAG
            console.print("[bold yellow]Generating response with RAG...[/bold yellow]")
            from app.rag.generator import generate_response_with_knowledge

            response = await generate_response_with_knowledge(
                query=test_query,
                knowledge=knowledge_results,
                llm_adapter=llm_adapter,
                department_id="marketing"
            )

            if response:
                console.print("[green]Response generation successful[/green]")
                console.print("\n[bold blue]Generated Response:[/bold blue]")
                console.print(Markdown(response))
            else:
                console.print("[bold red]Failed to generate response[/bold red]")

            console.print("\n[bold green]✓ RAG pipeline test completed[/bold green]")

        except Exception as e:
            console.print(f"[bold red]Error testing RAG pipeline: {str(e)}[/bold red]")
            logger.exception("Error in test_rag command")

    # Run the async function
    asyncio.run(run_test())


@app.command()
def test_all():
    """
    Run all tests to verify the system is working correctly.
    """
    try:
        # Header
        console.print("\n[bold blue]===== BusinessLM Python Backend Test Suite =====[/bold blue]\n")

        # Test health
        console.print("\n[bold cyan]1. Testing Backend Health[/bold cyan]")
        health()

        # Test JWT auth
        console.print("\n[bold cyan]2. Testing JWT Authentication[/bold cyan]")
        test_jwt_auth()

        # Test database
        console.print("\n[bold cyan]3. Testing PostgreSQL Database[/bold cyan]")
        test_database()

        # Test LLM integrations
        console.print("\n[bold cyan]4. Testing LLM Integrations[/bold cyan]")
        test_all_llms()

        # Test RAG pipeline
        console.print("\n[bold cyan]5. Testing RAG Pipeline[/bold cyan]")
        test_rag()

        # Summary
        console.print("\n[bold green]===== All Tests Completed =====[/bold green]")
        console.print("[bold green]The BusinessLM Python Backend is functioning correctly.[/bold green]")

    except Exception as e:
        console.print(f"[bold red]Error running test suite: {str(e)}[/bold red]")
        logger.exception("Error in test_all command")


if __name__ == "__main__":
    app()
