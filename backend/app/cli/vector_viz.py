"""
Vector Visualization Module

This module provides CLI visualization tools for vector embeddings.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich import box

from app.cli.formatting import (
    console,
    create_table,
    print_info,
    print_warning,
    print_error,
    print_success,
)
from app.rag.vector_utils import (
    reduce_dimensions,
    calculate_similarity,
    cluster_vectors,
    find_nearest_neighbors,
)

logger = logging.getLogger(__name__)


def visualize_vector_2d(
    vectors: List[List[float]],
    labels: Optional[List[str]] = None,
    metadata: Optional[List[Dict[str, Any]]] = None,
    method: str = "pca",
    width: int = 80,
    height: int = 20,
    title: str = "Vector Visualization (2D)",
) -> None:
    """Visualize vectors in 2D using ASCII art.

    Args:
        vectors: List of vectors to visualize
        labels: Optional labels for each vector
        metadata: Optional metadata for each vector
        method: Dimension reduction method ('pca', 'tsne', 'umap')
        width: Width of the visualization
        height: Height of the visualization
        title: Title of the visualization
    """
    if not vectors:
        print_warning("No vectors to visualize")
        return

    # Generate default labels if not provided
    if labels is None:
        labels = [f"Vector {i+1}" for i in range(len(vectors))]

    # Reduce dimensions to 2D
    try:
        vectors_2d = reduce_dimensions(vectors, method=method, n_components=2)
    except Exception as e:
        print_error(f"Error reducing dimensions: {e}")
        return

    # Normalize to fit in the grid
    min_x, max_x = vectors_2d[:, 0].min(), vectors_2d[:, 0].max()
    min_y, max_y = vectors_2d[:, 1].min(), vectors_2d[:, 1].max()
    x_range = max_x - min_x if max_x > min_x else 1
    y_range = max_y - min_y if max_y > min_y else 1
    vectors_2d[:, 0] = (vectors_2d[:, 0] - min_x) / x_range
    vectors_2d[:, 1] = (vectors_2d[:, 1] - min_y) / y_range

    # Create grid
    grid = [[" " for _ in range(width)] for _ in range(height)]

    # Plot vectors
    for i, (x, y) in enumerate(vectors_2d):
        # Convert to grid coordinates
        grid_x = int(x * (width - 1))
        grid_y = int((1 - y) * (height - 1))  # Invert y-axis

        # Ensure within bounds
        grid_x = max(0, min(grid_x, width - 1))
        grid_y = max(0, min(grid_y, height - 1))

        # Add point
        grid[grid_y][grid_x] = str(i + 1)

    # Convert grid to string
    grid_str = "\n".join("".join(row) for row in grid)

    # Create legend
    legend = []
    for i, label in enumerate(labels):
        if metadata and i < len(metadata):
            meta_str = ", ".join(f"{k}: {v}" for k, v in metadata[i].items())
            legend.append(f"{i+1}: {label} ({meta_str})")
        else:
            legend.append(f"{i+1}: {label}")

    # Create panel
    panel = Panel(
        Text(grid_str),
        title=title,
        subtitle=f"Method: {method.upper()}",
        border_style="blue",
    )

    # Print visualization
    console.print(panel)
    console.print("\nLegend:")
    for i in range(0, len(legend), 3):
        console.print("  ".join(legend[i:i+3]))


def visualize_query_similarity(
    query_vector: List[float],
    vectors: List[List[float]],
    labels: Optional[List[str]] = None,
    metadata: Optional[List[Dict[str, Any]]] = None,
    method: str = "cosine",
    top_k: int = 10,
    title: str = "Query Similarity",
) -> None:
    """Visualize query similarity to a set of vectors.

    Args:
        query_vector: Query vector
        vectors: List of vectors to compare
        labels: Optional labels for each vector
        metadata: Optional metadata for each vector
        method: Similarity method ('cosine', 'euclidean', 'dot')
        top_k: Number of top results to show
        title: Title of the visualization
    """
    if not vectors:
        print_warning("No vectors to compare")
        return

    # Generate default labels if not provided
    if labels is None:
        labels = [f"Vector {i+1}" for i in range(len(vectors))]

    # Find nearest neighbors
    try:
        neighbors = find_nearest_neighbors(query_vector, vectors, k=top_k, method=method)
    except Exception as e:
        print_error(f"Error finding nearest neighbors: {e}")
        return

    # Create table
    table = Table(
        title=title,
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta",
    )

    # Add columns
    table.add_column("Rank", style="cyan", width=5)
    table.add_column("Label", style="green")
    table.add_column("Similarity", style="yellow", width=10)
    if metadata:
        table.add_column("Metadata", style="blue")

    # Add rows
    for rank, (index, similarity) in enumerate(neighbors, 1):
        # Format similarity
        similarity_str = f"{similarity:.4f}"
        
        # Format metadata
        if metadata and index < len(metadata):
            meta_str = ", ".join(f"{k}: {v}" for k, v in metadata[index].items())
            table.add_row(str(rank), labels[index], similarity_str, meta_str)
        else:
            table.add_row(str(rank), labels[index], similarity_str)

    # Print table
    console.print(table)


def visualize_document_clusters(
    vectors: List[List[float]],
    labels: Optional[List[str]] = None,
    metadata: Optional[List[Dict[str, Any]]] = None,
    method: str = "kmeans",
    n_clusters: int = 5,
    title: str = "Document Clusters",
) -> None:
    """Visualize document clusters.

    Args:
        vectors: List of vectors to cluster
        labels: Optional labels for each vector
        metadata: Optional metadata for each vector
        method: Clustering method ('kmeans', 'dbscan')
        n_clusters: Number of clusters (for kmeans)
        title: Title of the visualization
    """
    if not vectors:
        print_warning("No vectors to cluster")
        return

    # Generate default labels if not provided
    if labels is None:
        labels = [f"Document {i+1}" for i in range(len(vectors))]

    # Cluster vectors
    try:
        cluster_labels = cluster_vectors(vectors, method=method, n_clusters=n_clusters)
    except Exception as e:
        print_error(f"Error clustering vectors: {e}")
        return

    # Group by cluster
    clusters = {}
    for i, cluster in enumerate(cluster_labels):
        if cluster not in clusters:
            clusters[cluster] = []
        clusters[cluster].append(i)

    # Create table
    table = Table(
        title=title,
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta",
    )

    # Add columns
    table.add_column("Cluster", style="cyan", width=8)
    table.add_column("Size", style="yellow", width=5)
    table.add_column("Documents", style="green")

    # Add rows
    for cluster, indices in sorted(clusters.items()):
        # Format document list
        if len(indices) > 5:
            docs_str = ", ".join(labels[i] for i in indices[:5]) + f" (+ {len(indices) - 5} more)"
        else:
            docs_str = ", ".join(labels[i] for i in indices)
        
        # Add row
        table.add_row(f"Cluster {cluster}", str(len(indices)), docs_str)

    # Print table
    console.print(table)

    # Print cluster details
    for cluster, indices in sorted(clusters.items()):
        console.print(f"\n[bold cyan]Cluster {cluster}[/bold cyan] ({len(indices)} documents)")
        
        # Create cluster table
        cluster_table = Table(show_header=True, box=box.SIMPLE)
        cluster_table.add_column("Document", style="green")
        if metadata:
            cluster_table.add_column("Metadata", style="blue")
        
        # Add rows
        for i in indices:
            if metadata and i < len(metadata):
                meta_str = ", ".join(f"{k}: {v}" for k, v in metadata[i].items())
                cluster_table.add_row(labels[i], meta_str)
            else:
                cluster_table.add_row(labels[i])
        
        # Print cluster table
        console.print(cluster_table)
