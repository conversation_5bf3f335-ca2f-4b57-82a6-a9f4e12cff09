"""
CLI Formatting Utilities

This module provides utilities for formatting CLI output in a more readable way.
It includes functions for creating consistent headers, sections, tables, and progress indicators.
"""

import json
from typing import Dict, List, Any, Optional, Union

from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.syntax import Syntax
from rich.tree import Tree
from rich.box import Box, ROUNDED, DOUBLE, HEAVY, SIMPLE
from rich import box

# Initialize console
console = Console()

# Define color schemes
COLOR_SCHEME = {
    "primary": "cyan",
    "secondary": "blue",
    "success": "green",
    "warning": "yellow",
    "error": "red",
    "info": "magenta",
    "highlight": "bold white on blue",
    "muted": "dim",
    "code": "bright_black on white",
}

# Define box styles
BOX_STYLES = {
    "default": ROUNDED,
    "heavy": HEAVY,
    "double": DOUBLE,
    "simple": SIMPLE,
}

# Section divider
def print_section_divider(title: str, style: str = "primary", box_style: str = "default"):
    """Print a section divider with a title."""
    box_type = BOX_STYLES.get(box_style, ROUNDED)
    console.print(Panel(f"[bold {COLOR_SCHEME[style]}]{title}[/bold {COLOR_SCHEME[style]}]",
                        box=box_type,
                        expand=False))

# Header
def print_header(title: str, subtitle: Optional[str] = None, style: str = "primary"):
    """Print a header with an optional subtitle."""
    console.print(f"\n[bold {COLOR_SCHEME[style]}]{title}[/bold {COLOR_SCHEME[style]}]")
    if subtitle:
        console.print(f"[{COLOR_SCHEME['muted']}]{subtitle}[/{COLOR_SCHEME['muted']}]")
    console.print()

# Success message
def print_success(message: str):
    """Print a success message."""
    console.print(f"[bold {COLOR_SCHEME['success']}]✓ {message}[/bold {COLOR_SCHEME['success']}]")

# Error message
def print_error(message: str, exception: Optional[Exception] = None):
    """Print an error message with optional exception details."""
    console.print(f"[bold {COLOR_SCHEME['error']}]✗ Error: {message}[/bold {COLOR_SCHEME['error']}]")
    if exception:
        console.print(f"[{COLOR_SCHEME['error']}]  {str(exception)}[/{COLOR_SCHEME['error']}]")

# Warning message
def print_warning(message: str):
    """Print a warning message."""
    console.print(f"[bold {COLOR_SCHEME['warning']}]⚠ Warning: {message}[/bold {COLOR_SCHEME['warning']}]")

# Info message
def print_info(message: str):
    """Print an info message."""
    console.print(f"[{COLOR_SCHEME['info']}]ℹ {message}[/{COLOR_SCHEME['info']}]")

# Code block
def print_code(code: str, language: str = "python", line_numbers: bool = True):
    """Print a code block with syntax highlighting."""
    syntax = Syntax(code, language, line_numbers=line_numbers, theme="monokai")
    console.print(syntax)

# JSON data
def print_json(data: Union[Dict, List, str], title: Optional[str] = None):
    """Print JSON data with syntax highlighting."""
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            print_error("Invalid JSON string")
            return

    json_str = json.dumps(data, indent=2)
    if title:
        console.print(f"[bold {COLOR_SCHEME['primary']}]{title}[/bold {COLOR_SCHEME['primary']}]")

    syntax = Syntax(json_str, "json", theme="monokai")
    console.print(syntax)

# Enhanced table
def create_table(title: str, columns: List[Dict[str, str]], box_style: str = "default") -> Table:
    """
    Create a Rich table with consistent styling.

    Args:
        title: The table title
        columns: List of column definitions with 'name' and optional 'style'
        box_style: Box style to use ('default', 'heavy', 'double', 'simple')

    Returns:
        A Rich Table object
    """
    box_type = BOX_STYLES.get(box_style, ROUNDED)
    table = Table(title=title, box=box_type, header_style="bold")

    for col in columns:
        name = col["name"]
        style = col.get("style", COLOR_SCHEME["primary"])
        justify = col.get("justify", "left")
        no_wrap = col.get("no_wrap", False)
        width = col.get("width", None)

        table.add_column(name, style=style, justify=justify, no_wrap=no_wrap, width=width)

    return table

# Progress spinner with better visibility
def create_progress() -> Progress:
    """Create a progress indicator with better visibility."""
    return Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=40),
        TimeElapsedColumn(),
        console=console
    )

# Tree view for hierarchical data
def create_tree(name: str, data: Dict[str, Any]) -> Tree:
    """Create a tree view for hierarchical data."""
    tree = Tree(f"[bold {COLOR_SCHEME['primary']}]{name}[/bold {COLOR_SCHEME['primary']}]")

    def add_to_tree(tree_node, data_node):
        if isinstance(data_node, dict):
            for key, value in data_node.items():
                if isinstance(value, (dict, list)):
                    branch = tree_node.add(f"[bold {COLOR_SCHEME['secondary']}]{key}[/bold {COLOR_SCHEME['secondary']}]")
                    add_to_tree(branch, value)
                else:
                    tree_node.add(f"[{COLOR_SCHEME['secondary']}]{key}:[/{COLOR_SCHEME['secondary']}] {value}")
        elif isinstance(data_node, list):
            for i, item in enumerate(data_node):
                if isinstance(item, (dict, list)):
                    branch = tree_node.add(f"[bold {COLOR_SCHEME['secondary']}]Item {i}[/bold {COLOR_SCHEME['secondary']}]")
                    add_to_tree(branch, item)
                else:
                    tree_node.add(str(item))

    add_to_tree(tree, data)
    return tree

# Response panel with citations
def print_response_panel(response: str, title: str = "Response", citations: Optional[List[Dict[str, str]]] = None):
    """
    Print a response panel with optional citations.

    Args:
        response: The response text (can be markdown)
        title: Panel title
        citations: Optional list of citation dictionaries with 'id', 'title', and 'source'
    """
    content = Markdown(response)

    if citations:
        citation_text = "\n\n**References**\n\n"
        for i, citation in enumerate(citations, 1):
            citation_text += f"{i}. {citation.get('title', 'Untitled')}, {citation.get('id', 'Unknown')} (Source: {citation.get('source', 'Unknown')})\n"

        content = Markdown(f"{response}\n\n{citation_text}")

    console.print(Panel(content, title=title, expand=False, border_style=COLOR_SCHEME["primary"]))

# Verbose mode toggle
VERBOSE_MODE = False

def set_verbose_mode(verbose: bool):
    """Set the verbose mode flag."""
    global VERBOSE_MODE
    VERBOSE_MODE = verbose

def print_verbose(message: str):
    """Print a message only in verbose mode."""
    if VERBOSE_MODE:
        console.print(f"[{COLOR_SCHEME['muted']}]DEBUG: {message}[/{COLOR_SCHEME['muted']}]")

# Query and response formatting
def format_query(query: str):
    """Format a user query."""
    console.print(f"\n[bold {COLOR_SCHEME['highlight']}]Query:[/bold {COLOR_SCHEME['highlight']}] {query}\n")

def format_step(step_number: int, step_name: str):
    """Format a processing step."""
    console.print(f"\n[bold {COLOR_SCHEME['secondary']}]Step {step_number}: {step_name}[/bold {COLOR_SCHEME['secondary']}]")

# Agent interaction visualization
def visualize_department_routing(analysis: Dict[str, Any], departments: List[str]):
    """
    Visualize the department routing decision.

    Args:
        analysis: The query analysis results
        departments: The departments that were selected
    """
    # Create a table for the routing decision
    table = create_table("Department Routing Decision", [
        {"name": "Department", "style": "cyan"},
        {"name": "Embedding Score", "style": "green"},
        {"name": "Selected", "style": "yellow"},
        {"name": "Reason", "style": "blue"}
    ])

    # Get embedding scores from analysis
    embedding_scores = analysis.get("embedding_scores", {})

    # Get relevant departments from LLM analysis
    llm_departments = analysis.get("relevant_departments", [])

    # Add rows for each department
    for dept, score in embedding_scores.items():
        selected = "✓" if dept in departments else ""
        reason = ""

        # Determine reason for selection/non-selection
        if dept in departments:
            if dept in llm_departments:
                reason = "Selected by LLM analysis"
            elif score > 0.35:  # This threshold is from the _combine_analysis_results method
                reason = "Selected by embedding similarity"
            else:
                reason = "Selected by fallback logic"
        elif score > 0.35:
            reason = "Not selected despite high similarity"
        else:
            reason = "Low similarity score"

        table.add_row(
            dept.capitalize(),
            f"{score:.4f}",
            selected,
            reason
        )

    console.print(table)

def visualize_agent_flow(state: Dict[str, Any]):
    """
    Visualize the agent interaction flow.

    Args:
        state: The current state of the agent system
    """
    # Create a tree for the agent flow
    tree = Tree("[bold cyan]Agent Interaction Flow[/bold cyan]")

    # Add query analysis
    analysis_node = tree.add("[bold blue]Query Analysis[/bold blue]")

    query_analysis = state.get("analysis", {})
    if query_analysis:
        analysis_node.add(f"[green]Query Type:[/green] {query_analysis.get('query_type', 'Unknown')}")
        analysis_node.add(f"[green]Priority:[/green] {query_analysis.get('priority', 'normal')}")
        topics = query_analysis.get("topics", [])
        if topics:
            topics_str = ", ".join(topics)
            analysis_node.add(f"[green]Topics:[/green] {topics_str}")

    # Add departments consulted
    departments = state.get("departments_consulted", [])
    if departments:
        dept_node = tree.add("[bold blue]Departments Consulted[/bold blue]")
        for dept in departments:
            dept_node.add(f"[green]{dept.capitalize()}[/green]")

    # Add response generation
    if "response" in state:
        response_preview = state["response"][:100] + "..." if len(state["response"]) > 100 else state["response"]
        response_node = tree.add("[bold blue]Response Generation[/bold blue]")
        response_node.add(f"[dim]{response_preview}[/dim]")

    console.print(tree)

def visualize_graph_execution(events: List[Dict[str, Any]]):
    """
    Visualize the step-by-step execution of the agent graph.

    Args:
        events: List of events from the execution
    """
    if not events:
        return

    # Create a table for the graph execution
    table = create_table("Graph Execution Steps", [
        {"name": "Step", "style": "cyan", "width": 10},
        {"name": "Component", "style": "green", "width": 20},
        {"name": "Action", "style": "yellow", "width": 10},
        {"name": "Details", "style": "blue"}
    ])

    # Add rows for each event
    for i, event in enumerate(events, 1):
        component = event.get("component", "Unknown")
        action = event.get("action", "Unknown")

        # Format details
        details = event.get("details", {})
        details_str = ""
        if details:
            # Format the most important details based on the component and action
            if component == "analyze_query_node":
                if "query" in details:
                    details_str = f"Query: {details['query']}"
                elif "analysis" in details:
                    analysis = details["analysis"]
                    if isinstance(analysis, dict):
                        dept_str = ", ".join(analysis.get("relevant_departments", []))
                        details_str = f"Departments: {dept_str}"
            elif component == "route_to_departments_node":
                if "departments" in details:
                    dept_str = ", ".join(details["departments"])
                    details_str = f"Routed to: {dept_str}"
            elif component == "retrieve_knowledge_node":
                if "doc_count" in details:
                    details_str = f"Retrieved {details['doc_count']} documents"
            elif component == "generate_response_node":
                if "departments" in details:
                    dept_str = ", ".join(details["departments"])
                    details_str = f"Using responses from: {dept_str}"

            # Add elapsed time if available
            if "elapsed_time" in details:
                time_str = f"{details['elapsed_time']:.2f}s"
                details_str = f"{details_str} (Time: {time_str})" if details_str else f"Time: {time_str}"

        table.add_row(
            str(i),
            component,
            action,
            details_str
        )

    console.print(table)
