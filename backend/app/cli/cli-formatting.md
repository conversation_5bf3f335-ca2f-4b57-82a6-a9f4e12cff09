# CLI Formatting Utilities

This module provides utilities for formatting CLI output in a more readable way. It includes functions for creating consistent headers, sections, tables, and progress indicators.

## Overview

The CLI formatting utilities are designed to make the output of CLI tools more readable and consistent. They provide a set of functions for common formatting tasks, such as:

- Creating headers and section dividers
- Displaying success, error, warning, and info messages
- Formatting code blocks and JSON data
- Creating tables and progress indicators
- Displaying responses with citations

## Usage

### Basic Usage

```python
from app.cli.formatting import (
    console,
    print_header,
    print_section_divider,
    print_success,
    print_error,
    print_warning,
    print_info,
    create_table,
    create_progress,
    print_response_panel,
    set_verbose_mode,
    print_verbose,
    format_query,
    format_step,
)

# Print a header
print_header("My CLI Tool", "A tool for doing things")

# Print a section divider
print_section_divider("Processing Data", "primary")

# Print success, error, warning, and info messages
print_success("Operation completed successfully")
print_error("An error occurred", exception=Exception("Something went wrong"))
print_warning("This might not work as expected")
print_info("Processing file: example.txt")

# Create and display a table
columns = [
    {"name": "ID", "style": "cyan"},
    {"name": "Name", "style": "green"},
    {"name": "Score", "style": "yellow"}
]
table = create_table("Results", columns)
table.add_row("1", "John", "85")
table.add_row("2", "Jane", "92")
console.print(table)

# Use a progress indicator
progress = create_progress()
with progress:
    task = progress.add_task("[yellow]Processing...", total=100)
    for i in range(100):
        # Do some work
        progress.update(task, completed=i + 1)

# Display a response panel with citations
citations = [
    {"id": "doc-1", "title": "Example Document", "source": "Local"}
]
print_response_panel("This is a response with a citation.", "Response", citations)

# Use verbose mode
set_verbose_mode(True)
print_verbose("This will only be printed in verbose mode")

# Format a query and steps
format_query("What is the capital of France?")
format_step(1, "Analyzing query")
format_step(2, "Retrieving documents")
format_step(3, "Generating response")
```

### Color Scheme

The formatting utilities use a consistent color scheme:

- `primary`: cyan
- `secondary`: blue
- `success`: green
- `warning`: yellow
- `error`: red
- `info`: magenta
- `highlight`: bold white on blue
- `muted`: dim
- `code`: bright_black on white

### Box Styles

The formatting utilities support different box styles for panels and tables:

- `default`: Rounded corners
- `heavy`: Heavy borders
- `double`: Double borders
- `simple`: Simple borders

## Functions

### Headers and Sections

- `print_header(title, subtitle=None, style="primary")`: Print a header with an optional subtitle
- `print_section_divider(title, style="primary", box_style="default")`: Print a section divider with a title

### Messages

- `print_success(message)`: Print a success message
- `print_error(message, exception=None)`: Print an error message with optional exception details
- `print_warning(message)`: Print a warning message
- `print_info(message)`: Print an info message

### Tables and Progress

- `create_table(title, columns, box_style="default")`: Create a Rich table with consistent styling
- `create_progress()`: Create a progress indicator with better visibility

### Code and JSON

- `print_code(code, language="python", line_numbers=True)`: Print a code block with syntax highlighting
- `print_json(data, title=None)`: Print JSON data with syntax highlighting

### Responses

- `print_response_panel(response, title="Response", citations=None)`: Print a response panel with optional citations

### Verbose Mode

- `set_verbose_mode(verbose)`: Set the verbose mode flag
- `print_verbose(message)`: Print a message only in verbose mode

### Query and Steps

- `format_query(query)`: Format a user query
- `format_step(step_number, step_name)`: Format a processing step

## Integration with Typer

These formatting utilities work well with [Typer](https://typer.tiangolo.com/), a library for building CLI applications in Python:

```python
import typer
from app.cli.formatting import print_success, print_error

app = typer.Typer()

@app.command()
def hello(name: str, verbose: bool = typer.Option(False, "--verbose", "-v")):
    """Say hello to someone."""
    try:
        print_success(f"Hello {name}!")
    except Exception as e:
        print_error(f"Failed to say hello: {str(e)}", e)

if __name__ == "__main__":
    app()
```
