"""
Tracing Visualization

This module provides functions for visualizing trace events in the CLI.
"""

import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree
from rich.text import Text
from rich import box

from app.core.tracing import TracingCollector, load_traces, filter_traces

console = Console()

def visualize_trace_text(
    traces: List[Dict[str, Any]],
    show_state: bool = False,
    show_metadata: bool = True,
    filter_node_id: Optional[str] = None,
    filter_event_type: Optional[str] = None
) -> None:
    """
    Visualize trace events in text format.

    Args:
        traces: List of trace events
        show_state: Whether to show state information
        show_metadata: Whether to show metadata
        filter_node_id: Filter by node ID
        filter_event_type: Filter by event type
    """
    # Filter traces if needed
    if filter_node_id or filter_event_type:
        traces = filter_traces(
            traces,
            node_id=filter_node_id,
            event_type=filter_event_type
        )

    # Create a table for the traces
    table = Table(
        title="Trace Events",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta"
    )

    table.add_column("Timestamp", style="dim")
    table.add_column("Node", style="cyan")
    table.add_column("Event", style="green")
    table.add_column("Details", style="yellow")

    for trace in traces:
        timestamp = trace.get("timestamp", "")
        if timestamp:
            # Format timestamp for better readability
            try:
                dt = datetime.fromisoformat(timestamp)
                timestamp = dt.strftime("%H:%M:%S.%f")[:-3]
            except (ValueError, TypeError):
                pass

        node_id = trace.get("node_id", "")
        event_type = trace.get("event_type", "")

        # Format details
        details = []

        # Add metadata if requested
        if show_metadata and "metadata" in trace:
            metadata = trace["metadata"]
            for key, value in metadata.items():
                if isinstance(value, (dict, list)):
                    details.append(f"{key}: {json.dumps(value, indent=2)}")
                else:
                    details.append(f"{key}: {value}")

        # Add state information if requested
        if show_state:
            if "state_before" in trace:
                details.append("State Before: " + json.dumps(trace["state_before"], indent=2))
            if "state_after" in trace:
                details.append("State After: " + json.dumps(trace["state_after"], indent=2))

        details_str = "\n".join(details)

        table.add_row(timestamp, node_id, event_type, details_str)

    console.print(table)

def visualize_trace_tree(
    traces: List[Dict[str, Any]],
    show_state: bool = False,
    show_metadata: bool = True
) -> None:
    """
    Visualize trace events in tree format.

    Args:
        traces: List of trace events
        show_state: Whether to show state information
        show_metadata: Whether to show metadata
    """
    # Create a tree for the traces
    tree = Tree("Trace Events", guide_style="bold bright_blue")

    # Group traces by node_id
    node_traces = {}
    for trace in traces:
        node_id = trace.get("node_id", "unknown")
        if node_id not in node_traces:
            node_traces[node_id] = []
        node_traces[node_id].append(trace)

    # Add nodes to the tree
    for node_id, node_trace_list in node_traces.items():
        node_branch = tree.add(f"[cyan]{node_id}[/cyan]")

        # Add events to the node branch
        for trace in node_trace_list:
            event_type = trace.get("event_type", "unknown")
            timestamp = trace.get("timestamp", "")

            if timestamp:
                # Format timestamp for better readability
                try:
                    dt = datetime.fromisoformat(timestamp)
                    timestamp = dt.strftime("%H:%M:%S.%f")[:-3]
                except (ValueError, TypeError):
                    pass

            event_text = f"[green]{event_type}[/green] ({timestamp})"
            event_branch = node_branch.add(event_text)

            # Add metadata if requested
            if show_metadata and "metadata" in trace:
                metadata = trace["metadata"]
                metadata_branch = event_branch.add("[yellow]Metadata[/yellow]")
                for key, value in metadata.items():
                    if isinstance(value, (dict, list)):
                        metadata_branch.add(f"[dim]{key}:[/dim] {json.dumps(value, indent=2)}")
                    else:
                        metadata_branch.add(f"[dim]{key}:[/dim] {value}")

            # Add state information if requested
            if show_state:
                if "state_before" in trace:
                    state_branch = event_branch.add("[yellow]State Before[/yellow]")
                    state_branch.add(json.dumps(trace["state_before"], indent=2))
                if "state_after" in trace:
                    state_branch = event_branch.add("[yellow]State After[/yellow]")
                    state_branch.add(json.dumps(trace["state_after"], indent=2))

    console.print(tree)

def visualize_agent_communication(
    traces: List[Dict[str, Any]],
    show_query: bool = True,
    show_response: bool = True,
    show_full_content: bool = False,
    show_node_events: bool = True
) -> None:
    """
    Visualize agent communications in a clear, interpretable format.

    This function creates a table showing the flow of communication between agents,
    with clear indicators of which agent is communicating with which agent, and
    the content of the communications.

    Args:
        traces: List of trace events
        show_query: Whether to show queries
        show_response: Whether to show responses
        show_full_content: Whether to show full content or truncate
        show_node_events: Whether to show node start/end events
    """
    # Filter traces to only include agent communications
    agent_traces = []

    # Check for agent_query and agent_response event types
    if show_query:
        agent_traces.extend(filter_traces(traces, event_type="agent_query"))

    if show_response:
        agent_traces.extend(filter_traces(traces, event_type="agent_response"))

    # Also check for department responses in state_after
    for trace in traces:
        if "state_after" in trace and isinstance(trace["state_after"], dict):
            state_after = trace["state_after"]

            # Check for department_responses in state_after
            if "department_responses" in state_after and state_after["department_responses"]:
                for dept, response in state_after["department_responses"].items():
                    # Check if this department response already exists in agent_traces
                    response_exists = False
                    response_text = response.get("response", "")
                    response_timestamp = response.get("timestamp", "")

                    for existing_trace in agent_traces:
                        if (existing_trace.get("node_id") == f"{dept}_agent" and
                            existing_trace.get("event_type") == "agent_response" and
                            existing_trace.get("metadata", {}).get("department") == dept and
                            existing_trace.get("metadata", {}).get("response") == response_text):
                            response_exists = True
                            break

                    # Create a synthetic trace for each department response if it doesn't exist
                    if not response_exists:
                        agent_traces.append({
                            "timestamp": response_timestamp or trace.get("timestamp", ""),
                            "node_id": f"{dept}_agent",
                            "event_type": "agent_response",
                            "metadata": {
                                "department": dept,
                                "response": response_text,
                                "timestamp": response_timestamp
                            }
                        })

            # Check for query_analysis in state_after
            if "query_analysis" in state_after and state_after["query_analysis"]:
                query_analysis = state_after["query_analysis"]
                if "relevant_departments" in query_analysis:
                    # Create a synthetic trace for the query analysis
                    agent_traces.append({
                        "timestamp": trace.get("timestamp", ""),
                        "node_id": "co_ceo_agent",
                        "event_type": "agent_query",
                        "metadata": {
                            "department": "co_ceo",
                            "query": trace.get("metadata", {}).get("query", state_after.get("query", "")),
                            "relevant_departments": ", ".join(query_analysis["relevant_departments"]),
                            "analysis": query_analysis.get("explanation", "")
                        }
                    })

    # Check for routing_decision in metadata
    for trace in traces:
        if "metadata" in trace and isinstance(trace["metadata"], dict):
            metadata = trace["metadata"]
            if "routing_decision" in metadata and metadata["routing_decision"]:
                routing_decision = metadata["routing_decision"]
                if "departments" in routing_decision and routing_decision["departments"]:
                    # Create a synthetic trace for the routing decision
                    agent_traces.append({
                        "timestamp": trace.get("timestamp", ""),
                        "node_id": "co_ceo_agent",
                        "event_type": "agent_response",
                        "metadata": {
                            "department": "co_ceo",
                            "response": f"Routing to departments: {', '.join(routing_decision['departments'])}",
                            "explanation": routing_decision.get("explanation", "")
                        }
                    })

    # Add node start/end events if requested
    if show_node_events:
        node_traces = []
        for trace in traces:
            event_type = trace.get("event_type", "")
            if event_type in ["node_start", "node_end"]:
                node_id = trace.get("node_id", "")

                # Create a synthetic trace for the node event
                node_traces.append({
                    "timestamp": trace.get("timestamp", ""),
                    "node_id": node_id,
                    "event_type": "node_event",
                    "metadata": {
                        "department": "system",
                        "event": event_type,
                        "details": f"{node_id}: {event_type}",
                        "elapsed_time": trace.get("metadata", {}).get("elapsed_time", "")
                    }
                })

        # Add node events to agent traces
        agent_traces.extend(node_traces)

    # Sort by timestamp
    agent_traces.sort(key=lambda x: x.get("timestamp", ""))

    # Group traces by conversation thread for better readability
    # A conversation thread is a sequence of related messages (e.g., a query followed by responses)
    grouped_traces = []
    current_group = []
    current_dept = None

    for trace in agent_traces:
        event_type = trace.get("event_type", "")
        dept = trace.get("metadata", {}).get("department", "")

        # Start a new group if:
        # 1. This is a node_event and the previous wasn't
        # 2. This is a query to a new department
        # 3. This is the first trace
        if (event_type == "node_event" and (not current_group or current_group[-1].get("event_type") != "node_event")) or \
           (event_type == "agent_query" and dept != current_dept) or \
           not current_group:
            if current_group:
                grouped_traces.append(current_group)
            current_group = [trace]
            current_dept = dept
        else:
            current_group.append(trace)

    # Add the last group
    if current_group:
        grouped_traces.append(current_group)

    # Flatten the groups back into a list, but add a separator between groups
    agent_traces = []
    for i, group in enumerate(grouped_traces):
        agent_traces.extend(group)
        # Add a separator between groups (except after the last group)
        if i < len(grouped_traces) - 1:
            agent_traces.append({
                "timestamp": "",
                "node_id": "separator",
                "event_type": "separator",
                "metadata": {}
            })

    # Create a table for the agent communications
    table = Table(
        title="Agent Communications Flow",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta",
        expand=True,
        title_style="bold magenta",
        border_style="bright_blue"
    )

    # Improved column structure for better readability
    table.add_column("#", style="bold", width=3)  # Sequence number
    table.add_column("Timestamp", style="dim", width=12)
    table.add_column("From", style="bold cyan", width=12)  # Source agent
    table.add_column("To", style="bold green", width=12)   # Target agent
    table.add_column("Type", style="yellow", width=10)     # Message type
    table.add_column("Content", style="white", width=60)   # Message content

    # Add sequence numbers to show the flow
    seq_num = 1

    for trace in agent_traces:
        # Handle separator
        if trace.get("event_type") == "separator":
            table.add_row("", "", "", "", "", "")
            continue

        # Skip duplicate routing messages
        if trace.get("metadata", {}).get("is_routing", False) and seq_num > 1:
            # Check if we already have a routing message
            routing_exists = False
            for i, prev_trace in enumerate(agent_traces[:agent_traces.index(trace)]):
                if (prev_trace.get("node_id") == trace.get("node_id") and
                    prev_trace.get("event_type") == trace.get("event_type") and
                    prev_trace.get("metadata", {}).get("is_routing", False)):
                    routing_exists = True
                    break

            if routing_exists:
                continue

        timestamp = trace.get("timestamp", "")
        if timestamp:
            # Format timestamp for better readability
            try:
                dt = datetime.fromisoformat(timestamp)
                timestamp = dt.strftime("%H:%M:%S.%f")[:-3]
            except (ValueError, TypeError):
                pass

        node_id = trace.get("node_id", "")
        event_type = trace.get("event_type", "")

        # Get content based on event type with improved formatting
        content = ""
        if event_type == "agent_query" and "metadata" in trace:
            # Format the query with better styling
            query_text = trace["metadata"].get("query", "")
            content = f"[bold]{query_text}[/bold]"

            # Add relevant departments with improved formatting
            if "relevant_departments" in trace["metadata"]:
                depts = trace["metadata"]["relevant_departments"]
                content += f"\n[dim italic]Relevant departments: [/dim italic][cyan]{depts}[/cyan]"

            # Add analysis with improved formatting
            if "analysis" in trace["metadata"] and trace["metadata"]["analysis"]:
                analysis = trace["metadata"]["analysis"]
                if show_full_content:
                    content += f"\n[dim italic]Analysis: [/dim italic][blue]{analysis}[/blue]"
                else:
                    truncated = analysis[:100] + "..." if len(analysis) > 100 else analysis
                    content += f"\n[dim italic]Analysis: [/dim italic][blue]{truncated}[/blue]"

        elif event_type == "agent_response" and "metadata" in trace:
            # Format the response with better styling
            response = trace["metadata"].get("response", "")

            # Format response with truncation if needed
            if show_full_content:
                content = f"[bold]{response}[/bold]"
            else:
                truncated = response[:150] + "..." if response and len(response) > 150 else response
                content = f"[bold]{truncated}[/bold]"

            # Add explanation with improved formatting
            if "explanation" in trace["metadata"] and trace["metadata"]["explanation"]:
                explanation = trace["metadata"]["explanation"]
                if show_full_content:
                    content += f"\n[dim italic]Reasoning: [/dim italic][green]{explanation}[/green]"
                else:
                    truncated = explanation[:100] + "..." if len(explanation) > 100 else explanation
                    content += f"\n[dim italic]Reasoning: [/dim italic][green]{truncated}[/green]"

        elif event_type == "node_event" and "metadata" in trace:
            # Format node events with better styling
            details = trace["metadata"].get("details", "")
            content = f"[dim]{details}[/dim]"

            # Add elapsed time with improved formatting
            elapsed_time = trace["metadata"].get("elapsed_time", "")
            if elapsed_time:
                content += f" [dim italic](elapsed: [/dim italic][yellow]{elapsed_time:.2f}s[/yellow][dim italic])[/dim italic]"

        # Get department from metadata
        department = ""
        if "metadata" in trace:
            department = trace["metadata"].get("department", "")

        # Format event type for display
        if event_type == "agent_query":
            display_type = "Query"
            style = "bold yellow"
        elif event_type == "agent_response":
            display_type = "Response"
            style = "bold green"
        elif event_type == "node_event":
            display_type = trace["metadata"].get("event", "Event")
            style = "dim"
        else:
            display_type = event_type
            style = "white"

        # Format department and node_id
        if department == "system":
            agent_display = f"[dim]{node_id}[/dim]"
        else:
            agent_display = f"{department} ({node_id})"

        # Determine source and target agents for the communication
        from_agent = ""
        to_agent = ""

        if event_type == "agent_query":
            # For queries, the direction is from user/co_ceo to the department
            if department == "co_ceo":
                from_agent = "User"
                to_agent = "co_ceo"
            else:
                from_agent = "co_ceo"
                to_agent = department
        elif event_type == "agent_response":
            # For responses, the direction is from the department to co_ceo/user
            if department == "co_ceo":
                # Check if this is a routing message
                if trace.get("metadata", {}).get("is_routing", False) or "routing" in trace.get("metadata", {}).get("response", "").lower():
                    from_agent = "co_ceo"
                    to_agent = "Departments"  # Routing to departments
                else:
                    from_agent = "co_ceo"
                    to_agent = "User"  # Final response to user
            else:
                from_agent = department
                to_agent = "co_ceo"
        elif event_type == "node_event":
            from_agent = "System"
            to_agent = node_id
        else:
            from_agent = "System"
            to_agent = "System"

        # Format source and target with appropriate styles
        from_display = f"[bold cyan]{from_agent}[/bold cyan]"
        to_display = f"[bold green]{to_agent}[/bold green]"

        # Add the row with sequence number
        table.add_row(
            str(seq_num),  # Sequence number
            timestamp,     # Timestamp
            from_display,  # From agent
            to_display,    # To agent
            f"[{style}]{display_type}[/{style}]",  # Message type
            content        # Message content
        )

        # Increment sequence number for the next row
        seq_num += 1

    console.print(table)

    # Print a more comprehensive legend
    console.print("\n[bold]Legend:[/bold]")
    console.print("[bold]#[/bold]: Sequence number showing the order of communication")
    console.print("[bold]From[/bold]: Source agent sending the message")
    console.print("[bold]To[/bold]: Target agent receiving the message")
    console.print("[bold]Type[/bold]: Type of communication")

    console.print("\n[bold]Message Types:[/bold]")
    console.print("[yellow]Query[/yellow]: A question or request sent to an agent")
    console.print("[green]Response[/green]: An answer or result from an agent")
    console.print("[dim]node_start/node_end[/dim]: System events showing graph node execution")

    console.print("\n[bold]Common Communication Patterns:[/bold]")
    console.print("[bold cyan]User[/bold cyan] → [bold green]co_ceo[/bold green]: Initial user query to the co_ceo agent")
    console.print("[bold cyan]co_ceo[/bold cyan] → [bold green]Departments[/bold green]: co_ceo agent routes a query to multiple departments")
    console.print("[bold cyan]co_ceo[/bold cyan] → [bold green]finance/marketing[/bold green]: co_ceo agent sends a specific query to a department")
    console.print("[bold cyan]finance/marketing[/bold cyan] → [bold green]co_ceo[/bold green]: Department agent sends its response to co_ceo")
    console.print("[bold cyan]co_ceo[/bold cyan] → [bold green]User[/bold green]: co_ceo agent sends the final response to the user")

    console.print("\n[bold]Tips:[/bold]")
    console.print("- Use --show-full-content flag to see complete messages")
    console.print("- Use --show-node-events=false to hide system events")
    console.print("- Trace files are saved in the 'traces' directory for later analysis")

def visualize_trace_summary(traces: List[Dict[str, Any]]) -> None:
    """
    Visualize a comprehensive summary of trace events.

    This function provides a detailed summary of the trace events, including:
    - Total number of events
    - Events by node
    - Events by type
    - Agent communication statistics
    - Execution timeline

    Args:
        traces: List of trace events
    """
    if not traces:
        console.print("[bold red]No trace events to summarize[/bold red]")
        return

    # Count events by node and type
    node_counts = {}
    event_counts = {}
    department_counts = {}
    agent_communications = []

    # Track execution timeline
    start_time = None
    end_time = None

    for trace in traces:
        node_id = trace.get("node_id", "unknown")
        event_type = trace.get("event_type", "unknown")
        timestamp = trace.get("timestamp", "")

        # Update node counts
        if node_id not in node_counts:
            node_counts[node_id] = 0
        node_counts[node_id] += 1

        # Update event type counts
        if event_type not in event_counts:
            event_counts[event_type] = 0
        event_counts[event_type] += 1

        # Track department involvement
        if "metadata" in trace and "department" in trace["metadata"]:
            dept = trace["metadata"]["department"]
            if dept:
                if dept not in department_counts:
                    department_counts[dept] = 0
                department_counts[dept] += 1

        # Track agent communications
        if event_type in ["agent_query", "agent_response"]:
            agent_communications.append(trace)

        # Track execution timeline
        if timestamp:
            try:
                dt = datetime.fromisoformat(timestamp)
                if start_time is None or dt < start_time:
                    start_time = dt
                if end_time is None or dt > end_time:
                    end_time = dt
            except (ValueError, TypeError):
                pass

    # Create a summary table
    table = Table(
        title="Trace Summary",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold magenta",
        title_style="bold magenta",
        border_style="bright_blue"
    )

    table.add_column("Category", style="cyan", width=20)
    table.add_column("Count", style="green", width=10)
    table.add_column("Details", style="yellow", width=60)

    # Add total count
    table.add_row("Total Events", str(len(traces)), "")

    # Add execution timeline
    if start_time and end_time:
        duration = (end_time - start_time).total_seconds()
        timeline_details = f"Start: {start_time.strftime('%H:%M:%S.%f')[:-3]}\nEnd: {end_time.strftime('%H:%M:%S.%f')[:-3]}\nDuration: {duration:.2f} seconds"
        table.add_row("Execution Timeline", "", timeline_details)

    # Add node counts (sorted by count)
    sorted_nodes = sorted(node_counts.items(), key=lambda x: x[1], reverse=True)
    node_details = "\n".join([f"{node}: {count}" for node, count in sorted_nodes])
    table.add_row("Nodes", str(len(node_counts)), node_details)

    # Add event type counts (sorted by count)
    sorted_events = sorted(event_counts.items(), key=lambda x: x[1], reverse=True)
    event_details = "\n".join([f"{event}: {count}" for event, count in sorted_events])
    table.add_row("Event Types", str(len(event_counts)), event_details)

    # Add department counts if available
    if department_counts:
        sorted_depts = sorted(department_counts.items(), key=lambda x: x[1], reverse=True)
        dept_details = "\n".join([f"{dept}: {count}" for dept, count in sorted_depts])
        table.add_row("Departments", str(len(department_counts)), dept_details)

    # Add agent communication stats
    query_count = len([t for t in agent_communications if t.get("event_type") == "agent_query"])
    response_count = len([t for t in agent_communications if t.get("event_type") == "agent_response"])
    table.add_row("Agent Communications", str(query_count + response_count), f"Queries: {query_count}\nResponses: {response_count}")

    console.print(table)

def visualize_trace_file(
    file_path: str,
    format: str = "text",
    show_state: bool = False,
    show_metadata: bool = True,
    filter_node_id: Optional[str] = None,
    filter_event_type: Optional[str] = None
) -> None:
    """
    Visualize trace events from a file.

    Args:
        file_path: Path to the trace file
        format: Visualization format (text, tree, communication, summary)
        show_state: Whether to show state information
        show_metadata: Whether to show metadata
        filter_node_id: Filter by node ID
        filter_event_type: Filter by event type
    """
    # Load traces from file
    traces = load_traces(file_path)

    # Visualize based on format
    if format == "text":
        visualize_trace_text(
            traces,
            show_state=show_state,
            show_metadata=show_metadata,
            filter_node_id=filter_node_id,
            filter_event_type=filter_event_type
        )
    elif format == "tree":
        visualize_trace_tree(
            traces,
            show_state=show_state,
            show_metadata=show_metadata
        )
    elif format == "communication":
        visualize_agent_communication(traces)
    elif format == "summary":
        visualize_trace_summary(traces)
    else:
        console.print(f"[bold red]Unknown format: {format}[/bold red]")
