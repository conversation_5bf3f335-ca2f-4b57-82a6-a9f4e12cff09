"""
BusinessLM Python Migration PoC - Main Application Entry Point

This module initializes the FastAPI application and sets up the routes.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import json

from app.config import get_settings
from app.api.error_handlers import register_error_handlers
from app.core.db.database import init_db
from app.rag.embedding_utils import get_embedding_model
from app.rag.vector_store import get_vector_store
from app.rag.knowledge_base_factory import get_knowledge_base_service

# Configure logging
from app.config.logging_config import configure_logging

configure_logging(
    log_level=get_settings().LOG_LEVEL,
    capture_warnings=True
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=get_settings().APP_NAME,
    description="Proof of Concept for BusinessLM Python Migration",
    version=get_settings().APP_VERSION,
)

# Add CORS middleware
# For development, we'll allow specific origins
# In production, this should be restricted to your frontend domain
frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
allowed_origins = [frontend_url]
if os.getenv("ADDITIONAL_ALLOWED_ORIGINS"):
    allowed_origins.extend(os.getenv("ADDITIONAL_ALLOWED_ORIGINS").split(","))

# If in development mode, allow all origins without credentials
if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=False,  # Must be False when using wildcard origins
        allow_methods=["*"],
        allow_headers=["*"],
    )
else:
    # In production or staging, use specific origins
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint to verify the API is running."""
    return {"status": "healthy", "version": "0.1.0"}

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with basic API information."""
    return {
        "message": "BusinessLM Python PoC API",
        "docs_url": "/docs",
        "health_check": "/health",
    }

# Import and include routers
from app.api.routes import agents_router, rag_router, llm_router

# Include routers
app.include_router(agents_router, prefix="/api/agents", tags=["agents"])
app.include_router(rag_router, prefix="/api/rag", tags=["rag"])
app.include_router(llm_router, prefix="/api/llm", tags=["llm"])

# Register error handlers
register_error_handlers(app)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting up BusinessLM API...")

    # Initialize database
    try:
        logger.info("Initializing database...")
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        # Don't raise the exception to allow the app to start even if DB init fails
        # This is useful for development when the database might not be available

    # Initialize embedding model
    try:
        logger.info("Initializing embedding model...")
        embedding_model = get_embedding_model()
        # Store in app state for reuse
        app.state.embedding_model = embedding_model
        logger.info(f"Embedding model initialized: {get_settings().EMBEDDING_MODEL}")

        # Validate embedding dimensions
        dimension = embedding_model.get_dimension()
        if dimension != get_settings().VECTOR_DIMENSION:
            logger.warning(
                f"Embedding dimension mismatch: model returns {dimension}, "
                f"but VECTOR_DIMENSION is set to {get_settings().VECTOR_DIMENSION}. "
                "This may cause issues with vector storage."
            )

        # Initialize vector store
        logger.info("Initializing vector store...")
        vector_store = get_vector_store(
            store_type=get_settings().VECTOR_STORE_TYPE,
            dimension=get_settings().VECTOR_DIMENSION
        )
        # Store in app state for reuse
        app.state.vector_store = vector_store
        logger.info(f"Vector store initialized: {get_settings().VECTOR_STORE_TYPE}")

        # Initialize knowledge base service
        logger.info("Initializing knowledge base service...")
        knowledge_base_service = await get_knowledge_base_service()
        # Store in app state for reuse
        app.state.knowledge_base_service = knowledge_base_service
        logger.info("Knowledge base service initialized")

        # Load mock documents if environment variable is set
        if os.getenv("USE_MOCK_DOCUMENTS", "false").lower() == "true":
            try:
                # Get the mock document path from environment variable or use default
                mock_document_path = os.environ.get("MOCK_DOCUMENT_PATH", "~/.businesslm/mock_documents.json")
                mock_document_path = os.path.expanduser(mock_document_path)

                # Check if file exists
                if os.path.exists(mock_document_path):
                    logger.info(f"Loading mock documents from {mock_document_path}")
                    with open(mock_document_path, "r") as f:
                        mock_documents = json.load(f)

                    # Add documents to knowledge base
                    for doc_id, doc in mock_documents.items():
                        # Format the document for the knowledge base
                        formatted_doc = {
                            "id": doc_id,
                            "title": doc.get("title", "Untitled"),
                            "content": doc.get("content", ""),
                            "department": doc.get("department", ""),
                            "tags": doc.get("tags", []),
                            "created_at": doc.get("created_at", ""),
                            "metadata": doc.get("metadata", {})
                        }

                        # Add the document to the knowledge base
                        await knowledge_base_service.add_document(formatted_doc)
                        logger.info(f"Added mock document {doc_id} to knowledge base")

                    logger.info(f"Loaded {len(mock_documents)} mock documents into knowledge base")
                else:
                    logger.warning(f"No mock documents file found at {mock_document_path}")
            except Exception as e:
                logger.error(f"Error loading mock documents: {e}")
        else:
            # No demo documents are loaded - users will upload documents through the frontend UI
            logger.info("USE_MOCK_DOCUMENTS is not set to true, skipping mock document loading")
    except Exception as e:
        logger.error(f"Error initializing embedding model or vector store: {e}")

    logger.info("BusinessLM API startup complete")

if __name__ == "__main__":
    import uvicorn
    import os

    port = int(os.getenv("PORT", 8000))
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)
