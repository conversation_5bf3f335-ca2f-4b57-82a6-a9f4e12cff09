"""
State Transition Logic for LangGraph Agent Orchestration

This module provides utility functions for state transitions in the LangGraph agent
orchestration system. It ensures thread safety and proper validation during state updates.

The transition functions are designed to:
1. Validate state before and after transitions
2. Provide clear error messages for invalid transitions
3. Ensure thread safety with append-only updates
4. Support the LangGraph execution model
"""

import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from app.langgraph.state import AgentState, RagContext

logger = logging.getLogger(__name__)


def validate_state(state: AgentState) -> Tuple[bool, Optional[str]]:
    """
    Validate the state for consistency and completeness.

    Args:
        state: The state to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check required fields
    if not state.query:
        return False, "Query is required"

    # Check metadata
    if not state.metadata:
        return False, "Metadata is required"

    if "timestamp" not in state.metadata:
        return False, "Timestamp is required in metadata"

    if "version" not in state.metadata:
        return False, "Version is required in metadata"

    # Check RAG context consistency
    if len(state.rag.docs) != len(state.rag.scores) and len(state.rag.docs) > 0:
        return False, "Number of documents and scores must match"

    # All checks passed
    return True, None


def transition_add_rag_documents(
    state: AgentState,
    docs: List[str],
    cites: List[Dict[str, Any]],
    scores: List[float]
) -> AgentState:
    """
    Add RAG documents to the state.

    Args:
        state: The current state
        docs: Document chunks to add
        cites: Citation metadata to add
        scores: Relevance scores to add

    Returns:
        Updated state with new documents
    """
    # Validate input
    if len(docs) != len(scores):
        raise ValueError("Number of documents and scores must match")

    if len(docs) != len(cites):
        raise ValueError("Number of documents and citations must match")

    # Use reducers to add documents
    state.rag.docs = state.rag.docs + docs
    state.rag.cites = state.rag.cites + cites
    state.rag.scores = state.rag.scores + scores

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after adding RAG documents: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


def transition_add_message(
    state: AgentState,
    role: str,
    content: str,
    **kwargs
) -> AgentState:
    """
    Add a message to the state.

    Args:
        state: The current state
        role: The role of the message sender
        content: The content of the message
        **kwargs: Additional message fields

    Returns:
        Updated state with new message
    """
    # Use the state method to add the message
    state.add_message(role, content, **kwargs)

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after adding message: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


def transition_add_department(
    state: AgentState,
    department: str
) -> AgentState:
    """
    Add a department to the consulted departments.

    Args:
        state: The current state
        department: The department to add

    Returns:
        Updated state with new department
    """
    # Use the state method to add the department
    state.add_department(department)

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after adding department: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


def transition_update_analysis(
    state: AgentState,
    department: str,
    score: float
) -> AgentState:
    """
    Update the analysis scores.

    Args:
        state: The current state
        department: The department to update
        score: The relevance score

    Returns:
        Updated state with new analysis
    """
    # Use the state method to update the analysis
    state.update_analysis(department, score)

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after updating analysis: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


def transition_set_response(
    state: AgentState,
    response: str
) -> AgentState:
    """
    Set the final response.

    Args:
        state: The current state
        response: The response to set

    Returns:
        Updated state with the response
    """
    # Use the state method to set the response
    state.set_response(response)

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after setting response: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


def transition_update_metadata(
    state: AgentState,
    **kwargs
) -> AgentState:
    """
    Update metadata fields.

    Args:
        state: The current state
        **kwargs: Metadata fields to update

    Returns:
        Updated state with new metadata
    """
    # Use the state method to update metadata
    state.update_metadata(**kwargs)

    # Validate the updated state
    is_valid, error = validate_state(state)
    if not is_valid:
        logger.error(f"Invalid state after updating metadata: {error}")
        raise ValueError(f"Invalid state after transition: {error}")

    return state


# Function to create a transition function with validation
def create_transition(
    transition_func: Callable[..., AgentState]
) -> Callable[..., AgentState]:
    """
    Create a transition function with validation.

    Args:
        transition_func: The transition function to wrap

    Returns:
        Wrapped transition function with validation
    """
    def wrapped_transition(state: AgentState, *args, **kwargs) -> AgentState:
        # Validate the initial state
        is_valid, error = validate_state(state)
        if not is_valid:
            logger.error(f"Invalid state before transition: {error}")
            raise ValueError(f"Invalid state before transition: {error}")

        # Apply the transition
        new_state = transition_func(state, *args, **kwargs)

        # Validate the updated state
        is_valid, error = validate_state(new_state)
        if not is_valid:
            logger.error(f"Invalid state after transition: {error}")
            raise ValueError(f"Invalid state after transition: {error}")

        return new_state

    return wrapped_transition
