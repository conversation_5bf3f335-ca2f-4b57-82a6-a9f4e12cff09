"""
Checkpointing for LangGraph Agent Orchestration

This module provides checkpointing functionality for the LangGraph agent orchestration system.
It includes an abstract checkpointer interface and concrete implementations for different
storage backends (SQLite for development, with hooks for PostgreSQL in production).

The checkpointing system is designed to:
1. Persist conversation state for resumption
2. Support different storage backends through a common interface
3. Handle serialization and deserialization of state objects
4. Provide proper error handling and recovery
"""

import json
import logging
import os
import sqlite3
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.langgraph.state import AgentState

logger = logging.getLogger(__name__)


class CheckpointerInterface(ABC):
    """
    Abstract interface for checkpointing implementations.

    This interface defines the methods that all checkpointer implementations
    must provide, allowing for different storage backends to be used.
    """

    @abstractmethod
    def save_checkpoint(self, thread_id: str, state: AgentState) -> bool:
        """
        Save a checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to save the checkpoint for
            state: The state to save

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    def load_checkpoint(self, thread_id: str) -> Optional[AgentState]:
        """
        Load the latest checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to load the checkpoint for

        Returns:
            The loaded state, or None if no checkpoint exists
        """
        pass

    @abstractmethod
    def list_checkpoints(self, thread_id: str) -> List[Dict[str, Any]]:
        """
        List all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to list checkpoints for

        Returns:
            List of checkpoint metadata
        """
        pass

    @abstractmethod
    def delete_checkpoint(self, thread_id: str, checkpoint_id: Optional[str] = None) -> bool:
        """
        Delete a checkpoint or all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to delete checkpoints for
            checkpoint_id: Optional specific checkpoint ID to delete

        Returns:
            True if successful, False otherwise
        """
        pass


class SQLiteCheckpointer(CheckpointerInterface):
    """
    SQLite implementation of the checkpointer interface.

    This implementation uses SQLite for storage, making it suitable for
    development and testing environments.
    """

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the SQLite checkpointer.

        Args:
            db_path: Path to the SQLite database file. If None, uses a default path.
        """
        if db_path is None:
            # Use default path in the project directory
            db_path = os.path.join(os.path.dirname(__file__), "..", "..", "data", "checkpoints.db")

        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        self.db_path = db_path
        self._initialize_db()

    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()

            # Create checkpoints table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS checkpoints (
                id TEXT PRIMARY KEY,
                thread_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                state TEXT NOT NULL,
                metadata TEXT
            )
            ''')

            # Create index on thread_id for faster lookups
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_thread_id ON checkpoints(thread_id)
            ''')

            conn.commit()
        except Exception as e:
            logger.error(f"Error initializing SQLite database: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def save_checkpoint(self, thread_id: str, state: AgentState) -> bool:
        """
        Save a checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to save the checkpoint for
            state: The state to save

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure thread_id is in metadata
            if "thread_id" not in state.metadata:
                state.metadata["thread_id"] = thread_id

            # Generate checkpoint ID from state metadata or create new one
            checkpoint_id = state.metadata.get("id", state.metadata.get("checkpoint_id"))

            # Serialize state to JSON
            state_json = state.model_dump_json()

            # Extract metadata for separate storage
            metadata = {
                "query": state.query,
                "timestamp": state.metadata.get("timestamp", datetime.now(timezone.utc).isoformat()),
                "version": state.metadata.get("version", "unknown"),
                "departments": state.departments
            }
            metadata_json = json.dumps(metadata)

            # Connect to database
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # Insert or replace checkpoint
                cursor.execute(
                    '''
                    INSERT OR REPLACE INTO checkpoints (id, thread_id, timestamp, state, metadata)
                    VALUES (?, ?, ?, ?, ?)
                    ''',
                    (
                        checkpoint_id,
                        thread_id,
                        state.metadata.get("timestamp", datetime.now(timezone.utc).isoformat()),
                        state_json,
                        metadata_json
                    )
                )

                conn.commit()
                return True
            except Exception as e:
                logger.error(f"Error saving checkpoint: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"Error in save_checkpoint: {e}")
            return False

    def load_checkpoint(self, thread_id: str) -> Optional[AgentState]:
        """
        Load the latest checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to load the checkpoint for

        Returns:
            The loaded state, or None if no checkpoint exists
        """
        try:
            # Connect to database
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # Get the latest checkpoint for the thread
                cursor.execute(
                    '''
                    SELECT state FROM checkpoints
                    WHERE thread_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 1
                    ''',
                    (thread_id,)
                )

                result = cursor.fetchone()
                if result is None:
                    return None

                # Deserialize state from JSON
                state_json = result[0]
                state = AgentState.model_validate_json(state_json)

                return state
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"Error loading checkpoint: {e}")
            return None

    def list_checkpoints(self, thread_id: str) -> List[Dict[str, Any]]:
        """
        List all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to list checkpoints for

        Returns:
            List of checkpoint metadata
        """
        try:
            # Connect to database
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # Get all checkpoints for the thread
                cursor.execute(
                    '''
                    SELECT id, timestamp, metadata FROM checkpoints
                    WHERE thread_id = ?
                    ORDER BY timestamp DESC
                    ''',
                    (thread_id,)
                )

                results = cursor.fetchall()
                checkpoints = []

                for row in results:
                    checkpoint_id, timestamp, metadata_json = row

                    # Parse metadata
                    try:
                        metadata = json.loads(metadata_json) if metadata_json else {}
                    except json.JSONDecodeError:
                        metadata = {}

                    checkpoints.append({
                        "id": checkpoint_id,
                        "thread_id": thread_id,
                        "timestamp": timestamp,
                        **metadata
                    })

                return checkpoints
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"Error listing checkpoints: {e}")
            return []

    def delete_checkpoint(self, thread_id: str, checkpoint_id: Optional[str] = None) -> bool:
        """
        Delete a checkpoint or all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to delete checkpoints for
            checkpoint_id: Optional specific checkpoint ID to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Connect to database
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                if checkpoint_id is None:
                    # Delete all checkpoints for the thread
                    cursor.execute(
                        "DELETE FROM checkpoints WHERE thread_id = ?",
                        (thread_id,)
                    )
                else:
                    # Delete specific checkpoint
                    cursor.execute(
                        "DELETE FROM checkpoints WHERE thread_id = ? AND id = ?",
                        (thread_id, checkpoint_id)
                    )

                conn.commit()
                return True
            except Exception as e:
                logger.error(f"Error deleting checkpoint: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"Error in delete_checkpoint: {e}")
            return False


class PostgreSQLCheckpointer(CheckpointerInterface):
    """
    PostgreSQL implementation of the checkpointer interface.

    This implementation uses PostgreSQL for storage, making it suitable for
    production environments.
    """

    def __init__(self, db_url: Optional[str] = None):
        """
        Initialize the PostgreSQL checkpointer.

        Args:
            db_url: Database URL for PostgreSQL. If None, uses the URL from settings.
        """
        from app.config import get_settings
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        if db_url is None:
            # Use the database URL from settings
            db_url = get_settings().DATABASE_URL

        self.db_url = db_url
        self.engine = create_engine(db_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # Initialize the database schema if it doesn't exist
        self._initialize_db()

    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        from sqlalchemy import text

        with self.engine.connect() as conn:
            try:
                # Check if the table exists
                result = conn.execute(text(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'conversation_checkpoints')"
                ))
                table_exists = result.scalar()

                if not table_exists:
                    # Create the table
                    conn.execute(text("""
                    CREATE TABLE conversation_checkpoints (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        thread_id VARCHAR(255) NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                        state JSONB NOT NULL,
                        metadata JSONB
                    )
                    """))

                    # Create index on thread_id
                    conn.execute(text("""
                    CREATE INDEX idx_thread_id ON conversation_checkpoints(thread_id)
                    """))

                    conn.commit()
                    logger.info("Created conversation_checkpoints table in PostgreSQL")
            except Exception as e:
                logger.error(f"Error initializing PostgreSQL schema: {e}")
                conn.rollback()
                raise

    def save_checkpoint(self, thread_id: str, state: AgentState) -> bool:
        """
        Save a checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to save the checkpoint for
            state: The state to save

        Returns:
            True if successful, False otherwise
        """
        from sqlalchemy import text
        import json

        try:
            # Ensure thread_id is in metadata
            if "thread_id" not in state.metadata:
                state.metadata["thread_id"] = thread_id

            # Generate checkpoint ID from state metadata or create new one
            checkpoint_id = state.metadata.get("id", state.metadata.get("checkpoint_id"))

            # Serialize state to JSON
            state_json = state.model_dump_json()

            # Extract metadata for separate storage
            metadata = {
                "query": state.query,
                "timestamp": state.metadata.get("timestamp", datetime.now(timezone.utc).isoformat()),
                "version": state.metadata.get("version", "unknown"),
                "departments": state.departments
            }
            metadata_json = json.dumps(metadata)

            # Connect to database
            with self.engine.connect() as conn:
                # Insert or replace checkpoint
                conn.execute(
                    text("""
                    INSERT INTO conversation_checkpoints (id, thread_id, created_at, updated_at, state, metadata)
                    VALUES (:id, :thread_id, :created_at, :updated_at, :state, :metadata)
                    ON CONFLICT (id) DO UPDATE
                    SET thread_id = :thread_id, updated_at = :updated_at, state = :state, metadata = :metadata
                    """),
                    {
                        "id": checkpoint_id,
                        "thread_id": thread_id,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc),
                        "state": state_json,
                        "metadata": metadata_json
                    }
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error saving checkpoint to PostgreSQL: {e}")
            return False

    def load_checkpoint(self, thread_id: str) -> Optional[AgentState]:
        """
        Load the latest checkpoint for the given thread ID.

        Args:
            thread_id: The thread ID to load the checkpoint for

        Returns:
            The loaded state, or None if no checkpoint exists
        """
        from sqlalchemy import text
        import json

        try:
            # Connect to database
            with self.engine.connect() as conn:
                # Get the latest checkpoint for the thread
                result = conn.execute(
                    text("""
                    SELECT state FROM conversation_checkpoints
                    WHERE thread_id = :thread_id
                    ORDER BY updated_at DESC
                    LIMIT 1
                    """),
                    {"thread_id": thread_id}
                )

                row = result.fetchone()
                if row is None:
                    return None

                # Deserialize state from JSON
                state_json = row[0]
                if isinstance(state_json, dict):
                    # If PostgreSQL returns a dict (JSONB column), convert it to JSON string
                    state_json = json.dumps(state_json)
                state = AgentState.model_validate_json(state_json)

                return state
        except Exception as e:
            logger.error(f"Error loading checkpoint from PostgreSQL: {e}")
            return None

    def list_checkpoints(self, thread_id: str) -> List[Dict[str, Any]]:
        """
        List all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to list checkpoints for

        Returns:
            List of checkpoint metadata
        """
        from sqlalchemy import text
        import json

        try:
            # Connect to database
            with self.engine.connect() as conn:
                # Get all checkpoints for the thread
                result = conn.execute(
                    text("""
                    SELECT id, created_at, updated_at, metadata FROM conversation_checkpoints
                    WHERE thread_id = :thread_id
                    ORDER BY updated_at DESC
                    """),
                    {"thread_id": thread_id}
                )

                checkpoints = []
                for row in result:
                    checkpoint_id, created_at, updated_at, metadata_json = row

                    # Parse metadata
                    try:
                        if isinstance(metadata_json, dict):
                            # If PostgreSQL returns a dict (JSONB column), use it directly
                            metadata = metadata_json
                        else:
                            # Otherwise, parse the JSON string
                            metadata = json.loads(metadata_json) if metadata_json else {}
                    except json.JSONDecodeError:
                        metadata = {}

                    checkpoints.append({
                        "id": str(checkpoint_id),
                        "thread_id": thread_id,
                        "created_at": created_at.isoformat() if hasattr(created_at, "isoformat") else created_at,
                        "updated_at": updated_at.isoformat() if hasattr(updated_at, "isoformat") else updated_at,
                        **metadata
                    })

                return checkpoints
        except Exception as e:
            logger.error(f"Error listing checkpoints from PostgreSQL: {e}")
            return []

    def delete_checkpoint(self, thread_id: str, checkpoint_id: Optional[str] = None) -> bool:
        """
        Delete a checkpoint or all checkpoints for the given thread ID.

        Args:
            thread_id: The thread ID to delete checkpoints for
            checkpoint_id: Optional specific checkpoint ID to delete

        Returns:
            True if successful, False otherwise
        """
        from sqlalchemy import text

        try:
            # Connect to database
            with self.engine.connect() as conn:
                if checkpoint_id is None:
                    # Delete all checkpoints for the thread
                    conn.execute(
                        text("DELETE FROM conversation_checkpoints WHERE thread_id = :thread_id"),
                        {"thread_id": thread_id}
                    )
                else:
                    # Delete specific checkpoint
                    conn.execute(
                        text("DELETE FROM conversation_checkpoints WHERE thread_id = :thread_id AND id = :id"),
                        {"thread_id": thread_id, "id": checkpoint_id}
                    )

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error deleting checkpoint from PostgreSQL: {e}")
            return False


# Factory function to get the appropriate checkpointer based on environment
def get_checkpointer() -> CheckpointerInterface:
    """
    Get the appropriate checkpointer based on environment configuration.

    Returns:
        A checkpointer implementation
    """
    # Get settings
    from app.config import get_settings
    settings = get_settings()

    # Check environment for configuration
    env = os.environ.get("CHECKPOINTER_TYPE", settings.CHECKPOINTER_TYPE).lower()

    if env == "sqlite":
        db_path = os.environ.get("SQLITE_CHECKPOINTER_PATH")
        logger.info("Using SQLite checkpointer")
        return SQLiteCheckpointer(db_path)

    elif env == "postgres" or env == "postgresql":
        db_url = os.environ.get("DATABASE_URL", settings.DATABASE_URL)
        logger.info("Using PostgreSQL checkpointer")
        return PostgreSQLCheckpointer(db_url)

    # For future implementation:
    # elif env == "redis":
    #     host = os.environ.get("REDIS_HOST", "localhost")
    #     port = os.environ.get("REDIS_PORT", "6379")
    #     db = os.environ.get("REDIS_DB", "0")
    #     password = os.environ.get("REDIS_PASSWORD", "")
    #     return RedisCheckpointer(host, port, db, password)

    # Default to PostgreSQL for production use
    logger.info(f"Unknown checkpointer type: {env}, defaulting to PostgreSQL")
    return PostgreSQLCheckpointer(settings.DATABASE_URL)
