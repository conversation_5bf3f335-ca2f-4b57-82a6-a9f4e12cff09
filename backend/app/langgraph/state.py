"""
State Management for LangGraph Agent Orchestration

This module defines the state models and reducers for the LangGraph agent orchestration system.
It provides thread-safe state management with append-only updates using Pydantic models and
annotated reducers.

The state models are designed to be:
1. Thread-safe with append-only updates
2. Type-safe with Pydantic validation
3. Extensible for future enhancements
4. Compatible with LangGraph's checkpointing
"""

from __future__ import annotations  # For forward references in type hints

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set, Union
from typing_extensions import Annotated
from pydantic import BaseModel, Field, field_validator, ConfigDict
import operator
from uuid import uuid4

# Version for state model compatibility
STATE_MODEL_VERSION = "0.1.0"


def append_unique(existing: List[str], new_items: List[str]) -> List[str]:
    """
    Append items to a list, ensuring uniqueness.

    Args:
        existing: The existing list
        new_items: New items to append

    Returns:
        Updated list with unique items
    """
    # Convert to set for deduplication
    unique_set = set(existing)

    # Add new items
    for item in new_items:
        unique_set.add(item)

    # Convert back to list, preserving original order where possible
    result = existing.copy()
    for item in new_items:
        if item not in existing:
            result.append(item)

    return result


class RagContext(BaseModel):
    """
    Context from the RAG system including retrieved documents and citations.

    This class stores the documents retrieved from the knowledge base and their
    associated citations. It uses append-only reducers to ensure thread safety.
    """

    # Retrieved document chunks with append-only reducer
    docs: Annotated[List[str], operator.add] = Field(
        default_factory=list,
        description="Retrieved document chunks"
    )

    # Citation metadata with append-only reducer
    cites: Annotated[List[Dict[str, Any]], operator.add] = Field(
        default_factory=list,
        description="Citation metadata"
    )

    # Document scores with append-only reducer
    scores: Annotated[List[float], operator.add] = Field(
        default_factory=list,
        description="Relevance scores for retrieved documents"
    )

    model_config = ConfigDict(
        validate_assignment=True,
        extra="forbid"  # Prevent additional fields
    )


class AgentState(BaseModel):
    """
    Primary state object that flows through the LangGraph.

    This class represents the complete state of the agent orchestration system,
    including the user query, RAG context, inter-agent messages, departments
    consulted, analysis results, and final response.
    """

    # Original user query
    query: str = Field(
        ...,  # Required field
        description="Original user query"
    )

    # RAG context with retrieved documents and citations
    rag: RagContext = Field(
        default_factory=RagContext,
        description="RAG context with documents and citations"
    )

    # Inter-agent messages with append-only reducer
    messages: Annotated[List[Dict[str, Any]], operator.add] = Field(
        default_factory=list,
        description="Inter-agent messages"
    )

    # Departments consulted with append-only reducer for uniqueness
    departments: Annotated[List[str], append_unique] = Field(
        default_factory=list,
        description="Departments consulted"
    )

    # Query analysis with scores per department
    analysis: Dict[str, float] = Field(
        default_factory=dict,
        description="Query analysis with scores per department"
    )

    # Query analysis results from Co-CEO agent
    query_analysis: Dict[str, Any] = Field(
        default_factory=dict,
        description="Query analysis results from Co-CEO agent"
    )

    # Retrieved knowledge from knowledge base
    retrieved_knowledge: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Retrieved knowledge from knowledge base"
    )

    # Department responses
    department_responses: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Responses from department agents"
    )

    # Final response to user
    response: Optional[str] = Field(
        None,
        description="Final response to user"
    )

    # Additional metadata
    metadata: Dict[str, Any] = Field(
        default_factory=lambda: {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": STATE_MODEL_VERSION,
            "id": str(uuid4())
        },
        description="Additional metadata"
    )

    model_config = ConfigDict(
        validate_assignment=True,
        extra="forbid"  # Prevent additional fields
    )

    @field_validator("metadata", mode="before")
    def ensure_metadata(cls, v):
        """Ensure metadata has required fields."""
        if v is None:
            v = {}

        # Ensure timestamp exists
        if "timestamp" not in v:
            v["timestamp"] = datetime.now(timezone.utc).isoformat()

        # Ensure version exists
        if "version" not in v:
            v["version"] = STATE_MODEL_VERSION

        # Ensure id exists
        if "id" not in v:
            v["id"] = str(uuid4())

        return v

    def add_message(self, role: str, content: str, **kwargs) -> AgentState:
        """
        Add a message to the state.

        Args:
            role: The role of the message sender (system, user, assistant, etc.)
            content: The content of the message
            **kwargs: Additional message fields

        Returns:
            Updated state with the new message
        """
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **kwargs
        }

        # Use the reducer to add the message
        self.messages = self.messages + [message]
        return self

    def add_department(self, department: str) -> AgentState:
        """
        Add a department to the consulted departments.

        Args:
            department: The department to add

        Returns:
            Updated state with the new department
        """
        # Use the reducer to add the department
        self.departments = append_unique(self.departments, [department])
        return self

    def update_analysis(self, department: str, score: float) -> AgentState:
        """
        Update the analysis scores.

        Args:
            department: The department to update
            score: The relevance score

        Returns:
            Updated state with the new analysis
        """
        # Create a new dictionary to ensure immutability
        new_analysis = self.analysis.copy()
        new_analysis[department] = score
        self.analysis = new_analysis
        return self

    def set_response(self, response: str) -> AgentState:
        """
        Set the final response.

        Args:
            response: The response to set

        Returns:
            Updated state with the response
        """
        self.response = response
        return self

    def update_metadata(self, **kwargs) -> AgentState:
        """
        Update metadata fields.

        Args:
            **kwargs: Metadata fields to update

        Returns:
            Updated state with new metadata
        """
        # Create a new dictionary to ensure immutability
        new_metadata = self.metadata.copy()
        new_metadata.update(kwargs)
        self.metadata = new_metadata
        return self


def create_initial_state(
    query: str,
    user_id: Optional[str] = None,
    thread_id: Optional[str] = None,
    **kwargs
) -> AgentState:
    """
    Create an initial state for a new conversation.

    Args:
        query: The user query
        user_id: Optional user ID for scoping
        thread_id: Optional thread ID for conversation tracking
        **kwargs: Additional metadata fields

    Returns:
        New AgentState instance
    """
    # Generate thread_id if not provided
    if thread_id is None:
        thread_id = str(uuid4())

    # Create metadata
    metadata = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": STATE_MODEL_VERSION,
        "thread_id": thread_id,
        "id": str(uuid4())
    }

    # Add user_id if provided
    if user_id is not None:
        metadata["user_id"] = user_id

    # Add additional metadata
    metadata.update(kwargs)

    # Create and return the state
    return AgentState(
        query=query,
        metadata=metadata
    )
