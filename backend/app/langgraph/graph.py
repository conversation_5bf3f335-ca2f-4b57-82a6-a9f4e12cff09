"""
LangGraph Orchestration

This module implements the LangGraph orchestration for the multi-agent system.
It defines the directed acyclic graph (DAG) for the agent workflow and the
node functions that process the state at each step.

The implementation includes timeout and retry logic for all node functions to ensure
that the system remains responsive even when components experience delays or failures.
It also implements conditional routing to direct queries to appropriate departments.
"""
from typing import Dict, Any, Optional, List, Callable, Union, Set
import asyncio
import logging
import uuid
from datetime import datetime, timezone
from langgraph.graph import StateGraph
from langchain_core.runnables import RunnableLambda

from app.langgraph.state import AgentState, create_initial_state
from app.langgraph.timeout import (
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout
)
from app.core.timeout import with_timeout_and_retry
from app.core.tracing import TracingCollector, TracingLevel

logger = logging.getLogger(__name__)


@with_analyze_query_timeout()
async def analyze_query_node(state: AgentState, co_ceo_agent) -> AgentState:
    """
    Node for analyzing the user query.

    Args:
        state: The current state
        co_ceo_agent: The Co-CEO agent

    Returns:
        Updated state
    """
    logger.info(f"Analyzing query: {state.query}")

    # Get metrics and tracing collectors if available
    metrics_collector = state.metadata.get("metrics_collector")
    tracing_collector = state.metadata.get("tracing_collector")
    start_time = datetime.now(timezone.utc)

    # Add trace event if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="analyze_query_node",
            event_type="node_start",
            state_before=state,
            metadata={"query": state.query}
        )

    # Add event if metrics collector is available
    if metrics_collector:
        metrics_collector.add_event(
            component="analyze_query_node",
            action="start",
            details={"query": state.query}
        )

    # Analyze the query
    analysis = await co_ceo_agent.analyze_query(state.query)

    # Update the state
    updated_state = state.model_copy(deep=True)
    updated_state.query_analysis = analysis

    # Track metrics if available
    if metrics_collector:
        elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()
        metrics_collector.add_event(
            component="analyze_query_node",
            action="complete",
            details={
                "elapsed_time": elapsed_time,
                "analysis": {k: v for k, v in analysis.items() if k != "raw_response"}
            }
        )

    # Add trace event for node completion if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="analyze_query_node",
            event_type="node_end",
            state_before=state,
            state_after=updated_state,
            metadata={
                "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                "analysis": {k: v for k, v in analysis.items() if k != "raw_response"}
            }
        )

    return updated_state


@with_retrieve_knowledge_timeout()
async def retrieve_knowledge_node(state: AgentState, knowledge_base_service) -> AgentState:
    """
    Node for retrieving knowledge.

    Args:
        state: The current state
        knowledge_base_service: The knowledge base service

    Returns:
        Updated state
    """
    logger.info("Retrieving knowledge")

    # Get metrics and tracing collectors if available
    metrics_collector = state.metadata.get("metrics_collector")
    tracing_collector = state.metadata.get("tracing_collector")
    start_time = datetime.now(timezone.utc)

    # Add trace event if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="retrieve_knowledge_node",
            event_type="node_start",
            state_before=state,
            metadata={"query": state.query}
        )

    # Add event if metrics collector is available
    if metrics_collector:
        metrics_collector.add_event(
            component="retrieve_knowledge_node",
            action="start",
            details={"query": state.query}
        )

    if "errors" in state.metadata and state.metadata["errors"]:
        logger.warning("Skipping knowledge retrieval due to previous errors")
        return state

    # Get the query
    query = state.query

    # Retrieve knowledge
    knowledge = await knowledge_base_service.search(query)

    # Update the state
    updated_state = state.model_copy(deep=True)
    updated_state.retrieved_knowledge = knowledge

    # Track metrics if available
    if metrics_collector:
        elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        # Track retrieval metrics
        metrics_collector.track_retrieval(
            query=query,
            results=knowledge,
            timing=elapsed_time
        )

        metrics_collector.add_event(
            component="retrieve_knowledge_node",
            action="complete",
            details={
                "elapsed_time": elapsed_time,
                "doc_count": len(knowledge)
            }
        )

    # Add trace event for node completion if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="retrieve_knowledge_node",
            event_type="node_end",
            state_before=state,
            state_after=updated_state,
            metadata={
                "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                "doc_count": len(knowledge),
                "knowledge_sources": [doc.get("title", "Unknown") for doc in knowledge[:3]]
            }
        )

    return updated_state


@with_route_to_departments_timeout()
async def route_to_departments_node(
    state: AgentState, co_ceo_agent
) -> AgentState:
    """
    Node for determining which departments to route to.

    This node analyzes the query and determines which departments should be consulted.
    It updates the state with the list of departments to route to.

    Args:
        state: The current state
        co_ceo_agent: The Co-CEO agent (used for coordination)

    Returns:
        Updated state with departments to route to
    """
    logger.info("Determining departments to route to")

    # Get metrics and tracing collectors if available
    metrics_collector = state.metadata.get("metrics_collector")
    tracing_collector = state.metadata.get("tracing_collector")
    start_time = datetime.now(timezone.utc)

    # Add trace event if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="route_to_departments_node",
            event_type="node_start",
            state_before=state,
            metadata={"query_analysis": state.query_analysis}
        )

    # Add event if metrics collector is available
    if metrics_collector:
        metrics_collector.add_event(
            component="route_to_departments_node",
            action="start",
            details={"query_analysis": state.query_analysis}
        )

    if "errors" in state.metadata and state.metadata["errors"]:
        logger.warning("Skipping department routing due to previous errors")
        return state

    # Get the query and analysis
    analysis = state.query_analysis or {}

    # Get relevant departments
    departments = analysis.get("relevant_departments", [])

    # Add departments to state
    updated_state = state.model_copy(deep=True)

    # Ensure departments list is initialized
    if not hasattr(updated_state, "departments") or updated_state.departments is None:
        updated_state.departments = []

    # Add each department
    for department in departments:
        if department not in updated_state.departments:
            updated_state.add_department(department)

    # Log routing decision
    if departments:
        logger.info(f"Will route to departments: {', '.join(departments)}")
        logger.info(f"Updated state departments: {updated_state.departments}")
    else:
        logger.info("No relevant departments found for this query")

    # Update metadata with routing decision
    routing_decision = {
        "departments": departments,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    updated_state.update_metadata(routing_decision=routing_decision)

    # Add trace event for routing decision if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="co_ceo_agent",
            event_type="agent_response",
            metadata={
                "department": "co_ceo",
                "response": f"Routing to departments: {', '.join(departments)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )

    # Track metrics if available
    if metrics_collector:
        elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        # Track routing metrics
        metrics_collector.track_routing(
            state=updated_state,
            departments=departments,
            confidence_scores=analysis.get("department_scores", {}),
            expected_departments=state.metadata.get("expected_departments")
        )

        metrics_collector.add_event(
            component="route_to_departments_node",
            action="complete",
            details={
                "elapsed_time": elapsed_time,
                "departments": departments,
                "routing_decision": routing_decision
            }
        )

    # Add trace event for node completion if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="route_to_departments_node",
            event_type="node_end",
            state_before=state,
            state_after=updated_state,
            metadata={
                "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                "departments": departments,
                "routing_decision": routing_decision
            }
        )

    return updated_state


async def process_departments_node(state: AgentState, finance_agent, marketing_agent) -> AgentState:
    """
    Node for processing queries with all relevant departments in sequence.

    This node processes the query with each relevant department in sequence,
    avoiding the parallel execution that causes the "Can receive only one value per step" error.

    Args:
        state: The current state
        finance_agent: The Finance agent
        marketing_agent: The Marketing agent

    Returns:
        Updated state with all department responses
    """
    # Get metrics and tracing collectors if available
    metrics_collector = state.metadata.get("metrics_collector")
    tracing_collector = state.metadata.get("tracing_collector")
    start_time = datetime.now(timezone.utc)

    # Add trace event if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="process_departments_node",
            event_type="node_start",
            state_before=state,
            metadata={"departments": list(state.departments)}
        )

    # Get the query and analysis
    query = state.query
    analysis = state.query_analysis or {}
    departments = state.departments

    # Create a copy of the state to update
    updated_state = state.model_copy(deep=True)
    department_responses = updated_state.department_responses.copy()

    # Process each department in sequence
    for department in departments:
        dept_start_time = datetime.now(timezone.utc)

        if department == "finance":
            logger.info("Processing query with Finance department")

            # Add trace event for department communication
            if tracing_collector:
                # Add trace event for co_ceo agent sending query to finance agent
                tracing_collector.add_trace(
                    node_id="co_ceo_agent",
                    event_type="agent_response",
                    metadata={
                        "department": "co_ceo",
                        "response": f"Routing query to finance department: {query[:100]}..." if len(query) > 100 else query,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

                # Add trace event for finance agent receiving query
                tracing_collector.add_trace(
                    node_id="finance_agent",
                    event_type="agent_query",
                    metadata={
                        "department": "finance",
                        "query": query,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "context": {
                            "analysis": str(analysis)[:200] + "..." if len(str(analysis)) > 200 else str(analysis),
                            "knowledge_count": len(state.retrieved_knowledge)
                        }
                    }
                )

            finance_response = await with_timeout_and_retry(
                finance_agent.process_query,
                query,
                context={
                    "analysis": analysis,
                    "knowledge": state.retrieved_knowledge,
                },
                timeout_seconds=30,
                operation_name="finance_agent.process_query"
            )
            department_responses["finance"] = finance_response

            # Add trace event for department response
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="finance_agent",
                    event_type="agent_response",
                    metadata={
                        "department": "finance",
                        "response": finance_response.get("response", ""),
                        "elapsed_time": (datetime.now(timezone.utc) - dept_start_time).total_seconds(),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

        elif department == "marketing":
            logger.info("Processing query with Marketing department")

            # Add trace event for department communication
            if tracing_collector:
                # Add trace event for co_ceo agent sending query to marketing agent
                tracing_collector.add_trace(
                    node_id="co_ceo_agent",
                    event_type="agent_response",
                    metadata={
                        "department": "co_ceo",
                        "response": f"Routing query to marketing department: {query[:100]}..." if len(query) > 100 else query,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

                # Add trace event for marketing agent receiving query
                tracing_collector.add_trace(
                    node_id="marketing_agent",
                    event_type="agent_query",
                    metadata={
                        "department": "marketing",
                        "query": query,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "context": {
                            "analysis": str(analysis)[:200] + "..." if len(str(analysis)) > 200 else str(analysis),
                            "knowledge_count": len(state.retrieved_knowledge)
                        }
                    }
                )

            marketing_response = await with_timeout_and_retry(
                marketing_agent.process_query,
                query,
                context={
                    "analysis": analysis,
                    "knowledge": state.retrieved_knowledge,
                },
                timeout_seconds=30,
                operation_name="marketing_agent.process_query"
            )
            department_responses["marketing"] = marketing_response

            # Add trace event for department response
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="marketing_agent",
                    event_type="agent_response",
                    metadata={
                        "department": "marketing",
                        "response": marketing_response.get("response", ""),
                        "elapsed_time": (datetime.now(timezone.utc) - dept_start_time).total_seconds(),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

        elif department == "co_ceo":
            logger.info("Processing query with Co-CEO department")

            # Add trace event for department communication
            if tracing_collector:
                # Add trace event for co_ceo agent receiving query from itself (for consistency)
                tracing_collector.add_trace(
                    node_id="co_ceo_agent",
                    event_type="agent_query",
                    metadata={
                        "department": "co_ceo",
                        "query": query,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "context": {
                            "analysis": str(analysis)[:200] + "..." if len(str(analysis)) > 200 else str(analysis),
                            "knowledge_count": len(state.retrieved_knowledge)
                        }
                    }
                )

            # For co_ceo, we don't need to call an external agent since it's the orchestrator
            # But we can add a response to indicate it's being processed
            co_ceo_response = {
                "response": "Coordinating response from multiple departments",
                "department": "co_ceo",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            department_responses["co_ceo"] = co_ceo_response

            # Add trace event for department response
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="co_ceo_agent",
                    event_type="agent_response",
                    metadata={
                        "department": "co_ceo",
                        "response": co_ceo_response.get("response", ""),
                        "elapsed_time": (datetime.now(timezone.utc) - dept_start_time).total_seconds(),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )

    # Update state with all department responses
    updated_state.department_responses = department_responses

    # Add trace event for node completion if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="process_departments_node",
            event_type="node_end",
            state_before=state,
            state_after=updated_state,
            metadata={
                "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                "departments_processed": list(departments),
                "response_count": len(department_responses)
            }
        )

    return updated_state


# Keep these functions for backward compatibility, but they won't be used in the new graph
async def finance_department_node(state: AgentState, finance_agent) -> AgentState:
    """
    Node for processing queries with the Finance department.

    Args:
        state: The current state
        finance_agent: The Finance agent

    Returns:
        Updated state with Finance department response
    """
    logger.info("Processing query with Finance department")

    # Get the query and analysis
    query = state.query
    analysis = state.query_analysis or {}

    # Process the query with the Finance agent
    finance_response = await with_timeout_and_retry(
        finance_agent.process_query,
        query,
        context={
            "analysis": analysis,
            "knowledge": state.retrieved_knowledge,
        },
        timeout_seconds=30,
        operation_name="finance_agent.process_query"
    )

    # Update the state with the Finance department response
    updated_state = state.model_copy(deep=True)

    # Get existing department responses
    department_responses = updated_state.department_responses.copy()
    department_responses["finance"] = finance_response

    # Update state
    updated_state.department_responses = department_responses

    return updated_state


async def marketing_department_node(state: AgentState, marketing_agent) -> AgentState:
    """
    Node for processing queries with the Marketing department.

    Args:
        state: The current state
        marketing_agent: The Marketing agent

    Returns:
        Updated state with Marketing department response
    """
    logger.info("Processing query with Marketing department")

    # Get the query and analysis
    query = state.query
    analysis = state.query_analysis or {}

    # Process the query with the Marketing agent
    marketing_response = await with_timeout_and_retry(
        marketing_agent.process_query,
        query,
        context={
            "analysis": analysis,
            "knowledge": state.retrieved_knowledge,
        },
        timeout_seconds=30,
        operation_name="marketing_agent.process_query"
    )

    # Update the state with the Marketing department response
    updated_state = state.model_copy(deep=True)

    # Get existing department responses
    department_responses = updated_state.department_responses.copy()
    department_responses["marketing"] = marketing_response

    # Update state
    updated_state.department_responses = department_responses

    return updated_state


async def fallback_node(state: AgentState, co_ceo_agent) -> AgentState:
    """
    Fallback node for when no departments are relevant.

    Args:
        state: The current state
        co_ceo_agent: The Co-CEO agent

    Returns:
        Updated state with fallback response
    """
    logger.info("No relevant departments found, using fallback")

    # Get the query
    query = state.query

    # Generate a generic response using the Co-CEO agent
    fallback_response = await with_timeout_and_retry(
        co_ceo_agent.generate_response,
        query=query,
        analysis=state.query_analysis or {},
        department_responses=[],
        timeout_seconds=30,
        operation_name="co_ceo_agent.generate_response"
    )

    # Update the state with the fallback response
    updated_state = state.model_copy(deep=True)
    updated_state.update_metadata(used_fallback=True)

    # Create a department response for the Co-CEO
    department_responses = updated_state.department_responses.copy()
    department_responses["co_ceo"] = {
        "response": fallback_response,
        "department": "co_ceo",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    # Update state
    updated_state.department_responses = department_responses

    return updated_state


@with_generate_response_timeout()
async def generate_response_node(state: AgentState, co_ceo_agent) -> AgentState:
    """
    Node for generating the final response.

    Args:
        state: The current state
        co_ceo_agent: The Co-CEO agent (used for coordination)

    Returns:
        Updated state
    """
    logger.info("Generating response")

    # Get metrics and tracing collectors if available
    metrics_collector = state.metadata.get("metrics_collector")
    tracing_collector = state.metadata.get("tracing_collector")
    start_time = datetime.now(timezone.utc)

    # Add trace event if tracing collector is available
    if tracing_collector:
        tracing_collector.add_trace(
            node_id="generate_response_node",
            event_type="node_start",
            state_before=state,
            metadata={
                "department_count": len(state.department_responses or {}),
                "departments": list(state.department_responses.keys()) if state.department_responses else []
            }
        )

    # Add event if metrics collector is available
    if metrics_collector:
        metrics_collector.add_event(
            component="generate_response_node",
            action="start",
            details={
                "department_count": len(state.department_responses or {})
            }
        )

    # Check for errors in metadata
    if "errors" in state.metadata and state.metadata["errors"]:
        error_messages = [error.get("message", "Unknown error") for error in state.metadata["errors"]]
        error_summary = "; ".join(error_messages)
        logger.warning(f"Generating response with errors: {error_summary}")

        updated_state = state.model_copy(deep=True)
        updated_state.response = f"I'm sorry, but I encountered some issues while processing your request: {error_summary}"

        # Track metrics if available
        if metrics_collector:
            elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            metrics_collector.add_event(
                component="generate_response_node",
                action="error",
                details={
                    "elapsed_time": elapsed_time,
                    "errors": error_messages
                }
            )

        # Add trace event for node completion if tracing collector is available
        if tracing_collector:
            tracing_collector.add_trace(
                node_id="generate_response_node",
                event_type="node_end",
                state_before=state,
                state_after=updated_state,
                metadata={
                    "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                    "error": True,
                    "error_messages": error_messages
                }
            )

        return updated_state

    # Get the department responses
    department_responses = state.department_responses or {}

    # Generate a response based on the department responses
    if department_responses:
        # Combine department responses
        combined_response = "Based on the information from our departments:\n\n"

        for dept, response in department_responses.items():
            # Get the response text
            response_text = response.get('response', '')

            # Check if the response already has references
            if "## References" not in response_text and "References" not in response_text:
                # Get knowledge sources from the response metadata
                knowledge_sources = response.get('knowledge_sources', [])

                # If there are knowledge sources, add them as references
                if knowledge_sources:
                    response_text += "\n\n## References\n"
                    for i, source in enumerate(knowledge_sources, 1):
                        response_text += f"{i}. {source}\n"

            combined_response += f"- {dept.capitalize()}: {response_text}\n\n"

        updated_state = state.model_copy(deep=True)
        updated_state.response = combined_response.strip()

        # Track metrics if available
        if metrics_collector:
            elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            # Track generation metrics
            metrics_collector.track_generation(
                query=state.query,
                response=combined_response.strip(),
                context_docs=state.retrieved_knowledge,
                timing=elapsed_time,
                departments=list(department_responses.keys())
            )

            metrics_collector.add_event(
                component="generate_response_node",
                action="complete",
                details={
                    "elapsed_time": elapsed_time,
                    "response_length": len(combined_response.strip()),
                    "departments": list(department_responses.keys())
                }
            )

        # Add trace event for node completion if tracing collector is available
        if tracing_collector:
            tracing_collector.add_trace(
                node_id="generate_response_node",
                event_type="node_end",
                state_before=state,
                state_after=updated_state,
                metadata={
                    "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                    "response_length": len(combined_response.strip()),
                    "departments": list(department_responses.keys()),
                    "final_response": True
                }
            )

            # Add a final trace event for the co_ceo agent's response
            tracing_collector.add_trace(
                node_id="co_ceo_agent",
                event_type="agent_response",
                metadata={
                    "department": "co_ceo",
                    "response": combined_response.strip()[:100] + "..." if len(combined_response.strip()) > 100 else combined_response.strip(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

        return updated_state
    else:
        # No department responses, generate a generic response
        generic_response = "I don't have specific information about that. Can you please provide more details or ask a different question?"

        updated_state = state.model_copy(deep=True)
        updated_state.response = generic_response

        # Track metrics if available
        if metrics_collector:
            elapsed_time = (datetime.now(timezone.utc) - start_time).total_seconds()

            # Track generation metrics
            metrics_collector.track_generation(
                query=state.query,
                response=generic_response,
                context_docs=[],
                timing=elapsed_time,
                departments=[]
            )

            metrics_collector.add_event(
                component="generate_response_node",
                action="complete_fallback",
                details={
                    "elapsed_time": elapsed_time,
                    "response_length": len(generic_response)
                }
            )

        # Add trace event for node completion if tracing collector is available
        if tracing_collector:
            tracing_collector.add_trace(
                node_id="generate_response_node",
                event_type="node_end",
                state_before=state,
                state_after=updated_state,
                metadata={
                    "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                    "response_length": len(generic_response),
                    "departments": [],
                    "final_response": True
                }
            )

            # Add a final trace event for the co_ceo agent's response
            tracing_collector.add_trace(
                node_id="co_ceo_agent",
                event_type="agent_response",
                metadata={
                    "department": "co_ceo",
                    "response": generic_response[:100] + "..." if len(generic_response) > 100 else generic_response,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

        return updated_state


def build_co_ceo_graph(co_ceo_agent, finance_agent, marketing_agent, knowledge_base_service) -> StateGraph:
    """
    Build the Co-CEO agent graph with sequential department processing.

    This function creates a directed acyclic graph (DAG) that processes departments
    sequentially to avoid the "Can receive only one value per step" error.

    Args:
        co_ceo_agent: The Co-CEO agent
        finance_agent: The Finance agent
        marketing_agent: The Marketing agent
        knowledge_base_service: The knowledge base service

    Returns:
        The state graph with sequential department processing
    """
    # Create the graph
    workflow = StateGraph(AgentState)

    # Create wrappers for async node functions using RunnableLambda
    # This is compatible with older versions of langchain-core

    # Add nodes with async functions wrapped in RunnableLambda
    # Use a helper function to properly handle async functions
    def async_wrapper(func):
        async def wrapper(state):
            return await func(state)
        return wrapper

    workflow.add_node("analyze_query", RunnableLambda(async_wrapper(lambda state: analyze_query_node(state, co_ceo_agent))))
    workflow.add_node("retrieve_knowledge", RunnableLambda(async_wrapper(lambda state: retrieve_knowledge_node(state, knowledge_base_service))))
    workflow.add_node("route_to_departments", RunnableLambda(async_wrapper(lambda state: route_to_departments_node(state, co_ceo_agent))))
    workflow.add_node("process_departments", RunnableLambda(async_wrapper(lambda state: process_departments_node(state, finance_agent, marketing_agent))))
    workflow.add_node("fallback", RunnableLambda(async_wrapper(lambda state: fallback_node(state, co_ceo_agent))))
    workflow.add_node("generate_response", RunnableLambda(async_wrapper(lambda state: generate_response_node(state, co_ceo_agent))))

    # Add standard edges
    workflow.add_edge("analyze_query", "retrieve_knowledge")
    workflow.add_edge("retrieve_knowledge", "route_to_departments")

    # Define the routing function
    def get_next_node(state: AgentState) -> str:
        """
        Determine the next node based on the state.

        Args:
            state: The current state

        Returns:
            Name of the next node
        """
        # Ensure departments is initialized
        departments = getattr(state, "departments", [])
        if departments is None:
            departments = []

        logger.info(f"Checking departments in state: {departments}")
        logger.info(f"State query_analysis: {state.query_analysis}")

        # Check if there are relevant departments in the query analysis
        if state.query_analysis and "relevant_departments" in state.query_analysis:
            relevant_depts = state.query_analysis["relevant_departments"]
            logger.info(f"Relevant departments from query analysis: {relevant_depts}")

            # If departments is empty but query_analysis has relevant_departments,
            # use those departments (this is a fallback in case the state wasn't properly updated)
            if not departments and relevant_depts:
                logger.info(f"No departments in state, but found relevant departments in query analysis: {relevant_depts}")
                departments = relevant_depts

        # Log the routing decision
        if departments:
            logger.info(f"Routing to process_departments with departments: {departments}")
            return "process_departments"
        else:
            logger.info("No relevant departments, using fallback")
            return "fallback"

    # Add conditional edges based on routing decision
    workflow.add_conditional_edges(
        "route_to_departments",
        get_next_node,
        {
            "process_departments": "process_departments",
            "fallback": "fallback"
        }
    )

    # Add edges to generate_response
    workflow.add_edge("process_departments", "generate_response")
    workflow.add_edge("fallback", "generate_response")

    # Set entry point
    workflow.set_entry_point("analyze_query")

    return workflow


async def process_query(
    query: str,
    co_ceo_agent,
    finance_agent,
    marketing_agent,
    knowledge_base_service,
    user_id: Optional[str] = None,
    thread_id: Optional[str] = None,
    timeout_seconds: Optional[float] = None,
    start_department: Optional[str] = None,
    metrics_collector = None,
    tracing_collector: Optional[TracingCollector] = None,
    tracing_level: TracingLevel = TracingLevel.STANDARD,
    **kwargs
) -> Dict[str, Any]:
    """
    Process a user query through the agent graph.

    Args:
        query: The user query
        co_ceo_agent: The Co-CEO agent
        finance_agent: The Finance agent
        marketing_agent: The Marketing agent
        knowledge_base_service: The knowledge base service
        user_id: Optional user ID for scoping
        thread_id: Optional thread ID for conversation tracking
        timeout_seconds: Optional timeout for the entire graph execution
        start_department: Optional department to start with (co_ceo, finance, marketing)
        metrics_collector: Optional metrics collector for tracking metrics
        tracing_collector: Optional tracing collector for observability
        tracing_level: Level of detail for tracing (default: STANDARD)
        **kwargs: Additional parameters

    Returns:
        Result of the graph execution
    """
    # Create initial state
    state = create_initial_state(query, user_id, thread_id, **kwargs)

    # Create a tracing collector if not provided
    if tracing_collector is None:
        tracing_collector = TracingCollector(
            session_id=kwargs.get("session_id", str(uuid.uuid4())),
            user_id=user_id,
            thread_id=thread_id,
            granularity=tracing_level
        )

    # Add tracing collector to state metadata
    state.metadata["tracing_collector"] = tracing_collector

    # Add initial trace event
    tracing_collector.add_trace(
        node_id="process_query",
        event_type="start",
        metadata={
            "query": query,
            "user_id": user_id,
            "thread_id": thread_id,
            "start_department": start_department
        }
    )

    # Add metrics collector to state metadata if provided
    if metrics_collector:
        state.metadata["metrics_collector"] = metrics_collector

        # Add initial event
        metrics_collector.add_event(
            component="process_query",
            action="start",
            details={
                "query": query,
                "user_id": user_id,
                "thread_id": thread_id,
                "start_department": start_department
            }
        )

    # If a specific department is specified, add it to the state
    if start_department and start_department in ["co_ceo", "finance", "marketing"]:
        logger.info(f"Starting with specific department: {start_department}")
        state.add_department(start_department)

        # Add expected departments to metadata for metrics
        if metrics_collector:
            state.metadata["expected_departments"] = [start_department]

    # Build the graph
    graph = build_co_ceo_graph(co_ceo_agent, finance_agent, marketing_agent, knowledge_base_service)

    try:
        # Compile the graph
        compiled_graph = graph.compile()

        # Execute the graph with timeout if specified
        logger.info("Executing LangGraph with state: %s", state)

        # Manually execute the graph nodes to ensure proper state updates
        try:
            # Define a helper function to properly handle async functions
            def async_wrapper(func):
                async def wrapper(state):
                    return await func(state)
                return wrapper

            # Start with the entry point node
            current_node = "analyze_query"
            current_state = state

            # Execute the analyze_query node
            logger.info("Executing node: %s", current_node)
            analyze_query_func = async_wrapper(lambda state: analyze_query_node(state, co_ceo_agent))
            current_state = await analyze_query_func(current_state)
            logger.info("Node %s completed with state: %s", current_node, current_state)

            # Execute the retrieve_knowledge node
            current_node = "retrieve_knowledge"
            logger.info("Executing node: %s", current_node)
            # Add trace event for node start if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="retrieve_knowledge_node",
                    event_type="node_start",
                    state_before=current_state,
                    metadata={"query": current_state.query}
                )
            retrieve_knowledge_func = async_wrapper(lambda state: retrieve_knowledge_node(state, knowledge_base_service))
            previous_state = current_state
            current_state = await retrieve_knowledge_func(current_state)
            logger.info("Node %s completed with state: %s", current_node, current_state)
            # Add trace event for node end if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="retrieve_knowledge_node",
                    event_type="node_end",
                    state_before=previous_state,
                    state_after=current_state,
                    metadata={
                        "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                        "doc_count": len(current_state.retrieved_knowledge)
                    }
                )

            # Execute the route_to_departments node
            current_node = "route_to_departments"
            logger.info("Executing node: %s", current_node)
            # Add trace event for node start if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="route_to_departments_node",
                    event_type="node_start",
                    state_before=current_state,
                    metadata={"query_analysis": current_state.query_analysis}
                )
            route_to_departments_func = async_wrapper(lambda state: route_to_departments_node(state, co_ceo_agent))
            previous_state = current_state
            current_state = await route_to_departments_func(current_state)
            logger.info("Node %s completed with state: %s", current_node, current_state)
            # Add trace event for node end if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="route_to_departments_node",
                    event_type="node_end",
                    state_before=previous_state,
                    state_after=current_state,
                    metadata={
                        "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                        "departments": list(current_state.departments),
                        "routing_decision": current_state.metadata.get("routing_decision", {})
                    }
                )

            # Determine the next node based on the state
            departments = current_state.departments
            if departments:
                # Execute the process_departments node
                current_node = "process_departments"
                logger.info("Executing node: %s", current_node)
                # Add trace event for node start if tracing collector is available
                if tracing_collector:
                    tracing_collector.add_trace(
                        node_id="process_departments_node",
                        event_type="node_start",
                        state_before=current_state,
                        metadata={"departments": list(current_state.departments)}
                    )
                process_departments_func = async_wrapper(lambda state: process_departments_node(state, finance_agent, marketing_agent))
                previous_state = current_state
                current_state = await process_departments_func(current_state)
                logger.info("Node %s completed with state: %s", current_node, current_state)
                # Add trace event for node end if tracing collector is available
                if tracing_collector:
                    tracing_collector.add_trace(
                        node_id="process_departments_node",
                        event_type="node_end",
                        state_before=previous_state,
                        state_after=current_state,
                        metadata={
                            "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                            "departments_processed": list(current_state.departments),
                            "response_count": len(current_state.department_responses)
                        }
                    )
            else:
                # Execute the fallback node
                current_node = "fallback"
                logger.info("Executing node: %s", current_node)
                # Add trace event for node start if tracing collector is available
                if tracing_collector:
                    tracing_collector.add_trace(
                        node_id="fallback_node",
                        event_type="node_start",
                        state_before=current_state,
                        metadata={"query": current_state.query}
                    )
                fallback_func = async_wrapper(lambda state: fallback_node(state, co_ceo_agent))
                previous_state = current_state
                current_state = await fallback_func(current_state)
                logger.info("Node %s completed with state: %s", current_node, current_state)
                # Add trace event for node end if tracing collector is available
                if tracing_collector:
                    tracing_collector.add_trace(
                        node_id="fallback_node",
                        event_type="node_end",
                        state_before=previous_state,
                        state_after=current_state,
                        metadata={
                            "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds()
                        }
                    )

            # Execute the generate_response node
            current_node = "generate_response"
            logger.info("Executing node: %s", current_node)
            # Add trace event for node start if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="generate_response_node",
                    event_type="node_start",
                    state_before=current_state,
                    metadata={
                        "department_count": len(current_state.department_responses or {}),
                        "departments": list(current_state.department_responses.keys()) if current_state.department_responses else []
                    }
                )
            generate_response_func = async_wrapper(lambda state: generate_response_node(state, co_ceo_agent))
            previous_state = current_state
            current_state = await generate_response_func(current_state)
            logger.info("Node %s completed with state: %s", current_node, current_state)
            # Add trace event for node end if tracing collector is available
            if tracing_collector:
                tracing_collector.add_trace(
                    node_id="generate_response_node",
                    event_type="node_end",
                    state_before=previous_state,
                    state_after=current_state,
                    metadata={
                        "elapsed_time": (datetime.now(timezone.utc) - start_time).total_seconds(),
                        "response_length": len(current_state.response or ""),
                        "departments": list(current_state.department_responses.keys()) if current_state.department_responses else [],
                        "final_response": True
                    }
                )

            # Set the result to the final state
            result = current_state
            logger.info("Manual graph execution completed with result: %s", result)
        except Exception as e:
            logger.error("Error in manual graph execution: %s", str(e), exc_info=True)
            # Fall back to using the LangGraph execution
            logger.info("Falling back to LangGraph execution")

            if timeout_seconds:
                if hasattr(asyncio, 'timeout'):
                    async with asyncio.timeout(timeout_seconds):
                        logger.info("Executing LangGraph with timeout: %s", timeout_seconds)
                        result = await compiled_graph.ainvoke(state)
                else:
                    # Use asyncio.wait_for for timeout
                    logger.info("Executing LangGraph with asyncio.wait_for timeout: %s", timeout_seconds)
                    result = await asyncio.wait_for(
                        compiled_graph.ainvoke(state),
                        timeout=timeout_seconds
                    )
            else:
                # Use asynchronous invoke
                logger.info("Executing LangGraph without timeout")
                result = await compiled_graph.ainvoke(state)

            logger.info("LangGraph execution completed with result: %s", result)

        # Finalize metrics if available
        # Extract response from result
        response = ""
        if hasattr(result, "response"):
            response = result.response
        elif isinstance(result, dict) and "response" in result:
            response = result["response"]

        # Extract departments from result
        departments = []
        if hasattr(result, "departments"):
            departments = result.departments
        elif isinstance(result, dict) and "departments" in result:
            departments = result["departments"]

        # Extract query analysis from result
        query_analysis = {}
        if hasattr(result, "query_analysis"):
            query_analysis = result.query_analysis
        elif isinstance(result, dict) and "query_analysis" in result:
            query_analysis = result["query_analysis"]

        # Extract metadata from result
        metadata = {}
        if hasattr(result, "metadata"):
            metadata = result.metadata
        elif isinstance(result, dict) and "metadata" in result:
            metadata = result["metadata"]

        if metrics_collector:
            metrics_collector.finalize()

            # Add final event
            metrics_collector.add_event(
                component="process_query",
                action="complete",
                details={
                    "response_length": len(response or ""),
                    "departments": departments,
                    "total_time": metrics_collector.metrics["overall"].get("total_time", 0)
                }
            )

        # Add final trace event if tracing collector is available
        if tracing_collector:
            tracing_collector.add_trace(
                node_id="process_query",
                event_type="complete",
                metadata={
                    "query": query,
                    "response": response[:100] + "..." if len(response) > 100 else response,
                    "departments": departments,
                    "elapsed_time": metrics_collector.metrics["overall"].get("total_time", 0) if metrics_collector else 0
                }
            )

        # Return the result
        return {
            "query": query,
            "response": response,
            "departments": departments,
            "analysis": query_analysis,
            "metadata": metadata
        }
    except asyncio.TimeoutError:
        logger.error(
            f"Graph execution timed out after {timeout_seconds}s",
            extra={"thread_id": thread_id, "user_id": user_id}
        )

        # Track metrics if available
        if metrics_collector:
            metrics_collector.add_event(
                component="process_query",
                action="timeout",
                details={
                    "timeout_seconds": timeout_seconds
                }
            )
            metrics_collector.finalize()

        # Create a timeout response
        timeout_response = {
            "query": query,
            "response": f"I'm sorry, but I couldn't complete your request within the allotted time ({timeout_seconds} seconds). Please try again with a simpler query or contact support if this issue persists.",
            "metadata": {
                "error": "timeout",
                "timeout_seconds": timeout_seconds,
                "thread_id": thread_id,
                "user_id": user_id
            }
        }

        return timeout_response
    except Exception as e:
        logger.error(
            f"Error in graph execution: {str(e)}",
            extra={"thread_id": thread_id, "user_id": user_id},
            exc_info=True
        )

        # Track metrics if available
        if metrics_collector:
            metrics_collector.add_event(
                component="process_query",
                action="error",
                details={
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            metrics_collector.finalize()

        # Create an error response
        error_response = {
            "query": query,
            "response": f"I'm sorry, but an error occurred while processing your request: {str(e)}",
            "metadata": {
                "error": "exception",
                "message": str(e),
                "thread_id": thread_id,
                "user_id": user_id
            }
        }

        return error_response
