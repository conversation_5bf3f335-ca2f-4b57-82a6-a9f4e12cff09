"""
Timeout Handling for LangGraph Nodes (Compatibility Module)

This module provides backward compatibility with the old LangGraph timeout utilities.
It re-exports the utilities from the new modular structure.

IMPORTANT: This module is deprecated and will be removed in a future version.
Please use the new modular structure in app.core.timeout.graph instead.
"""

import warnings
import logging

# Show deprecation warning
warnings.warn(
    "The app.langgraph.timeout module is deprecated and will be removed in a future version. "
    "Please use the new modular structure in app.core.timeout.graph instead.",
    DeprecationWarning,
    stacklevel=2
)

# Set up logging
logger = logging.getLogger(__name__)

# Re-export all symbols from the new modular structure
from app.core.timeout.graph import (
    NODE_TIMEOUTS,
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout,
    with_co_ceo_timeout,
    with_finance_department_timeout,
    with_marketing_department_timeout,
    with_fallback_timeout
)

__all__ = [
    "NODE_TIMEOUTS",
    "with_node_timeout_and_retry",
    "with_node_timeout",
    "with_analyze_query_timeout",
    "with_retrieve_knowledge_timeout",
    "with_route_to_departments_timeout",
    "with_generate_response_timeout",
    "with_co_ceo_timeout",
    "with_finance_department_timeout",
    "with_marketing_department_timeout",
    "with_fallback_timeout"
]
