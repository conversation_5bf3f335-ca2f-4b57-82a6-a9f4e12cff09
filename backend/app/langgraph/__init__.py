"""
LangGraph orchestration module.

This module contains the components for LangGraph-based agent orchestration,
including state management, checkpointing, transitions, and timeout handling.
"""

from app.langgraph.state import (
    AgentState,
    RagContext,
    append_unique,
    create_initial_state,
    STATE_MODEL_VERSION
)
from app.langgraph.transitions import (
    validate_state,
    transition_add_rag_documents,
    transition_add_message,
    transition_add_department,
    transition_update_analysis,
    transition_set_response,
    transition_update_metadata,
    create_transition
)
from app.langgraph.checkpointing import (
    CheckpointerInterface,
    SQLiteCheckpointer,
    get_checkpointer
)
from app.langgraph.timeout import (
    with_node_timeout_and_retry,
    with_node_timeout,
    with_analyze_query_timeout,
    with_retrieve_knowledge_timeout,
    with_route_to_departments_timeout,
    with_generate_response_timeout
)

__all__ = [
    # State
    "AgentState",
    "RagContext",
    "append_unique",
    "create_initial_state",
    "STATE_MODEL_VERSION",

    # Transitions
    "validate_state",
    "transition_add_rag_documents",
    "transition_add_message",
    "transition_add_department",
    "transition_update_analysis",
    "transition_set_response",
    "transition_update_metadata",
    "create_transition",

    # Checkpointing
    "CheckpointerInterface",
    "SQLiteCheckpointer",
    "get_checkpointer",

    # Timeout Handling
    "with_node_timeout_and_retry",
    "with_node_timeout",
    "with_analyze_query_timeout",
    "with_retrieve_knowledge_timeout",
    "with_route_to_departments_timeout",
    "with_generate_response_timeout"
]
