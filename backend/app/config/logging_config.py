"""
Logging configuration for the application.

This module provides a centralized configuration for logging in the application.
It includes configuration for different log levels, formatters, and handlers.
"""
import logging
import sys
import warnings
from typing import Dict, Any, Optional

# Default log format
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Default log level
DEFAULT_LOG_LEVEL = logging.INFO

# Configure logging for the application
def configure_logging(
    log_level: Optional[int] = None,
    log_format: Optional[str] = None,
    capture_warnings: bool = True
) -> None:
    """
    Configure logging for the application.
    
    Args:
        log_level: The log level to use (default: INFO)
        log_format: The log format to use
        capture_warnings: Whether to capture warnings as logs
    """
    # Set default values if not provided
    log_level = log_level or DEFAULT_LOG_LEVEL
    log_format = log_format or DEFAULT_LOG_FORMAT
    
    # Configure the root logger
    logging.basicConfig(
        level=log_level,
        format=log_format,
        stream=sys.stdout
    )
    
    # Configure warning capture
    if capture_warnings:
        # Capture warnings as logs
        logging.captureWarnings(True)
        
        # Set the warning format
        warnings.formatwarning = _format_warning
        
        # Set the warning filter to always show deprecation warnings
        warnings.filterwarnings("always", category=DeprecationWarning)
    
    # Configure specific loggers
    _configure_specific_loggers()


def _format_warning(
    message: str,
    category: type,
    filename: str,
    lineno: int,
    line: Optional[str] = None
) -> str:
    """
    Format a warning message.
    
    Args:
        message: The warning message
        category: The warning category
        filename: The filename where the warning occurred
        lineno: The line number where the warning occurred
        line: The line of code that caused the warning
        
    Returns:
        The formatted warning message
    """
    return f"{filename}:{lineno}: {category.__name__}: {message}\n"


def _configure_specific_loggers() -> None:
    """Configure specific loggers with custom settings."""
    # Set higher log level for noisy libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    
    # Set custom log level for application modules
    logging.getLogger("app").setLevel(logging.INFO)
    
    # Set custom log level for deprecation warnings
    logging.getLogger("py.warnings").setLevel(logging.INFO)


# Dictionary-based logging configuration for more advanced setups
def get_logging_config() -> Dict[str, Any]:
    """
    Get a dictionary-based logging configuration.
    
    This can be used with logging.config.dictConfig for more advanced setups.
    
    Returns:
        A dictionary with logging configuration
    """
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": DEFAULT_LOG_FORMAT
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "standard",
                "stream": "ext://sys.stdout"
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filename": "app.log",
                "maxBytes": 10485760,  # 10 MB
                "backupCount": 5
            }
        },
        "loggers": {
            "": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": True
            },
            "app": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False
            },
            "py.warnings": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            }
        }
    }
