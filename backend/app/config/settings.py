"""
Application Settings

This module contains the settings for the application.
"""

import os
from functools import lru_cache
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings.

    This class uses Pydantic's BaseSettings to load configuration from environment variables.
    """
    # Application settings
    APP_NAME: str = "BusinessLM"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = Field(default=False)
    DEVELOPMENT_MODE: bool = Field(default=False)
    LOG_LEVEL: str = Field(default="INFO")

    # API settings
    API_PREFIX: str = "/api"
    FRONTEND_URL: str = Field(default="http://localhost:3000")

    # Database settings
    DATABASE_URL: str = Field(
        default="postgresql://postgres:postgres@localhost:5432/businesslm"
    )
    DATABASE_POOL_SIZE: int = Field(default=5)
    DATABASE_MAX_OVERFLOW: int = Field(default=10)
    DATABASE_POOL_TIMEOUT: int = Field(default=30)
    DATABASE_POOL_RECYCLE: int = Field(default=1800)

    # JWT Authentication settings
    JWT_SECRET_KEY: str = Field(default="your-secret-key-change-in-production")
    JWT_ALGORITHM: str = Field(default="HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30)
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7)
    USE_REFRESH_TOKEN_COOKIE: bool = Field(default=True)

    # Password hashing settings
    PASSWORD_BCRYPT_ROUNDS: int = Field(default=12)

    # Firebase settings (legacy, will be removed)
    FIREBASE_PROJECT_ID: Optional[str] = None
    FIREBASE_PRIVATE_KEY: Optional[str] = None
    FIREBASE_CLIENT_EMAIL: Optional[str] = None
    FIREBASE_CREDENTIALS_PATH: Optional[str] = None

    # LLM API keys
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None

    # RAG settings
    EMBEDDING_MODEL: str = Field(default="intfloat/e5-base-v2")
    VECTOR_STORE_TYPE: str = Field(default="pgvector")  # "memory", "faiss", "chroma", or "pgvector"
    VECTOR_STORE_PATH: str = Field(default="./data/vector_store")
    VECTOR_DIMENSION: int = Field(default=768)  # 768 for e5-base-v2
    DEFAULT_EMBEDDING_MODEL: str = Field(default="huggingface")  # "huggingface" or "openai"

    # LangGraph settings
    CHECKPOINTER_TYPE: str = Field(default="postgresql")  # "sqlite" or "postgresql"

    # Timeout settings (in seconds)
    TIMEOUT_EMBEDDING: int = Field(default=30)
    TIMEOUT_VECTOR_SEARCH: int = Field(default=10)
    TIMEOUT_RERANKING: int = Field(default=10)
    TIMEOUT_LLM_DEFAULT: int = Field(default=60)

    # Cache settings
    CACHE_TTL: int = Field(default=3600)  # 1 hour

    # Additional environment variables
    ADDITIONAL_ALLOWED_ORIGINS: Optional[str] = None

    @field_validator("JWT_SECRET_KEY")
    @classmethod
    def validate_jwt_secret(cls, v):
        if v == "your-secret-key-change-in-production" and not os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
            import logging
            logging.warning(
                "WARNING: Using default JWT_SECRET_KEY in production. "
                "This is insecure and should be changed."
            )
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields in environment variables


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings.

    This function is cached to avoid loading the settings multiple times.

    Returns:
        Settings: Application settings
    """
    return Settings()
