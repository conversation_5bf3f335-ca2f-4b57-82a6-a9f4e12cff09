#!/usr/bin/env python
"""
Clean PostgreSQL Database Script

This script cleans the PostgreSQL database by removing all documents and document chunks.
It's useful for resetting the database before re-migrating documents.
"""

import os
import sys
import logging
import asyncio
import argparse
from sqlalchemy import text

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.db.database import get_db_context, init_db
from rich.console import Console

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create console for rich output
console = Console()


async def clean_database(confirm: bool = False):
    """
    Clean the database by removing all documents and document chunks.

    Args:
        confirm: Whether to confirm before cleaning
    """
    if not confirm:
        console.print("[bold red]This will delete ALL documents and document chunks from the database.[/bold red]")
        console.print("[bold yellow]Run with --confirm to proceed.[/bold yellow]")
        return

    try:
        # Initialize database
        init_db()

        with get_db_context() as db:
            # Delete all document chunks first (due to foreign key constraints)
            chunk_count = db.execute(text("SELECT COUNT(*) FROM document_chunks")).scalar()
            db.execute(text("DELETE FROM document_chunks"))
            db.commit()

            # Delete all documents
            doc_count = db.execute(text("SELECT COUNT(*) FROM documents")).scalar()
            db.execute(text("DELETE FROM documents"))
            db.commit()

            # Delete all embeddings
            try:
                embed_count = db.execute(text("SELECT COUNT(*) FROM embeddings")).scalar()
                db.execute(text("DELETE FROM embeddings"))
                db.commit()
                console.print(f"[bold green]Successfully deleted {embed_count} embeddings.[/bold green]")
            except Exception as e:
                console.print(f"[bold yellow]No embeddings table found or error: {e}[/bold yellow]")

            console.print(f"[bold green]Successfully deleted {chunk_count} document chunks and {doc_count} documents.[/bold green]")

    except Exception as e:
        logger.error(f"Error cleaning database: {e}")
        console.print(f"[bold red]Error cleaning database: {e}[/bold red]")
        raise


async def main():
    """Main function to clean the database."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Clean PostgreSQL database")
    parser.add_argument("--confirm", action="store_true", help="Confirm database cleaning")
    args = parser.parse_args()

    # Clean database
    await clean_database(confirm=args.confirm)


if __name__ == "__main__":
    asyncio.run(main())
