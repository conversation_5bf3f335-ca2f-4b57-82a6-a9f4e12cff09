#!/usr/bin/env python3
"""
Verify pgvector Installation and Configuration

This script verifies that the pgvector extension is properly installed and configured
in the PostgreSQL database. It checks:
1. The pgvector extension is installed
2. The vector type is registered with SQLAlchemy
3. The vector indexes are created
4. Vector operations work correctly
5. Full vector insert and retrieval cycle with a test table

This script can be run in two modes:
- Application mode: Verifies pgvector in the context of the application database
- Standalone mode: Creates a temporary test table to verify basic pgvector functionality

Usage:
    python -m backend.scripts.verify_pgvector [--standalone]
"""

import sys
import os
import logging
import argparse
import numpy as np
from sqlalchemy import text, Column, Integer, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Add the backend directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the necessary modules
from app.core.db.database import get_db_context, init_db
from app.core.db.pgvector import Vector, setup_pgvector
from app.config import get_settings

# Create a base class for the test model
TestBase = declarative_base()

# Define a test model with a vector column
class TestVector(TestBase):
    __tablename__ = "test_pgvector"

    id = Column(Integer, primary_key=True)
    embedding = Column(Vector(3))  # 3-dimensional vector for simplicity


def verify_pgvector_extension():
    """Verify that the pgvector extension is installed."""
    logger.info("Verifying pgvector extension...")

    with get_db_context() as db:
        # Check if the pgvector extension is installed
        result = db.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'")).fetchone()
        if result:
            logger.info("✅ pgvector extension is installed")
        else:
            logger.error("❌ pgvector extension is NOT installed")
            return False

        # Check if the vector type is registered
        try:
            result = db.execute(text("SELECT '[1,2,3]'::vector")).fetchone()
            logger.info(f"✅ Vector type is registered: {result[0]}")
        except Exception as e:
            logger.error(f"❌ Vector type is NOT registered: {e}")
            return False

        return True


def verify_vector_operations():
    """Verify that vector operations work correctly."""
    logger.info("Verifying vector operations...")

    with get_db_context() as db:
        try:
            # Test vector creation
            result = db.execute(text("SELECT '[1,2,3]'::vector")).fetchone()
            logger.info(f"✅ Vector creation works: {result[0]}")

            # Test vector distance calculation (L2)
            result = db.execute(text("SELECT '[1,2,3]'::vector <-> '[4,5,6]'::vector AS distance")).fetchone()
            logger.info(f"✅ Vector L2 distance works: {result.distance}")

            # Test vector distance calculation (cosine)
            result = db.execute(text("SELECT '[1,2,3]'::vector <=> '[4,5,6]'::vector AS distance")).fetchone()
            logger.info(f"✅ Vector cosine distance works: {result.distance}")

            # Test vector distance calculation (inner product)
            result = db.execute(text("SELECT '[1,2,3]'::vector <#> '[4,5,6]'::vector AS distance")).fetchone()
            logger.info(f"✅ Vector inner product distance works: {result.distance}")

            return True
        except Exception as e:
            logger.error(f"❌ Vector operations failed: {e}")
            return False


def verify_vector_indexes():
    """Verify that vector indexes are created."""
    logger.info("Verifying vector indexes...")

    with get_db_context() as db:
        # Check if the vector indexes are created
        result = db.execute(text(
            "SELECT indexname FROM pg_indexes WHERE tablename = 'document_chunks'"
        )).fetchall()

        index_names = [r[0] for r in result]
        logger.info(f"Found indexes: {index_names}")

        # Check for the embedding index
        if 'idx_document_chunks_embedding' in index_names:
            logger.info("✅ Vector embedding index is created")
        else:
            logger.warning("⚠️ Vector embedding index is NOT created")

        # Check for the content search index
        if 'idx_document_chunks_content_search' in index_names:
            logger.info("✅ Content search index is created")
        else:
            logger.warning("⚠️ Content search index is NOT created")

        # Check for the document_id/chunk_index composite index
        if 'idx_document_chunks_document_id_chunk_index' in index_names:
            logger.info("✅ Document ID/Chunk index composite index is created")
        else:
            logger.warning("⚠️ Document ID/Chunk index composite index is NOT created")

        return len(index_names) > 0


def verify_standalone_setup():
    """Verify pgvector setup using a temporary test table."""
    logger.info("Verifying pgvector setup in standalone mode...")

    try:
        # Get database URL
        db_url = get_settings().DATABASE_URL
        logger.info(f"Using database URL: {db_url}")

        # Create engine
        engine = create_engine(db_url)

        # Set up pgvector with the engine
        setup_pgvector(engine)

        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()

        # Verify pgvector extension is installed
        with engine.connect() as conn:
            result = conn.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
            extensions = result.fetchall()

            if not extensions:
                logger.error("❌ pgvector extension is NOT installed in PostgreSQL")
                return False

            logger.info("✅ pgvector extension is installed in PostgreSQL")

        try:
            # Create the test table
            TestBase.metadata.create_all(engine)
            logger.info("✅ Created test table with vector column")

            # Create a test vector
            test_vector = TestVector(embedding=[1.0, 2.0, 3.0])

            # Insert the test vector
            session.add(test_vector)
            session.commit()
            logger.info("✅ Inserted test vector")

            # Query the test vector
            result = session.query(TestVector).first()
            if result is None:
                logger.error("❌ Failed to retrieve test vector")
                return False

            # Verify the vector values
            expected = [1.0, 2.0, 3.0]
            actual = result.embedding

            if not np.allclose(actual, expected):
                logger.error(f"❌ Vector values do not match: expected {expected}, got {actual}")
                return False

            logger.info("✅ Retrieved test vector with correct values")

            # Perform a vector similarity search
            query_vector = [1.1, 2.1, 3.1]  # Slightly different from the test vector
            vector_str = f"[{','.join(str(x) for x in query_vector)}]"

            # Use cosine distance for similarity search
            result = session.execute(
                text(f"SELECT id, embedding <=> '{vector_str}'::vector AS distance FROM {TestVector.__tablename__} ORDER BY distance LIMIT 1")
            ).fetchone()

            if result is None:
                logger.error("❌ Failed to perform vector similarity search")
                return False

            logger.info(f"✅ Vector similarity search successful: distance = {result.distance}")

            return True
        except Exception as e:
            logger.error(f"❌ Error during vector operations: {e}")
            return False
        finally:
            # Clean up the test table
            try:
                TestBase.metadata.drop_all(engine)
                logger.info("✅ Cleaned up test table")
            except Exception as e:
                logger.error(f"❌ Error cleaning up test table: {e}")
    except Exception as e:
        logger.error(f"❌ Error verifying pgvector setup: {e}")
        return False


def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Verify pgvector installation and configuration")
    parser.add_argument("--standalone", action="store_true", help="Run in standalone mode with a test table")
    args = parser.parse_args()

    if args.standalone:
        # Run in standalone mode
        logger.info("Running in standalone mode...")
        standalone_ok = verify_standalone_setup()

        # Print summary
        logger.info("\n=== Verification Summary (Standalone Mode) ===")
        logger.info(f"pgvector standalone setup: {'✅ OK' if standalone_ok else '❌ FAILED'}")

        if standalone_ok:
            logger.info("\n✅ pgvector is properly installed and configured in standalone mode!")
            return 0
        else:
            logger.error("\n❌ pgvector verification failed in standalone mode!")
            return 1
    else:
        # Run in application mode
        logger.info("Running in application mode...")

        # Initialize the database
        init_db()

        # Verify pgvector extension
        extension_ok = verify_pgvector_extension()

        # Verify vector operations
        operations_ok = verify_vector_operations()

        # Verify vector indexes
        indexes_ok = verify_vector_indexes()

        # Print summary
        logger.info("\n=== Verification Summary (Application Mode) ===")
        logger.info(f"pgvector extension: {'✅ OK' if extension_ok else '❌ FAILED'}")
        logger.info(f"Vector operations: {'✅ OK' if operations_ok else '❌ FAILED'}")
        logger.info(f"Vector indexes: {'✅ OK' if indexes_ok else '⚠️ WARNING'}")

        if extension_ok and operations_ok:
            logger.info("\n✅ pgvector is properly installed and configured in application mode!")
            return 0
        else:
            logger.error("\n❌ pgvector verification failed in application mode!")
            return 1


if __name__ == "__main__":
    sys.exit(main())
