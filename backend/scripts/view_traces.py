#!/usr/bin/env python3
"""
Trace Manager

This script provides a command-line interface for managing trace files
generated by the tracing system. It allows you to:

1. View trace files with different visualization formats
2. List available trace files
3. Delete trace files
4. Compare multiple trace files
5. Export trace visualizations

This tool is essential for analyzing the behavior of the multi-agent orchestration
system and understanding how agents communicate with each other.
"""

import os
import sys
import json
import shutil
import typer
from typing import Optional, List
from datetime import datetime
from rich.prompt import Confirm

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import tracing utilities
from app.core.tracing import load_traces, filter_traces, save_traces
from app.cli.tracing import (
    visualize_trace_text,
    visualize_trace_tree,
    visualize_agent_communication,
    visualize_trace_summary
)
from app.cli.formatting import (
    console,
    print_header,
    print_section_divider,
    print_success,
    print_error,
    print_warning,
    print_info,
    create_table,
    print_json
)

# Initialize Typer app
app = typer.Typer(
    name="trace-manager",
    help="CLI for managing and analyzing trace files from multi-agent orchestration",
    add_completion=False,
)


@app.command()
def view(
    file_path: str = typer.Argument(..., help="Path to the trace file"),
    format: str = typer.Option("text", help="Visualization format (text, tree, communication, summary)"),
    show_state: bool = typer.Option(False, help="Show state information"),
    show_metadata: bool = typer.Option(True, help="Show metadata"),
    filter_node: Optional[str] = typer.Option(None, help="Filter by node ID"),
    filter_event: Optional[str] = typer.Option(None, help="Filter by event type"),
):
    """
    View a trace file with various visualization options.
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print_error(f"File not found: {file_path}")
            return

        # Load traces from file
        traces = load_traces(file_path)
        print_success(f"Loaded {len(traces)} trace events from {file_path}")

        # Apply filters if specified
        if filter_node or filter_event:
            filtered_traces = filter_traces(
                traces,
                node_id=filter_node,
                event_type=filter_event
            )
            print_info(f"Filtered to {len(filtered_traces)} trace events")
            traces = filtered_traces

        # Visualize based on format
        if format == "text":
            print_header("Trace Events (Text Format)")
            visualize_trace_text(
                traces,
                show_state=show_state,
                show_metadata=show_metadata,
                filter_node_id=filter_node,
                filter_event_type=filter_event
            )
        elif format == "tree":
            print_header("Trace Events (Tree Format)")
            visualize_trace_tree(
                traces,
                show_state=show_state,
                show_metadata=show_metadata
            )
        elif format == "communication":
            print_header("Agent Communications")
            visualize_agent_communication(traces)
        elif format == "summary":
            print_header("Trace Summary")
            visualize_trace_summary(traces)
        else:
            print_error(f"Unknown format: {format}")

    except Exception as e:
        print_error(f"Error viewing trace file: {str(e)}")
        import traceback
        traceback.print_exc()


@app.command()
def list_files(
    directory: str = typer.Option("traces", help="Directory containing trace files"),
    limit: int = typer.Option(10, help="Maximum number of files to show"),
):
    """
    List available trace files.
    """
    try:
        # Check if directory exists
        if not os.path.exists(directory):
            print_error(f"Directory not found: {directory}")
            return

        # Get trace files
        files = [
            os.path.join(directory, f)
            for f in os.listdir(directory)
            if f.startswith("trace-") and (f.endswith(".json") or f.endswith(".ndjson"))
        ]

        # Sort by modification time (newest first)
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        # Limit the number of files
        files = files[:limit]

        if not files:
            print_warning("No trace files found")
            return

        print_header(f"Trace Files in {directory}")
        for i, file_path in enumerate(files, 1):
            # Get file info
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / 1024  # KB
            file_time = os.path.getmtime(file_path)

            # Get trace count
            try:
                with open(file_path, "r") as f:
                    if file_path.endswith(".json"):
                        traces = json.load(f)
                        trace_count = len(traces)
                    else:
                        trace_count = sum(1 for _ in f)
            except:
                trace_count = "Unknown"

            # Format file time
            from datetime import datetime
            file_time_str = datetime.fromtimestamp(file_time).strftime("%Y-%m-%d %H:%M:%S")

            print_info(f"{i}. {file_name}")
            console.print(f"   [dim]Path:[/dim] {file_path}")
            console.print(f"   [dim]Size:[/dim] {file_size:.2f} KB")
            console.print(f"   [dim]Modified:[/dim] {file_time_str}")
            console.print(f"   [dim]Traces:[/dim] {trace_count}")
            console.print("")

        print_info("To view a trace file, use:")
        print_info("python backend/scripts/view_traces.py view <file_path>")

    except Exception as e:
        print_error(f"Error listing trace files: {str(e)}")
        import traceback
        traceback.print_exc()


@app.command()
def delete(
    file_path: str = typer.Argument(..., help="Path to the trace file to delete"),
    force: bool = typer.Option(False, "--force", "-f", help="Delete without confirmation"),
):
    """
    Delete a trace file.
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print_error(f"File not found: {file_path}")
            return

        # Confirm deletion
        if not force and not Confirm.ask(f"Are you sure you want to delete {file_path}?"):
            print_info("Deletion cancelled")
            return

        # Delete the file
        os.remove(file_path)
        print_success(f"Deleted {file_path}")

    except Exception as e:
        print_error(f"Error deleting trace file: {str(e)}")
        import traceback
        traceback.print_exc()


@app.command()
def cleanup(
    directory: str = typer.Option("traces", help="Directory containing trace files"),
    days: int = typer.Option(7, help="Delete files older than this many days"),
    force: bool = typer.Option(False, "--force", "-f", help="Delete without confirmation"),
):
    """
    Clean up old trace files.
    """
    try:
        # Check if directory exists
        if not os.path.exists(directory):
            print_error(f"Directory not found: {directory}")
            return

        # Get trace files
        files = [
            os.path.join(directory, f)
            for f in os.listdir(directory)
            if f.startswith("trace-") and (f.endswith(".json") or f.endswith(".ndjson"))
        ]

        # Filter by age
        now = datetime.now().timestamp()
        old_files = [
            f for f in files
            if (now - os.path.getmtime(f)) > (days * 24 * 60 * 60)
        ]

        if not old_files:
            print_info(f"No trace files older than {days} days found")
            return

        # Confirm deletion
        if not force:
            print_info(f"Found {len(old_files)} trace files older than {days} days:")
            for f in old_files:
                file_time = datetime.fromtimestamp(os.path.getmtime(f)).strftime("%Y-%m-%d %H:%M:%S")
                print_info(f"  {os.path.basename(f)} (modified: {file_time})")

            if not Confirm.ask(f"Are you sure you want to delete these {len(old_files)} files?"):
                print_info("Cleanup cancelled")
                return

        # Delete the files
        for f in old_files:
            os.remove(f)

        print_success(f"Deleted {len(old_files)} trace files older than {days} days")

    except Exception as e:
        print_error(f"Error cleaning up trace files: {str(e)}")
        import traceback
        traceback.print_exc()


@app.command()
def compare(
    file_path1: str = typer.Argument(..., help="Path to the first trace file"),
    file_path2: str = typer.Argument(..., help="Path to the second trace file"),
    format: str = typer.Option("summary", help="Comparison format (summary, communication, detailed)"),
):
    """
    Compare two trace files to identify differences.
    """
    try:
        # Check if files exist
        if not os.path.exists(file_path1):
            print_error(f"File not found: {file_path1}")
            return
        if not os.path.exists(file_path2):
            print_error(f"File not found: {file_path2}")
            return

        # Load traces from files
        traces1 = load_traces(file_path1)
        traces2 = load_traces(file_path2)

        print_success(f"Loaded {len(traces1)} trace events from {file_path1}")
        print_success(f"Loaded {len(traces2)} trace events from {file_path2}")

        # Create comparison table
        table = create_table("Trace Comparison", [
            {"name": "Metric", "style": "cyan", "width": 30},
            {"name": file_path1, "style": "green", "width": 30},
            {"name": file_path2, "style": "yellow", "width": 30},
            {"name": "Difference", "style": "magenta", "width": 20}
        ])

        # Compare basic metrics
        table.add_row("Total Events", str(len(traces1)), str(len(traces2)),
                     f"{len(traces2) - len(traces1):+d}")

        # Compare node counts
        node_counts1 = {}
        node_counts2 = {}

        for trace in traces1:
            node_id = trace.get("node_id", "unknown")
            if node_id not in node_counts1:
                node_counts1[node_id] = 0
            node_counts1[node_id] += 1

        for trace in traces2:
            node_id = trace.get("node_id", "unknown")
            if node_id not in node_counts2:
                node_counts2[node_id] = 0
            node_counts2[node_id] += 1

        table.add_row("Unique Nodes", str(len(node_counts1)), str(len(node_counts2)),
                     f"{len(node_counts2) - len(node_counts1):+d}")

        # Compare event types
        event_counts1 = {}
        event_counts2 = {}

        for trace in traces1:
            event_type = trace.get("event_type", "unknown")
            if event_type not in event_counts1:
                event_counts1[event_type] = 0
            event_counts1[event_type] += 1

        for trace in traces2:
            event_type = trace.get("event_type", "unknown")
            if event_type not in event_counts2:
                event_counts2[event_type] = 0
            event_counts2[event_type] += 1

        table.add_row("Event Types", str(len(event_counts1)), str(len(event_counts2)),
                     f"{len(event_counts2) - len(event_counts1):+d}")

        # Compare execution time if available
        start_time1 = min([datetime.fromisoformat(t.get("timestamp", "2099-01-01T00:00:00"))
                          for t in traces1 if "timestamp" in t], default=None)
        end_time1 = max([datetime.fromisoformat(t.get("timestamp", "1970-01-01T00:00:00"))
                        for t in traces1 if "timestamp" in t], default=None)

        start_time2 = min([datetime.fromisoformat(t.get("timestamp", "2099-01-01T00:00:00"))
                          for t in traces2 if "timestamp" in t], default=None)
        end_time2 = max([datetime.fromisoformat(t.get("timestamp", "1970-01-01T00:00:00"))
                        for t in traces2 if "timestamp" in t], default=None)

        if start_time1 and end_time1 and start_time2 and end_time2:
            duration1 = (end_time1 - start_time1).total_seconds()
            duration2 = (end_time2 - start_time2).total_seconds()
            table.add_row("Duration (sec)", f"{duration1:.2f}", f"{duration2:.2f}",
                         f"{duration2 - duration1:+.2f}")

        # Print the comparison table
        console.print(table)

        # Show detailed comparison based on format
        if format == "communication":
            print_header("Agent Communications Comparison")
            print_section_divider(f"File 1: {os.path.basename(file_path1)}", "primary")
            visualize_agent_communication(traces1)

            print_section_divider(f"File 2: {os.path.basename(file_path2)}", "primary")
            visualize_agent_communication(traces2)

        elif format == "detailed":
            # Show detailed differences
            print_header("Detailed Differences")

            # Compare node execution
            print_section_divider("Node Execution Differences", "secondary")
            for node in set(node_counts1.keys()) | set(node_counts2.keys()):
                count1 = node_counts1.get(node, 0)
                count2 = node_counts2.get(node, 0)
                if count1 != count2:
                    diff = count2 - count1
                    print_info(f"Node '{node}': {count1} → {count2} ({diff:+d})")

            # Compare event types
            print_section_divider("Event Type Differences", "secondary")
            for event in set(event_counts1.keys()) | set(event_counts2.keys()):
                count1 = event_counts1.get(event, 0)
                count2 = event_counts2.get(event, 0)
                if count1 != count2:
                    diff = count2 - count1
                    print_info(f"Event '{event}': {count1} → {count2} ({diff:+d})")

    except Exception as e:
        print_error(f"Error comparing trace files: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Create traces directory if it doesn't exist
    os.makedirs("traces", exist_ok=True)

    app()
