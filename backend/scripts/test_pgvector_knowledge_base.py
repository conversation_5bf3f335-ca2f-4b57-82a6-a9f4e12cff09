#!/usr/bin/env python3
"""
Test script for the PgVectorKnowledgeBaseService.

This script tests the PgVectorKnowledgeBaseService by adding some test documents
and performing vector, keyword, and hybrid searches.
"""

import asyncio
import logging
import sys
import os
from typing import List, Dict, Any

# Add the parent directory to the path so we can import the app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Import the necessary modules
from app.core.db.database import init_db
from app.rag.knowledge_base_factory import get_knowledge_base_service


async def test_pgvector_knowledge_base():
    """Test the PgVectorKnowledgeBaseService."""
    logger.info("Testing PgVectorKnowledgeBaseService...")

    # Initialize the database
    init_db()

    # Get the knowledge base service
    knowledge_base = await get_knowledge_base_service()

    # Create some test documents
    test_texts = [
        "PostgreSQL is a powerful, open source object-relational database system with over 35 years of active development.",
        "pgvector is a PostgreSQL extension for vector similarity search. It provides vector data types and vector similarity search operators.",
        "Python is a programming language that lets you work quickly and integrate systems more effectively.",
        "FastAPI is a modern, fast (high-performance), web framework for building APIs with Python based on standard Python type hints.",
        "SQLAlchemy is the Python SQL toolkit and Object Relational Mapper that gives application developers the full power and flexibility of SQL."
    ]

    # Add the test documents to the knowledge base
    logger.info("Adding test documents to the knowledge base...")

    # First, get the vector store from the knowledge base
    vector_store = knowledge_base.vector_store

    # Embed the test texts
    embeddings = []
    for i, text in enumerate(test_texts):
        logger.info(f"Embedding text {i+1}/{len(test_texts)}: {text[:50]}...")
        try:
            embedding = await knowledge_base.embedding_model.embed_query(text)
            embeddings.append(embedding)
            logger.info(f"Successfully embedded text {i+1} (dimension: {len(embedding)})")
        except Exception as e:
            logger.error(f"Error embedding text {i+1}: {e}")
            raise

    # Add the embeddings to the vector store
    logger.info("Adding embeddings to the vector store...")
    metadatas = [{"source": f"test_document_{i}"} for i in range(len(test_texts))]
    try:
        doc_ids = await vector_store.add_embeddings(
            embeddings=embeddings,
            texts=test_texts,
            metadatas=metadatas
        )
        logger.info(f"Successfully added embeddings to the vector store")
    except Exception as e:
        logger.error(f"Error adding embeddings to the vector store: {e}")
        raise

    logger.info(f"Added {len(doc_ids)} documents with IDs: {doc_ids}")

    # Test vector search
    logger.info("Testing vector search...")
    query = "What is PostgreSQL?"
    results = await knowledge_base.search(query, search_type="vector", limit=2)
    logger.info(f"Vector search results for '{query}':")
    for i, result in enumerate(results):
        logger.info(f"  {i+1}. {result['text']} (score: {result['score']})")

    # Test keyword search
    logger.info("Testing keyword search...")
    query = "PostgreSQL database"
    results = await knowledge_base.search(query, search_type="keyword", limit=2)
    logger.info(f"Keyword search results for '{query}':")
    for i, result in enumerate(results):
        logger.info(f"  {i+1}. {result['text']} (score: {result['score']})")

    # Test hybrid search
    logger.info("Testing hybrid search...")
    query = "PostgreSQL vector database"
    results = await knowledge_base.search(query, search_type="hybrid", limit=2)
    logger.info(f"Hybrid search results for '{query}':")
    for i, result in enumerate(results):
        logger.info(f"  {i+1}. {result['text']} (score: {result['score']})")

    # Clean up
    logger.info("Cleaning up...")
    await vector_store.clear()

    logger.info("PgVectorKnowledgeBaseService test completed successfully!")


if __name__ == "__main__":
    asyncio.run(test_pgvector_knowledge_base())
