#!/usr/bin/env python
"""
Migrate Documents to PostgreSQL

This script migrates documents from a JSON file to PostgreSQL database.
It processes documents into chunks, generates embeddings, and stores them
in the database with proper metadata.

Usage:
    python migrate_documents_to_postgres.py [--file-path PATH] [--chunk-size SIZE] [--overlap OVERLAP]

Options:
    --file-path PATH    Path to the JSON file containing documents [default: from env var]
    --chunk-size SIZE   Size of document chunks in characters [default: 1000]
    --overlap OVERLAP   Overlap between chunks in characters [default: 200]
    --clear             Clear existing documents before migration
    --dry-run           Show what would be done without making changes
"""

import os
import sys
import json
import logging
import asyncio
import argparse
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.rag.embedding_utils import get_embedding_model
from app.core.db.database import get_db_context, init_db
from app.config import get_settings
from sqlalchemy import text
from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create console for rich output
console = Console()


def chunk_document(doc: Dict[str, Any], chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
    """
    Split a document into chunks with overlap.

    Args:
        doc: Document to chunk
        chunk_size: Size of chunks in characters
        overlap: Overlap between chunks in characters

    Returns:
        List of document chunks
    """
    content = doc.get("content", "")
    if not content:
        return []

    # Split content into chunks
    chunks = []
    for i in range(0, len(content), chunk_size - overlap):
        chunk_content = content[i:i + chunk_size]
        if len(chunk_content) < 50:  # Skip very small chunks
            continue

        # Create chunk with metadata
        chunk = {
            "id": str(uuid.uuid4()),
            "document_id": doc.get("id"),  # Use the UUID from the document
            "chunk_index": len(chunks),
            "content": chunk_content,
            "department": doc.get("department", ""),
            "title": doc.get("title", "Untitled"),
            "tags": doc.get("tags", []),
            "metadata": {
                "source": doc.get("metadata", {}).get("source", "unknown"),
                "section": doc.get("metadata", {}).get("section", "unknown"),
                "department": doc.get("department", "unknown"),
                "chunk_index": len(chunks),
                "total_chunks": (len(content) // (chunk_size - overlap)) + 1,
                "original_doc_id": doc.get("metadata", {}).get("original_id", "unknown")
            }
        }
        chunks.append(chunk)

    return chunks


async def migrate_documents(
    file_path: str,
    chunk_size: int = 1000,
    overlap: int = 200,
    clear_existing: bool = False,
    dry_run: bool = False
):
    """
    Migrate documents from JSON file to PostgreSQL.

    Args:
        file_path: Path to JSON file containing documents
        chunk_size: Size of document chunks in characters
        overlap: Overlap between chunks in characters
        clear_existing: Whether to clear existing documents
        dry_run: Show what would be done without making changes
    """
    try:
        # Load documents from file
        with open(file_path, 'r') as f:
            documents_dict = json.load(f)

        # Convert dictionary to list of documents
        documents = []
        for doc_id, doc in documents_dict.items():
            documents.append(doc)

        console.print(f"[bold green]Loaded {len(documents)} documents from {file_path}[/bold green]")

        if dry_run:
            console.print("[bold yellow]DRY RUN: No changes will be made[/bold yellow]")

        # Initialize embedding model
        embedding_model = get_embedding_model()
        console.print("[bold blue]Embedding model initialized[/bold blue]")

        # Clear existing documents if requested
        if clear_existing and not dry_run:
            with get_db_context() as db:
                # Delete document chunks first (due to foreign key constraints)
                db.execute(text("DELETE FROM document_chunks"))
                # Delete documents
                db.execute(text("DELETE FROM documents"))
                db.commit()
            console.print("[bold red]Cleared existing documents from database[/bold red]")

        # Process documents
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
        ) as progress:
            # Create tasks
            doc_task = progress.add_task("[green]Processing documents...", total=len(documents))
            chunk_task = progress.add_task("[blue]Processing chunks...", total=0, visible=False)
            embed_task = progress.add_task("[yellow]Generating embeddings...", total=0, visible=False)

            # Process each document
            for doc in documents:
                # Format the document
                original_id = doc.get("id", str(uuid.uuid4()))

                # Check if document with this original_id already exists
                existing_doc_id = None
                if not dry_run:
                    with get_db_context() as db:
                        # Query for existing document with the same original_id in metadata
                        result = db.execute(
                            text(
                                "SELECT id FROM documents WHERE meta_info->>'original_id' = :original_id"
                            ),
                            {"original_id": original_id}
                        ).fetchone()

                        if result:
                            existing_doc_id = str(result[0])
                            logger.info(f"Document with original_id {original_id} already exists with ID {existing_doc_id}")

                # Use existing ID if found, otherwise generate a new one
                doc_id = existing_doc_id if existing_doc_id else str(uuid.uuid4())

                formatted_doc = {
                    "id": doc_id,
                    "title": doc.get("title", "Untitled"),
                    "content": doc.get("content", ""),
                    "department": doc.get("department", ""),
                    "tags": doc.get("tags", []),
                    "created_at": doc.get("created_at", datetime.now().isoformat()),
                    "metadata": doc.get("metadata", {})
                }

                # Store the original ID and department in the metadata for reference
                if "metadata" not in formatted_doc or formatted_doc["metadata"] is None:
                    formatted_doc["metadata"] = {}
                formatted_doc["metadata"]["original_id"] = original_id

                # Store department in metadata
                if "department" in doc:
                    formatted_doc["metadata"]["department"] = doc["department"]

                # Insert or update document in database
                if not dry_run:
                    with get_db_context() as db:
                        # Convert metadata to JSON string
                        meta_info_json = json.dumps(formatted_doc.get("metadata", {}))

                        if existing_doc_id:
                            # Update existing document
                            db.execute(
                                text(
                                    "UPDATE documents SET "
                                    "title = :title, "
                                    "content = :content, "
                                    "meta_info = :meta_info "
                                    "WHERE id = :id"
                                ),
                                {
                                    "id": formatted_doc["id"],
                                    "title": formatted_doc["title"],
                                    "content": formatted_doc["content"],
                                    "meta_info": meta_info_json
                                }
                            )
                            logger.info(f"Updated existing document with ID {formatted_doc['id']}")
                        else:
                            # Insert new document
                            db.execute(
                                text(
                                    "INSERT INTO documents (id, title, content, meta_info, created_at) "
                                    "VALUES (:id, :title, :content, :meta_info, :created_at)"
                                ),
                                {
                                    "id": formatted_doc["id"],
                                    "title": formatted_doc["title"],
                                    "content": formatted_doc["content"],
                                    "meta_info": meta_info_json,
                                    "created_at": formatted_doc["created_at"]
                                }
                            )
                            logger.info(f"Inserted new document with ID {formatted_doc['id']}")

                        db.commit()

                # Chunk document
                chunks = chunk_document(formatted_doc, chunk_size, overlap)

                # Update progress
                progress.update(chunk_task, total=len(chunks), visible=True)
                progress.update(embed_task, total=len(chunks), visible=True)

                # Process chunks
                for chunk in chunks:
                    progress.update(chunk_task, advance=1)

                    # Generate embedding
                    embedding = await embedding_model.embed_query(chunk["content"])
                    progress.update(embed_task, advance=1)

                    # Insert or update chunk in database
                    if not dry_run:
                        with get_db_context() as db:
                            # Convert embedding to PostgreSQL vector format
                            vector_str = f"[{','.join(str(x) for x in embedding)}]"
                            # Convert metadata to JSON string
                            meta_info_json = json.dumps(chunk["metadata"])

                            # Check if chunk already exists for this document and chunk_index
                            existing_chunk = db.execute(
                                text(
                                    "SELECT id FROM document_chunks "
                                    "WHERE document_id = :document_id AND chunk_index = :chunk_index"
                                ),
                                {
                                    "document_id": chunk["document_id"],
                                    "chunk_index": chunk["chunk_index"]
                                }
                            ).fetchone()

                            if existing_chunk:
                                # Update existing chunk
                                chunk_id = existing_chunk[0]
                                sql = f"""
                                UPDATE document_chunks
                                SET content = $${chunk["content"]}$$,
                                    meta_info = '{meta_info_json}'::jsonb,
                                    embedding = '{vector_str}'::vector
                                WHERE id = '{chunk_id}'
                                """
                                db.execute(text(sql))
                                logger.debug(f"Updated existing chunk {chunk_id} for document {chunk['document_id']}")
                            else:
                                # Insert new chunk
                                sql = f"""
                                INSERT INTO document_chunks
                                (id, document_id, chunk_index, content, meta_info, embedding)
                                VALUES (
                                    '{chunk["id"]}',
                                    '{chunk["document_id"]}',
                                    {chunk["chunk_index"]},
                                    $${chunk["content"]}$$,
                                    '{meta_info_json}'::jsonb,
                                    '{vector_str}'::vector
                                )
                                """
                                db.execute(text(sql))
                                logger.debug(f"Inserted new chunk {chunk['id']} for document {chunk['document_id']}")

                            db.commit()

                # Update document progress
                progress.update(doc_task, advance=1)

        console.print("[bold green]Migration completed successfully![/bold green]")

        # Print summary
        if not dry_run:
            with get_db_context() as db:
                doc_count = db.execute(text("SELECT COUNT(*) FROM documents")).scalar()
                chunk_count = db.execute(text("SELECT COUNT(*) FROM document_chunks")).scalar()
                console.print(f"[bold blue]Documents in database: {doc_count}[/bold blue]")
                console.print(f"[bold blue]Document chunks in database: {chunk_count}[/bold blue]")

    except Exception as e:
        logger.error(f"Error migrating documents: {e}")
        console.print(f"[bold red]Error migrating documents: {e}[/bold red]")
        raise


async def main():
    """Main function to migrate documents."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Migrate documents to PostgreSQL")
    parser.add_argument("--file-path", help="Path to JSON file containing documents")
    parser.add_argument("--chunk-size", type=int, default=1000, help="Size of document chunks in characters")
    parser.add_argument("--overlap", type=int, default=200, help="Overlap between chunks in characters")
    parser.add_argument("--clear", action="store_true", help="Clear existing documents before migration")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    args = parser.parse_args()

    # Get file path from arguments or environment variable
    file_path = args.file_path
    if not file_path:
        file_path = os.environ.get("MOCK_DOCUMENT_PATH", "./backend/data/mock_documents/mock_documents.json")
    file_path = os.path.expanduser(file_path)

    # Check if file exists
    if not os.path.exists(file_path):
        console.print(f"[bold red]Document file not found at {file_path}[/bold red]")
        return

    # Initialize database
    init_db()

    # Migrate documents
    await migrate_documents(
        file_path=file_path,
        chunk_size=args.chunk_size,
        overlap=args.overlap,
        clear_existing=args.clear,
        dry_run=args.dry_run
    )


if __name__ == "__main__":
    asyncio.run(main())
