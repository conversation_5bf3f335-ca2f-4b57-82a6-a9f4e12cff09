#!/usr/bin/env python
"""
Fix PGVector Schema Script

This script fixes the pgvector schema to handle large embedding vectors.
It drops the existing index and recreates it with a more appropriate type.
"""

import os
import sys
import logging
import asyncio
import sqlalchemy as sa

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.db.database import get_db_context

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def fix_pgvector_schema():
    """
    Fix the pgvector schema to handle large embedding vectors.
    """
    try:
        logger.info("Starting pgvector schema fix...")
        
        with get_db_context() as db:
            # Check if the embeddings table exists
            table_exists = db.execute(
                sa.text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'embeddings')")
            ).scalar()
            
            if not table_exists:
                logger.info("Embeddings table does not exist. Creating it...")
                
                # Create the embeddings table with appropriate settings
                db.execute(
                    sa.text("""
                    CREATE TABLE embeddings (
                        id UUID PRIMARY KEY,
                        embedding vector(768),
                        text TEXT NOT NULL,
                        meta_info JSONB DEFAULT '{}'::jsonb,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )
                    """)
                )
                
                # Create a text search index
                db.execute(
                    sa.text("""
                    ALTER TABLE embeddings ADD COLUMN text_search tsvector 
                    GENERATED ALWAYS AS (to_tsvector('english', text)) STORED
                    """)
                )
                
                db.execute(
                    sa.text("""
                    CREATE INDEX idx_embeddings_text_search ON embeddings USING GIN (text_search)
                    """)
                )
                
                db.commit()
                logger.info("Created embeddings table with appropriate settings")
            else:
                logger.info("Embeddings table exists. Checking for existing index...")
                
                # Check if the index exists
                index_exists = db.execute(
                    sa.text("SELECT EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_embeddings_embedding')")
                ).scalar()
                
                if index_exists:
                    logger.info("Dropping existing embedding index...")
                    db.execute(sa.text("DROP INDEX IF EXISTS idx_embeddings_embedding"))
                    db.commit()
                    logger.info("Existing embedding index dropped")
                
                # Create a new index using GiST
                logger.info("Creating new GiST index for embeddings...")
                try:
                    db.execute(
                        sa.text("""
                        CREATE INDEX idx_embeddings_embedding ON embeddings 
                        USING gist (embedding vector_cosine_ops)
                        """)
                    )
                    db.commit()
                    logger.info("Created GiST index for embeddings")
                except Exception as e:
                    logger.warning(f"Failed to create GiST index: {e}")
                    db.rollback()
                    
                    # Try with HNSW index with reduced parameters
                    try:
                        logger.info("Trying HNSW index with reduced parameters...")
                        db.execute(
                            sa.text("""
                            CREATE INDEX idx_embeddings_embedding ON embeddings 
                            USING hnsw (embedding vector_cosine_ops) WITH (m=8, ef_construction=32)
                            """)
                        )
                        db.commit()
                        logger.info("Created HNSW index with reduced parameters")
                    except Exception as e2:
                        logger.warning(f"Failed to create HNSW index: {e2}")
                        db.rollback()
                        
                        # Try with no operator class
                        try:
                            logger.info("Trying basic index without operator class...")
                            db.execute(
                                sa.text("""
                                CREATE INDEX idx_embeddings_embedding ON embeddings (embedding)
                                """)
                            )
                            db.commit()
                            logger.info("Created basic index without operator class")
                        except Exception as e3:
                            logger.warning(f"Failed to create basic index: {e3}")
                            db.rollback()
                            logger.warning("Could not create any vector index. Vector search may be slower.")
        
        logger.info("PGVector schema fix completed successfully")
    except Exception as e:
        logger.error(f"Error fixing pgvector schema: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(fix_pgvector_schema())
