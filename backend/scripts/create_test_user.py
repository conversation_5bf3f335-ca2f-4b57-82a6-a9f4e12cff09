#!/usr/bin/env python
"""
Create Test User

This script creates a test user in the database for development and testing.
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.db.database import get_db_context
from app.core.db.models import User
from app.core.auth.password import hash_password

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def create_test_user(email: str, password: str, roles: list = None, permissions: list = None):
    """
    Create a test user in the database.
    
    Args:
        email: User's email address
        password: User's password
        roles: List of roles to assign to the user
        permissions: List of permissions to assign to the user
    """
    if roles is None:
        roles = ["user"]
    
    if permissions is None:
        permissions = []
    
    with get_db_context() as db:
        try:
            # Check if user already exists
            existing_user = db.query(User).filter(User.email == email).first()
            
            if existing_user:
                logger.info(f"User with email {email} already exists")
                
                # Update user if needed
                if roles and existing_user.roles != roles:
                    existing_user.roles = roles
                    logger.info(f"Updated roles for user {email}: {roles}")
                
                if permissions and existing_user.permissions != permissions:
                    existing_user.permissions = permissions
                    logger.info(f"Updated permissions for user {email}: {permissions}")
                
                db.commit()
                return
            
            # Create new user
            user = User(
                email=email,
                password=password,
                roles=roles,
                permissions=permissions,
                is_active=True,
                email_verified=True
            )
            
            db.add(user)
            db.commit()
            
            logger.info(f"Created test user: {email}")
            logger.info(f"Roles: {roles}")
            logger.info(f"Permissions: {permissions}")
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating test user: {e}")
            raise


if __name__ == "__main__":
    # Create test users
    create_test_user(
        email="<EMAIL>",
        password="secure123",
        roles=["user"],
        permissions=[]
    )
    
    create_test_user(
        email="<EMAIL>",
        password="admin123",
        roles=["admin", "user"],
        permissions=["*"]
    )
    
    logger.info("Test users created successfully")
