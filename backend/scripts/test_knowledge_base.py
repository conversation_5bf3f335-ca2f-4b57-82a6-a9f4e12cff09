#!/usr/bin/env python
"""
Test Knowledge Base Service

This script tests the knowledge base service by:
1. Initializing the PGVectorStore and embedding model
2. Creating a knowledge base service
3. Adding a document to the knowledge base
4. Searching for the document
5. Testing the end-to-end flow

Usage:
    python -m backend.scripts.test_knowledge_base
"""

import sys
import os
import logging
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the necessary modules
from app.core.db.database import init_db
from app.rag.pgvector_store import PGVectorStore
from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService
from app.rag.embeddings import get_embedding_model


async def test_add_document():
    """Test adding a document to the knowledge base."""
    try:
        # Initialize the vector store
        store = PGVectorStore()
        logger.info("PGVectorStore initialized successfully")

        # Initialize the embedding model
        embedding_model = get_embedding_model()
        logger.info("Embedding model initialized successfully")

        # Initialize the knowledge base service
        kb = PgVectorKnowledgeBaseService(store, embedding_model)
        logger.info("PgVectorKnowledgeBaseService initialized successfully")

        # Add a document to the knowledge base
        doc = {
            "title": "Test Document",
            "content": "This is a test document for pgvector. It contains information about PostgreSQL and vector embeddings.",
            "author": "Test Author",
            "source": "Test Source"
        }
        doc_id = await kb.add_document(doc)
        logger.info(f"Document added with ID: {doc_id}")

        return doc_id
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise


async def test_search_document(doc_id):
    """Test searching for a document in the knowledge base."""
    try:
        # Initialize the vector store
        store = PGVectorStore()
        
        # Initialize the embedding model
        embedding_model = get_embedding_model()
        
        # Initialize the knowledge base service
        kb = PgVectorKnowledgeBaseService(store, embedding_model)
        
        # Search for the document
        results = await kb.search("PostgreSQL vector embeddings", limit=5)
        
        # Print the results
        logger.info(f"Search results: {len(results)} found")
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  ID: {result['id']}")
            logger.info(f"  Score: {result['score']}")
            logger.info(f"  Text: {result['text'][:100]}...")
            logger.info(f"  Metadata: {result['metadata']}")
            
        return results
    except Exception as e:
        logger.error(f"Error searching for document: {e}")
        raise


async def main():
    """Main function to run the tests."""
    try:
        # Initialize the database
        init_db()
        logger.info("Database initialized")
        
        # Test adding a document
        doc_id = await test_add_document()
        
        # Test searching for the document
        results = await test_search_document(doc_id)
        
        logger.info("All tests completed successfully!")
        
        return doc_id, results
    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
