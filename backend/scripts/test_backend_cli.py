#!/usr/bin/env python3
"""
RAG and Multi-Agent CLI Testing Tool

This script provides a command-line interface for testing the RAG and multi-agent
orchestration capabilities of the BusinessLM Python backend without requiring
the frontend. It uses mock documents and allows for interactive testing.
"""

import os
import sys
import json
import uuid
import asyncio
import logging
from typing import List, Optional
from datetime import datetime

import typer
from rich.panel import Panel
from rich.table import Table
from rich.console import Console
from dotenv import load_dotenv

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import CLI formatting utilities
from app.cli.formatting import (
    console,
    print_header,
    print_section_divider,
    print_success,
    print_error,
    print_warning,
    print_info,
    create_table,
    create_progress,
    print_response_panel,
    set_verbose_mode,
    print_verbose,
    format_query,
    format_step,
    visualize_department_routing,
    visualize_agent_flow,
    visualize_graph_execution,
)

# Import tracing visualization utilities
from app.cli.tracing import (
    visualize_trace_text,
    visualize_trace_tree,
    visualize_agent_communication,
    visualize_trace_summary
)

# Import metrics and benchmarking utilities
from app.core.metrics import MetricsCollector
from app.core.benchmarks import BenchmarkRunner, load_benchmark_queries, save_benchmark_results
from app.core.tracing import TracingCollector, TracingLevel

# Initialize Typer app
app = typer.Typer(
    name="rag-cli",
    help="CLI for testing RAG and multi-agent orchestration with mock documents",
    add_completion=False,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("rag-cli")

# Load environment variables
load_dotenv()

# Add a verbose mode option
verbose_mode = False

# Global storage for mock documents
mock_documents = {}


@app.callback()
def callback():
    """
    RAG and Multi-Agent CLI Testing Tool

    This tool allows you to test the RAG and multi-agent orchestration capabilities
    of the BusinessLM Python backend without requiring the frontend.
    """
    # This function will be called before any command
    pass


@app.command()
def create_mock_document(
    title: str = typer.Option(..., help="Title of the mock document"),
    content: str = typer.Option(None, help="Content of the document (or path to file if --from-file is used)"),
    department: str = typer.Option("co_ceo", help="Department ID (co_ceo, finance, marketing)"),
    from_file: bool = typer.Option(False, help="Read content from a file instead of command line"),
    tags: List[str] = typer.Option(None, help="Tags for the document"),
):
    """
    Create a mock document for testing.
    """
    global mock_documents

    try:
        # Get content from file if specified
        if from_file and content:
            try:
                with open(content, 'r') as file:
                    content = file.read()
                console.print(f"[green]Read content from file: {content}[/green]")
            except Exception as e:
                console.print(f"[bold red]Error reading file: {str(e)}[/bold red]")
                return

        # If content is still None, prompt for it
        if content is None:
            console.print("[yellow]Enter document content (press Ctrl+D when finished):[/yellow]")
            content_lines = []
            try:
                while True:
                    line = input()
                    content_lines.append(line)
            except EOFError:
                content = "\n".join(content_lines)

        # Generate a document ID
        doc_id = f"mock-doc-{uuid.uuid4()}"

        # Create the mock document
        mock_documents[doc_id] = {
            "id": doc_id,
            "title": title,
            "content": content,
            "department": department,
            "tags": tags or [],
            "created_at": datetime.now().isoformat(),
            "metadata": {
                "source": "mock",
                "section": "knowledge"
            }
        }

        console.print(f"[bold green]Created mock document:[/bold green] {doc_id}")
        console.print(f"[bold blue]Title:[/bold blue] {title}")
        console.print(f"[bold blue]Department:[/bold blue] {department}")
        console.print(f"[bold blue]Content length:[/bold blue] {len(content)} characters")

        # Save to disk for persistence
        save_mock_documents(append=True)

    except Exception as e:
        console.print(f"[bold red]Error creating mock document: {str(e)}[/bold red]")
        logger.exception("Error in create_mock_document command")


@app.command()
def list_mock_documents():
    """
    List all available mock documents.
    """
    global mock_documents

    try:
        # Load documents if not already loaded
        load_mock_documents()

        if not mock_documents:
            console.print("[yellow]No mock documents found. Create some with the 'create-mock-document' command.[/yellow]")
            return

        # Create a table to display the documents
        docs_table = Table(title="Mock Documents")
        docs_table.add_column("ID", style="cyan")
        docs_table.add_column("Title", style="green")
        docs_table.add_column("Department", style="blue")
        docs_table.add_column("Content Preview", style="yellow")
        docs_table.add_column("Created At", style="magenta")

        for doc_id, doc in mock_documents.items():
            # Truncate content for preview
            content_preview = doc["content"][:50] + "..." if len(doc["content"]) > 50 else doc["content"]

            docs_table.add_row(
                doc_id,
                doc["title"],
                doc["department"],
                content_preview,
                doc.get("created_at", "Unknown")
            )

        console.print(docs_table)

    except Exception as e:
        console.print(f"[bold red]Error listing mock documents: {str(e)}[/bold red]")
        logger.exception("Error in list_mock_documents command")


@app.command()
def view_mock_document(
    doc_id: str = typer.Argument(..., help="ID of the mock document to view"),
):
    """
    View the content of a mock document.
    """
    global mock_documents

    try:
        # Load documents if not already loaded
        load_mock_documents()

        if doc_id not in mock_documents:
            console.print(f"[bold red]Document not found: {doc_id}[/bold red]")
            return

        doc = mock_documents[doc_id]

        # Display document details
        console.print(Panel(
            f"[bold blue]Title:[/bold blue] {doc['title']}\n"
            f"[bold blue]Department:[/bold blue] {doc['department']}\n"
            f"[bold blue]Created:[/bold blue] {doc.get('created_at', 'Unknown')}\n"
            f"[bold blue]Tags:[/bold blue] {', '.join(doc.get('tags', []))}\n\n"
            f"[bold yellow]Content:[/bold yellow]\n\n"
            f"{doc['content']}",
            title=f"Document: {doc_id}",
            expand=False
        ))

    except Exception as e:
        console.print(f"[bold red]Error viewing mock document: {str(e)}[/bold red]")
        logger.exception("Error in view_mock_document command")


@app.command()
def delete_mock_document(
    doc_id: str = typer.Argument(..., help="ID of the mock document to delete"),
):
    """
    Delete a mock document.
    """
    global mock_documents

    try:
        # Load documents if not already loaded
        load_mock_documents()

        if doc_id not in mock_documents:
            console.print(f"[bold red]Document not found: {doc_id}[/bold red]")
            return

        # Delete the document
        del mock_documents[doc_id]
        console.print(f"[bold green]Deleted document: {doc_id}[/bold green]")

        # Save changes
        save_mock_documents()

    except Exception as e:
        console.print(f"[bold red]Error deleting mock document: {str(e)}[/bold red]")
        logger.exception("Error in delete_mock_document command")


@app.command()
def list_postgres_docs(
    limit: int = typer.Option(10, help="Maximum number of documents to list"),
):
    """
    List documents stored in PostgreSQL database.
    """
    try:
        from app.core.db.database import get_db_context
        import sqlalchemy as sa

        with get_db_context() as db:
            # Query documents from the documents table
            result = db.execute(
                sa.text(f"SELECT id, title, meta_info FROM documents LIMIT {limit}")
            )

            # Create a table to display the documents
            docs_table = Table(title="PostgreSQL Documents")
            docs_table.add_column("ID", style="cyan")
            docs_table.add_column("Title", style="green")
            docs_table.add_column("Department", style="blue")
            docs_table.add_column("Metadata", style="yellow")

            # Add rows to the table
            for row in result:
                # Extract department from metadata
                meta_info = row.meta_info or {}
                department = meta_info.get("department", "N/A")

                # Format metadata for display
                metadata_str = ", ".join(f"{k}: {v}" for k, v in meta_info.items() if k != "department")

                docs_table.add_row(
                    str(row.id),
                    row.title,
                    department,
                    metadata_str
                )

            console.print(docs_table)

    except Exception as e:
        console.print(f"[bold red]Error listing PostgreSQL documents: {str(e)}[/bold red]")
        logger.exception("Error in list_postgres_docs command")


@app.command()
def list_postgres_chunks(
    limit: int = typer.Option(10, help="Maximum number of chunks to list"),
    document_id: str = typer.Option(None, help="Filter by document ID"),
):
    """
    List document chunks stored in PostgreSQL database.
    """
    try:
        from app.core.db.database import get_db_context
        import sqlalchemy as sa

        with get_db_context() as db:
            # Build the query
            query = f"SELECT id, content as text, meta_info, document_id FROM document_chunks"
            params = {}

            # Add document_id filter if provided
            if document_id:
                query += " WHERE document_id = :document_id"
                params["document_id"] = document_id

            # Add limit
            query += f" LIMIT {limit}"

            # Execute the query
            result = db.execute(sa.text(query), params)

            # Create a table to display the chunks
            chunks_table = Table(title="PostgreSQL Document Chunks")
            chunks_table.add_column("ID", style="cyan")
            chunks_table.add_column("Department", style="blue")
            chunks_table.add_column("Document ID", style="green")
            chunks_table.add_column("Text Preview", style="yellow")

            # Add rows to the table
            for row in result:
                # Extract metadata
                meta_info = row.meta_info or {}
                department = meta_info.get("department", "N/A")
                doc_id = str(row.document_id) if hasattr(row, 'document_id') else meta_info.get("document_id", "N/A")

                # Truncate text for preview
                text_preview = row.text[:100] + "..." if len(row.text) > 100 else row.text

                chunks_table.add_row(
                    str(row.id),
                    department,
                    doc_id,
                    text_preview
                )

            console.print(chunks_table)

    except Exception as e:
        console.print(f"[bold red]Error listing PostgreSQL chunks: {str(e)}[/bold red]")
        logger.exception("Error in list_postgres_chunks command")


def save_mock_documents(append=False):
    """Save mock documents to disk."""
    try:
        # Get the mock document path from environment variable or use default
        mock_document_path = os.environ.get("MOCK_DOCUMENT_PATH", "~/.businesslm/mock_documents.json")
        mock_document_path = os.path.expanduser(mock_document_path)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(mock_document_path), exist_ok=True)

        if append and os.path.exists(mock_document_path):
            # If append is True, we need to load existing documents first and merge them
            try:
                with open(mock_document_path, "r") as f:
                    existing_documents = json.load(f)

                # Merge existing documents with new ones
                existing_documents.update(mock_documents)

                # Save the merged documents
                with open(mock_document_path, "w") as f:
                    json.dump(existing_documents, f, indent=2)

                logger.info(f"Saved {len(existing_documents)} mock documents to {mock_document_path}")
                return
            except json.JSONDecodeError:
                logger.warning(f"Could not parse existing document file at {mock_document_path}, overwriting")

        # If not appending or if there was an error, just save the current documents
        with open(mock_document_path, "w") as f:
            json.dump(mock_documents, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving mock documents: {e}")


def load_mock_documents():
    """Load mock documents from disk."""
    global mock_documents

    try:
        # Get the mock document path from environment variable or use default
        mock_document_path = os.environ.get("MOCK_DOCUMENT_PATH", "~/.businesslm/mock_documents.json")
        mock_document_path = os.path.expanduser(mock_document_path)

        # Check if file exists
        if os.path.exists(mock_document_path):
            with open(mock_document_path, "r") as f:
                mock_documents = json.load(f)

            logger.info(f"Loaded {len(mock_documents)} mock documents from {mock_document_path}")
        else:
            logger.info(f"No mock documents file found at {mock_document_path}")
    except Exception as e:
        logger.error(f"Error loading mock documents: {e}")


async def initialize_components(provider, model=None, init_embedding=True, init_agents=False, progress=None, use_postgres=False):
    """
    Initialize common components needed for testing.

    Args:
        provider (str): The LLM provider to use
        model (str, optional): The specific model to use
        init_embedding (bool): Whether to initialize the embedding model
        init_agents (bool): Whether to initialize agents
        progress: Progress bar object for displaying initialization progress
        use_postgres (bool): Whether to use PostgreSQL for document storage

    Returns:
        dict: Dictionary containing initialized components
    """
    from app.core import get_llm_adapter
    from app.rag import initialize_knowledge_base_service, initialize_embedding_model

    components = {}

    # Create progress bar if not provided
    if progress is None:
        progress = create_progress()

    # Check if mock documents are loaded
    load_mock_documents()
    if not mock_documents and not use_postgres:
        print_error("No mock documents found. Create some with the 'create-mock-document' command.")
        return None

    try:
        with progress:
            # Initialize LLM adapter
            task = progress.add_task("[yellow]Initializing LLM adapter...", total=None)
            # Create kwargs for the LLM adapter
            llm_kwargs = {}
            if model:
                llm_kwargs["model"] = model

            # Initialize the LLM adapter with the specified provider and model
            llm_adapter = get_llm_adapter(provider=provider, **llm_kwargs)
            progress.update(task, description=f"[green]LLM adapter initialized ({provider})")
            components["llm_adapter"] = llm_adapter

            # Initialize embedding model if requested
            if init_embedding:
                task = progress.add_task("[yellow]Initializing embedding model...", total=None)
                embedding_model = await initialize_embedding_model()
                progress.update(task, description="[green]Embedding model initialized")
                components["embedding_model"] = embedding_model

            # Initialize knowledge base service
            task = progress.add_task("[yellow]Initializing knowledge base service...", total=None)
            if use_postgres:
                # Use PostgreSQL for document storage
                from app.rag.pgvector_knowledge_base import PgVectorKnowledgeBaseService
                from app.rag.vector_store import get_vector_store

                # Get the vector store
                vector_store = get_vector_store()

                # Initialize the knowledge base service with PostgreSQL
                knowledge_base_service = PgVectorKnowledgeBaseService(
                    vector_store=vector_store,
                    embedding_model=embedding_model,
                    vector_weight=0.7,
                    keyword_weight=0.3,
                    use_reranking=False
                )
                progress.update(task, description="[green]PostgreSQL knowledge base service initialized")
                print_info("Using PostgreSQL for document storage")
            else:
                # Use in-memory document storage
                knowledge_base_service = await initialize_knowledge_base_service()
                progress.update(task, description="[green]In-memory knowledge base service initialized")
                print_info("Using in-memory document storage")

            components["knowledge_base_service"] = knowledge_base_service

            # Initialize agents if requested
            if init_agents:
                from app.agents import initialize_agents
                task = progress.add_task("[yellow]Initializing agents...", total=None)
                agents = initialize_agents(llm_adapter, knowledge_base_service)
                progress.update(task, description="[green]Agents initialized")
                components["agents"] = agents

        return components
    except Exception as e:
        print_error(f"Error initializing components: {str(e)}", e)
        logger.exception("Error in initialize_components")
        return None


async def initialize_rag_components(components, progress=None):
    """
    Initialize RAG-specific components.

    Args:
        components (dict): Dictionary containing base components (llm_adapter, knowledge_base_service)
        progress: Progress bar object for displaying initialization progress

    Returns:
        dict: Dictionary containing initialized RAG components
    """
    from app.rag.retriever import HybridRetriever, ContextWindowManager
    from app.rag.generator import RAGGenerator
    from app.rag.query_analyzer import QueryAnalyzer

    rag_components = {}

    # Create progress bar if not provided
    if progress is None:
        progress = create_progress()

    try:
        with progress:
            llm_adapter = components["llm_adapter"]
            knowledge_base_service = components["knowledge_base_service"]

            # Initialize query analyzer
            task = progress.add_task("[yellow]Initializing query analyzer...", total=None)
            query_analyzer = QueryAnalyzer(llm_adapter=llm_adapter)
            progress.update(task, description="[green]Query analyzer initialized")
            rag_components["query_analyzer"] = query_analyzer

            # Initialize retriever
            task = progress.add_task("[yellow]Initializing retriever...", total=None)
            retriever = HybridRetriever(
                query_analyzer=query_analyzer,
                knowledge_base_service=knowledge_base_service,
                default_vector_weight=0.7,
                default_keyword_weight=0.3
            )
            progress.update(task, description="[green]Retriever initialized")
            rag_components["retriever"] = retriever

            # Initialize context window manager
            task = progress.add_task("[yellow]Initializing context window manager...", total=None)
            context_window_manager = ContextWindowManager(
                llm_adapter=llm_adapter,
                max_tokens=4000,
                token_buffer=1000
            )
            retriever.context_window_manager = context_window_manager
            progress.update(task, description="[green]Context window manager initialized")
            rag_components["context_window_manager"] = context_window_manager

            # Initialize generator
            task = progress.add_task("[yellow]Initializing generator...", total=None)
            generator = RAGGenerator(
                llm_adapter=llm_adapter,
                include_citations=True,
                citation_format="inline"
            )
            progress.update(task, description="[green]Generator initialized")
            rag_components["generator"] = generator

        return rag_components
    except Exception as e:
        print_error(f"Error initializing RAG components: {str(e)}", e)
        logger.exception("Error in initialize_rag_components")
        return None


def initialize_tracing(tracing_enabled=True, tracing_level="standard", user_id="cli-user", thread_id=None):
    """
    Initialize tracing components.

    Args:
        tracing_enabled (bool): Whether to enable tracing
        tracing_level (str): Tracing detail level (none, basic, standard, detailed, debug)
        user_id (str): User ID for the session
        thread_id (str): Thread ID for the session

    Returns:
        tuple: (metrics_collector, tracing_collector, thread_id)
    """
    # Create a metrics collector to track events
    metrics_collector = MetricsCollector()

    # Create a tracing collector if tracing is enabled
    tracing_collector = None

    # Generate a thread ID if not provided
    if not thread_id:
        thread_id = str(uuid.uuid4())
        print_info(f"Generated new thread ID: {thread_id}")

    if tracing_enabled:
        # Map string level to TracingLevel enum
        level_map = {
            "none": TracingLevel.NONE,
            "basic": TracingLevel.BASIC,
            "standard": TracingLevel.STANDARD,
            "detailed": TracingLevel.DETAILED,
            "debug": TracingLevel.DEBUG
        }
        trace_level = level_map.get(tracing_level.lower(), TracingLevel.STANDARD)

        # Create the tracing collector
        tracing_collector = TracingCollector(
            session_id=str(uuid.uuid4()),
            user_id=user_id,
            thread_id=thread_id,
            granularity=trace_level
        )

        print_verbose(f"Tracing enabled at level: {tracing_level}")

    return metrics_collector, tracing_collector, thread_id


@app.command()
def test_rag(
    query: str = typer.Argument(..., help="The query to test RAG with"),
    department: str = typer.Option("co_ceo", help="Department ID (co_ceo, finance, marketing)"),
    provider: str = typer.Option("mock", help="The LLM provider to use (openai, anthropic, gemini, mock)"),
    model: str = typer.Option(None, help="The specific model to use (e.g., gpt-4, claude-3-opus, gemini-pro)"),
    limit: int = typer.Option(5, help="Maximum number of documents to retrieve"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    tracing: bool = typer.Option(False, help="Enable tracing for observability"),
    tracing_level: str = typer.Option("standard", help="Tracing detail level (none, basic, standard, detailed, debug)"),
    use_postgres: bool = typer.Option(False, help="Use PostgreSQL for document storage"),
):
    """
    Test the RAG pipeline with mock documents.

    This command allows you to test the Retrieval-Augmented Generation (RAG) pipeline
    with a custom query. You can specify a department to filter documents by department,
    and you can choose which LLM provider and model to use.

    You can also choose to use PostgreSQL for document storage instead of in-memory storage.
    """
    # Set verbose mode
    set_verbose_mode(verbose)

    async def run_test():
        try:
            # Initialize base components
            components = await initialize_components(provider, model, use_postgres=use_postgres)
            if not components:
                return

            # Initialize RAG components
            rag_components = await initialize_rag_components(components)
            if not rag_components:
                return

            # Initialize tracing if enabled
            metrics_collector, tracing_collector, _ = initialize_tracing(
                tracing_enabled=tracing,
                tracing_level=tracing_level
            )

            # Extract components
            query_analyzer = rag_components["query_analyzer"]
            retriever = rag_components["retriever"]
            generator = rag_components["generator"]

            # Process the query
            format_query(query)

            # Step 1: Analyze query
            format_step(1, "Analyzing query...")
            analysis = await query_analyzer.analyze(query)

            # Display analysis
            columns = [
                {"name": "Key", "style": "cyan"},
                {"name": "Value", "style": "green"}
            ]
            analysis_table = create_table("Query Analysis", columns)

            import json
            for key, value in analysis.items():
                if isinstance(value, dict):
                    analysis_table.add_row(key, json.dumps(value, indent=2))
                elif isinstance(value, list):
                    analysis_table.add_row(key, ", ".join(str(item) for item in value))
                else:
                    analysis_table.add_row(key, str(value))

            console.print(analysis_table)

            # Step 2: Retrieve documents
            format_step(2, "Retrieving documents...")
            retrieval_strategy = analysis.get("retrieval_strategy", {})

            # Create filters based on department
            filters = {"department": department} if department else None

            documents = await retriever.retrieve(
                query=query,
                retrieval_strategy=retrieval_strategy,
                limit=limit,
                filters=filters
            )

            # Display retrieved documents
            if documents:
                print_success(f"Retrieved {len(documents)} documents")

                columns = [
                    {"name": "ID", "style": "cyan"},
                    {"name": "Content Preview", "style": "yellow"},
                    {"name": "Score", "style": "green"},
                    {"name": "Department", "style": "blue"}
                ]
                docs_table = create_table("Retrieved Documents", columns)

                for doc in documents:
                    # Truncate content for preview
                    content = doc.get("text", "")
                    content_preview = content[:100] + "..." if len(content) > 100 else content

                    docs_table.add_row(
                        doc.get("id", "Unknown"),
                        content_preview,
                        f"{doc.get('score', 0):.4f}",
                        doc.get("metadata", {}).get("department", "Unknown")
                    )

                console.print(docs_table)
            else:
                print_warning("No documents retrieved")

            # Step 3: Generate response
            format_step(3, "Generating response...")
            response = await generator.generate(query, documents)

            # Prepare citations
            citations = []
            for i, doc in enumerate(documents, 1):
                citations.append({
                    "id": doc.get("id", f"doc-{i}"),
                    "title": doc.get("metadata", {}).get("title", "Untitled"),
                    "source": doc.get("metadata", {}).get("source", "Unknown")
                })

            # Display response
            print_response_panel(response, "RAG Response", citations)

            # Display tracing information if enabled
            if tracing and tracing_collector and tracing_collector.traces:
                print_section_divider("Tracing Visualization", "primary")

                # Save traces to file
                try:
                    # Create traces directory if it doesn't exist
                    import os
                    os.makedirs("traces", exist_ok=True)

                    # Generate filename with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
                    filename = f"trace-rag-{timestamp}.json"
                    trace_file = os.path.join("traces", filename)

                    # Get the traces directly from the collector
                    traces = tracing_collector.traces

                    # Debug print
                    print_info(f"Trace type: {type(traces)}")
                    print_info(f"Trace length: {len(traces)}")
                    if len(traces) > 0:
                        print_info(f"First trace type: {type(traces[0])}")

                    # Save traces directly
                    import json
                    with open(trace_file, "w") as f:
                        json.dump(traces, f, indent=2, default=str)

                    print_info(f"Traces saved to: {trace_file}")
                except Exception as e:
                    print_warning(f"Could not save traces: {str(e)}")
                    trace_file = None

                # Show trace summary
                print_section_divider("Trace Summary", "secondary")
                visualize_trace_summary(tracing_collector.traces)

        except Exception as e:
            print_error(f"Error testing RAG: {str(e)}", e)
            logger.exception("Error in test_rag command")

    # Run the async function
    asyncio.run(run_test())


@app.command()
def test_multi_agent(
    query: str = typer.Argument(..., help="The query to test multi-agent orchestration with"),
    provider: str = typer.Option("mock", help="The LLM provider to use (openai, anthropic, gemini, mock)"),
    model: str = typer.Option(None, help="The specific model to use (e.g., gpt-4, claude-3-opus, gemini-pro)"),
    department: str = typer.Option(None, help="Department to start with (co_ceo, finance, marketing)"),
    thread_id: str = typer.Option(None, help="Thread ID for continuing a conversation"),
    user_id: str = typer.Option("cli-user", help="User ID for the query"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    tracing: bool = typer.Option(True, help="Enable tracing for observability"),
    tracing_level: str = typer.Option("standard", help="Tracing detail level (none, basic, standard, detailed, debug)"),
    show_agent_communication: bool = typer.Option(True, help="Show agent communication visualization"),
    show_full_content: bool = typer.Option(False, help="Show full content in agent communications"),
    show_node_events: bool = typer.Option(True, help="Show node execution events in agent communications"),
    use_postgres: bool = typer.Option(False, help="Use PostgreSQL for document storage"),
):
    """
    Test the multi-agent orchestration with mock documents.

    If a department is specified, the query will start with that department,
    which can then decide to handle it directly or engage in multi-agent orchestration.

    The tracing feature provides observability into the multi-agent orchestration process,
    showing how agents communicate and make decisions during execution.

    You can also choose to use PostgreSQL for document storage instead of in-memory storage.
    """
    from app.langgraph.graph import process_query

    # Set verbose mode
    set_verbose_mode(verbose)

    async def run_test():
        # Use thread_id from outer scope
        nonlocal thread_id

        try:
            # Initialize base components with agents
            components = await initialize_components(provider, model, init_agents=True, use_postgres=use_postgres)
            if not components:
                return

            # Initialize tracing
            metrics_collector, tracing_collector, new_thread_id = initialize_tracing(
                tracing_enabled=tracing,
                tracing_level=tracing_level,
                user_id=user_id,
                thread_id=thread_id
            )

            # Update thread_id if a new one was generated
            thread_id = new_thread_id

            # Extract components
            llm_adapter = components["llm_adapter"]
            knowledge_base_service = components["knowledge_base_service"]
            agents = components["agents"]

            # Process the query
            format_query(query)
            print_verbose(f"Using thread ID: {thread_id}")
            print_verbose(f"Using user ID: {user_id}")

            # Process the query using LangGraph
            print_section_divider("Multi-Agent Orchestration", "primary")
            co_ceo_agent = agents.get("co_ceo")
            finance_agent = agents.get("finance")
            marketing_agent = agents.get("marketing")

            result = await process_query(
                query=query,
                thread_id=thread_id,
                user_id=user_id,
                co_ceo_agent=co_ceo_agent,
                finance_agent=finance_agent,
                marketing_agent=marketing_agent,
                knowledge_base_service=knowledge_base_service,
                start_department=department,  # Pass the department parameter
                metrics_collector=metrics_collector,  # Pass the metrics collector
                tracing_collector=tracing_collector,  # Pass the tracing collector
            )

            # Prepare citations if available
            citations = []
            if "documents" in result:
                for i, doc in enumerate(result.get("documents", []), 1):
                    citations.append({
                        "id": doc.get("id", f"doc-{i}"),
                        "title": doc.get("metadata", {}).get("title", "Untitled"),
                        "source": doc.get("metadata", {}).get("source", "Unknown")
                    })

            # Display the response
            print_response_panel(
                result.get("response", "No response received."),
                "Multi-Agent Response",
                citations
            )

            # Visualize department routing if analysis is available
            if "analysis" in result and result["analysis"]:
                print_section_divider("Department Routing Visualization", "secondary")
                # Use the departments field from the result, falling back to departments_consulted if available
                departments_to_visualize = result.get("departments", result.get("departments_consulted", []))
                visualize_department_routing(
                    result["analysis"],
                    departments_to_visualize
                )

            # Visualize agent flow
            print_section_divider("Agent Interaction Flow", "secondary")
            visualize_agent_flow(result)

            # Visualize graph execution steps if metrics are available
            if metrics_collector and metrics_collector.events:
                print_section_divider("Graph Execution Steps", "secondary")
                visualize_graph_execution(metrics_collector.events)

            # Display metadata
            if "metadata" in result and result["metadata"]:
                columns = [
                    {"name": "Key", "style": "cyan"},
                    {"name": "Value", "style": "green"}
                ]
                metadata_table = create_table("Response Metadata", columns)

                import json
                for key, value in result["metadata"].items():
                    if isinstance(value, dict):
                        metadata_table.add_row(key, json.dumps(value, indent=2))
                    else:
                        metadata_table.add_row(key, str(value))

                console.print(metadata_table)

            # Display departments consulted
            if "departments_consulted" in result and result["departments_consulted"]:
                print_section_divider("Departments Consulted", "secondary")
                for dept in result["departments_consulted"]:
                    print_info(f"- {dept}")

            print_header("Thread Information", "Use this ID to continue the conversation")
            print_success(f"Thread ID: {thread_id}")

            # Display tracing information if enabled
            if tracing and tracing_collector and tracing_collector.traces:
                print_section_divider("Tracing Visualization", "primary")

                # Save traces to file
                try:
                    # Create traces directory if it doesn't exist
                    import os
                    os.makedirs("traces", exist_ok=True)

                    # Generate filename with timestamp
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
                    filename = f"trace-{thread_id}-{timestamp}.json"
                    trace_file = os.path.join("traces", filename)

                    # Get the traces directly from the collector
                    traces = tracing_collector.traces

                    # Debug print
                    print_info(f"Trace type: {type(traces)}")
                    print_info(f"Trace length: {len(traces)}")
                    if len(traces) > 0:
                        print_info(f"First trace type: {type(traces[0])}")

                    # Ensure all trace events are included
                    # Extract department responses from state if available
                    if result and "department_responses" in result:
                        for dept, response in result["department_responses"].items():
                            response_text = response.get("response", "")
                            response_timestamp = response.get("timestamp", datetime.now().isoformat())

                            # Create a synthetic trace for each department response if not already present
                            dept_response_exists = False
                            for trace in traces:
                                if (trace.get("node_id") == f"{dept}_agent" and
                                    trace.get("event_type") == "agent_response" and
                                    trace.get("metadata", {}).get("department") == dept and
                                    trace.get("metadata", {}).get("response", "").startswith(response_text[:50])):
                                    dept_response_exists = True
                                    break

                            if not dept_response_exists:
                                # Add a query trace if one doesn't exist for this department
                                dept_query_exists = False
                                for trace in traces:
                                    if (trace.get("node_id") == f"{dept}_agent" and
                                        trace.get("event_type") == "agent_query" and
                                        trace.get("metadata", {}).get("department") == dept):
                                        dept_query_exists = True
                                        break

                                if not dept_query_exists:
                                    # Add a synthetic query trace
                                    traces.append({
                                        "timestamp": datetime.now().isoformat(),
                                        "correlation": {
                                            "thread_id": thread_id,
                                            "user_id": user_id,
                                            "session_id": tracing_collector.session_id
                                        },
                                        "node_id": f"{dept}_agent",
                                        "event_type": "agent_query",
                                        "metadata": {
                                            "department": dept,
                                            "query": query,
                                            "timestamp": datetime.now().isoformat()
                                        }
                                    })

                                # Add the response trace
                                traces.append({
                                    "timestamp": response_timestamp,
                                    "correlation": {
                                        "thread_id": thread_id,
                                        "user_id": user_id,
                                        "session_id": tracing_collector.session_id
                                    },
                                    "node_id": f"{dept}_agent",
                                    "event_type": "agent_response",
                                    "metadata": {
                                        "department": dept,
                                        "response": response_text,
                                        "timestamp": response_timestamp
                                    }
                                })

                    # Add a final trace event for the overall process completion
                    traces.append({
                        "timestamp": datetime.now().isoformat(),
                        "correlation": {
                            "thread_id": thread_id,
                            "user_id": user_id,
                            "session_id": tracing_collector.session_id
                        },
                        "node_id": "process_query",
                        "event_type": "complete",
                        "metadata": {
                            "query": query,
                            "response": result.get("response", "")[:200] + "..." if len(result.get("response", "")) > 200 else result.get("response", ""),
                            "departments": result.get("departments", []),
                            "elapsed_time": metrics_collector.metrics["overall"].get("total_time", 0) if metrics_collector else 0
                        }
                    })

                    # Sort traces by timestamp
                    traces.sort(key=lambda x: x.get("timestamp", ""))

                    # Save traces directly
                    import json
                    with open(trace_file, "w") as f:
                        json.dump(traces, f, indent=2, default=str)

                    print_info(f"Traces saved to: {trace_file}")
                except Exception as e:
                    print_warning(f"Could not save traces: {str(e)}")
                    trace_file = None

                # Show agent communication visualization if requested
                if show_agent_communication:
                    print_section_divider("Agent Communications", "secondary")
                    # Check if we have any trace events
                    if tracing_collector and tracing_collector.traces:
                        # Extract routing decision from metadata if available
                        routing_decision = result.get("metadata", {}).get("routing_decision", {})

                        # Add routing decision to trace events if available
                        if routing_decision and "departments" in routing_decision:
                            for trace in tracing_collector.traces:
                                if "metadata" not in trace:
                                    trace["metadata"] = {}
                                trace["metadata"]["routing_decision"] = routing_decision

                        # Extract agent communications from result
                        def extract_agent_communications(result, traces, thread_id, user_id, session_id):
                            """Extract and add agent communications from result to traces."""
                            # Get the query
                            query = result.get("query", "")

                            # Get departments involved
                            departments = result.get("departments", [])

                            # Get department responses
                            department_responses = result.get("department_responses", {})

                            # Check if we have co_ceo routing information
                            if "metadata" in result and "routing_decision" in result["metadata"]:
                                routing_decision = result["metadata"]["routing_decision"]
                                if "departments" in routing_decision:
                                    routed_departments = routing_decision["departments"]

                                    # Add routing trace if not already present - but only once
                                    # We'll use a more strict check to avoid duplicate routing messages
                                    routing_exists = False
                                    routing_message = f"Routing to departments: {', '.join(routed_departments)}"

                                    for trace in traces:
                                        if (trace.get("node_id") == "co_ceo_agent" and
                                            trace.get("event_type") == "agent_response" and
                                            trace.get("metadata", {}).get("response", "") == routing_message):
                                            routing_exists = True
                                            break

                                    if not routing_exists:
                                        # Only add one routing message
                                        traces.append({
                                            "timestamp": datetime.now().isoformat(),
                                            "correlation": {
                                                "thread_id": thread_id,
                                                "user_id": user_id,
                                                "session_id": session_id
                                            },
                                            "node_id": "co_ceo_agent",
                                            "event_type": "agent_response",
                                            "metadata": {
                                                "department": "co_ceo",
                                                "response": routing_message,
                                                "timestamp": datetime.now().isoformat(),
                                                "is_routing": True  # Add a flag to identify routing messages
                                            }
                                        })

                            # Extract department responses from the final response if available
                            final_response = result.get("response", "")
                            if final_response and "Based on the information from our departments:" in final_response:
                                # Parse the response to extract department responses
                                lines = final_response.split("\n")
                                current_dept = None
                                dept_response = ""

                                for line in lines:
                                    if line.startswith("- ") and ":" in line:
                                        # If we were collecting a previous department's response, add it
                                        if current_dept and dept_response:
                                            # Check if this department response already exists
                                            dept_exists = False
                                            for trace in traces:
                                                if (trace.get("node_id") == f"{current_dept}_agent" and
                                                    trace.get("event_type") == "agent_response" and
                                                    trace.get("metadata", {}).get("department") == current_dept):
                                                    dept_exists = True
                                                    break

                                            # Add the department response if it doesn't exist
                                            if not dept_exists:
                                                traces.append({
                                                    "timestamp": datetime.now().isoformat(),
                                                    "correlation": {
                                                        "thread_id": thread_id,
                                                        "user_id": user_id,
                                                        "session_id": session_id
                                                    },
                                                    "node_id": f"{current_dept}_agent",
                                                    "event_type": "agent_response",
                                                    "metadata": {
                                                        "department": current_dept,
                                                        "response": dept_response.strip(),
                                                        "timestamp": datetime.now().isoformat()
                                                    }
                                                })

                                        # Start collecting the next department's response
                                        parts = line.split(":", 1)
                                        if len(parts) == 2:
                                            dept_name = parts[0].replace("- ", "").strip().lower()
                                            current_dept = dept_name
                                            dept_response = parts[1].strip()
                                    elif current_dept:
                                        # Continue collecting the current department's response
                                        dept_response += "\n" + line

                                # Add the last department's response if we were collecting one
                                if current_dept and dept_response:
                                    # Check if this department response already exists
                                    dept_exists = False
                                    for trace in traces:
                                        if (trace.get("node_id") == f"{current_dept}_agent" and
                                            trace.get("event_type") == "agent_response" and
                                            trace.get("metadata", {}).get("department") == current_dept):
                                            dept_exists = True
                                            break

                                    # Add the department response if it doesn't exist
                                    if not dept_exists:
                                        traces.append({
                                            "timestamp": datetime.now().isoformat(),
                                            "correlation": {
                                                "thread_id": thread_id,
                                                "user_id": user_id,
                                                "session_id": session_id
                                            },
                                            "node_id": f"{current_dept}_agent",
                                            "event_type": "agent_response",
                                            "metadata": {
                                                "department": current_dept,
                                                "response": dept_response.strip(),
                                                "timestamp": datetime.now().isoformat()
                                            }
                                        })

                            # Ensure all departments have query and response traces
                            for dept in departments:
                                # Check if department has a query trace
                                dept_query_exists = False
                                for trace in traces:
                                    if (trace.get("node_id") == f"{dept}_agent" and
                                        trace.get("event_type") == "agent_query"):
                                        dept_query_exists = True
                                        break

                                # Add query trace if missing
                                if not dept_query_exists:
                                    traces.append({
                                        "timestamp": datetime.now().isoformat(),
                                        "correlation": {
                                            "thread_id": thread_id,
                                            "user_id": user_id,
                                            "session_id": session_id
                                        },
                                        "node_id": f"{dept}_agent",
                                        "event_type": "agent_query",
                                        "metadata": {
                                            "department": dept,
                                            "query": query,
                                            "timestamp": datetime.now().isoformat()
                                        }
                                    })

                                # Check if department has a response trace
                                if dept in department_responses:
                                    response = department_responses[dept]
                                    response_text = response.get("response", "")

                                    # Always add department response trace to ensure it's visible
                                    # This is important for the visualization
                                    traces.append({
                                        "timestamp": datetime.now().isoformat(),
                                        "correlation": {
                                            "thread_id": thread_id,
                                            "user_id": user_id,
                                            "session_id": session_id
                                        },
                                        "node_id": f"{dept}_agent",
                                        "event_type": "agent_response",
                                        "metadata": {
                                            "department": dept,
                                            "response": response_text,
                                            "timestamp": response.get("timestamp", datetime.now().isoformat())
                                        }
                                    })

                            # Sort traces by timestamp
                            traces.sort(key=lambda x: x.get("timestamp", ""))
                            return traces

                        # Extract and add agent communications
                        traces = extract_agent_communications(
                            result,
                            tracing_collector.traces,
                            thread_id,
                            user_id,
                            tracing_collector.session_id
                        )

                        # Visualize agent communications
                        visualize_agent_communication(
                            traces=traces,
                            show_query=True,
                            show_response=True,
                            show_full_content=show_full_content,
                            show_node_events=show_node_events
                        )
                    else:
                        print_warning("No trace events available for visualization")

                # Show trace summary
                print_section_divider("Trace Summary", "secondary")
                visualize_trace_summary(tracing_collector.traces)

                # Offer to show detailed trace if a file was saved
                if trace_file:
                    print_info("Use the following command to view detailed trace information:")
                    print_info(f"python backend/scripts/view_traces.py {trace_file}")

        except Exception as e:
            print_error(f"Error testing multi-agent orchestration: {str(e)}", e)
            logger.exception("Error in test_multi_agent command")

    # Run the async function
    asyncio.run(run_test())


@app.command()
def run_benchmarks(
    category: Optional[str] = typer.Option(None, help="Benchmark category to run (all if not specified)"),
    provider: str = typer.Option("mock", help="The LLM provider to use (openai, anthropic, gemini, mock)"),
    model: str = typer.Option(None, help="The specific model to use (e.g., gpt-4, claude-3-opus, gemini-pro)"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
    report_format: str = typer.Option("text", help="Report format (text or json)"),
    output_file: Optional[str] = typer.Option(None, help="File to save results to"),
    include_details: bool = typer.Option(True, help="Include detailed results in the report")
):
    """
    Run benchmark queries to evaluate system performance.

    This command runs a set of predefined benchmark queries to evaluate
    the performance of the RAG and multi-agent orchestration system.
    """
    from app.langgraph.graph import process_query

    # Set verbose mode
    set_verbose_mode(verbose)

    async def run_benchmark_test():
        try:
            # Initialize base components with agents
            components = await initialize_components(provider, model, init_agents=True)
            if not components:
                return

            # Initialize benchmark runner
            progress = create_progress()
            with progress:
                task = progress.add_task("[yellow]Initializing benchmark runner...", total=None)
                benchmark_runner = BenchmarkRunner()
                progress.update(task, description="[green]Benchmark runner initialized")

            # Extract components
            llm_adapter = components["llm_adapter"]
            knowledge_base_service = components["knowledge_base_service"]
            agents = components["agents"]

            # Get agent references
            co_ceo_agent = agents["co_ceo"]
            finance_agent = agents["finance"]
            marketing_agent = agents["marketing"]

            # Create a simple agent system interface for the benchmark runner
            class AgentSystem:
                def __init__(self, co_ceo_agent, finance_agent, marketing_agent, knowledge_base_service):
                    self.co_ceo_agent = co_ceo_agent
                    self.finance_agent = finance_agent
                    self.marketing_agent = marketing_agent
                    self.knowledge_base_service = knowledge_base_service

                async def process_query(self, query, metrics_collector=None, **kwargs):
                    # Add metrics collector to state metadata if provided
                    if metrics_collector:
                        if "metadata" not in kwargs:
                            kwargs["metadata"] = {}
                        kwargs["metadata"]["metrics_collector"] = metrics_collector

                    # Process the query using the multi-agent system
                    result = await process_query(
                        query=query,
                        co_ceo_agent=self.co_ceo_agent,
                        finance_agent=self.finance_agent,
                        marketing_agent=self.marketing_agent,
                        knowledge_base_service=self.knowledge_base_service,
                        **kwargs
                    )

                    return result

            # Create agent system
            agent_system = AgentSystem(co_ceo_agent, finance_agent, marketing_agent, knowledge_base_service)

            # Run benchmarks
            print_header("Running Benchmarks")

            if category:
                print_info(f"Running benchmark category: {category}")
                await benchmark_runner.run_category(category, agent_system)
            else:
                print_info("Running all benchmark categories")
                await benchmark_runner.run_all(agent_system)

            # Generate and display report
            print_header("Benchmark Results")
            report = benchmark_runner.generate_report(format=report_format, include_details=include_details)

            if output_file:
                # Save report to file
                if report_format == "text":
                    with open(output_file, "w") as f:
                        f.write(report)
                else:
                    benchmark_runner.save_results(output_file)
                print_success(f"Results saved to {output_file}")

            # Display report
            if report_format == "text":
                console.print(report)
            else:
                # For JSON format, print a summary
                summary = benchmark_runner.generate_report(format="text", include_details=False)
                console.print(summary)

                if not output_file:
                    # If not saved to file, also print the JSON
                    console.print(report)

        except Exception as e:
            print_error(f"Error running benchmarks: {str(e)}", e)
            logger.exception("Error in run_benchmarks command")

    # Run the async function
    asyncio.run(run_benchmark_test())


@app.command()
def interactive_session(
    provider: str = typer.Option("mock", help="The LLM provider to use (openai, anthropic, gemini, mock)"),
    model: str = typer.Option(None, help="The specific model to use (e.g., gpt-4, claude-3-opus, gemini-pro)"),
    user_id: str = typer.Option("cli-user", help="User ID for the session"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
):
    """
    Start an interactive session for testing RAG and multi-agent orchestration.
    """
    import questionary
    from app.langgraph.graph import process_query

    # Set verbose mode
    set_verbose_mode(verbose)

    # Create a completely different approach for interactive mode
    # that doesn't rely on asyncio.run() inside the questionary library

    # Initialize components synchronously first
    try:
        # Initialize components
        print_section_divider("Initializing Components", "primary")

        # Create a new event loop for async initialization
        init_loop = asyncio.new_event_loop()
        try:
            # Run the async initialization in the new loop
            components = init_loop.run_until_complete(initialize_components(provider, model, init_agents=True))
            if not components:
                return

            # Initialize RAG components
            rag_components = init_loop.run_until_complete(initialize_rag_components(components))
            if not rag_components:
                return
        finally:
            # Clean up the event loop
            init_loop.close()

        # Extract components
        llm_adapter = components["llm_adapter"]
        knowledge_base_service = components["knowledge_base_service"]
        agents = components["agents"]

        # Extract RAG components
        query_analyzer = rag_components["query_analyzer"]
        retriever = rag_components["retriever"]
        generator = rag_components["generator"]

        print_success("Initialization complete!")

        # Generate a thread ID for the session
        thread_id = str(uuid.uuid4())
        print_info(f"Session thread ID: {thread_id}")

        # Start interactive loop
        print_header("Interactive Session", "Type 'exit' to quit")

        while True:
            # Get user input
            query = questionary.text("Enter your query:").ask()

            if query.lower() in ["exit", "quit", "q"]:
                break

            # Choose mode
            mode = questionary.select(
                "Choose processing mode:",
                choices=["RAG Only", "Multi-Agent Orchestration", "Both"]
            ).ask()

            # Format the query
            format_query(query)

            # Create a new event loop for each query
            query_loop = asyncio.new_event_loop()

            try:
                if mode == "RAG Only" or mode == "Both":
                    # Test RAG
                    print_section_divider("Testing RAG", "secondary")

                    # Import RAG components
                    from app.rag.retriever import HybridRetriever, ContextWindowManager
                    from app.rag.generator import RAGGenerator
                    from app.rag.query_analyzer import QueryAnalyzer

                    # Initialize RAG components
                    print_verbose("Initializing RAG components")
                    query_analyzer = QueryAnalyzer(llm_adapter=llm_adapter)
                    retriever = HybridRetriever(
                        query_analyzer=query_analyzer,
                        knowledge_base_service=knowledge_base_service,
                        default_vector_weight=0.7,
                        default_keyword_weight=0.3
                    )
                    context_window_manager = ContextWindowManager(
                        llm_adapter=llm_adapter,
                        max_tokens=4000,
                        token_buffer=1000
                    )
                    retriever.context_window_manager = context_window_manager
                    generator = RAGGenerator(
                        llm_adapter=llm_adapter,
                        include_citations=True,
                        citation_format="inline"
                    )

                    # Process query with RAG
                    format_step(1, "Analyzing query")
                    analysis = query_loop.run_until_complete(query_analyzer.analyze(query))

                    format_step(2, "Retrieving documents")
                    documents = query_loop.run_until_complete(
                        retriever.retrieve(
                            query=query,
                            retrieval_strategy=analysis.get("retrieval_strategy", {}),
                            limit=5
                        )
                    )

                    # Display retrieved documents
                    if documents:
                        print_success(f"Retrieved {len(documents)} documents")

                        columns = [
                            {"name": "ID", "style": "cyan"},
                            {"name": "Content Preview", "style": "yellow"},
                            {"name": "Score", "style": "green"}
                        ]
                        docs_table = create_table("Retrieved Documents", columns)

                        for doc in documents:
                            # Truncate content for preview
                            content = doc.get("text", "")
                            content_preview = content[:100] + "..." if len(content) > 100 else content

                            docs_table.add_row(
                                doc.get("id", "Unknown"),
                                content_preview,
                                f"{doc.get('score', 0):.4f}"
                            )

                        console.print(docs_table)
                    else:
                        print_warning("No documents retrieved")

                    # Generate response
                    format_step(3, "Generating response")
                    rag_response = query_loop.run_until_complete(generator.generate(query, documents))

                    # Prepare citations
                    citations = []
                    for i, doc in enumerate(documents, 1):
                        citations.append({
                            "id": doc.get("id", f"doc-{i}"),
                            "title": doc.get("metadata", {}).get("title", "Untitled"),
                            "source": doc.get("metadata", {}).get("source", "Unknown")
                        })

                    # Display response
                    print_response_panel(rag_response, "RAG Response", citations)

                if mode == "Multi-Agent Orchestration" or mode == "Both":
                    # Test multi-agent
                    print_section_divider("Testing Multi-Agent Orchestration", "primary")

                    # Process query with multi-agent
                    co_ceo_agent = agents.get("co_ceo")
                    finance_agent = agents.get("finance")
                    marketing_agent = agents.get("marketing")

                    # Ask if user wants to specify a department
                    use_specific_department = questionary.confirm(
                        "Do you want to start with a specific department?",
                        default=False
                    ).ask()

                    start_department = None
                    if use_specific_department:
                        start_department = questionary.select(
                            "Select department:",
                            choices=["co_ceo", "finance", "marketing"]
                        ).ask()
                        print_info(f"Starting with department: {start_department}")

                    # Initialize tracing
                    metrics_collector, tracing_collector, _ = initialize_tracing(
                        tracing_enabled=True,
                        tracing_level="standard",
                        user_id=user_id,
                        thread_id=thread_id
                    )

                    print_verbose(f"Using thread ID: {thread_id}")
                    result = query_loop.run_until_complete(
                        process_query(
                            query=query,
                            thread_id=thread_id,
                            user_id=user_id,
                            co_ceo_agent=co_ceo_agent,
                            finance_agent=finance_agent,
                            marketing_agent=marketing_agent,
                            knowledge_base_service=knowledge_base_service,
                            start_department=start_department,
                            metrics_collector=metrics_collector,
                            tracing_collector=tracing_collector
                        )
                    )

                    # Prepare citations if available
                    citations = []
                    if "documents" in result:
                        for i, doc in enumerate(result.get("documents", []), 1):
                            citations.append({
                                "id": doc.get("id", f"doc-{i}"),
                                "title": doc.get("metadata", {}).get("title", "Untitled"),
                                "source": doc.get("metadata", {}).get("source", "Unknown")
                            })

                    # Display response
                    print_response_panel(
                        result.get("response", "No response received."),
                        "Multi-Agent Response",
                        citations
                    )

                    # Visualize department routing if analysis is available
                    if "analysis" in result and result["analysis"]:
                        print_section_divider("Department Routing Visualization", "secondary")
                        # Use the departments field from the result, falling back to departments_consulted if available
                        departments_to_visualize = result.get("departments", result.get("departments_consulted", []))
                        visualize_department_routing(
                            result["analysis"],
                            departments_to_visualize
                        )

                    # Visualize agent flow
                    print_section_divider("Agent Interaction Flow", "secondary")
                    visualize_agent_flow(result)

                    # Visualize graph execution steps if metrics are available
                    if metrics_collector and metrics_collector.events:
                        print_section_divider("Graph Execution Steps", "secondary")
                        visualize_graph_execution(metrics_collector.events)

                    # Display departments consulted
                    if "departments_consulted" in result and result["departments_consulted"]:
                        print_section_divider("Departments Consulted", "secondary")
                        for dept in result["departments_consulted"]:
                            print_info(f"- {dept}")

                    # Display agent communications if tracing is available
                    if tracing_collector and tracing_collector.traces:
                        print_section_divider("Agent Communications", "secondary")
                        visualize_agent_communication(tracing_collector.traces)
            finally:
                # Clean up the query loop
                query_loop.close()

            # Ask if user wants to continue
            continue_session = questionary.confirm("Continue session?").ask()
            if not continue_session:
                break

        print_header("Session Ended", "Thank you for using the interactive session")

    except Exception as e:
        print_error(f"Error in interactive session: {str(e)}", e)
        logger.exception("Error in interactive_session command")


if __name__ == "__main__":
    app()
