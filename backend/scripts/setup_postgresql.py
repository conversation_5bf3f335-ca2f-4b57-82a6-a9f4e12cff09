#!/usr/bin/env python
"""
PostgreSQL Setup Script

This script sets up the PostgreSQL database for the BusinessLM application.
It creates the necessary tables, extensions, and indexes.
"""

import os
import sys
import logging
import argparse
from sqlalchemy import create_engine, text

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.config import get_settings
from app.core.db.database import Base, engine
from app.core.db.models import User, Document, DocumentChunk, ConversationCheckpoint, RefreshToken
from app.core.auth.password import hash_password

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_pgvector():
    """Set up the pgvector extension."""
    try:
        with engine.connect() as conn:
            # Create the pgvector extension
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            conn.commit()
            logger.info("pgvector extension created successfully")
    except Exception as e:
        logger.error(f"Error creating pgvector extension: {e}")
        raise


def create_tables():
    """Create all tables defined in the models."""
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise


def create_indexes():
    """Create indexes for better performance."""
    try:
        with engine.connect() as conn:
            # Create index on document_chunks.document_id
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id
                ON document_chunks(document_id)
            """))

            # Create index on conversation_checkpoints.thread_id
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_conversation_checkpoints_thread_id
                ON conversation_checkpoints(thread_id)
            """))

            # Create index on refresh_tokens.user_id
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id
                ON refresh_tokens(user_id)
            """))

            # Create indexes for query_logs table
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_query_logs_timestamp
                ON query_logs(timestamp)
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_query_logs_user_id
                ON query_logs(user_id)
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_query_logs_thread_id
                ON query_logs(thread_id)
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_query_logs_session_id
                ON query_logs(session_id)
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_query_logs_department
                ON query_logs(department)
            """))

            # Create vector index for query_logs if table exists
            try:
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_query_logs_embedding
                    ON query_logs USING hnsw (embedding vector_cosine_ops)
                """))
                logger.info("Vector index created for query_logs table")
            except Exception as e:
                logger.warning(f"Could not create vector index for query_logs: {e}")
                logger.warning("This is normal if the table doesn't exist yet or pgvector is not installed")

            conn.commit()
            logger.info("Database indexes created successfully")
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")
        raise


def create_test_user(email, password):
    """Create a test user for development."""
    from sqlalchemy.orm import sessionmaker

    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Check if user already exists
        existing_user = session.query(User).filter(User.email == email).first()
        if existing_user:
            logger.info(f"Test user {email} already exists")
            return

        # Create a new user
        hashed_password = hash_password(password)
        user = User(
            email=email,
            hashed_password=hashed_password,
            is_active=True,
            email_verified=True,
            roles=["admin"],
            permissions=["read", "write", "admin"]
        )
        session.add(user)
        session.commit()
        logger.info(f"Test user {email} created successfully")
    except Exception as e:
        session.rollback()
        logger.error(f"Error creating test user: {e}")
        raise
    finally:
        session.close()


def main():
    """Main function to set up the database."""
    parser = argparse.ArgumentParser(description='Set up the PostgreSQL database')
    parser.add_argument('--create-test-user', action='store_true', help='Create a test user')
    parser.add_argument('--email', default='<EMAIL>', help='Email for test user')
    parser.add_argument('--password', default='secure123', help='Password for test user')
    args = parser.parse_args()

    try:
        # Set up pgvector extension
        setup_pgvector()

        # Create tables
        create_tables()

        # Create indexes
        create_indexes()

        # Create test user if requested
        if args.create_test_user:
            create_test_user(args.email, args.password)

        logger.info("Database setup completed successfully")
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
