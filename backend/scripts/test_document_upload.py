#!/usr/bin/env python
"""
Test Document Upload Script

This script tests the document upload functionality by creating a test document
and adding it to the knowledge base.
"""

import os
import sys
import logging
import asyncio
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.rag import initialize_knowledge_base_service
from app.core.db.database import get_db_context
from app.core.db.models import Document, DocumentChunk
from sqlalchemy import select

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_document_upload():
    """
    Test document upload functionality.
    """
    try:
        # Initialize knowledge base service
        logger.info("Initializing knowledge base service...")
        knowledge_base_service = await initialize_knowledge_base_service()
        logger.info("Knowledge base service initialized successfully")

        # Create a test document
        logger.info("Creating test document...")
        test_document = {
            "title": "Test Document",
            "content": "This is a test document for the BusinessLM system. It contains information about artificial intelligence and machine learning.",
            "metadata": {
                "author": "Test User",
                "created_at": datetime.now().isoformat(),
                "tags": ["test", "ai", "ml"]
            }
        }

        # Add document to knowledge base
        logger.info("Adding document to knowledge base...")
        doc_id = await knowledge_base_service.add_document(test_document)
        logger.info(f"Document added successfully with ID: {doc_id}")

        # Verify document was added to database
        logger.info("Verifying document was added to database...")
        with get_db_context() as db:
            # Query document
            document = db.execute(
                select(Document).where(Document.id == uuid.UUID(doc_id))
            ).scalar_one_or_none()

            if document:
                logger.info(f"Document found in database: {document.title}")
                
                # Query document chunks
                chunks = db.execute(
                    select(DocumentChunk).where(DocumentChunk.document_id == document.id)
                ).scalars().all()
                
                logger.info(f"Document has {len(chunks)} chunks")
                
                # Check if chunks have embeddings
                chunks_with_embeddings = [chunk for chunk in chunks if chunk.embedding is not None]
                logger.info(f"{len(chunks_with_embeddings)} out of {len(chunks)} chunks have embeddings")
                
                # Print first chunk for verification
                if chunks:
                    logger.info(f"First chunk: {chunks[0].content[:100]}...")
                    logger.info(f"First chunk embedding dimension: {len(chunks[0].embedding) if chunks[0].embedding else 'No embedding'}")
            else:
                logger.error(f"Document not found in database with ID: {doc_id}")

        # Search for the document
        logger.info("Searching for the document...")
        search_results = await knowledge_base_service.search("artificial intelligence")
        logger.info(f"Search returned {len(search_results)} results")
        
        if search_results:
            logger.info(f"First search result: {search_results[0]}")

        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Error testing document upload: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(test_document_upload())
