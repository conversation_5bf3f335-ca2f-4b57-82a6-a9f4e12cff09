#!/usr/bin/env python
"""
Test Server Script

This script tests the server by starting it and making a request to the health endpoint.
"""

import sys
import os
import asyncio
import uvicorn
import threading
import time
import requests
from contextlib import contextmanager

# Add the backend directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.main import app


@contextmanager
def run_server_in_thread(host="127.0.0.1", port=8001):
    """
    Run the server in a separate thread.
    """
    # Create a thread to run the server
    server_thread = threading.Thread(
        target=uvicorn.run,
        args=(app,),
        kwargs={"host": host, "port": port}
    )
    
    # Start the server
    server_thread.daemon = True
    server_thread.start()
    
    # Wait for the server to start
    time.sleep(2)
    
    try:
        # Yield control back to the caller
        yield
    finally:
        # No need to stop the server, it will be stopped when the script exits
        pass


def test_health_endpoint():
    """
    Test the health endpoint.
    """
    try:
        response = requests.get("http://127.0.0.1:8001/health")
        print(f"Health endpoint response: {response.status_code}")
        print(f"Response body: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing health endpoint: {e}")
        return False


def main():
    """Main function."""
    print("Starting server...")
    with run_server_in_thread():
        print("Server started, testing health endpoint...")
        if test_health_endpoint():
            print("Health endpoint test passed!")
        else:
            print("Health endpoint test failed!")


if __name__ == "__main__":
    main()
