#!/usr/bin/env python
"""
Run Database Migrations Script

This script runs the Alembic migrations to set up or update the database schema.
"""

import os
import sys
import logging
import argparse
import subprocess

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.config import get_settings

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_migrations(revision='head', sql=False):
    """
    Run Alembic migrations.

    Args:
        revision: Revision to upgrade to (default: 'head')
        sql: Whether to output SQL instead of running migrations
    """
    try:
        # Check if the database is available
        from sqlalchemy import create_engine
        from sqlalchemy.exc import OperationalError

        engine = create_engine(get_settings().DATABASE_URL)
        try:
            with engine.connect() as conn:
                logger.info("Database connection successful")
        except OperationalError as e:
            logger.error(f"Database connection failed: {e}")
            logger.error("Please make sure the database is running and the connection URL is correct")
            sys.exit(1)

        # Build the Alembic command
        cmd = ['alembic', 'upgrade']
        if sql:
            cmd.append('--sql')
        cmd.append(revision)

        # Run the Alembic command
        logger.info(f"Running migrations: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info("Migrations completed successfully")
            if result.stdout:
                logger.info(f"Output:\n{result.stdout}")
        else:
            logger.error(f"Migrations failed with exit code {result.returncode}")
            if result.stdout:
                logger.error(f"Output:\n{result.stdout}")
            if result.stderr:
                logger.error(f"Error:\n{result.stderr}")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        sys.exit(1)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Run database migrations')
    parser.add_argument('--revision', default='head', help='Revision to upgrade to (default: head)')
    parser.add_argument('--sql', action='store_true', help='Output SQL instead of running migrations')
    args = parser.parse_args()

    run_migrations(args.revision, args.sql)


if __name__ == '__main__':
    main()
