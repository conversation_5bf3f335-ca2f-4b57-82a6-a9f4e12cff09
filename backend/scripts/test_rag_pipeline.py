"""
Test RAG Pipeline

This script tests the complete RAG pipeline with PostgreSQL + pgvector.
"""

import asyncio
import logging
import sys
import os

# Add the parent directory to the path so we can import the app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.config import get_settings
from app.rag.embedding_utils import get_embedding_model
from app.rag.vector_store import get_vector_store
from app.rag.knowledge_base_factory import get_knowledge_base_service
from app.rag.retriever import HybridRetriever, QueryAnalyzer
from app.core.llm.factory import get_llm_adapter
from app.rag.generator import RAGGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


async def test_rag_pipeline():
    """Test the complete RAG pipeline."""
    logger.info("Testing RAG pipeline...")
    
    try:
        # Initialize components
        logger.info("Initializing embedding model...")
        embedding_model = get_embedding_model()
        
        logger.info("Initializing vector store...")
        vector_store = get_vector_store(
            store_type=get_settings().VECTOR_STORE_TYPE,
            dimension=get_settings().VECTOR_DIMENSION
        )
        
        logger.info("Initializing knowledge base service...")
        knowledge_base_service = await get_knowledge_base_service()
        
        logger.info("Initializing query analyzer...")
        query_analyzer = QueryAnalyzer()
        
        logger.info("Initializing retriever...")
        retriever = HybridRetriever(
            query_analyzer=query_analyzer,
            knowledge_base_service=knowledge_base_service
        )
        
        logger.info("Initializing LLM adapter...")
        llm_adapter = get_llm_adapter()
        
        logger.info("Initializing generator...")
        generator = RAGGenerator(llm_adapter=llm_adapter)
        
        # Test queries
        test_queries = [
            "What is RAG?",
            "How does vector search work?",
            "What are the benefits of using PostgreSQL with pgvector?",
            "Explain the difference between cosine similarity and Euclidean distance",
            "What is the role of embedding models in RAG?"
        ]
        
        for query in test_queries:
            logger.info(f"\n\nTesting query: {query}")
            
            # Retrieve relevant documents
            logger.info("Retrieving documents...")
            documents = await retriever.retrieve(query, limit=3)
            
            logger.info(f"Retrieved {len(documents)} documents")
            for i, doc in enumerate(documents):
                logger.info(f"Document {i+1}:")
                logger.info(f"  Score: {doc['score']:.4f}")
                logger.info(f"  Text: {doc['text'][:100]}...")
            
            # Generate response
            logger.info("Generating response...")
            response = await generator.generate(query, documents)
            
            logger.info(f"Response: {response}")
            
        logger.info("\nRAG pipeline test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error testing RAG pipeline: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(test_rag_pipeline())
