#!/usr/bin/env python
"""
Test PostgreSQL Checkpointing

This script tests the PostgreSQL implementation of the LangGraph checkpointing system.
It creates a test state, saves it to PostgreSQL, loads it back, and verifies that
the state is preserved correctly.

Usage:
    python -m backend.scripts.test_postgresql_checkpointing
"""

import os
import sys
import logging
import uuid
from datetime import datetime, timezone

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.langgraph.state import AgentState, create_initial_state
from app.langgraph.checkpointing import PostgreSQLCheckpointer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def create_test_state():
    """Create a test state with sample data."""
    # Create a unique thread ID
    thread_id = str(uuid.uuid4())
    
    # Create initial state
    state = create_initial_state(
        query="What is our marketing budget for Q4?",
        user_id="test-user",
        thread_id=thread_id
    )
    
    # Add some test data
    state.add_department("marketing")
    state.add_department("finance")
    
    state.add_message("user", "What is our marketing budget for Q4?")
    state.add_message("assistant", "Let me check that for you.")
    
    state.update_analysis("marketing", 0.9)
    state.update_analysis("finance", 0.8)
    
    state.query_analysis = {
        "relevant_departments": ["marketing", "finance"],
        "keywords": ["marketing", "budget", "Q4"],
        "intent": "budget_inquiry"
    }
    
    state.retrieved_knowledge = [
        {
            "id": "doc1",
            "text": "The Q4 marketing budget is $500,000.",
            "metadata": {"source": "budget_report.pdf", "page": 5}
        },
        {
            "id": "doc2",
            "text": "Marketing expenses for Q4 are allocated as follows: digital ads $200,000, events $150,000, content $100,000, other $50,000.",
            "metadata": {"source": "marketing_plan.pdf", "page": 12}
        }
    ]
    
    state.department_responses = {
        "marketing": {
            "response": "The Q4 marketing budget is $500,000, allocated across digital ads, events, content creation, and other initiatives.",
            "department": "marketing",
            "timestamp": datetime.now(timezone.utc).isoformat()
        },
        "finance": {
            "response": "According to our financial records, the approved Q4 marketing budget is $500,000, which is a 10% increase from Q3.",
            "department": "finance",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    }
    
    state.response = "The Q4 marketing budget is $500,000, which represents a 10% increase from Q3. This budget is allocated across digital ads ($200,000), events ($150,000), content creation ($100,000), and other marketing initiatives ($50,000)."
    
    return state, thread_id


def test_postgresql_checkpointing():
    """Test the PostgreSQL checkpointing functionality."""
    logger.info("Testing PostgreSQL checkpointing...")
    
    try:
        # Create a PostgreSQL checkpointer
        checkpointer = PostgreSQLCheckpointer()
        logger.info("Created PostgreSQL checkpointer")
        
        # Create a test state
        state, thread_id = create_test_state()
        logger.info(f"Created test state with thread ID: {thread_id}")
        
        # Save the state
        success = checkpointer.save_checkpoint(thread_id, state)
        if not success:
            logger.error("Failed to save checkpoint")
            return False
        logger.info("Saved checkpoint to PostgreSQL")
        
        # List checkpoints
        checkpoints = checkpointer.list_checkpoints(thread_id)
        logger.info(f"Found {len(checkpoints)} checkpoints for thread ID: {thread_id}")
        for i, checkpoint in enumerate(checkpoints):
            logger.info(f"Checkpoint {i+1}: {checkpoint['id']} ({checkpoint['timestamp']})")
        
        # Load the state
        loaded_state = checkpointer.load_checkpoint(thread_id)
        if loaded_state is None:
            logger.error("Failed to load checkpoint")
            return False
        logger.info("Loaded checkpoint from PostgreSQL")
        
        # Verify the state
        assert loaded_state.query == state.query, "Query mismatch"
        assert loaded_state.departments == state.departments, "Departments mismatch"
        assert len(loaded_state.messages) == len(state.messages), "Messages count mismatch"
        assert loaded_state.query_analysis == state.query_analysis, "Query analysis mismatch"
        assert len(loaded_state.retrieved_knowledge) == len(state.retrieved_knowledge), "Retrieved knowledge count mismatch"
        assert loaded_state.department_responses.keys() == state.department_responses.keys(), "Department responses keys mismatch"
        assert loaded_state.response == state.response, "Response mismatch"
        
        logger.info("✅ State verification successful")
        
        # Delete the checkpoint
        success = checkpointer.delete_checkpoint(thread_id)
        if not success:
            logger.error("Failed to delete checkpoint")
            return False
        logger.info("Deleted checkpoint from PostgreSQL")
        
        # Verify deletion
        checkpoints = checkpointer.list_checkpoints(thread_id)
        assert len(checkpoints) == 0, "Checkpoint was not deleted"
        logger.info("✅ Checkpoint deletion successful")
        
        logger.info("✅ PostgreSQL checkpointing test completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error testing PostgreSQL checkpointing: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    # Set environment variable to use PostgreSQL checkpointer
    os.environ["CHECKPOINTER_TYPE"] = "postgres"
    
    success = test_postgresql_checkpointing()
    sys.exit(0 if success else 1)
