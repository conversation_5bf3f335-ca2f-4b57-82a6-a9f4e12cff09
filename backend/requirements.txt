# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.in --output-file requirements.txt
alembic==1.15.2
    # via -r requirements.in
annotated-types==0.7.0
    # via pydantic
anthropic==0.50.0
    # via -r requirements.in
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via posthog
bcrypt==4.3.0
    # via
    #   chromadb
    #   passlib
black==25.1.0
    # via -r requirements.in
build==1.2.2.post1
    # via
    #   chromadb
    #   pip-tools
cachecontrol==0.14.2
    # via firebase-admin
cachetools==5.5.2
    # via google-auth
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via requests
chroma-hnswlib==0.7.6
    # via chromadb
chromadb==1.0.7
    # via -r requirements.in
click==8.1.8
    # via
    #   black
    #   nltk
    #   pip-tools
    #   typer
    #   uvicorn
coloredlogs==15.0.1
    # via onnxruntime
cryptography==44.0.2
    # via pyjwt
deprecated==1.2.18
    # via
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-semantic-conventions
distro==1.9.0
    # via
    #   anthropic
    #   openai
    #   posthog
durationpy==0.9
    # via kubernetes
ecdsa==0.19.1
    # via python-jose
faiss-cpu==1.7.4
    # via -r requirements.in
fastapi==0.115.9
    # via
    #   -r requirements.in
    #   chromadb
filelock==3.18.0
    # via
    #   huggingface-hub
    #   torch
    #   transformers
firebase-admin==6.8.0
    # via -r requirements.in
flake8==7.2.0
    # via -r requirements.in
flatbuffers==25.2.10
    # via onnxruntime
fsspec==2025.3.2
    # via
    #   huggingface-hub
    #   torch
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.24.2
    # via
    #   firebase-admin
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
    #   google-generativeai
google-api-python-client==2.168.0
    # via
    #   firebase-admin
    #   google-generativeai
google-auth==2.39.0
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
    #   google-generativeai
    #   kubernetes
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-cloud-core==2.4.3
    # via
    #   google-cloud-firestore
    #   google-cloud-storage
google-cloud-firestore==2.20.2
    # via firebase-admin
google-cloud-storage==3.1.0
    # via firebase-admin
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-generativeai==0.8.5
    # via -r requirements.in
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
grpcio==1.71.0
    # via
    #   chromadb
    #   google-api-core
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   anthropic
    #   chromadb
    #   langgraph-sdk
    #   langsmith
    #   openai
huggingface-hub==0.30.2
    # via
    #   sentence-transformers
    #   tokenizers
    #   transformers
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
importlib-metadata==8.6.1
    # via opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
iniconfig==2.1.0
    # via pytest
isort==6.0.1
    # via -r requirements.in
jinja2==3.1.6
    # via torch
jiter==0.9.0
    # via
    #   anthropic
    #   openai
joblib==1.4.2
    # via
    #   nltk
    #   scikit-learn
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via chromadb
jsonschema-specifications==2025.4.1
    # via jsonschema
kubernetes==32.0.1
    # via chromadb
langchain==0.3.24
    # via -r requirements.in
langchain-core==0.3.56
    # via
    #   -r requirements.in
    #   langchain
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.3.34
    # via -r requirements.in
langgraph-checkpoint==2.0.25
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.1.8
    # via langgraph
langgraph-sdk==0.1.63
    # via langgraph
langsmith==0.3.37
    # via
    #   langchain
    #   langchain-core
mako==1.3.10
    # via alembic
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
mccabe==0.7.0
    # via flake8
mdurl==0.1.2
    # via markdown-it-py
mmh3==5.1.0
    # via chromadb
monotonic==1.6
    # via posthog
mpmath==1.3.0
    # via sympy
msgpack==1.1.0
    # via cachecontrol
mypy-extensions==1.1.0
    # via black
networkx==3.4.2
    # via torch
nltk==3.9.1
    # via sentence-transformers
numpy==1.24.3
    # via
    #   -r requirements.in
    #   chroma-hnswlib
    #   chromadb
    #   onnxruntime
    #   pgvector
    #   scikit-learn
    #   scipy
    #   sentence-transformers
    #   torchvision
    #   transformers
oauthlib==3.2.2
    # via
    #   kubernetes
    #   requests-oauthlib
onnxruntime==1.21.1
    # via chromadb
openai==1.76.0
    # via -r requirements.in
opentelemetry-api==1.32.1
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.32.1
    # via opentelemetry-exporter-otlp-proto-grpc
opentelemetry-exporter-otlp-proto-grpc==1.32.1
    # via chromadb
opentelemetry-instrumentation==0.53b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.53b1
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.53b1
    # via chromadb
opentelemetry-proto==1.32.1
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk==1.32.1
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-semantic-conventions==0.53b1
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
opentelemetry-util-http==0.53b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.10.16
    # via
    #   chromadb
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.9.1
    # via langgraph-checkpoint
overrides==7.7.0
    # via chromadb
packaging==24.2
    # via
    #   black
    #   build
    #   huggingface-hub
    #   langchain-core
    #   langsmith
    #   onnxruntime
    #   opentelemetry-instrumentation
    #   pytest
    #   transformers
passlib==1.7.4
    # via -r requirements.in
pathspec==0.12.1
    # via black
pgvector==0.4.1
    # via -r requirements.in
pillow==11.2.1
    # via torchvision
pip==25.1
    # via pip-tools
pip-tools==7.4.1
    # via -r requirements.in
platformdirs==4.3.7
    # via black
pluggy==1.5.0
    # via pytest
posthog==4.0.0
    # via chromadb
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-firestore
protobuf==5.29.4
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-firestore
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   onnxruntime
    #   opentelemetry-proto
    #   proto-plus
psycopg2-binary==2.9.10
    # via -r requirements.in
pyasn1==0.4.8
    # via
    #   pyasn1-modules
    #   python-jose
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pycodestyle==2.13.0
    # via flake8
pycparser==2.22
    # via cffi
pydantic==2.11.3
    # via
    #   anthropic
    #   chromadb
    #   fastapi
    #   google-generativeai
    #   langchain
    #   langchain-core
    #   langsmith
    #   openai
    #   pydantic-settings
pydantic-core==2.33.1
    # via pydantic
pydantic-settings==2.9.1
    # via -r requirements.in
pyflakes==3.3.2
    # via flake8
pygments==2.19.1
    # via rich
pyjwt==2.10.1
    # via firebase-admin
pyparsing==3.2.3
    # via httplib2
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   -r requirements.in
    #   pytest-asyncio
pytest-asyncio==0.26.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   kubernetes
    #   posthog
python-dotenv==1.1.0
    # via
    #   -r requirements.in
    #   pydantic-settings
    #   uvicorn
python-jose==3.4.0
    # via -r requirements.in
python-multipart==0.0.20
    # via -r requirements.in
pyyaml==6.0.2
    # via
    #   chromadb
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-core
    #   transformers
    #   uvicorn
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   nltk
    #   tiktoken
    #   transformers
requests==2.32.3
    # via
    #   -r requirements.in
    #   cachecontrol
    #   google-api-core
    #   google-cloud-storage
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langsmith
    #   posthog
    #   requests-oauthlib
    #   requests-toolbelt
    #   tiktoken
    #   transformers
requests-oauthlib==2.0.0
    # via kubernetes
requests-toolbelt==1.0.0
    # via langsmith
rich==14.0.0
    # via
    #   -r requirements.in
    #   chromadb
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via
    #   google-auth
    #   python-jose
safetensors==0.5.3
    # via transformers
scikit-learn==1.6.1
    # via sentence-transformers
scipy==1.15.2
    # via
    #   scikit-learn
    #   sentence-transformers
sentence-transformers==2.2.2
    # via -r requirements.in
sentencepiece==0.2.0
    # via sentence-transformers
setuptools==80.0.0
    # via
    #   pip-tools
    #   torch
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   ecdsa
    #   kubernetes
    #   posthog
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
sqlalchemy==2.0.40
    # via
    #   -r requirements.in
    #   alembic
    #   langchain
starlette==0.45.3
    # via fastapi
sympy==1.14.0
    # via
    #   onnxruntime
    #   torch
tenacity==9.1.2
    # via
    #   -r requirements.in
    #   chromadb
    #   langchain-core
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.9.0
    # via -r requirements.in
tokenizers==0.21.1
    # via
    #   chromadb
    #   transformers
torch==2.7.0
    # via
    #   -r requirements.in
    #   sentence-transformers
    #   torchvision
torchvision==0.22.0
    # via sentence-transformers
tqdm==4.67.1
    # via
    #   chromadb
    #   google-generativeai
    #   huggingface-hub
    #   nltk
    #   openai
    #   sentence-transformers
    #   transformers
transformers==4.51.3
    # via
    #   -r requirements.in
    #   sentence-transformers
typer==0.15.2
    # via
    #   -r requirements.in
    #   chromadb
typing-extensions==4.13.2
    # via
    #   alembic
    #   anthropic
    #   chromadb
    #   fastapi
    #   google-generativeai
    #   huggingface-hub
    #   langchain-core
    #   openai
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   torch
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   pydantic
    #   pydantic-settings
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.4.0
    # via
    #   kubernetes
    #   requests
uv==0.6.17
    # via -r requirements.in
uvicorn==0.34.2
    # via
    #   -r requirements.in
    #   chromadb
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.5
    # via uvicorn
websocket-client==1.8.0
    # via kubernetes
websockets==15.0.1
    # via uvicorn
wheel==0.45.1
    # via pip-tools
wrapt==1.17.2
    # via
    #   deprecated
    #   opentelemetry-instrumentation
xxhash==3.5.0
    # via langgraph
zipp==3.21.0
    # via importlib-metadata
zstandard==0.23.0
    # via langsmith
