#!/bin/bash
# <PERSON>ript to check PostgreSQL database tables and structure

# Database connection parameters
DB_NAME="businesslm"
DB_USER="mateusdobele"

echo "Checking PostgreSQL database tables..."

# Check if embeddings table exists
echo -e "\n--- Checking embeddings table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'embeddings');"

# Check columns in embeddings table
echo -e "\n--- Columns in embeddings table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'embeddings';"

# Check if there are any rows in the embeddings table
echo -e "\n--- Number of rows in embeddings table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) FROM embeddings;"

# Check if documents table exists
echo -e "\n--- Checking documents table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'documents');"

# Check columns in documents table
echo -e "\n--- Columns in documents table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'documents';"

# Check if there are any rows in the documents table
echo -e "\n--- Number of rows in documents table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT COUNT(*) FROM documents;"

# Check sample data from embeddings table
echo -e "\n--- Sample data from embeddings table ---"
psql -U $DB_USER -d $DB_NAME -c "SELECT id, text, meta_info FROM embeddings LIMIT 3;"
