#!/bin/bash

# Script to run the BusinessLM CLI

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not installed."
    exit 1
fi

# Check if the backend directory exists
if [ ! -d "backend" ]; then
    echo "Error: 'backend' directory not found."
    exit 1
fi

# Check if the CLI file exists
if [ ! -f "backend/app/cli.py" ]; then
    echo "Error: CLI file 'backend/app/cli.py' not found."
    exit 1
fi

# Check if a virtual environment exists
if [ -d "backend_env" ]; then
    # Activate the virtual environment
    source backend_env/bin/activate
elif [ -d "venv" ]; then
    # Activate the virtual environment
    source venv/bin/activate
else
    echo "Warning: No virtual environment detected. It's recommended to create one."
    echo "You can create one with: python -m venv backend_env"
    echo "Then activate it with: source backend_env/bin/activate"
    echo "And install dependencies with: pip install -r backend/requirements.txt"
    
    # Ask if user wants to continue
    read -p "Continue without virtual environment? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run the CLI with the provided arguments
cd backend
python -m app.cli "$@"
