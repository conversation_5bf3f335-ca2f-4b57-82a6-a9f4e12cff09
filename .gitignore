# TypeScript

# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage
/logs
/test-expenses

# production
/dist
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.vite/

# Environment files
.env
.env.*
.env.local
.env.development
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Security-sensitive files
*.pem
*.key
*.cert
*.crt
firebase-service-account.json
*-service-account.json
*-credentials.json
*-keyfile.json

# Python
venv/
.uv/
__pycache__/
*.py[cod]
*.pyo
*.pyd

# Jupyter Notebook
.ipynb_checkpoints/

# Unit test cache
.pytest_cache/# Cleanup script backup directories
pre_cleanup_backup_*/
.env
typescript_backend_backup/

# Ignore local TypeScript cleanup backups
.backups/

# Ignore user-specific directories
~/

# Ignore log files
*.log
backend/server.log

# Ignore local database files
backend/data/checkpoints.db
