# Installing PostgreSQL and pgvector

This guide provides step-by-step instructions for installing PostgreSQL and the pgvector extension on macOS, which are required for the BusinessLM Python backend.

## Prerequisites

- macOS (this guide is specific to macOS, but similar steps apply to Linux and Windows)
- Homebrew package manager
- Command line access

## Installing PostgreSQL

### 1. Install PostgreSQL using Homebrew

```bash
brew install postgresql@15
```

This command installs PostgreSQL 15, which is the recommended version for our application.

### 2. Start the PostgreSQL service

```bash
brew services start postgresql@15
```

This command starts PostgreSQL as a background service that will automatically restart when you reboot your computer.

### 3. Verify the installation

```bash
psql --version
```

You should see output indicating PostgreSQL 15.x is installed.

### 4. Create a database for the application

```bash
createdb businesslm
```

This creates a new database named "businesslm" that will be used by the application.

## Installing pgvector Extension

The pgvector extension enables PostgreSQL to store and search vector embeddings efficiently, which is essential for our RAG implementation.

### Method 1: Install from Source (Recommended)

This method ensures compatibility with your specific PostgreSQL version.

1. Clone the pgvector repository:

```bash
cd /tmp
git clone --branch v0.5.1 https://github.com/pgvector/pgvector.git
cd pgvector
```

2. Compile and install the extension:

```bash
# Set PG_CONFIG to point to your PostgreSQL installation
PG_CONFIG=/opt/homebrew/opt/postgresql@15/bin/pg_config make
PG_CONFIG=/opt/homebrew/opt/postgresql@15/bin/pg_config make install
```

> **Note**: The path to pg_config may vary depending on your system. For Intel Macs, it might be `/usr/local/opt/postgresql@15/bin/pg_config`.

### Method 2: Install using Homebrew (Alternative)

```bash
brew install pgvector
```

> **Warning**: The Homebrew version might not be compatible with your PostgreSQL version. Use Method 1 if you encounter issues.

## Enabling pgvector in Your Database

After installing the extension, you need to enable it in your database:

```bash
psql -d businesslm -c "CREATE EXTENSION IF NOT EXISTS vector;"
```

## Verifying the Installation

To verify that PostgreSQL and pgvector are correctly installed and configured:

```bash
psql -d businesslm
```

Once in the PostgreSQL shell, run:

```sql
-- Check if pgvector extension is installed
SELECT * FROM pg_extension WHERE extname = 'vector';

-- Test vector functionality
CREATE TABLE vector_test (id serial primary key, embedding vector(3));
INSERT INTO vector_test (embedding) VALUES ('[1,2,3]');
SELECT * FROM vector_test;
DROP TABLE vector_test;

-- Exit PostgreSQL shell
\q
```

## Troubleshooting

### Common Issues

1. **Permission denied errors**:
   - Ensure your user has the necessary permissions
   - Try running commands with `sudo` if needed

2. **"Could not connect to server" errors**:
   - Verify PostgreSQL is running: `brew services list | grep postgresql`
   - Restart the service: `brew services restart postgresql@15`

3. **pgvector compilation errors**:
   - Ensure you have development tools installed: `xcode-select --install`
   - Verify the path to pg_config is correct

4. **"Extension 'vector' not found" error**:
   - Verify the extension was compiled against the correct PostgreSQL version
   - Check if the extension files are in the correct PostgreSQL extension directory

### PostgreSQL Configuration

The PostgreSQL configuration file is located at:
- `/opt/homebrew/var/postgresql@15/postgresql.conf` (Apple Silicon Macs)
- `/usr/local/var/postgresql@15/postgresql.conf` (Intel Macs)

You may need to adjust settings like `shared_buffers` for optimal performance.

## Updating the Database URL

After installation, update your application's `.env` file with the correct database URL:

```
DATABASE_URL=postgresql://username@localhost:5432/businesslm
```

Replace `username` with your macOS username.

## Next Steps

After successfully installing PostgreSQL and pgvector:

1. Run the database initialization script to create tables
2. Create test users for development
3. Start the backend application

For detailed instructions on these steps, refer to the `101-Run-and-Test-Python-Backend.md` document.

For a comprehensive guide to the PostgreSQL + pgvector implementation in the BusinessLM Python backend, including database schema, vector storage and retrieval, authentication, LangGraph checkpointing, and the Knowledge Base Service, refer to the `backend/docs/postgresql-pgvector-implementation.md` document.

## Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/15/index.html)
- [pgvector GitHub Repository](https://github.com/pgvector/pgvector)
- [pgvector Documentation](https://github.com/pgvector/pgvector/blob/master/README.md)
