# Development Guide

This document provides comprehensive guidance for developers working on the BusinessLM Python backend, covering development environment setup, code organization, testing, and best practices.

## Development Environment Setup

### Prerequisites

- Python 3.11+
- PostgreSQL 15+ with pgvector extension
- Git
- IDE with Python support (VS Code, PyCharm, etc.)

### Setting Up the Development Environment

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd businesslm
   ```

2. Create a virtual environment:
   ```bash
   python -m venv backend_env
   source backend_env/bin/activate  # On Windows: backend_env\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. Set up the database:
   ```bash
   # Follow instructions in 101-installing-postgresql-and-pgvector.md
   ```

6. Create test users:
   ```bash
   python scripts/create_test_user.py
   ```

7. Run the application:
   ```bash
   uvicorn app.main:app --reload
   ```

## Code Organization

The backend follows a modular architecture with clear separation of concerns:

```
backend/
  app/
    main.py                # Application entry point
    api/                   # API layer
      routes/              # API endpoints
      dependencies/        # FastAPI dependencies
      middleware/          # Middleware components
    core/                  # Core functionality
      auth/                # Authentication
      db/                  # Database
      errors/              # Error handling
      llm/                 # LLM adapters
    rag/                   # RAG components
    agents/                # Agent implementations
    langgraph/             # LangGraph components
    config/                # Configuration
  tests/                   # Tests
  scripts/                 # Utility scripts
  alembic/                 # Database migrations
```

### Key Design Principles

1. **Separation of Concerns**: Each module has a specific responsibility
2. **Dependency Injection**: Dependencies are injected rather than imported directly
3. **Async First**: Asynchronous code is used throughout for better performance
4. **Type Hints**: Type hints are used for better IDE support and code quality
5. **Error Handling**: Consistent error handling with custom exceptions
6. **Configuration**: Environment-based configuration with sensible defaults

## Database Migrations

The project uses Alembic for database migrations:

### Creating a New Migration

When you make changes to the database models, create a new migration:

```bash
# Generate a migration automatically based on model changes
alembic revision --autogenerate -m "Description of changes"

# Or create an empty migration
alembic revision -m "Description of changes"
```

### Applying Migrations

To apply migrations to the database:

```bash
# Apply all pending migrations
alembic upgrade head

# Apply specific migration
alembic upgrade <revision>

# Downgrade to a previous migration
alembic downgrade <revision>
```

### Migration Best Practices

1. Always review auto-generated migrations before applying them
2. Test migrations on a development database before production
3. Include data migrations when necessary
4. Keep migrations small and focused
5. Document complex migrations with comments

## Testing

The project uses pytest for testing:

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth/test_jwt.py

# Run with coverage report
pytest --cov=app

# Run with verbose output
pytest -v
```

### Test Organization

Tests are organized to mirror the application structure:

```
tests/
  conftest.py              # Shared fixtures
  test_auth/               # Authentication tests
    test_jwt.py            # JWT tests
    test_auth_api.py       # Auth API tests
  test_rag/                # RAG tests
  test_agents/             # Agent tests
  test_api/                # API tests
```

### Writing Tests

1. **Unit Tests**: Test individual functions and classes
2. **Integration Tests**: Test interactions between components
3. **API Tests**: Test API endpoints with FastAPI TestClient

Example test:

```python
def test_create_access_token():
    # Arrange
    data = {"sub": "user-id", "email": "<EMAIL>"}
    
    # Act
    token = create_access_token(data)
    
    # Assert
    assert token is not None
    payload = decode_token(token)
    assert payload["sub"] == "user-id"
    assert payload["email"] == "<EMAIL>"
```

### Test Fixtures

Use fixtures to set up test dependencies:

```python
@pytest.fixture
def db_session():
    # Set up test database
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    
    yield session
    
    # Clean up
    session.close()
```

## Adding New Features

### Adding a New API Endpoint

1. Create a new route file or add to an existing one:
   ```python
   @router.post("/new-endpoint")
   async def new_endpoint(
       data: NewEndpointRequest,
       current_user: User = Depends(get_current_user)
   ):
       # Implementation
       return {"result": "success"}
   ```

2. Register the router in `app/api/__init__.py` or `app/main.py`

3. Add tests for the new endpoint

### Adding a New Database Model

1. Define the model in `app/core/db/models.py`:
   ```python
   class NewModel(Base):
       __tablename__ = "new_models"
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
       name = Column(String, nullable=False)
       created_at = Column(DateTime, default=func.now())
   ```

2. Create a migration:
   ```bash
   alembic revision --autogenerate -m "Add NewModel"
   ```

3. Apply the migration:
   ```bash
   alembic upgrade head
   ```

### Adding a New Agent

1. Create a new agent file in `app/agents/`:
   ```python
   class NewAgent:
       def __init__(self, config):
           self.config = config
       
       async def process(self, query):
           # Implementation
           return {"response": "Agent response"}
   ```

2. Register the agent in the agent factory or orchestrator

3. Add tests for the new agent

## Error Handling

The application uses a consistent error handling approach:

1. **Custom Exceptions**: Define domain-specific exceptions in `app/core/errors/`
2. **Error Handlers**: Register error handlers in `app/api/error_handlers.py`
3. **Consistent Responses**: Return consistent error responses

Example:

```python
# Define custom exception
class ResourceNotFoundError(BusinessLMError):
    def __init__(self, resource_type, resource_id):
        super().__init__(
            message=f"{resource_type} with ID {resource_id} not found",
            details={"resource_type": resource_type, "resource_id": resource_id},
            status_code=404
        )

# Use in code
if not user:
    raise ResourceNotFoundError("User", user_id)

# Error handler (registered in app)
@app.exception_handler(BusinessLMError)
async def business_lm_error_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.message, "details": exc.details}
    )
```

## Logging

The application uses Python's built-in logging module:

```python
import logging

logger = logging.getLogger(__name__)

def some_function():
    logger.debug("Debug information")
    logger.info("Information message")
    logger.warning("Warning message")
    logger.error("Error message")
    logger.critical("Critical error")
```

Configure logging in `app/config/logging_config.py`.

## Performance Optimization

### Database Optimization

1. **Indexing**: Add indexes for frequently queried fields
2. **Connection Pooling**: Configure connection pooling parameters
3. **Query Optimization**: Use SQLAlchemy's query optimization features

### Async Processing

1. **Async Routes**: Use async/await for I/O-bound operations
2. **Background Tasks**: Use FastAPI background tasks for non-critical operations
3. **Task Queues**: Consider using Celery for heavy processing

## Deployment

### Preparing for Deployment

1. Set secure environment variables:
   ```
   JWT_SECRET_KEY=<secure-random-key>
   DATABASE_URL=<production-db-url>
   ```

2. Run tests to ensure everything works:
   ```bash
   pytest
   ```

3. Apply migrations:
   ```bash
   alembic upgrade head
   ```

### Deployment Options

1. **Docker**: Use the provided Dockerfile
2. **Kubernetes**: Deploy with Kubernetes for scaling
3. **Cloud Platforms**: Deploy to AWS, GCP, or Azure

## Contributing Guidelines

1. **Branch Naming**: Use descriptive branch names (e.g., `feature/jwt-auth`, `fix/database-connection`)
2. **Commit Messages**: Write clear, descriptive commit messages
3. **Pull Requests**: Include a description of changes and any related issues
4. **Code Style**: Follow PEP 8 and project-specific style guidelines
5. **Documentation**: Update documentation for new features
6. **Tests**: Add tests for new functionality

## Troubleshooting

### Common Issues

1. **Database Connection**: Check PostgreSQL is running and connection string is correct
2. **JWT Authentication**: Verify secret key and token expiration
3. **Embedding Models**: Ensure required packages are installed
4. **API Errors**: Check request format and authentication

### Debugging Tools

1. **FastAPI Debug Mode**: Run with `--debug` flag
2. **SQLAlchemy Echo**: Set `echo=True` in engine creation
3. **Logging**: Increase log level for more details
4. **Pytest**: Use `-v` flag for verbose output

## Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Alembic Documentation](https://alembic.sqlalchemy.org/)
- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [LangGraph Documentation](https://github.com/langchain-ai/langgraph)
