# RAG Pipeline with pgvector

This document provides a comprehensive guide to the Retrieval-Augmented Generation (RAG) pipeline implemented in the BusinessLM Python backend using PostgreSQL with pgvector.

## Overview

The RAG pipeline combines the power of large language models (LLMs) with a retrieval system that fetches relevant information from a knowledge base. This approach enhances the quality and factual accuracy of generated responses by grounding them in specific documents.

Key components:
- Document processing and chunking
- Embedding generation using various models
- Vector storage with pgvector for similarity search
- Retrieval based on semantic similarity
- Integration with LLMs for generation

## Architecture

The RAG pipeline consists of the following components:

1. **Document Loader**: Processes and chunks documents
2. **Embedding Model**: Converts text to vector embeddings
3. **Vector Store**: Stores and retrieves embeddings using similarity search
4. **Retriever**: Finds relevant documents based on a query
5. **Generator**: Combines retrieved documents with the query to generate a response

```
Query → [Embedding Model] → Vector Embedding
                              ↓
Document → [Chunking] → [Embedding Model] → [Vector Store (pgvector)]
                                                   ↓
                                            Retrieved Documents → [LLM] → Response
```

## Embedding Models

The system supports multiple embedding models through a unified interface:

### HuggingFace Models (Local)

- Default model: `intfloat/e5-base-v2` (768 dimensions)
- Runs locally on your machine
- No API costs
- Works offline
- Requires more computational resources

### OpenAI Models (API)

- Default model: `text-embedding-3-small` (1024 dimensions)
- Requires an OpenAI API key
- Better quality embeddings
- Less computational load on your machine
- Incurs API costs

### Embedding Factory

The `EmbeddingFactory` provides a unified way to create embedding models with automatic fallback:

```python
# Create a HuggingFace embedding model with fallback to OpenAI
embedding_model = EmbeddingFactory.create(
    provider="huggingface",
    fallback=True,
    model_name="intfloat/e5-base-v2"
)

# Use the model
embedding = await embedding_model.embed_query("What is RAG?")
```

## Vector Store with pgvector

The system uses PostgreSQL with the pgvector extension to store and retrieve vector embeddings efficiently.

### Vector Store Schema

```sql
CREATE TABLE embeddings (
    id TEXT PRIMARY KEY,
    embedding VECTOR(768),  -- Dimension depends on the embedding model
    text TEXT,
    meta_info JSONB
);

-- Create indexes for similarity search
CREATE INDEX ON embeddings USING ivfflat (embedding vector_cosine_ops);
```

### Supported Distance Metrics

- **Cosine Similarity**: Measures the cosine of the angle between vectors (default)
- **Euclidean Distance (L2)**: Measures the straight-line distance between vectors
- **Dot Product**: Measures the product of the vectors (useful for normalized embeddings)

### Vector Store Interface

The `VectorStore` interface provides a unified way to interact with different vector store implementations:

```python
# Create a pgvector store
vector_store = get_vector_store(
    store_type="pgvector",
    table_name="embeddings",
    distance_metric="cosine"
)

# Add documents to the store
ids = await vector_store.add_texts(
    texts=["Document 1", "Document 2"],
    metadatas=[{"source": "file1.txt"}, {"source": "file2.txt"}]
)

# Search for similar documents
results = await vector_store.search(
    query="What is RAG?",
    k=5  # Number of results to return
)
```

## Document Processing

Documents are processed and chunked before being stored in the vector store:

1. **Loading**: Documents are loaded from various sources (files, URLs, etc.)
2. **Splitting**: Documents are split into smaller chunks for better retrieval
3. **Embedding**: Each chunk is converted to a vector embedding
4. **Storage**: Embeddings are stored in the vector store with metadata

Example of processing a document:

```python
# Load a document
document = "This is a long document about RAG..."

# Split into chunks
chunks = split_text(document, chunk_size=1000, overlap=200)

# Generate embeddings and store
ids = await vector_store.add_texts(
    texts=chunks,
    metadatas=[{"source": "example.txt"} for _ in chunks]
)
```

## Similarity Search

The pgvector extension enables efficient similarity search in PostgreSQL:

```sql
-- Cosine similarity search
SELECT id, text, meta_info, embedding <=> '[0.1, 0.2, ...]'::vector AS score
FROM embeddings
ORDER BY score
LIMIT 5;

-- Euclidean distance search
SELECT id, text, meta_info, embedding <-> '[0.1, 0.2, ...]'::vector AS score
FROM embeddings
ORDER BY score
LIMIT 5;

-- Dot product search
SELECT id, text, meta_info, embedding <#> '[0.1, 0.2, ...]'::vector AS score
FROM embeddings
ORDER BY score
LIMIT 5;
```

## Performance Optimization

The pgvector extension supports several indexing methods for performance optimization:

### IVFFlat Index

```sql
CREATE INDEX ON embeddings USING ivfflat (embedding vector_cosine_ops);
```

- Divides vectors into lists for faster search
- Good balance of search speed and accuracy
- Recommended for most use cases

### HNSW Index

```sql
CREATE INDEX ON embeddings USING hnsw (embedding vector_cosine_ops);
```

- Hierarchical Navigable Small World graph
- Faster search but slower index creation
- Better for read-heavy workloads

## Using the RAG Pipeline

### Adding Documents

```python
from app.rag.loader import load_documents

# Load and process documents
documents = [
    "Document 1 content...",
    "Document 2 content..."
]

# Add to vector store
await load_documents(
    documents=documents,
    metadatas=[{"source": "manual"} for _ in documents]
)
```

### Querying the RAG Pipeline

```python
from app.rag.vector_store import get_vector_store

# Get the vector store
vector_store = get_vector_store()

# Search for relevant documents
results = await vector_store.search(
    query="What is the capital of France?",
    k=3
)

# Process results
for result in results:
    print(f"Document: {result['text']}")
    print(f"Score: {result['score']}")
    print(f"Metadata: {result['metadata']}")
```

## Configuration

The RAG pipeline can be configured through environment variables:

```
# Embedding Model
EMBEDDING_MODEL=intfloat/e5-base-v2

# Vector Store
VECTOR_STORE_TYPE=pgvector
VECTOR_STORE_TABLE=embeddings
VECTOR_STORE_DISTANCE_METRIC=cosine

# OpenAI API (optional)
OPENAI_API_KEY=your-api-key
```

## Troubleshooting

### Embedding Generation Issues

If embedding generation fails:
- Check that the required packages are installed (`sentence-transformers` for HuggingFace, `openai` for OpenAI)
- Verify API keys if using OpenAI
- Check for sufficient memory if using large local models

### Vector Store Issues

If vector store operations fail:
- Verify PostgreSQL is running
- Check that the pgvector extension is installed
- Ensure the database connection string is correct
- Verify the embedding dimensions match the vector store schema

### Search Quality Issues

If search results are poor:
- Try different embedding models
- Adjust chunk size and overlap
- Consider different distance metrics
- Add more diverse and high-quality documents

## Implementation Details

The RAG pipeline is implemented in the following files:

- `app/rag/embeddings.py`: Embedding model implementations
- `app/rag/embedding_utils.py`: Utilities for working with embeddings
- `app/rag/vector_store.py`: Vector store interface and factory
- `app/rag/pgvector_store.py`: PostgreSQL vector store implementation
- `app/rag/pgvector_knowledge_base.py`: Knowledge base service using pgvector
- `app/rag/knowledge_base_factory.py`: Factory for creating knowledge base services
- `app/rag/loader.py`: Document loading and processing

### Key Components

1. **PGVectorStore**: A vector store implementation that uses PostgreSQL with pgvector for storing and retrieving embeddings. It supports:
   - Vector similarity search using cosine similarity, L2 distance, or inner product
   - Hybrid search combining vector similarity and keyword search
   - Vector indexing using HNSW, IVFFlat, or basic indexing
   - Metadata filtering for search queries

2. **PgVectorKnowledgeBaseService**: A knowledge base service implementation that uses the `PGVectorStore` for storing and retrieving embeddings. It supports:
   - Vector similarity search
   - Keyword search
   - Hybrid search combining vector similarity and keyword search
   - Document ingestion and chunking
   - Metadata filtering
   - Reranking for improved search results

### Challenges and Solutions

During the implementation, we encountered several challenges:

1. **PostgreSQL Type Handling**: PostgreSQL's pgvector extension requires special handling for vector types. We solved this by using direct string interpolation for vector parameters in SQL queries.

2. **SQL Parameter Binding**: We encountered issues with SQL parameter binding for vector types. We solved this by using direct string interpolation for vector parameters in SQL queries.

3. **Table Creation**: We encountered issues with table creation and verification. We solved this by adding explicit verification steps after table creation.

4. **Vector Dimension Limitations**: We encountered issues with vector dimension limitations in PostgreSQL's btree index. We solved this by using smaller vector dimensions for testing.

5. **Vector Index Operators**: The syntax for creating indexes with specialized operators (`vector_l2_ops`, `vector_cosine_ops`, `vector_ip_ops`) varies between pgvector versions. We implemented a robust index creation method that tries different approaches and falls back gracefully.

### Testing

We've created test scripts to verify the functionality of the implementation:

1. `scripts/test_pgvector.py`: Tests both the raw pgvector functionality and the `PGVectorStore` class
2. `scripts/test_pgvector_knowledge_base.py`: Tests the `PgVectorKnowledgeBaseService` class

The tests verify:
- Vector embedding storage and retrieval
- Vector similarity search
- Hybrid search combining vector similarity and keyword search
- Metadata filtering for search queries
- Error handling and graceful degradation

## Advanced Features

### Hybrid Search

Combine semantic search with keyword search for better results:

```python
# Search with filters
results = await vector_store.search(
    query="What is RAG?",
    filters={"source": "documentation"}
)
```

### Metadata Filtering

Filter search results based on metadata:

```python
# Search with metadata filters
results = await vector_store.search(
    query="What is RAG?",
    filters={"metadata.category": "technical"}
)
```

### Custom Embedding Dimensions

Adjust embedding dimensions for different use cases:

```python
# Create OpenAI embeddings with custom dimensions
embedding_model = OpenAIEmbedding(
    model_name="text-embedding-3-small",
    dimensions=512  # Default is 1024
)
```

## Future Improvements

The implementation can be enhanced in the following ways:

1. **Performance Optimization**: The implementation can be optimized for better performance, especially for large document collections.
   - Implement batch processing for document ingestion
   - Optimize query execution plans
   - Add more sophisticated caching strategies

2. **Advanced Indexing**: The implementation can be enhanced with more advanced indexing techniques.
   - Fine-tune HNSW and IVFFlat parameters for specific use cases
   - Implement automatic index selection based on query patterns
   - Add support for hybrid indexes combining vector and text search

3. **Caching**: The implementation can be enhanced with caching for frequently accessed documents and search results.
   - Implement multi-level caching (results and embeddings)
   - Add support for different eviction policies
   - Consider distributed caching for scaling

4. **Scalability**: The implementation can be enhanced for better scalability, especially for large document collections.
   - Implement sharding strategies for very large collections
   - Add support for read replicas
   - Optimize for high-concurrency scenarios
