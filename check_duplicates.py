#!/usr/bin/env python3
import asyncio
import os
import sys
from sqlalchemy import text

async def check_duplicates():
    # Add the current directory to the path
    sys.path.append('.')
    
    # Import the database module
    from backend.app.core.db.database import get_db_engine
    
    print("Connecting to PostgreSQL database...")
    engine = await get_db_engine()
    
    async with engine.connect() as conn:
        # Count total documents
        result = await conn.execute(text('SELECT COUNT(*) FROM documents'))
        doc_count = result.scalar()
        print(f"Total documents in database: {doc_count}")
        
        # Count total document chunks
        result = await conn.execute(text('SELECT COUNT(*) FROM document_chunks'))
        chunk_count = result.scalar()
        print(f"Total document chunks in database: {chunk_count}")
        
        # List all documents
        print("\nAll documents in database:")
        result = await conn.execute(text('''
            SELECT id, metadata->>'title' as title, metadata->>'department' as department
            FROM documents
            ORDER BY metadata->>'department', metadata->>'title'
        '''))
        documents = result.fetchall()
        for doc in documents:
            print(f"- ID: {doc[0]}, Title: {doc[1]}, Department: {doc[2]}")
        
        # Check for duplicate documents by title
        print("\nChecking for duplicate documents by title...")
        result = await conn.execute(text('''
            SELECT metadata->>'title' as title, COUNT(*) as count
            FROM documents
            GROUP BY metadata->>'title'
            HAVING COUNT(*) > 1
        '''))
        title_duplicates = result.fetchall()
        
        if title_duplicates:
            print("Found duplicate document titles:")
            for dup in title_duplicates:
                print(f"- Title: {dup[0]}, Count: {dup[1]}")
                
                # Show the duplicate documents
                result = await conn.execute(text('''
                    SELECT id, metadata->>'department' as department
                    FROM documents
                    WHERE metadata->>'title' = :title
                '''), {"title": dup[0]})
                dup_docs = result.fetchall()
                for doc in dup_docs:
                    print(f"  - ID: {doc[0]}, Department: {doc[1]}")
        else:
            print("No duplicate document titles found.")
        
        # Check for duplicate documents by content
        print("\nChecking for duplicate documents by content...")
        result = await conn.execute(text('''
            SELECT LEFT(content, 50) as content_preview, COUNT(*) as count
            FROM documents
            GROUP BY content
            HAVING COUNT(*) > 1
        '''))
        content_duplicates = result.fetchall()
        
        if content_duplicates:
            print("Found duplicate document content:")
            for dup in content_duplicates:
                print(f"- Content preview: {dup[0]}..., Count: {dup[1]}")
        else:
            print("No duplicate document content found.")
        
        # Check for duplicate document chunks
        print("\nChecking for duplicate document chunks...")
        result = await conn.execute(text('''
            SELECT document_id, chunk_index, COUNT(*) as count
            FROM document_chunks
            GROUP BY document_id, chunk_index
            HAVING COUNT(*) > 1
        '''))
        chunk_duplicates = result.fetchall()
        
        if chunk_duplicates:
            print("Found duplicate document chunks:")
            for dup in chunk_duplicates:
                print(f"- Document ID: {dup[0]}, Chunk Index: {dup[1]}, Count: {dup[2]}")
        else:
            print("No duplicate document chunks found.")

if __name__ == "__main__":
    asyncio.run(check_duplicates())
