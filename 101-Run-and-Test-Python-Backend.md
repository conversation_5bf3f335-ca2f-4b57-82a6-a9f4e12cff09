# BusinessLM Python Backend: Setup and Testing Guide

> **IMPORTANT**: This repository is now focused on the Python backend implementation located in the `@backend/` directory. The TypeScript frontend code has been frozen and is not actively maintained. See [FRONTEND-FROZEN.md](./FRONTEND-FROZEN.md) for details.

This guide provides comprehensive instructions for setting up, running, and testing the Python implementation of BusinessLM.

## Overview

BusinessLM is an all-in-one business co-pilot that provides solopreneurs and small business owners with their own complete AI executive team. The platform delivers specialized AI department heads across Finance, Marketing, Operations, HR, Sales, Product, and a strategic Co-CEO that function as a virtual leadership team with agentic capabilities.

What sets BusinessLM apart is its ability to not only provide expert advice across business domains but also execute real business actions through powerful integrations with external systems. The AI agents can autonomously perform tasks like recording financial transactions, posting to social media, querying CRM data, and managing emails, creating a truly agentic experience.

The development focus has shifted to the Python-based backend implementation located in the `@backend/` directory. The backend provides:

- FastAPI web framework
- PostgreSQL with pgvector for vector storage
- JWT authentication
- RAG (Retrieval Augmented Generation) pipeline with HuggingFace embeddings
- Multi-agent orchestration with LangGraph
- Support for multiple LLM providers (OpenAI, Anthropic, Google Gemini)

## Architecture

The backend is built using the following technologies:

- **FastAPI**: A modern, fast web framework for building APIs with Python
- **PostgreSQL**: A powerful, open-source relational database with pgvector extension for vector storage
- **JWT Authentication**: Secure authentication using JSON Web Tokens
- **SQLAlchemy**: SQL toolkit and Object-Relational Mapping (ORM) for Python
- **LangGraph**: Framework for building stateful, multi-agent applications
- **Pydantic**: Data validation and settings management using Python type annotations

The backend follows a modular architecture with the following components:

- **API Layer** (`app/api/`): FastAPI routes, dependencies, and middleware
- **Service Layer** (`app/agents/`, `app/langgraph/`): Business logic and orchestration
- **Data Layer** (`app/core/db/`): Database models and repositories
- **RAG Pipeline** (`app/rag/`): Embeddings, vector store, and retrieval
- **Agent Orchestration** (`app/langgraph/`): LangGraph for multi-agent coordination
- **Authentication** (`app/core/auth/`): JWT-based authentication
- **LLM adapters** (`app/core/llm/`): Unified interface for multiple LLM providers

### Authentication

The backend uses JWT authentication for secure API access. This includes:

- Token-based authentication with access and refresh tokens
- Role-based access control
- Secure password hashing with bcrypt
- HTTP-only cookies for refresh tokens

For more details on the authentication system, see the [JWT Authentication Documentation](backend/docs/auth/jwt-auth-middleware.md) and [Firebase to JWT Migration](backend/docs/auth/firebase-to-jwt-migration.md).

### RAG (Retrieval Augmented Generation)

The backend includes a comprehensive RAG system for retrieving relevant information from documents:

- **Vector Store**: PostgreSQL with pgvector extension for efficient vector similarity search
- **Embedding Models**: Support for both local (HuggingFace) and cloud-based (OpenAI) embedding models
- **Retrieval**: Hybrid retrieval combining vector similarity and text search
- **Reranking**: Optional reranking of retrieved documents for improved relevance

For more details on the RAG system, see the [RAG Documentation](backend/docs/rag/).

### Multi-Agent Orchestration

The backend uses LangGraph for orchestrating multiple agents:

- **Agent Definitions**: Modular agent definitions with specific roles and capabilities
- **Conversation State**: Persistent conversation state using PostgreSQL
- **Checkpointing**: Ability to save and restore conversation state

### Directory Structure

```
backend/
  app/
    main.py                # FastAPI application entry point
    api/                   # API routes and middleware
      middleware/          # Middleware components
      routes/              # API routes
      dependencies/        # FastAPI dependencies
    agents/                # Agent implementations
    core/                  # Core functionality
      auth/                # Authentication
      db/                  # Database
      errors/              # Error handling
      llm/                 # LLM adapters
      timeout/             # Timeout utilities
    rag/                   # RAG components
      embeddings.py        # Embedding models
      vector_store.py      # Vector storage
      retriever.py         # Document retrieval
      generator.py         # Response generation
    langgraph/             # LangGraph components
    config/                # Configuration
  tests/                   # Tests
  scripts/                 # Utility scripts
```

## Prerequisites

- Python 3.11+
- PostgreSQL 15+ with pgvector extension (see [101-installing-postgresql-and-pgvector.md](./101-installing-postgresql-and-pgvector.md))
- Git
- API keys for LLM services (optional, for production use)

## Installation

1. Clone the repository (if not already done)

2. Create a virtual environment:
   ```bash
   python -m venv backend_env
   source backend_env/bin/activate  # On Windows: backend_env\Scripts\activate
   ```

3. Install Python dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit .env with your configuration
   # For PostgreSQL connection:
   DATABASE_URL=postgresql://username@localhost:5432/businesslm

   # For JWT authentication:
   JWT_SECRET_KEY=your-secret-key-change-in-production

   # For development mode:
   DEVELOPMENT_MODE=true
   DEBUG=true

   # For LLM providers (optional):
   # OPENAI_API_KEY=your-openai-api-key
   # ANTHROPIC_API_KEY=your-anthropic-api-key
   # GOOGLE_API_KEY=your-gemini-api-key

   # For embedding model:
   EMBEDDING_MODEL=intfloat/e5-base-v2
   ```

5. Create test users:
   ```bash
   cd backend
   python scripts/create_test_user.py
   ```

   This will create two test users:
   - Regular User:
     - Email: <EMAIL>
     - Password: secure123
     - Roles: ["user"]

   - Admin User:
     - Email: <EMAIL>
     - Password: admin123
     - Roles: ["admin", "user"]
     - Permissions: ["*"]

## Running the Backend

To run the backend:

```bash
# Navigate to the backend directory
cd backend

# Start the server with auto-reload
uvicorn app.main:app --reload

# Or specify a custom port
uvicorn app.main:app --reload --port 8080
```

The backend will be available at http://localhost:8000 (or your specified port) and the API documentation at http://localhost:8000/docs

### Development Mode

For testing without API keys, you can use development mode by setting `DEVELOPMENT_MODE=true` in your `.env` file.

Development mode provides:
- Mock LLM adapters instead of requiring real API keys
- In-memory vector store if PostgreSQL is not available
- Simplified authentication for testing
- Proper CORS configuration for local development

### Using Real LLM Providers

If you want to use real LLM providers, set `DEVELOPMENT_MODE=false` in your `.env` file and add your API keys:

```
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Anthropic
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google (Gemini)
GOOGLE_API_KEY=your-google-api-key
```

The backend will automatically detect which providers are available based on the API keys you provide.

## Testing the Backend

### Running Tests

You can run the automated test suite to verify that everything is working correctly:

```bash
# Navigate to the backend directory
cd backend

# Run all tests
pytest

# Run specific test modules
pytest tests/test_auth/

# Run with verbose output
pytest -v
```

### API Testing

You can test the API directly using the FastAPI Swagger UI at http://localhost:8000/docs.

The Swagger UI provides an interactive interface to:
1. Explore available endpoints
2. Test authentication
3. Send requests and view responses
4. View request/response schemas

Example API endpoints to test:

- `POST /api/auth/login`: Login with username and password
- `POST /api/auth/refresh`: Refresh an access token
- `GET /api/auth/me`: Get current user information
- `POST /api/agents/query`: Query an agent
- `GET /health`: Health check endpoint

### Testing with curl

You can also test the API using curl:

```bash
# Test the health endpoint
curl http://localhost:8000/health

# Login to get a JWT token
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=secure123"

# Use the token for authenticated requests
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test the agents query endpoint
curl -X POST http://localhost:8000/api/agents/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"query": "Help me with marketing"}'
```

## Running the Frontend with Python Backend

1. Install frontend dependencies:
   ```bash
   npm install
   ```

2. Ensure the `USE_PYTHON_BACKEND` flag is set to `true` in `src/genkit/config/backendConfig.ts`:
   ```typescript
   // Set this to true to use the Python backend, false to use the TypeScript implementation
   export const USE_PYTHON_BACKEND = true;
   ```

3. Start the frontend development server:
   ```bash
   npm run dev
   ```

4. The frontend will be available at http://localhost:3000 (or the port specified by your development server)

## API Endpoints

The Python backend provides the following API endpoints:

### Authentication
- `POST /api/auth/login`: Login with username and password
- `POST /api/auth/refresh`: Refresh an access token
- `GET /api/auth/me`: Get current user information
- `POST /api/auth/logout`: Logout and invalidate refresh token

### Agents
- `POST /api/agents/query`: Query the agent with a user message
- `GET /api/agents/departments`: Get the list of available departments

### LLM
- `GET /api/llm/models`: Get available LLM models

### System
- `GET /health`: Health check endpoint

## Department Agent System

BusinessLM's core functionality revolves around specialized department agents that provide domain-specific expertise:

- **Finance Department:** Financial planning, budgeting, forecasting, investment strategy, cost optimization, financial risk management, cash flow management, and financial reporting. Can perform actions like recording expenses, recording revenue, and querying financial data.

- **Marketing Department:** Brand strategy, digital marketing, content marketing, social media strategy, marketing analytics, customer acquisition, and market research. Can perform actions like querying CRM data, posting on social media platforms (X/Twitter, LinkedIn), and managing marketing emails.

- **Operations Department:** Process optimization, supply chain management, quality control, operational efficiency, vendor management, logistics, and sustainability.

- **HR Department:** Talent acquisition, employee development, performance management, compensation and benefits, employee engagement, organizational development, and HR compliance.

- **Sales Department:** Sales strategy, pipeline management, customer relationship management, sales forecasting, territory management, sales enablement, and negotiation.

- **Product Department:** Product strategy, product development, user experience, product roadmapping, feature prioritization, product analytics, and market requirements.

- **Co-CEO:** Strategic oversight, cross-department coordination, executive leadership, business strategy, organizational vision, stakeholder management, and corporate governance.

### Agentic Functions & Automations

The department agents can perform various automated actions through function calling capabilities:

- **Financial Functions:**
  - `record_expense`: Records expense transactions with details like date, amount, category, type, payment method, and vendor
  - `record_revenue`: Records revenue transactions with date, amount, and product/service information
  - `query_financial_data`: Retrieves and analyzes financial data based on specific queries

- **Marketing & CRM Functions:**
  - `query_crm`: Retrieves information about contacts, companies, and outreach efforts
  - `post_x`: Creates and posts content on X (Twitter) with support for hashtags and mentions
  - `post_linkedin`: Creates and posts content on LinkedIn with various post types

- **Email Management Functions:**
  - `read_email`: Searches and reads emails based on specific queries
  - `draft_email`: Creates draft emails with recipients, subject, body, and optional CC/BCC
  - `send_email`: Sends emails directly from the platform
  - `respond_mail`: Responds to specific emails

## Troubleshooting

### Database Issues

If you encounter database-related errors:

1. Verify PostgreSQL is running:
   ```bash
   brew services list | grep postgresql
   ```

2. Check your database connection string in `.env`:
   - Ensure the username matches your system username
   - Verify the database name is correct
   - Make sure the port is correct (default: 5432)

3. Verify pgvector extension is installed:
   ```bash
   psql -d businesslm -c "SELECT * FROM pg_extension WHERE extname = 'vector';"
   ```

### Authentication Issues

If authentication fails:

1. Verify the test users were created:
   ```bash
   cd backend
   python scripts/create_test_user.py
   ```

2. Check that you're using the correct credentials:
   - Regular user: <EMAIL> / secure123
   - Admin user: <EMAIL> / admin123

3. Verify the JWT secret key in your `.env` file:
   ```
   JWT_SECRET_KEY=your-secret-key-change-in-production
   ```

### API Response Issues

If the agent doesn't respond or returns errors:

1. Check the backend logs for errors:
   ```bash
   # Look for error messages in the terminal where the backend is running
   ```

2. Verify that the LLM providers are properly configured:
   - In development mode, mock adapters will be used
   - If using real providers, ensure API keys are correctly set

3. Check the embedding model configuration:
   - Verify the model name in your `.env` file: `EMBEDDING_MODEL=intfloat/e5-base-v2`
   - Ensure the sentence-transformers package is installed

4. Test the API directly using the Swagger UI or curl as shown above

### Timeout Issues

If requests are timing out:

1. Adjust the timeout settings in your `.env` file:
   - Increase `TIMEOUT_NODE_DEFAULT` for longer processing time
   - Increase `TIMEOUT_LLM_DEFAULT` for slower LLM responses

2. Check for network issues:
   - Ensure your internet connection is stable
   - Verify that the LLM provider services are available

## Development

For development:

1. Run the backend with auto-reload:
   ```bash
   cd backend
   uvicorn app.main:app --reload --port 8000
   ```

2. Run tests:
   ```bash
   cd backend
   pytest
   ```

3. Create new database migrations:
   ```bash
   cd backend
   alembic revision --autogenerate -m "Description of changes"
   ```

4. Apply migrations:
   ```bash
   cd backend
   alembic upgrade head
   ```

## Security Considerations

- **Authentication:** Secure JWT-based authentication with refresh tokens
- **Data Protection:** PostgreSQL role-based access control and row-level security
- **Password Security:** Bcrypt password hashing with configurable rounds
- **API Security:** Secure handling of API keys through environment variables
- **Token Management:** HTTP-only cookies for refresh tokens and secure token validation

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Run tests to ensure everything works
4. Submit a pull request

## Note on Frontend

The TypeScript frontend code remains in the repository but is frozen and not actively maintained. All development focus is on the Python backend.

## License

This project is proprietary. All rights reserved.
